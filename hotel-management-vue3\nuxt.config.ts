// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-05-15',
  devtools: { enabled: true },

  modules: [
    '@nuxtjs/tailwindcss',
    '@vueuse/nuxt',
    '@pinia/nuxt',
    '@nuxt/icon'
  ],

  css: ['~/assets/css/main.css'],

  runtimeConfig: {
    public: {
      leancloudAppId: process.env.LEANCLOUD_APP_ID,
      leancloudAppKey: process.env.LEANCLOUD_APP_KEY,
      leancloudServerUrl: process.env.LEANCLOUD_SERVER_URL,
      wechatCorpId: process.env.WECHAT_CORP_ID,
      wechatAgentId: process.env.WECHAT_AGENT_ID
    }
  },

  app: {
    head: {
      title: '酒店管理系统',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'format-detection', content: 'telephone=no' }
      ]
    }
  },

  ssr: false // 使用SPA模式，适合LeanCloud
})
