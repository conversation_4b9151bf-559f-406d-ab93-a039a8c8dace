<template>
  <div class="text-center py-12">
    <!-- 图标 -->
    <div class="mb-4">
      <Icon 
        :name="icon" 
        :size="iconSize" 
        class="mx-auto"
        :class="iconColorClass"
      />
    </div>
    
    <!-- 标题 -->
    <h3 class="text-lg font-medium text-gray-900 mb-2">
      {{ title }}
    </h3>
    
    <!-- 描述 -->
    <p class="text-gray-600 mb-6 max-w-sm mx-auto">
      {{ description }}
    </p>
    
    <!-- 操作按钮 -->
    <div v-if="$slots.action" class="flex justify-center">
      <slot name="action"></slot>
    </div>
    
    <!-- 默认操作按钮 -->
    <button 
      v-else-if="actionText && actionHandler"
      @click="actionHandler"
      class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors"
    >
      {{ actionText }}
    </button>
  </div>
</template>

<script setup lang="ts">
interface Props {
  icon?: string
  iconSize?: number
  title: string
  description: string
  actionText?: string
  actionHandler?: () => void
  iconColor?: 'blue' | 'green' | 'red' | 'yellow' | 'purple' | 'gray'
}

const props = withDefaults(defineProps<Props>(), {
  icon: 'mdi:inbox',
  iconSize: 64,
  iconColor: 'gray'
})

// 计算属性
const iconColorClass = computed(() => {
  const colorMap = {
    blue: 'text-blue-400',
    green: 'text-green-400',
    red: 'text-red-400',
    yellow: 'text-yellow-400',
    purple: 'text-purple-400',
    gray: 'text-gray-400'
  }
  return colorMap[props.iconColor]
})
</script>
