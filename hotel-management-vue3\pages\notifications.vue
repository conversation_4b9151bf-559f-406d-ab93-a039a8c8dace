<template>
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">通知中心</h1>
          <p class="mt-1 text-sm text-gray-600">
            查看系统通知和消息
          </p>
        </div>
        
        <div class="mt-4 sm:mt-0 flex space-x-3">
          <button
            @click="markAllAsRead"
            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            :disabled="unreadCount === 0"
          >
            <Icon name="mdi:check-all" size="16" class="mr-2" />
            全部标记为已读
          </button>
          
          <button
            @click="clearAllNotifications"
            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            :disabled="notifications.length === 0"
          >
            <Icon name="mdi:delete-sweep" size="16" class="mr-2" />
            清空所有通知
          </button>
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:bell" size="24" class="text-blue-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">总通知</p>
            <p class="text-2xl font-semibold text-gray-900">{{ notifications.length }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:bell-badge" size="24" class="text-red-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">未读通知</p>
            <p class="text-2xl font-semibold text-gray-900">{{ unreadCount }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:clock-outline" size="24" class="text-green-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">今日通知</p>
            <p class="text-2xl font-semibold text-gray-900">{{ todayNotifications }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 通知列表 -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">通知列表</h3>
      </div>
      
      <div v-if="notifications.length === 0" class="p-8 text-center">
        <Icon name="mdi:bell-off" size="48" class="mx-auto text-gray-400 mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无通知</h3>
        <p class="text-gray-600">当有新的系统通知时，会在这里显示</p>
      </div>
      
      <div v-else class="divide-y divide-gray-200">
        <div
          v-for="notification in notifications"
          :key="notification.id"
          class="p-6 hover:bg-gray-50 transition-colors"
        >
          <div class="flex items-start">
            <!-- 图标 -->
            <div class="flex-shrink-0 mr-4">
              <div 
                :class="[
                  'w-10 h-10 rounded-full flex items-center justify-center',
                  getNotificationBgClass(notification.type)
                ]"
              >
                <Icon 
                  :name="getNotificationIcon(notification.type)" 
                  size="20" 
                  class="text-white"
                />
              </div>
            </div>
            
            <!-- 内容 -->
            <div class="flex-1 min-w-0">
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <h4 class="text-sm font-medium text-gray-900 mb-1">
                    {{ notification.title }}
                  </h4>
                  
                  <p 
                    v-if="notification.message" 
                    class="text-sm text-gray-600 mb-2"
                  >
                    {{ notification.message }}
                  </p>
                  
                  <div class="flex items-center text-xs text-gray-500">
                    <Icon name="mdi:clock-outline" size="12" class="mr-1" />
                    {{ formatTime(notification.createdAt) }}
                  </div>
                </div>
                
                <!-- 操作按钮 -->
                <div class="flex items-center space-x-2 ml-4">
                  <button
                    v-if="notification.actions && notification.actions.length > 0"
                    @click="handleNotificationAction(notification)"
                    class="text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    查看详情
                  </button>
                  
                  <button
                    @click="removeNotification(notification.id)"
                    class="text-gray-400 hover:text-gray-600"
                  >
                    <Icon name="mdi:close" size="16" />
                  </button>
                </div>
              </div>
              
              <!-- 操作按钮组 -->
              <div 
                v-if="notification.actions && notification.actions.length > 0"
                class="flex space-x-2 mt-3"
              >
                <button
                  v-for="action in notification.actions"
                  :key="action.label"
                  @click="handleAction(action, notification.id)"
                  :class="[
                    'px-3 py-1 text-xs font-medium rounded transition-colors',
                    action.style === 'primary' 
                      ? 'bg-blue-600 text-white hover:bg-blue-700'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  ]"
                >
                  {{ action.label }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 测试通知按钮 -->
    <div class="mt-8 bg-gray-50 rounded-lg p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">测试通知</h3>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <button
          @click="testNotification('success')"
          class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
        >
          成功通知
        </button>
        
        <button
          @click="testNotification('error')"
          class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors"
        >
          错误通知
        </button>
        
        <button
          @click="testNotification('warning')"
          class="bg-yellow-600 text-white px-4 py-2 rounded-md hover:bg-yellow-700 transition-colors"
        >
          警告通知
        </button>
        
        <button
          @click="testNotification('info')"
          class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          信息通知
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useGlobalNotifications, type NotificationAction } from '~/composables/useNotifications'

// 页面元数据
definePageMeta({
  title: '通知中心',
  middleware: 'auth'
})

const { 
  notifications, 
  unreadCount, 
  removeNotification, 
  clearAllNotifications, 
  markAsRead,
  success,
  error,
  warning,
  info,
  systemNotification
} = useGlobalNotifications()

// 计算今日通知数量
const todayNotifications = computed(() => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  
  return notifications.value.filter(notification => {
    const notificationDate = new Date(notification.createdAt)
    notificationDate.setHours(0, 0, 0, 0)
    return notificationDate.getTime() === today.getTime()
  }).length
})

// 全部标记为已读
const markAllAsRead = () => {
  markAsRead()
}

// 获取通知图标
const getNotificationIcon = (type: string) => {
  const icons = {
    success: 'mdi:check-circle',
    error: 'mdi:alert-circle',
    warning: 'mdi:alert',
    info: 'mdi:information'
  }
  return icons[type as keyof typeof icons] || icons.info
}

// 获取通知背景色
const getNotificationBgClass = (type: string) => {
  const classes = {
    success: 'bg-green-500',
    error: 'bg-red-500',
    warning: 'bg-yellow-500',
    info: 'bg-blue-500'
  }
  return classes[type as keyof typeof classes] || classes.info
}

// 格式化时间
const formatTime = (date: Date) => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 24小时内
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}

// 处理通知操作
const handleAction = (action: NotificationAction, notificationId: string) => {
  action.action()
  removeNotification(notificationId)
}

// 处理通知详情
const handleNotificationAction = (notification: any) => {
  if (notification.actions && notification.actions.length > 0) {
    notification.actions[0].action()
  }
}

// 测试通知
const testNotification = (type: 'success' | 'error' | 'warning' | 'info') => {
  const messages = {
    success: { title: '操作成功', message: '这是一个成功通知的示例' },
    error: { title: '操作失败', message: '这是一个错误通知的示例' },
    warning: { title: '注意事项', message: '这是一个警告通知的示例' },
    info: { title: '系统信息', message: '这是一个信息通知的示例' }
  }
  
  const config = messages[type]
  
  if (type === 'success') {
    success(config.title, config.message)
  } else if (type === 'error') {
    error(config.title, config.message)
  } else if (type === 'warning') {
    warning(config.title, config.message)
  } else {
    info(config.title, config.message)
  }
}

// 页面标题
useHead({
  title: '通知中心 - 酒店管理系统'
})
</script>
