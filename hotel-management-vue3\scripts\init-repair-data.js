// scripts/init-repair-data.js
// 初始化报修模块的LeanCloud数据

import AV from 'leancloud-storage'

// 初始化LeanCloud
AV.init({
  appId: 'epbCQbfBnJNaZv0O5CCLacgJ-gzGzoHsz',
  appKey: '9atvXPb61ih8GXsOVHD8dRCh',
  serverURL: 'https://epbcqbfb.lc-cn-n1-shared.com'
})

async function initRepairData() {
  console.log('🔧 初始化报修模块数据...')
  
  try {
    // 创建报修工单示例数据
    console.log('📋 创建报修工单示例数据...')
    
    const repairOrders = [
      {
        orderNumber: 'R1704067200001',
        title: '客房空调不制冷',
        description: '301房间空调开启后不制冷，客人反映房间温度过高',
        location: '301房间',
        category: '空调设备',
        priority: 3, // 1=低, 2=中, 3=高
        status: 'pending',
        reporterId: 'user1',
        contactInfo: '前台-小李',
        images: [],
        estimatedCost: 0,
        actualCost: 0
      },
      {
        orderNumber: 'R1704067200002',
        title: '卫生间水龙头漏水',
        description: '205房间卫生间洗手台水龙头持续滴水，需要维修',
        location: '205房间卫生间',
        category: '水电设备',
        priority: 2, // 1=低, 2=中, 3=高
        status: 'assigned',
        reporterId: 'user2',
        assigneeId: 'engineer1',
        contactInfo: '客房部-小王',
        images: [],
        estimatedCost: 50,
        actualCost: 0
      },
      {
        orderNumber: 'R1704067200003',
        title: '电梯按钮失灵',
        description: '2号电梯3楼按钮按下后无反应，影响客人使用',
        location: '2号电梯',
        category: '电梯设备',
        priority: 3, // 1=低, 2=中, 3=高
        status: 'in_progress',
        reporterId: 'user3',
        assigneeId: 'engineer2',
        contactInfo: '保安部-小张',
        images: [],
        estimatedCost: 200,
        actualCost: 0
      },
      {
        orderNumber: 'R1704067200004',
        title: '大堂灯具故障',
        description: '大堂吊灯有几盏不亮，影响整体照明效果',
        location: '大堂',
        category: '照明设备',
        priority: 1, // 1=低, 2=中, 3=高
        status: 'completed',
        reporterId: 'user4',
        assigneeId: 'engineer1',
        contactInfo: '工程部-小陈',
        images: [],
        estimatedCost: 80,
        actualCost: 75,
        completedAt: new Date(Date.now() - 24 * 60 * 60 * 1000) // 1天前完成
      },
      {
        orderNumber: 'R1704067200005',
        title: '热水器温度不够',
        description: '员工宿舍热水器加热效果差，水温达不到要求',
        location: '员工宿舍B栋',
        category: '水电设备',
        priority: 2, // 1=低, 2=中, 3=高
        status: 'pending',
        reporterId: 'user5',
        contactInfo: '人事部-小刘',
        images: [],
        estimatedCost: 0,
        actualCost: 0
      }
    ]
    
    for (const orderData of repairOrders) {
      const RepairOrder = AV.Object.extend('RepairOrder')
      const order = new RepairOrder()
      
      Object.keys(orderData).forEach(key => {
        order.set(key, orderData[key])
      })
      
      await order.save()
      console.log(`✅ 创建报修工单: ${orderData.title}`)
    }
    
    // 创建报修分类配置
    console.log('🏷️ 创建报修分类配置...')
    
    const categories = [
      {
        name: '空调设备',
        description: '空调、新风系统等制冷制热设备',
        icon: 'mdi:air-conditioner',
        color: '#3B82F6'
      },
      {
        name: '水电设备',
        description: '水管、电路、照明等基础设施',
        icon: 'mdi:water-pump',
        color: '#10B981'
      },
      {
        name: '电梯设备',
        description: '客梯、货梯等垂直交通设备',
        icon: 'mdi:elevator',
        color: '#F59E0B'
      },
      {
        name: '照明设备',
        description: '各类灯具、照明控制系统',
        icon: 'mdi:lightbulb',
        color: '#EF4444'
      },
      {
        name: '安防设备',
        description: '监控、门禁、报警等安全设备',
        icon: 'mdi:security',
        color: '#8B5CF6'
      },
      {
        name: '网络设备',
        description: 'WiFi、网络、通信等设备',
        icon: 'mdi:wifi',
        color: '#06B6D4'
      }
    ]
    
    for (const categoryData of categories) {
      const RepairCategory = AV.Object.extend('RepairCategory')
      const category = new RepairCategory()
      
      Object.keys(categoryData).forEach(key => {
        category.set(key, categoryData[key])
      })
      
      await category.save()
      console.log(`✅ 创建报修分类: ${categoryData.name}`)
    }
    
    // 创建工程师用户数据
    console.log('👷 创建工程师用户数据...')
    
    const engineers = [
      {
        id: 'engineer1',
        username: 'engineer1',
        realName: '张师傅',
        department: '工程部',
        phone: '13800138001',
        email: '<EMAIL>',
        specialties: ['空调设备', '水电设备'],
        status: 'active'
      },
      {
        id: 'engineer2',
        username: 'engineer2',
        realName: '李师傅',
        department: '工程部',
        phone: '13800138002',
        email: '<EMAIL>',
        specialties: ['电梯设备', '安防设备'],
        status: 'active'
      },
      {
        id: 'engineer3',
        username: 'engineer3',
        realName: '王师傅',
        department: '工程部',
        phone: '13800138003',
        email: '<EMAIL>',
        specialties: ['照明设备', '网络设备'],
        status: 'active'
      }
    ]
    
    for (const engineerData of engineers) {
      const Engineer = AV.Object.extend('Engineer')
      const engineer = new Engineer()
      
      Object.keys(engineerData).forEach(key => {
        engineer.set(key, engineerData[key])
      })
      
      await engineer.save()
      console.log(`✅ 创建工程师: ${engineerData.realName}`)
    }
    
    console.log('🎉 报修模块数据初始化完成！')
    
  } catch (error) {
    console.error('❌ 初始化失败:', error)
  }
}

async function main() {
  console.log('🚀 开始初始化报修模块数据...')
  
  try {
    await initRepairData()
    console.log('✅ 报修模块数据初始化完成！')
  } catch (error) {
    console.error('❌ 初始化过程中出现错误:', error)
  }
  
  process.exit(0)
}

// 直接运行主函数
main()
