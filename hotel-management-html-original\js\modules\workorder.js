/**
 * 工单模块 - 实现工单提交功能
 * 继承自BaseWorkLogApp，实现工单特定功能
 */

class WorkOrderApp extends BaseWorkLogApp {
    constructor() {
        super({
            pageType: 'workorder',
            requiredElements: ['workOrderSection', 'submitBtn']
        });
        
        // 绑定方法上下文
        this.handleSubmit = this.handleSubmit.bind(this);
        this.handleImageUpload = this.handleImageUpload.bind(this);
        this.handleCancel = this.handleCancel.bind(this);
        
        // 上传的图片列表
        this.uploadedImages = [];
    }

    /**
     * 获取页面特定的DOM元素
     */
    getPageElements() {
        return {
            // 页面区域
            welcomeSection: 'welcomeSection',
            workOrderSection: 'workOrderSection',
            loginPrompt: 'loginPrompt',
            promptLoginBtn: 'promptLoginBtn',

            // 表单字段
            workOrderForm: 'workOrderForm',
            category: 'category',
            urgency: 'urgency',
            location: 'location',
            description: 'description',

            // 图片上传
            imageInput: 'imageInput',
            uploadArea: 'uploadArea',
            imagePreview: 'imagePreview',

            // 按钮
            submitBtn: 'submitBtn',
            cancelBtn: 'cancelBtn',
            welcomeLoginBtn: 'welcomeLoginBtn',

            // 用户信息
            realName: 'realName',
            userInfo: 'userInfo',
            loginBtn: 'loginBtn',
            logoutBtn: 'logoutBtn'
        };
    }

    /**
     * 绑定页面特定的事件
     */
    bindPageEvents() {
        // 表单提交
        if (this.elements.workOrderForm) {
            this.elements.workOrderForm.addEventListener('submit', this.handleSubmit);
        }

        // 取消按钮
        if (this.elements.cancelBtn) {
            this.elements.cancelBtn.addEventListener('click', this.handleCancel);
        }

        // 图片上传
        if (this.elements.imageInput) {
            this.elements.imageInput.addEventListener('change', this.handleImageUpload);
        }

        // 上传区域点击
        if (this.elements.uploadArea) {
            this.elements.uploadArea.addEventListener('click', () => {
                this.elements.imageInput.click();
            });
        }

        // 欢迎页面登录按钮
        if (this.elements.welcomeLoginBtn) {
            this.elements.welcomeLoginBtn.addEventListener('click', () => {
                this.showLoginModal();
            });
        }

        // 提示登录按钮
        if (this.elements.promptLoginBtn) {
            this.elements.promptLoginBtn.addEventListener('click', () => {
                this.showLoginModal();
            });
        }
    }

    /**
     * 用户登录后的回调
     */
    onUserLoggedIn() {
        this.showUserInterface();
    }

    /**
     * 用户登出后的回调
     */
    onUserLoggedOut() {
        this.showWelcomePage();
    }

    /**
     * 显示用户界面
     */
    showUserInterface() {
        // 更新头部显示
        if (this.elements.userInfo) {
            this.elements.userInfo.classList.remove('hidden');
        }
        if (this.elements.loginBtn) {
            this.elements.loginBtn.classList.add('hidden');
        }

        // 更新用户信息显示
        this.updateUserDisplay();

        // 显示主要内容区域
        if (this.elements.loginPrompt) {
            this.elements.loginPrompt.style.display = 'none';
        }
        if (this.elements.welcomeSection) {
            this.elements.welcomeSection.style.display = 'none';
        }
        if (this.elements.workOrderSection) {
            this.elements.workOrderSection.style.display = 'block';
        }
    }

    /**
     * 更新用户显示信息
     */
    updateUserDisplay() {
        const currentUser = WorkLogAuth.getCurrentUser();
        if (!currentUser) return;

        const username = currentUser.get('username');
        const realName = currentUser.get('realName') || username;

        // 更新真实姓名显示
        if (this.elements.realName) {
            this.elements.realName.textContent = realName;
        }
    }

    /**
     * 显示欢迎页面
     */
    showWelcomePage() {
        // 更新头部显示
        if (this.elements.userInfo) {
            this.elements.userInfo.classList.add('hidden');
        }
        if (this.elements.loginBtn) {
            this.elements.loginBtn.classList.remove('hidden');
        }

        // 显示欢迎页面内容
        if (this.elements.workOrderSection) {
            this.elements.workOrderSection.style.display = 'none';
        }
        if (this.elements.loginPrompt) {
            this.elements.loginPrompt.style.display = 'none';
        }
        if (this.elements.welcomeSection) {
            this.elements.welcomeSection.style.display = 'block';
        }
    }

    /**
     * 处理表单提交
     */
    async handleSubmit(event) {
        event.preventDefault();

        if (!this.validateForm()) {
            return;
        }

        try {
            // 显示提交状态
            this.elements.submitBtn.disabled = true;
            this.elements.submitBtn.textContent = '提交中...';

            // 生成工单号
            const orderNumber = this.generateOrderNumber();

            // 构建工单数据
            const orderData = this.buildOrderData(orderNumber);

            // 保存工单
            await this.saveWorkOrder(orderData);

            // 显示成功消息
            alert(`工单提交成功！\n工单号：${orderNumber}\n请保存工单号以便查询进度。`);

            // 重置表单
            this.resetForm();

            // 跳转到查询页面
            setTimeout(() => {
                window.location.href = `workorder-query.html?order=${orderNumber}`;
            }, 1500);
            
        } catch (error) {
            globalErrorHandler.handle(error, '提交工单', {
                showToUser: true,
                allowRetry: true,
                retryCallback: () => this.handleSubmit(event)
            });
        } finally {
            // 恢复按钮状态
            this.elements.submitBtn.disabled = false;
            this.elements.submitBtn.textContent = '提交工单';
        }
    }

    /**
     * 验证表单
     */
    validateForm() {
        try {
            // 基本字段验证
            const formData = {
                category: this.elements.category.value.trim(),
                urgency: this.elements.urgency.value.trim(),
                location: this.elements.location.value.trim(),
                description: this.elements.description.value.trim()
            };

            // 验证规则
            const validationRules = {
                category: {
                    required: true,
                    minLength: 2,
                    maxLength: 50
                },
                urgency: {
                    required: true,
                    minLength: 1,
                    maxLength: 10
                },
                location: {
                    required: true,
                    minLength: 2,
                    maxLength: 100
                },
                description: {
                    required: true,
                    minLength: 5,
                    maxLength: 1000
                }
            };

            // 使用SecurityValidator进行验证
            const validation = SecurityValidator.validateForm(formData, validationRules);

            if (!validation.isValid) {
                // 显示第一个错误
                const firstError = Object.values(validation.errors)[0];
                const firstField = Object.keys(validation.errors)[0];

                globalErrorHandler.showUserError(firstError);

                // 聚焦到错误字段
                const fieldElement = this.elements[firstField];
                if (fieldElement) {
                    fieldElement.focus();
                }

                return false;
            }

            // 检查用户信息是否完整
            const phone = this.currentUser?.get('phone');
            const department = this.currentUser?.get('department');

            // 验证手机号
            const phoneValidation = SecurityValidator.validatePhone(phone, true);
            if (!phoneValidation.isValid) {
                globalErrorHandler.showUserError('您的联系电话未设置或格式不正确，请联系管理员完善账户信息');
                return false;
            }

            // 验证部门信息
            const deptValidation = SecurityValidator.validateText(department, {
                required: true,
                minLength: 2,
                maxLength: 50
            });
            if (!deptValidation.isValid) {
                globalErrorHandler.showUserError('您的部门信息未设置，请联系管理员完善账户信息');
                return false;
            }

            return true;

        } catch (error) {
            globalErrorHandler.handle(error, '表单验证');
            return false;
        }
    }

    /**
     * 重置表单
     */
    resetForm() {
        if (this.elements.workOrderForm) {
            this.elements.workOrderForm.reset();
        }
        this.uploadedImages = [];
        this.updateImagePreview();
    }

    /**
     * 处理取消操作
     */
    handleCancel() {
        if (confirm('确定要取消吗？已填写的信息将丢失。')) {
            this.resetForm();
            window.location.href = 'index.html';
        }
    }

    /**
     * 处理图片上传
     */
    async handleImageUpload(event) {
        const files = Array.from(event.target.files);
        
        for (const file of files) {
            if (this.uploadedImages.length >= 6) {
                alert('最多只能上传6张图片');
                break;
            }

            try {
                const imageUrl = await this.uploadImage(file);
                this.uploadedImages.push(imageUrl);
            } catch (error) {
                console.error('图片上传失败:', error);
                alert(`图片 ${file.name} 上传失败: ${error.message}`);
            }
        }

        this.updateImagePreview();
        event.target.value = ''; // 清空input
    }

    /**
     * 上传图片到服务器
     */
    async uploadImage(file) {
        // 验证文件大小
        if (file.size > 5 * 1024 * 1024) {
            throw new Error('图片大小不能超过5MB');
        }

        // 验证文件类型
        if (!file.type.startsWith('image/')) {
            throw new Error('只能上传图片文件');
        }

        // 使用LeanCloud文件上传
        const avFile = new AV.File(file.name, file);
        const savedFile = await avFile.save();
        return savedFile.url();
    }

    /**
     * 更新图片预览
     */
    updateImagePreview() {
        if (!this.elements.imagePreview) return;

        if (this.uploadedImages.length === 0) {
            this.elements.imagePreview.style.display = 'none';
            return;
        }

        this.elements.imagePreview.style.display = 'grid';
        this.elements.imagePreview.innerHTML = this.uploadedImages.map((url, index) => `
            <div class="relative">
                <img src="${url}" alt="预览图片" class="w-full h-24 object-cover rounded-lg">
                <button type="button" onclick="workOrderApp.removeImage(${index})" 
                        class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600">
                    ×
                </button>
            </div>
        `).join('');
    }

    /**
     * 移除图片
     */
    removeImage(index) {
        this.uploadedImages.splice(index, 1);
        this.updateImagePreview();
    }

    /**
     * 生成工单号
     */
    generateOrderNumber() {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hour = String(now.getHours()).padStart(2, '0');
        const minute = String(now.getMinutes()).padStart(2, '0');
        const second = String(now.getSeconds()).padStart(2, '0');
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        
        return `WO${year}${month}${day}${hour}${minute}${second}${random}`;
    }

    /**
     * 构建工单数据
     */
    buildOrderData(orderNumber) {
        // 生成标题（基于类别和位置）
        const title = `${this.elements.category.value} - ${this.elements.location.value}`;

        return {
            orderNumber: orderNumber,
            title: title,
            category: this.elements.category.value,
            urgency: this.elements.urgency.value,
            location: this.elements.location.value.trim(),
            description: this.elements.description.value.trim(),
            reporterName: this.currentUser.get('realName') || this.currentUser.get('username'),
            reporterPhone: this.currentUser.get('phone'),
            reporterDept: this.currentUser.get('department'),
            images: this.uploadedImages,
            status: 'pending',
            priority: this.calculatePriority(),
            reportTime: new Date(),
            reporter: this.currentUser
        };
    }

    /**
     * 计算优先级
     */
    calculatePriority() {
        const urgencyMap = {
            '紧急': 5,
            '高': 4,
            '中': 3,
            '低': 2
        };
        return urgencyMap[this.elements.urgency.value] || 2;
    }

    /**
     * 保存工单
     */
    async saveWorkOrder(orderData) {
        // 暂时使用RepairOrder表名，保持与现有数据的兼容性
        const RepairOrder = AV.Object.extend('RepairOrder');
        const order = new RepairOrder();

        // 设置所有字段
        Object.keys(orderData).forEach(key => {
            order.set(key, orderData[key]);
        });

        // 保存到数据库
        await order.save();

        // 同时写入工作日志
        console.log('开始写入工作日志，工单数据:', orderData);
        await this.saveToWorkLog(orderData, 'create');

        return order;
    }

    /**
     * 保存工单数据到工作日志
     */
    async saveToWorkLog(orderData, action = 'create') {
        try {
            // 根据操作类型设置不同的内容
            let content = '';
            let logData = {};

            if (action === 'create') {
                content = `创建工单：${orderData.orderNumber}\n` +
                         `类别：${orderData.category}\n` +
                         `位置：${orderData.location}\n` +
                         `紧急程度：${orderData.urgency}\n` +
                         `描述：${orderData.description}`;

                logData = {
                    action: 'create',
                    orderNumber: orderData.orderNumber,
                    category: orderData.category,
                    location: orderData.location,
                    urgency: orderData.urgency,
                    description: orderData.description,
                    images: orderData.images || []
                };
            } else if (action === 'complete') {
                content = `完成工单：${orderData.orderNumber}\n` +
                         `完成说明：${orderData.completeNote || '无'}`;

                logData = {
                    action: 'complete',
                    orderNumber: orderData.orderNumber,
                    completeNote: orderData.completeNote,
                    completeTime: orderData.completeTime
                };
            }

            // 使用基类的工作日志保存方法
            await this.submitWorkLog(logData, orderData.images || [], content);

            console.log('工单操作已记录到工作日志:', action, orderData.orderNumber);

        } catch (error) {
            console.error('保存工作日志失败:', error);
            console.error('错误详情:', error.message);
            // 不影响主流程，但要记录详细错误信息
            alert('工作日志保存失败: ' + error.message);
        }
    }
}
