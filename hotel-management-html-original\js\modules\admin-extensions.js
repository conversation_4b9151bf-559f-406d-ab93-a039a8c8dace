/**
 * 管理后台扩展功能 - 部门管理和角色管理
 * 扩展AdminApp类的功能
 */

// 等待AdminApp类定义后再扩展
document.addEventListener('DOMContentLoaded', function() {
    // 确保AdminApp已定义
    if (typeof AdminApp === 'undefined') {
        console.error('AdminApp未定义，无法加载扩展功能');
        return;
    }

    // 扩展AdminApp类，添加部门管理功能
    Object.assign(AdminApp.prototype, {
    
    /**
     * 初始化部门和角色数据
     */
    initDepartmentsAndRoles() {
        this.departments = [];
        this.roles = [];
        this.bindDepartmentEvents();
        this.bindRoleEvents();
    },

    /**
     * 绑定部门管理事件
     */
    bindDepartmentEvents() {
        // 添加部门按钮
        if (this.elements.addDepartmentBtn) {
            this.elements.addDepartmentBtn.addEventListener('click', () => {
                this.showDepartmentModal();
            });
        }
        
        // 刷新部门按钮
        if (this.elements.refreshDepartments) {
            this.elements.refreshDepartments.addEventListener('click', () => {
                this.loadDepartments();
            });
        }
        
        // 关闭部门弹窗
        if (this.elements.closeDepartmentModal) {
            this.elements.closeDepartmentModal.addEventListener('click', () => {
                this.hideDepartmentModal();
            });
        }
        
        // 取消部门编辑
        if (this.elements.cancelDepartmentEdit) {
            this.elements.cancelDepartmentEdit.addEventListener('click', () => {
                this.hideDepartmentModal();
            });
        }
        
        // 部门表单提交
        if (this.elements.departmentForm) {
            this.elements.departmentForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                await this.saveDepartment();
            });
        }
    },

    /**
     * 绑定角色管理事件
     */
    bindRoleEvents() {
        // 添加角色按钮
        if (this.elements.addRoleBtn) {
            this.elements.addRoleBtn.addEventListener('click', () => {
                this.showRoleModal();
            });
        }
        
        // 刷新角色按钮
        if (this.elements.refreshRoles) {
            this.elements.refreshRoles.addEventListener('click', () => {
                this.loadRoles();
            });
        }
        
        // 关闭角色弹窗
        if (this.elements.closeRoleModal) {
            this.elements.closeRoleModal.addEventListener('click', () => {
                this.hideRoleModal();
            });
        }
        
        // 取消角色编辑
        if (this.elements.cancelRoleEdit) {
            this.elements.cancelRoleEdit.addEventListener('click', () => {
                this.hideRoleModal();
            });
        }
        
        // 角色表单提交
        if (this.elements.roleForm) {
            this.elements.roleForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                await this.saveRole();
            });
        }
    },

    /**
     * 加载部门列表
     */
    async loadDepartments() {
        try {
            this.showLoading(true);
            
            // 查询部门数据
            const Department = AV.Object.extend('Department');
            const query = new AV.Query(Department);
            query.ascending('name');
            
            this.departments = await query.find();
            
            // 计算部门统计
            this.updateDepartmentStats();
            
            // 渲染部门列表
            this.renderDepartments();
            
        } catch (error) {
            console.error('加载部门失败:', error);
            
            // 如果表不存在，创建默认部门
            if (error.code === 101) {
                await this.initializeDefaultDepartments();
            } else {
                WorkLogUtils.showMessage('加载部门失败: ' + error.message, 'error');
            }
        } finally {
            this.showLoading(false);
        }
    },

    /**
     * 初始化默认部门
     */
    async initializeDefaultDepartments() {
        try {
            const defaultDepartments = [
                { name: '前厅部', description: '负责客人接待和前台服务' },
                { name: '客房部', description: '负责客房清洁和维护' },
                { name: '餐饮部', description: '负责餐厅和宴会服务' },
                { name: '工程部', description: '负责设备维护和维修' },
                { name: '保安部', description: '负责酒店安全保卫' },
                { name: '财务部', description: '负责财务管理和会计' },
                { name: '人事部', description: '负责人力资源管理' },
                { name: '销售部', description: '负责市场营销和销售' }
            ];
            
            const Department = AV.Object.extend('Department');
            const departments = defaultDepartments.map(dept => {
                const department = new Department();
                department.set('name', dept.name);
                department.set('description', dept.description);
                department.set('isSystem', true);
                return department;
            });
            
            await AV.Object.saveAll(departments);
            
            WorkLogUtils.showMessage('已初始化默认部门', 'success');
            this.loadDepartments();
            
        } catch (error) {
            console.error('初始化默认部门失败:', error);
            WorkLogUtils.showMessage('初始化默认部门失败: ' + error.message, 'error');
        }
    },

    /**
     * 更新部门统计
     */
    updateDepartmentStats() {
        const deptUserCounts = {};
        
        // 统计每个部门的用户数量
        if (this.users) {
            this.users.forEach(user => {
                const dept = user.get('department');
                if (dept) {
                    deptUserCounts[dept] = (deptUserCounts[dept] || 0) + 1;
                }
            });
        }
        
        const counts = Object.values(deptUserCounts);
        const totalDepts = this.departments.length;
        const maxUsers = counts.length > 0 ? Math.max(...counts) : 0;
        const avgUsers = counts.length > 0 ? Math.round(counts.reduce((a, b) => a + b, 0) / counts.length) : 0;
        
        if (this.elements.totalDepartments) this.elements.totalDepartments.textContent = totalDepts;
        if (this.elements.maxDeptUsers) this.elements.maxDeptUsers.textContent = maxUsers;
        if (this.elements.avgDeptUsers) this.elements.avgDeptUsers.textContent = avgUsers;
    },

    /**
     * 渲染部门列表
     */
    renderDepartments() {
        if (!this.elements.departmentsTableBody) return;
        
        if (this.departments.length === 0) {
            this.elements.departmentsTableBody.innerHTML = `
                <tr>
                    <td colspan="4" class="px-6 py-8 text-center text-gray-500">
                        暂无部门数据
                        <button onclick="adminApp.initializeDefaultDepartments()" 
                                class="ml-2 text-blue-500 hover:text-blue-600">
                            初始化默认部门
                        </button>
                    </td>
                </tr>
            `;
            return;
        }
        
        // 计算每个部门的用户数量
        const deptUserCounts = {};
        if (this.users) {
            this.users.forEach(user => {
                const dept = user.get('department');
                if (dept) {
                    deptUserCounts[dept] = (deptUserCounts[dept] || 0) + 1;
                }
            });
        }
        
        this.elements.departmentsTableBody.innerHTML = this.departments.map(dept => {
            const userCount = deptUserCounts[dept.get('name')] || 0;
            const isSystem = dept.get('isSystem');
            
            return `
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="text-sm font-medium text-gray-900">${dept.get('name')}</div>
                            ${isSystem ? '<span class="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">系统</span>' : ''}
                        </div>
                        <div class="text-sm text-gray-500">${dept.get('description') || ''}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${userCount} 人
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${dept.get('createdAt')?.toLocaleDateString('zh-CN') || ''}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button onclick="adminApp.editDepartment('${dept.id}')" 
                                class="text-blue-600 hover:text-blue-900 mr-3">编辑</button>
                        ${!isSystem ? `<button onclick="adminApp.deleteDepartment('${dept.id}')" 
                                class="text-red-600 hover:text-red-900">删除</button>` : ''}
                    </td>
                </tr>
            `;
        }).join('');
    },

    /**
     * 显示部门编辑弹窗
     */
    showDepartmentModal(departmentId = null) {
        if (departmentId) {
            // 编辑模式
            const dept = this.departments.find(d => d.id === departmentId);
            if (dept) {
                if (this.elements.editDepartmentId) this.elements.editDepartmentId.value = departmentId;
                if (this.elements.departmentName) this.elements.departmentName.value = dept.get('name');
                if (this.elements.departmentDescription) this.elements.departmentDescription.value = dept.get('description') || '';
                if (this.elements.departmentModalTitle) this.elements.departmentModalTitle.textContent = '编辑部门';
            }
        } else {
            // 添加模式
            if (this.elements.editDepartmentId) this.elements.editDepartmentId.value = '';
            if (this.elements.departmentName) this.elements.departmentName.value = '';
            if (this.elements.departmentDescription) this.elements.departmentDescription.value = '';
            if (this.elements.departmentModalTitle) this.elements.departmentModalTitle.textContent = '添加部门';
        }
        
        if (this.elements.departmentModal) {
            this.elements.departmentModal.classList.remove('hidden');
        }
    },

    /**
     * 隐藏部门编辑弹窗
     */
    hideDepartmentModal() {
        if (this.elements.departmentModal) {
            this.elements.departmentModal.classList.add('hidden');
        }
    },

    /**
     * 保存部门
     */
    async saveDepartment() {
        const departmentId = this.elements.editDepartmentId?.value;
        const name = this.elements.departmentName?.value?.trim();
        const description = this.elements.departmentDescription?.value?.trim();
        
        if (!name) {
            WorkLogUtils.showMessage('请输入部门名称', 'warning');
            return;
        }
        
        try {
            let department;
            
            if (departmentId) {
                // 编辑现有部门
                department = AV.Object.createWithoutData('Department', departmentId);
            } else {
                // 创建新部门
                const Department = AV.Object.extend('Department');
                department = new Department();
                department.set('isSystem', false);
            }
            
            department.set('name', name);
            department.set('description', description);
            
            await department.save();
            
            WorkLogUtils.showMessage('部门保存成功', 'success');
            this.hideDepartmentModal();
            this.loadDepartments();
            
        } catch (error) {
            console.error('保存部门失败:', error);
            WorkLogUtils.showMessage('保存部门失败: ' + error.message, 'error');
        }
    },

    /**
     * 编辑部门
     */
    editDepartment(departmentId) {
        this.showDepartmentModal(departmentId);
    },

    /**
     * 删除部门
     */
    async deleteDepartment(departmentId) {
        const dept = this.departments.find(d => d.id === departmentId);
        if (!dept) return;

        if (!confirm(`确定要删除部门"${dept.get('name')}"吗？`)) {
            return;
        }

        try {
            await dept.destroy();
            WorkLogUtils.showMessage('部门删除成功', 'success');
            this.loadDepartments();

        } catch (error) {
            console.error('删除部门失败:', error);
            WorkLogUtils.showMessage('删除部门失败: ' + error.message, 'error');
        }
    },

    /**
     * 加载角色列表
     */
    async loadRoles() {
        try {
            this.showLoading(true);

            // 初始化系统默认角色
            this.roles = [
                {
                    id: 'admin',
                    name: 'admin',
                    displayName: '管理员',
                    description: '系统管理员，拥有所有权限',
                    permissions: ['admin', 'engineer', 'reporter'],
                    isSystem: true,
                    userCount: 0
                },
                {
                    id: 'engineer',
                    name: 'engineer',
                    displayName: '工程师',
                    description: '工程师，可以处理工单',
                    permissions: ['engineer', 'reporter'],
                    isSystem: true,
                    userCount: 0
                },
                {
                    id: 'reporter',
                    name: 'reporter',
                    displayName: '普通用户',
                    description: '普通用户，可以提交报修',
                    permissions: ['reporter'],
                    isSystem: true,
                    userCount: 0
                }
            ];

            // 统计每个角色的用户数量
            if (this.users) {
                this.users.forEach(user => {
                    const userRoles = this.parseUserRoles(user.get('roles'));
                    userRoles.forEach(roleName => {
                        const role = this.roles.find(r => r.name === roleName);
                        if (role) {
                            role.userCount++;
                        }
                    });

                    // 如果用户没有角色，算作普通用户
                    if (userRoles.length === 0) {
                        const reporterRole = this.roles.find(r => r.name === 'reporter');
                        if (reporterRole) {
                            reporterRole.userCount++;
                        }
                    }
                });
            }

            this.updateRoleStats();
            this.renderRoles();

        } catch (error) {
            console.error('加载角色失败:', error);
            WorkLogUtils.showMessage('加载角色失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    },

    /**
     * 更新角色统计
     */
    updateRoleStats() {
        const totalRoles = this.roles.length;
        const systemRoles = this.roles.filter(r => r.isSystem).length;
        const customRoles = totalRoles - systemRoles;

        if (this.elements.totalRoles) this.elements.totalRoles.textContent = totalRoles;
        if (this.elements.systemRoles) this.elements.systemRoles.textContent = systemRoles;
        if (this.elements.customRoles) this.elements.customRoles.textContent = customRoles;
    },

    /**
     * 渲染角色列表
     */
    renderRoles() {
        if (!this.elements.rolesTableBody) return;

        this.elements.rolesTableBody.innerHTML = this.roles.map(role => {
            const permissionText = role.permissions.map(p => {
                const permMap = {
                    'admin': '管理员',
                    'engineer': '工程师',
                    'reporter': '报修'
                };
                return permMap[p] || p;
            }).join(', ');

            return `
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="text-sm font-medium text-gray-900">${role.displayName}</div>
                            ${role.isSystem ? '<span class="ml-2 px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">系统</span>' : ''}
                        </div>
                        <div class="text-sm text-gray-500">${role.name}</div>
                    </td>
                    <td class="px-6 py-4">
                        <div class="text-sm text-gray-900">${role.description}</div>
                    </td>
                    <td class="px-6 py-4">
                        <div class="text-sm text-gray-900">${permissionText}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${role.userCount} 人
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 py-1 text-xs rounded-full ${role.isSystem ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'}">
                            ${role.isSystem ? '系统角色' : '自定义'}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        ${!role.isSystem ? `
                            <button onclick="adminApp.editRole('${role.id}')"
                                    class="text-indigo-600 hover:text-indigo-900 mr-3">编辑</button>
                            <button onclick="adminApp.deleteRole('${role.id}')"
                                    class="text-red-600 hover:text-red-900">删除</button>
                        ` : '<span class="text-gray-400">系统角色</span>'}
                    </td>
                </tr>
            `;
        }).join('');
    },

    /**
     * 显示角色编辑弹窗
     */
    showRoleModal(roleId = null) {
        WorkLogUtils.showMessage('角色管理功能开发中，当前只支持查看系统默认角色', 'info');
    },

    /**
     * 隐藏角色编辑弹窗
     */
    hideRoleModal() {
        if (this.elements.roleModal) {
            this.elements.roleModal.classList.add('hidden');
        }
    },

    /**
     * 保存角色
     */
    async saveRole() {
        WorkLogUtils.showMessage('角色管理功能开发中', 'info');
    },

    /**
     * 编辑角色
     */
    editRole(roleId) {
        this.showRoleModal(roleId);
    },

    /**
     * 删除角色
     */
    async deleteRole(roleId) {
        WorkLogUtils.showMessage('系统角色不能删除', 'warning');
    }
    });

    // 导出扩展
    if (typeof window !== 'undefined') {
        window.AdminExtensions = true;
    }
});
