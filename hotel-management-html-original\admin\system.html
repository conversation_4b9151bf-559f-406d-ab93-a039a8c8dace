<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统设置 - 系统管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <style>
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .btn-fix {
            touch-action: manipulation;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="index.html" class="text-blue-600 hover:text-blue-800 mr-4">
                        ← 返回管理首页
                    </a>
                    <h1 class="text-xl font-semibold text-gray-900">系统设置</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="saveAllBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                        保存所有设置
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 基本设置 -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-medium text-gray-900">基本设置</h2>
            </div>
            <div class="p-6 space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="systemName" class="block text-sm font-medium text-gray-700 mb-1">系统名称</label>
                        <input type="text" id="systemName" value="酒店管理系统"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                    </div>
                    <div>
                        <label for="systemVersion" class="block text-sm font-medium text-gray-700 mb-1">系统版本</label>
                        <input type="text" id="systemVersion" value="v1.0.0"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                    </div>
                    <div>
                        <label for="companyName" class="block text-sm font-medium text-gray-700 mb-1">公司名称</label>
                        <input type="text" id="companyName" value="示例酒店"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                    </div>
                    <div>
                        <label for="contactEmail" class="block text-sm font-medium text-gray-700 mb-1">联系邮箱</label>
                        <input type="email" id="contactEmail" value="<EMAIL>"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                    </div>
                </div>
                <div>
                    <label for="systemDescription" class="block text-sm font-medium text-gray-700 mb-1">系统描述</label>
                    <textarea id="systemDescription" rows="3"
                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">现代化酒店管理系统，提供全面的酒店运营管理功能</textarea>
                </div>
            </div>
        </div>

        <!-- 安全设置 -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-medium text-gray-900">安全设置</h2>
            </div>
            <div class="p-6 space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="sessionTimeout" class="block text-sm font-medium text-gray-700 mb-1">会话超时时间（分钟）</label>
                        <input type="number" id="sessionTimeout" value="30" min="5" max="480"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                    </div>
                    <div>
                        <label for="passwordMinLength" class="block text-sm font-medium text-gray-700 mb-1">密码最小长度</label>
                        <input type="number" id="passwordMinLength" value="6" min="4" max="20"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                    </div>
                    <div>
                        <label for="maxLoginAttempts" class="block text-sm font-medium text-gray-700 mb-1">最大登录尝试次数</label>
                        <input type="number" id="maxLoginAttempts" value="5" min="3" max="10"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                    </div>
                    <div>
                        <label for="lockoutDuration" class="block text-sm font-medium text-gray-700 mb-1">账户锁定时间（分钟）</label>
                        <input type="number" id="lockoutDuration" value="15" min="5" max="60"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                    </div>
                </div>
                <div class="space-y-4">
                    <label class="flex items-center">
                        <input type="checkbox" id="requireStrongPassword" checked class="rounded">
                        <span class="ml-2 text-sm text-gray-700">要求强密码（包含大小写字母、数字和特殊字符）</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" id="enableTwoFactor" class="rounded">
                        <span class="ml-2 text-sm text-gray-700">启用双因素认证</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" id="logSecurityEvents" checked class="rounded">
                        <span class="ml-2 text-sm text-gray-700">记录安全事件日志</span>
                    </label>
                </div>
            </div>
        </div>

        <!-- 功能设置 -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-medium text-gray-900">功能设置</h2>
            </div>
            <div class="p-6 space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="defaultPageSize" class="block text-sm font-medium text-gray-700 mb-1">默认分页大小</label>
                        <select id="defaultPageSize"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                            <option value="10">10条/页</option>
                            <option value="20" selected>20条/页</option>
                            <option value="50">50条/页</option>
                            <option value="100">100条/页</option>
                        </select>
                    </div>
                    <div>
                        <label for="dateFormat" class="block text-sm font-medium text-gray-700 mb-1">日期格式</label>
                        <select id="dateFormat"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                            <option value="YYYY-MM-DD" selected>YYYY-MM-DD</option>
                            <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                            <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                            <option value="YYYY年MM月DD日">YYYY年MM月DD日</option>
                        </select>
                    </div>
                    <div>
                        <label for="timeFormat" class="block text-sm font-medium text-gray-700 mb-1">时间格式</label>
                        <select id="timeFormat"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                            <option value="24" selected>24小时制</option>
                            <option value="12">12小时制</option>
                        </select>
                    </div>
                    <div>
                        <label for="currency" class="block text-sm font-medium text-gray-700 mb-1">货币符号</label>
                        <select id="currency"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                            <option value="¥" selected>人民币 (¥)</option>
                            <option value="$">美元 ($)</option>
                            <option value="€">欧元 (€)</option>
                            <option value="£">英镑 (£)</option>
                        </select>
                    </div>
                </div>
                <div class="space-y-4">
                    <label class="flex items-center">
                        <input type="checkbox" id="enableNotifications" checked class="rounded">
                        <span class="ml-2 text-sm text-gray-700">启用系统通知</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" id="enableAutoBackup" checked class="rounded">
                        <span class="ml-2 text-sm text-gray-700">启用自动备份</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" id="enableDebugMode" class="rounded">
                        <span class="ml-2 text-sm text-gray-700">启用调试模式</span>
                    </label>
                </div>
            </div>
        </div>

        <!-- 邮件设置 -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-medium text-gray-900">邮件设置</h2>
            </div>
            <div class="p-6 space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="smtpHost" class="block text-sm font-medium text-gray-700 mb-1">SMTP服务器</label>
                        <input type="text" id="smtpHost" placeholder="smtp.example.com"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                    </div>
                    <div>
                        <label for="smtpPort" class="block text-sm font-medium text-gray-700 mb-1">SMTP端口</label>
                        <input type="number" id="smtpPort" value="587"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                    </div>
                    <div>
                        <label for="smtpUsername" class="block text-sm font-medium text-gray-700 mb-1">SMTP用户名</label>
                        <input type="text" id="smtpUsername"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                    </div>
                    <div>
                        <label for="smtpPassword" class="block text-sm font-medium text-gray-700 mb-1">SMTP密码</label>
                        <input type="password" id="smtpPassword"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                    </div>
                    <div>
                        <label for="fromEmail" class="block text-sm font-medium text-gray-700 mb-1">发件人邮箱</label>
                        <input type="email" id="fromEmail"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                    </div>
                    <div>
                        <label for="fromName" class="block text-sm font-medium text-gray-700 mb-1">发件人名称</label>
                        <input type="text" id="fromName" value="酒店管理系统"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                    </div>
                </div>
                <div class="space-y-4">
                    <label class="flex items-center">
                        <input type="checkbox" id="enableSSL" checked class="rounded">
                        <span class="ml-2 text-sm text-gray-700">启用SSL加密</span>
                    </label>
                    <div class="flex space-x-4">
                        <button id="testEmailBtn" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                            测试邮件发送
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex justify-end space-x-4">
            <button id="resetBtn" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg text-sm btn-fix">
                重置为默认
            </button>
            <button id="saveBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg text-sm btn-fix">
                保存设置
            </button>
        </div>
    </div>

    <!-- 加载指示器 -->
    <div id="loadingIndicator" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-40" style="display: none;">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <span class="text-gray-700">保存中...</span>
        </div>
    </div>

    <!-- 引入模块化JS文件 -->
    <script src="../js/config.js"></script>
    <script src="../js/permission-manager.js"></script>
    <script>
        // 系统设置应用类
        class SystemSettingsApp {
            constructor() {
                this.settings = {};
            }

            init() {
                this.bindEvents();
                this.loadSettings();
            }

            bindEvents() {
                // 保存按钮
                document.getElementById('saveBtn').addEventListener('click', () => {
                    this.saveSettings();
                });

                document.getElementById('saveAllBtn').addEventListener('click', () => {
                    this.saveSettings();
                });

                // 重置按钮
                document.getElementById('resetBtn').addEventListener('click', () => {
                    if (confirm('确定要重置为默认设置吗？')) {
                        this.resetToDefault();
                    }
                });

                // 测试邮件按钮
                document.getElementById('testEmailBtn').addEventListener('click', () => {
                    this.testEmail();
                });
            }

            async loadSettings() {
                try {
                    // 从 LeanCloud 加载系统设置
                    const query = new AV.Query('SystemSettings');
                    const settings = await query.first();
                    
                    if (settings) {
                        this.settings = settings.toJSON();
                        this.populateForm();
                    }
                } catch (error) {
                    console.error('加载系统设置失败:', error);
                }
            }

            populateForm() {
                // 基本设置
                if (this.settings.systemName) document.getElementById('systemName').value = this.settings.systemName;
                if (this.settings.systemVersion) document.getElementById('systemVersion').value = this.settings.systemVersion;
                if (this.settings.companyName) document.getElementById('companyName').value = this.settings.companyName;
                if (this.settings.contactEmail) document.getElementById('contactEmail').value = this.settings.contactEmail;
                if (this.settings.systemDescription) document.getElementById('systemDescription').value = this.settings.systemDescription;

                // 安全设置
                if (this.settings.sessionTimeout) document.getElementById('sessionTimeout').value = this.settings.sessionTimeout;
                if (this.settings.passwordMinLength) document.getElementById('passwordMinLength').value = this.settings.passwordMinLength;
                if (this.settings.maxLoginAttempts) document.getElementById('maxLoginAttempts').value = this.settings.maxLoginAttempts;
                if (this.settings.lockoutDuration) document.getElementById('lockoutDuration').value = this.settings.lockoutDuration;
                
                document.getElementById('requireStrongPassword').checked = this.settings.requireStrongPassword !== false;
                document.getElementById('enableTwoFactor').checked = this.settings.enableTwoFactor === true;
                document.getElementById('logSecurityEvents').checked = this.settings.logSecurityEvents !== false;

                // 功能设置
                if (this.settings.defaultPageSize) document.getElementById('defaultPageSize').value = this.settings.defaultPageSize;
                if (this.settings.dateFormat) document.getElementById('dateFormat').value = this.settings.dateFormat;
                if (this.settings.timeFormat) document.getElementById('timeFormat').value = this.settings.timeFormat;
                if (this.settings.currency) document.getElementById('currency').value = this.settings.currency;
                
                document.getElementById('enableNotifications').checked = this.settings.enableNotifications !== false;
                document.getElementById('enableAutoBackup').checked = this.settings.enableAutoBackup !== false;
                document.getElementById('enableDebugMode').checked = this.settings.enableDebugMode === true;

                // 邮件设置
                if (this.settings.smtpHost) document.getElementById('smtpHost').value = this.settings.smtpHost;
                if (this.settings.smtpPort) document.getElementById('smtpPort').value = this.settings.smtpPort;
                if (this.settings.smtpUsername) document.getElementById('smtpUsername').value = this.settings.smtpUsername;
                if (this.settings.fromEmail) document.getElementById('fromEmail').value = this.settings.fromEmail;
                if (this.settings.fromName) document.getElementById('fromName').value = this.settings.fromName;
                
                document.getElementById('enableSSL').checked = this.settings.enableSSL !== false;
            }

            async saveSettings() {
                try {
                    this.showLoading();

                    // 收集表单数据
                    const formData = {
                        // 基本设置
                        systemName: document.getElementById('systemName').value,
                        systemVersion: document.getElementById('systemVersion').value,
                        companyName: document.getElementById('companyName').value,
                        contactEmail: document.getElementById('contactEmail').value,
                        systemDescription: document.getElementById('systemDescription').value,

                        // 安全设置
                        sessionTimeout: parseInt(document.getElementById('sessionTimeout').value),
                        passwordMinLength: parseInt(document.getElementById('passwordMinLength').value),
                        maxLoginAttempts: parseInt(document.getElementById('maxLoginAttempts').value),
                        lockoutDuration: parseInt(document.getElementById('lockoutDuration').value),
                        requireStrongPassword: document.getElementById('requireStrongPassword').checked,
                        enableTwoFactor: document.getElementById('enableTwoFactor').checked,
                        logSecurityEvents: document.getElementById('logSecurityEvents').checked,

                        // 功能设置
                        defaultPageSize: parseInt(document.getElementById('defaultPageSize').value),
                        dateFormat: document.getElementById('dateFormat').value,
                        timeFormat: document.getElementById('timeFormat').value,
                        currency: document.getElementById('currency').value,
                        enableNotifications: document.getElementById('enableNotifications').checked,
                        enableAutoBackup: document.getElementById('enableAutoBackup').checked,
                        enableDebugMode: document.getElementById('enableDebugMode').checked,

                        // 邮件设置
                        smtpHost: document.getElementById('smtpHost').value,
                        smtpPort: parseInt(document.getElementById('smtpPort').value),
                        smtpUsername: document.getElementById('smtpUsername').value,
                        smtpPassword: document.getElementById('smtpPassword').value,
                        fromEmail: document.getElementById('fromEmail').value,
                        fromName: document.getElementById('fromName').value,
                        enableSSL: document.getElementById('enableSSL').checked
                    };

                    // 保存到 LeanCloud
                    let settingsObj;
                    if (this.settings.objectId) {
                        settingsObj = AV.Object.createWithoutData('SystemSettings', this.settings.objectId);
                    } else {
                        settingsObj = new AV.Object('SystemSettings');
                    }

                    Object.keys(formData).forEach(key => {
                        settingsObj.set(key, formData[key]);
                    });

                    await settingsObj.save();
                    this.settings = settingsObj.toJSON();

                    this.hideLoading();
                    alert('系统设置保存成功');
                } catch (error) {
                    console.error('保存系统设置失败:', error);
                    this.hideLoading();
                    alert('保存系统设置失败: ' + error.message);
                }
            }

            resetToDefault() {
                // 重置为默认值
                document.getElementById('systemName').value = '酒店管理系统';
                document.getElementById('systemVersion').value = 'v1.0.0';
                document.getElementById('companyName').value = '示例酒店';
                document.getElementById('contactEmail').value = '<EMAIL>';
                document.getElementById('systemDescription').value = '现代化酒店管理系统，提供全面的酒店运营管理功能';

                document.getElementById('sessionTimeout').value = '30';
                document.getElementById('passwordMinLength').value = '6';
                document.getElementById('maxLoginAttempts').value = '5';
                document.getElementById('lockoutDuration').value = '15';
                document.getElementById('requireStrongPassword').checked = true;
                document.getElementById('enableTwoFactor').checked = false;
                document.getElementById('logSecurityEvents').checked = true;

                document.getElementById('defaultPageSize').value = '20';
                document.getElementById('dateFormat').value = 'YYYY-MM-DD';
                document.getElementById('timeFormat').value = '24';
                document.getElementById('currency').value = '¥';
                document.getElementById('enableNotifications').checked = true;
                document.getElementById('enableAutoBackup').checked = true;
                document.getElementById('enableDebugMode').checked = false;

                document.getElementById('smtpHost').value = '';
                document.getElementById('smtpPort').value = '587';
                document.getElementById('smtpUsername').value = '';
                document.getElementById('smtpPassword').value = '';
                document.getElementById('fromEmail').value = '';
                document.getElementById('fromName').value = '酒店管理系统';
                document.getElementById('enableSSL').checked = true;
            }

            testEmail() {
                // 实现邮件测试功能
                alert('邮件测试功能开发中...');
            }

            showLoading() {
                document.getElementById('loadingIndicator').style.display = 'flex';
            }

            hideLoading() {
                document.getElementById('loadingIndicator').style.display = 'none';
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            function initApp() {
                if (typeof AV !== 'undefined') {
                    const systemApp = new SystemSettingsApp();
                    systemApp.init();
                } else {
                    setTimeout(initApp, 100);
                }
            }
            initApp();
        });
    </script>
</body>
</html>
