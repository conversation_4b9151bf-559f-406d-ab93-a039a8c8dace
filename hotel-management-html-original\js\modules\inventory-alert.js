/**
 * 库存预警系统
 * 提供库存监控、预警通知、自动补货建议等功能
 */

class InventoryAlertSystem {
    constructor() {
        this.alertRules = new Map();
        this.notifications = [];
        this.checkInterval = 5 * 60 * 1000; // 5分钟检查一次
        this.isRunning = false;
        
        // 默认预警规则
        this.defaultRules = {
            lowStock: {
                enabled: true,
                threshold: 'minStock', // 使用商品设置的最低库存
                message: '库存不足预警'
            },
            zeroStock: {
                enabled: true,
                threshold: 0,
                message: '库存为零预警'
            },
            expiringSoon: {
                enabled: true,
                threshold: 7, // 7天内过期
                message: '商品即将过期'
            }
        };
        
        this.init();
    }

    /**
     * 初始化预警系统
     */
    async init() {
        try {
            // 检查LeanCloud是否可用
            if (typeof AV === 'undefined') {
                console.warn('LeanCloud SDK未加载，库存预警系统将等待初始化');
                this.waitForLeanCloud();
                return;
            }

            // 检查LeanCloud是否已经初始化
            if (!AV.applicationId) {
                console.warn('LeanCloud未初始化，库存预警系统将等待初始化');
                this.waitForLeanCloud();
                return;
            }

            // 加载预警规则
            await this.loadAlertRules();

            // 启动定期检查
            this.startPeriodicCheck();

            console.log('库存预警系统初始化完成');
        } catch (error) {
            console.error('库存预警系统初始化失败:', error);
        }
    }

    /**
     * 等待LeanCloud SDK加载和初始化
     */
    waitForLeanCloud() {
        // 使用简单的轮询检查
        setTimeout(() => {
            this.init();
        }, 1000);
    }

    /**
     * 加载预警规则
     */
    async loadAlertRules() {
        try {
            // 检查LeanCloud是否可用且已初始化
            if (typeof AV === 'undefined' || !AV.applicationId) {
                throw new Error('LeanCloud未初始化');
            }

            const query = new AV.Query('InventoryAlertRule');
            query.equalTo('status', 'active');
            const rules = await query.find();
            
            rules.forEach(rule => {
                this.alertRules.set(rule.get('ruleType'), {
                    id: rule.id,
                    type: rule.get('ruleType'),
                    enabled: rule.get('enabled'),
                    threshold: rule.get('threshold'),
                    message: rule.get('message'),
                    notifyUsers: rule.get('notifyUsers') || [],
                    notifyMethods: rule.get('notifyMethods') || ['system']
                });
            });
            
            // 如果没有规则，使用默认规则
            if (this.alertRules.size === 0) {
                await this.createDefaultRules();
            }
            
        } catch (error) {
            console.error('加载预警规则失败:', error);
            // 使用默认规则
            Object.entries(this.defaultRules).forEach(([type, rule]) => {
                this.alertRules.set(type, rule);
            });
        }
    }

    /**
     * 创建默认预警规则
     */
    async createDefaultRules() {
        try {
            for (const [type, rule] of Object.entries(this.defaultRules)) {
                const AlertRule = AV.Object.extend('InventoryAlertRule');
                const alertRule = new AlertRule();
                
                alertRule.set('ruleType', type);
                alertRule.set('enabled', rule.enabled);
                alertRule.set('threshold', rule.threshold);
                alertRule.set('message', rule.message);
                alertRule.set('status', 'active');
                alertRule.set('notifyMethods', ['system']);
                
                await alertRule.save();
                
                this.alertRules.set(type, {
                    id: alertRule.id,
                    type,
                    ...rule,
                    notifyMethods: ['system']
                });
            }
            
            console.log('默认预警规则创建完成');
        } catch (error) {
            console.error('创建默认预警规则失败:', error);
        }
    }

    /**
     * 启动定期检查
     */
    startPeriodicCheck() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.checkTimer = setInterval(() => {
            this.performStockCheck();
        }, this.checkInterval);
        
        // 立即执行一次检查
        this.performStockCheck();
    }

    /**
     * 停止定期检查
     */
    stopPeriodicCheck() {
        if (this.checkTimer) {
            clearInterval(this.checkTimer);
            this.checkTimer = null;
        }
        this.isRunning = false;
    }

    /**
     * 执行库存检查
     */
    async performStockCheck() {
        try {
            // 检查LeanCloud是否可用且已初始化
            if (typeof AV === 'undefined' || !AV.applicationId) {
                console.warn('LeanCloud未初始化，跳过库存检查');
                return;
            }

            console.log('开始库存预警检查...');

            // 获取所有启用的商品
            const productQuery = new AV.Query('Product');
            productQuery.equalTo('status', 'active');
            productQuery.limit(1000);
            const products = await productQuery.find();
            
            const alerts = [];
            
            for (const product of products) {
                const productAlerts = await this.checkProductAlerts(product);
                alerts.push(...productAlerts);
            }
            
            // 处理预警
            if (alerts.length > 0) {
                await this.processAlerts(alerts);
                console.log(`发现 ${alerts.length} 个库存预警`);
            } else {
                console.log('库存检查完成，无预警');
            }
            
        } catch (error) {
            console.error('库存检查失败:', error);
            globalErrorHandler.handle(error, '库存预警检查');
        }
    }

    /**
     * 检查单个商品的预警
     * @param {AV.Object} product - 商品对象
     * @returns {Array} 预警列表
     */
    async checkProductAlerts(product) {
        const alerts = [];
        const currentStock = product.get('currentStock') || 0;
        const minStock = product.get('minStock') || 0;
        const productName = product.get('name');
        const productCode = product.get('code');
        
        // 检查库存为零
        if (this.alertRules.get('zeroStock')?.enabled && currentStock === 0) {
            alerts.push({
                type: 'zeroStock',
                productId: product.id,
                productName,
                productCode,
                currentStock,
                minStock,
                message: `商品 ${productName}(${productCode}) 库存为零`,
                severity: 'critical',
                timestamp: new Date()
            });
        }
        
        // 检查低库存
        else if (this.alertRules.get('lowStock')?.enabled && minStock > 0 && currentStock <= minStock) {
            alerts.push({
                type: 'lowStock',
                productId: product.id,
                productName,
                productCode,
                currentStock,
                minStock,
                message: `商品 ${productName}(${productCode}) 库存不足，当前库存：${currentStock}，最低库存：${minStock}`,
                severity: 'warning',
                timestamp: new Date()
            });
        }
        
        // 检查即将过期（如果有过期日期字段）
        const expiryDate = product.get('expiryDate');
        if (this.alertRules.get('expiringSoon')?.enabled && expiryDate) {
            const daysUntilExpiry = Math.ceil((expiryDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24));
            const threshold = this.alertRules.get('expiringSoon').threshold;
            
            if (daysUntilExpiry <= threshold && daysUntilExpiry > 0) {
                alerts.push({
                    type: 'expiringSoon',
                    productId: product.id,
                    productName,
                    productCode,
                    currentStock,
                    expiryDate,
                    daysUntilExpiry,
                    message: `商品 ${productName}(${productCode}) 将在 ${daysUntilExpiry} 天后过期`,
                    severity: 'info',
                    timestamp: new Date()
                });
            }
        }
        
        return alerts;
    }

    /**
     * 处理预警
     * @param {Array} alerts - 预警列表
     */
    async processAlerts(alerts) {
        for (const alert of alerts) {
            try {
                // 检查是否已经发送过相同预警
                if (await this.isDuplicateAlert(alert)) {
                    continue;
                }
                
                // 保存预警记录
                await this.saveAlertRecord(alert);
                
                // 发送通知
                await this.sendAlertNotification(alert);
                
                // 添加到内存通知列表
                this.notifications.unshift(alert);
                
                // 限制内存通知数量
                if (this.notifications.length > 100) {
                    this.notifications = this.notifications.slice(0, 100);
                }
                
            } catch (error) {
                console.error('处理预警失败:', error);
            }
        }
    }

    /**
     * 检查是否为重复预警
     * @param {Object} alert - 预警对象
     * @returns {boolean} 是否重复
     */
    async isDuplicateAlert(alert) {
        try {
            const query = new AV.Query('InventoryAlert');
            query.equalTo('productId', alert.productId);
            query.equalTo('type', alert.type);
            query.equalTo('status', 'active');
            query.greaterThan('createdAt', new Date(Date.now() - 24 * 60 * 60 * 1000)); // 24小时内
            
            const existingAlert = await query.first();
            return !!existingAlert;
            
        } catch (error) {
            console.error('检查重复预警失败:', error);
            return false;
        }
    }

    /**
     * 保存预警记录
     * @param {Object} alert - 预警对象
     */
    async saveAlertRecord(alert) {
        try {
            const InventoryAlert = AV.Object.extend('InventoryAlert');
            const alertRecord = new InventoryAlert();
            
            alertRecord.set('type', alert.type);
            alertRecord.set('productId', alert.productId);
            alertRecord.set('productName', alert.productName);
            alertRecord.set('productCode', alert.productCode);
            alertRecord.set('currentStock', alert.currentStock);
            alertRecord.set('minStock', alert.minStock);
            alertRecord.set('message', alert.message);
            alertRecord.set('severity', alert.severity);
            alertRecord.set('status', 'active');
            alertRecord.set('acknowledged', false);
            
            if (alert.expiryDate) {
                alertRecord.set('expiryDate', alert.expiryDate);
                alertRecord.set('daysUntilExpiry', alert.daysUntilExpiry);
            }
            
            await alertRecord.save();
            alert.id = alertRecord.id;
            
        } catch (error) {
            console.error('保存预警记录失败:', error);
        }
    }

    /**
     * 发送预警通知
     * @param {Object} alert - 预警对象
     */
    async sendAlertNotification(alert) {
        try {
            // 系统通知
            this.showSystemNotification(alert);
            
            // 可以在这里添加其他通知方式
            // 如邮件、短信、企业微信等
            
        } catch (error) {
            console.error('发送预警通知失败:', error);
        }
    }

    /**
     * 显示系统通知
     * @param {Object} alert - 预警对象
     */
    showSystemNotification(alert) {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `alert alert-${this.getSeverityClass(alert.severity)} alert-dismissible fade show`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 400px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;
        
        notification.innerHTML = `
            <div class="d-flex align-items-start">
                <i class="fas fa-exclamation-triangle me-2 mt-1"></i>
                <div class="flex-grow-1">
                    <strong>${alert.type === 'zeroStock' ? '库存为零' : alert.type === 'lowStock' ? '库存不足' : '即将过期'}</strong>
                    <div class="small">${alert.message}</div>
                    <div class="small text-muted">${alert.timestamp.toLocaleString()}</div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // 自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 10000);
    }

    /**
     * 获取严重程度对应的CSS类
     * @param {string} severity - 严重程度
     * @returns {string} CSS类名
     */
    getSeverityClass(severity) {
        switch (severity) {
            case 'critical': return 'danger';
            case 'warning': return 'warning';
            case 'info': return 'info';
            default: return 'secondary';
        }
    }

    /**
     * 获取当前预警列表
     * @returns {Array} 预警列表
     */
    getCurrentAlerts() {
        return [...this.notifications];
    }

    /**
     * 确认预警
     * @param {string} alertId - 预警ID
     */
    async acknowledgeAlert(alertId) {
        try {
            const query = new AV.Query('InventoryAlert');
            const alert = await query.get(alertId);
            
            alert.set('acknowledged', true);
            alert.set('acknowledgedAt', new Date());
            alert.set('acknowledgedBy', AV.User.current());
            
            await alert.save();
            
            // 从内存列表中移除
            this.notifications = this.notifications.filter(n => n.id !== alertId);
            
            return true;
        } catch (error) {
            console.error('确认预警失败:', error);
            return false;
        }
    }

    /**
     * 获取预警统计
     * @returns {Object} 统计信息
     */
    async getAlertStatistics() {
        try {
            const query = new AV.Query('InventoryAlert');
            query.equalTo('status', 'active');
            query.equalTo('acknowledged', false);
            
            const alerts = await query.find();
            
            const stats = {
                total: alerts.length,
                critical: alerts.filter(a => a.get('severity') === 'critical').length,
                warning: alerts.filter(a => a.get('severity') === 'warning').length,
                info: alerts.filter(a => a.get('severity') === 'info').length,
                byType: {}
            };
            
            // 按类型统计
            alerts.forEach(alert => {
                const type = alert.get('type');
                stats.byType[type] = (stats.byType[type] || 0) + 1;
            });
            
            return stats;
        } catch (error) {
            console.error('获取预警统计失败:', error);
            return { total: 0, critical: 0, warning: 0, info: 0, byType: {} };
        }
    }
}

// 创建全局库存预警系统实例
const inventoryAlertSystem = new InventoryAlertSystem();

// 导出到全局
if (typeof window !== 'undefined') {
    window.InventoryAlertSystem = InventoryAlertSystem;
    window.inventoryAlertSystem = inventoryAlertSystem;
}

// Node.js环境支持
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { InventoryAlertSystem, inventoryAlertSystem };
}
