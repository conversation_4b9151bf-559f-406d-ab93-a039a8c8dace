<template>
  <div 
    v-if="show" 
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    @click="handleBackdropClick"
  >
    <div 
      class="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-hidden"
      @click.stop
    >
      <!-- 头部 -->
      <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-900">
          {{ isEditing ? '编辑工作日志' : '创建工作日志' }}
        </h3>
        <button 
          @click="handleClose"
          class="text-gray-400 hover:text-gray-600"
        >
          <Icon name="mdi:close" size="24" />
        </button>
      </div>

      <!-- 内容 -->
      <div class="px-6 py-4 max-h-[calc(90vh-140px)] overflow-y-auto">
        <form @submit.prevent="handleSubmit">
          <!-- 日志类型 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              日志类型 *
            </label>
            <select 
              v-model="formData.pageType"
              required
              :disabled="loading"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">请选择日志类型</option>
              <option value="main">工作日志</option>
              <option value="powerstation">变电站</option>
              <option value="waterfilter">净水器</option>
              <option value="aircondition">空调</option>
              <option value="construction">施工登记</option>
            </select>
          </div>

          <!-- 根据日志类型显示不同的表单 -->

          <!-- 工作日志表单 -->
          <div v-if="formData.pageType === 'main'" class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              日志内容 *
            </label>
            <textarea
              v-model="formData.content"
              required
              :disabled="loading"
              rows="6"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
              placeholder="请详细描述工作内容、遇到的问题、解决方案等..."
            ></textarea>
            <div class="mt-1 text-xs text-gray-500">
              {{ formData.content.length }} / 1000 字符
            </div>
          </div>

          <!-- 变电站表单 -->
          <div v-if="formData.pageType === 'powerstation'" class="mb-6 space-y-6">
            <!-- 环境参数 -->
            <div>
              <h4 class="text-md font-medium text-gray-900 mb-3">环境参数</h4>
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">室内温度 (°C) *</label>
                  <input
                    v-model="formData.indoorTemp"
                    type="number"
                    step="0.1"
                    required
                    :disabled="loading"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入室内温度"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">室内湿度 (%) *</label>
                  <input
                    v-model="formData.indoorHumidity"
                    type="number"
                    step="0.1"
                    min="0"
                    max="100"
                    required
                    :disabled="loading"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入室内湿度"
                  />
                </div>
              </div>
            </div>

            <!-- 变压器2000KV温度 -->
            <div>
              <h4 class="text-md font-medium text-gray-900 mb-3">变压器2000KV温度</h4>
              <div class="grid grid-cols-3 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">A相温度 (°C)</label>
                  <input
                    v-model="formData.transformer2000A"
                    type="number"
                    step="0.1"
                    :disabled="loading"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="A相温度"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">B相温度 (°C)</label>
                  <input
                    v-model="formData.transformer2000B"
                    type="number"
                    step="0.1"
                    :disabled="loading"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="B相温度"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">C相温度 (°C)</label>
                  <input
                    v-model="formData.transformer2000C"
                    type="number"
                    step="0.1"
                    :disabled="loading"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="C相温度"
                  />
                </div>
              </div>
            </div>

            <!-- 变压器1250KV温度 -->
            <div>
              <h4 class="text-md font-medium text-gray-900 mb-3">变压器1250KV温度</h4>
              <div class="grid grid-cols-3 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">A相温度 (°C)</label>
                  <input
                    v-model="formData.transformer1250A"
                    type="number"
                    step="0.1"
                    :disabled="loading"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="A相温度"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">B相温度 (°C)</label>
                  <input
                    v-model="formData.transformer1250B"
                    type="number"
                    step="0.1"
                    :disabled="loading"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="B相温度"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">C相温度 (°C)</label>
                  <input
                    v-model="formData.transformer1250C"
                    type="number"
                    step="0.1"
                    :disabled="loading"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="C相温度"
                  />
                </div>
              </div>
            </div>

            <!-- 设备状态 -->
            <div>
              <h4 class="text-md font-medium text-gray-900 mb-3">设备状态</h4>
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">变压器运行状态</label>
                  <div class="flex space-x-4">
                    <label class="flex items-center">
                      <input
                        v-model="formData.transformerStatus"
                        type="radio"
                        value="平稳"
                        :disabled="loading"
                        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                      />
                      <span class="ml-2 text-sm text-gray-700">平稳</span>
                    </label>
                    <label class="flex items-center">
                      <input
                        v-model="formData.transformerStatus"
                        type="radio"
                        value="异常"
                        :disabled="loading"
                        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                      />
                      <span class="ml-2 text-sm text-gray-700">异常</span>
                    </label>
                  </div>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">消防设施状态</label>
                  <div class="flex space-x-4">
                    <label class="flex items-center">
                      <input
                        v-model="formData.fireStatus"
                        type="radio"
                        value="正常"
                        :disabled="loading"
                        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                      />
                      <span class="ml-2 text-sm text-gray-700">正常</span>
                    </label>
                    <label class="flex items-center">
                      <input
                        v-model="formData.fireStatus"
                        type="radio"
                        value="失压"
                        :disabled="loading"
                        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                      />
                      <span class="ml-2 text-sm text-gray-700">失压</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 净水器表单 -->
          <div v-if="formData.pageType === 'waterfilter'" class="mb-6 space-y-6">
            <!-- 水位监测 -->
            <div>
              <h4 class="text-md font-medium text-gray-900 mb-3">水位监测</h4>
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">自来水箱水位 (m)</label>
                  <input
                    v-model="formData.tapWaterLevel"
                    type="number"
                    min="0"
                    max="10"
                    step="0.1"
                    :disabled="loading"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入水位高度"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">纯净水箱水位 (m)</label>
                  <input
                    v-model="formData.purifiedWaterLevel"
                    type="number"
                    min="0"
                    max="10"
                    step="0.1"
                    :disabled="loading"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入水位高度"
                  />
                </div>
              </div>
            </div>

            <!-- 加压泵组状态 -->
            <div>
              <h4 class="text-md font-medium text-gray-900 mb-3">加压泵组状态</h4>
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">研发楼加压泵压力 (MPa)</label>
                  <input
                    v-model="formData.rdBuildingPump"
                    type="number"
                    min="0"
                    max="10"
                    step="0.1"
                    :disabled="loading"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入压力值"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">高区加压泵压力 (MPa)</label>
                  <input
                    v-model="formData.highZonePump"
                    type="number"
                    min="0"
                    max="10"
                    step="0.1"
                    :disabled="loading"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入压力值"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">低区加压泵压力 (MPa)</label>
                  <input
                    v-model="formData.lowZonePump"
                    type="number"
                    min="0"
                    max="10"
                    step="0.1"
                    :disabled="loading"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入压力值"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 空调表单 -->
          <div v-if="formData.pageType === 'aircondition'" class="mb-6 space-y-6">
            <!-- 温度监测 -->
            <div>
              <h4 class="text-md font-medium text-gray-900 mb-3">温度监测</h4>
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">冷冻水进水温度 (°C)</label>
                  <input
                    v-model="formData.chilledWaterInletTemp"
                    type="number"
                    step="0.1"
                    :disabled="loading"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入温度"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">冷冻水出水温度 (°C)</label>
                  <input
                    v-model="formData.chilledWaterOutletTemp"
                    type="number"
                    step="0.1"
                    :disabled="loading"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入温度"
                  />
                </div>
              </div>
              <div class="grid grid-cols-2 gap-4 mt-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">高温发生器温度 (°C)</label>
                  <input
                    v-model="formData.highTempGeneratorTemp"
                    type="number"
                    step="0.1"
                    :disabled="loading"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入温度"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">真空压力 (kPa)</label>
                  <input
                    v-model="formData.vacuumPressure"
                    type="number"
                    step="0.1"
                    :disabled="loading"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入压力"
                  />
                </div>
              </div>
            </div>

            <!-- 冷却水系统 -->
            <div>
              <h4 class="text-md font-medium text-gray-900 mb-3">冷却水系统</h4>
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">冷却水进水温度 (°C)</label>
                  <input
                    v-model="formData.coolingWaterInletTemp"
                    type="number"
                    step="0.1"
                    :disabled="loading"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入温度"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">冷却水出水温度 (°C)</label>
                  <input
                    v-model="formData.coolingWaterOutletTemp"
                    type="number"
                    step="0.1"
                    :disabled="loading"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入温度"
                  />
                </div>
              </div>
            </div>

            <!-- 分区水温 -->
            <div>
              <h4 class="text-md font-medium text-gray-900 mb-3">分区水温</h4>
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">高区水温 (°C)</label>
                  <input
                    v-model="formData.highZoneWaterTemp"
                    type="number"
                    step="0.1"
                    :disabled="loading"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入温度"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">低区水温 (°C)</label>
                  <input
                    v-model="formData.lowZoneWaterTemp"
                    type="number"
                    step="0.1"
                    :disabled="loading"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入温度"
                  />
                </div>
              </div>
            </div>

            <!-- 水泵状态 -->
            <div>
              <h4 class="text-md font-medium text-gray-900 mb-3">水泵运行状态</h4>
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="flex items-center">
                    <input
                      v-model="formData.chilledWaterPump"
                      type="checkbox"
                      :disabled="loading"
                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span class="ml-2 text-sm text-gray-700">冷冻水泵运行</span>
                  </label>
                </div>
                <div>
                  <label class="flex items-center">
                    <input
                      v-model="formData.coolingWaterPump"
                      type="checkbox"
                      :disabled="loading"
                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span class="ml-2 text-sm text-gray-700">冷却水泵运行</span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          <!-- 施工登记表单 -->
          <div v-if="formData.pageType === 'construction'" class="mb-6 space-y-4">
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">施工项目 *</label>
                <input
                  v-model="formData.projectName"
                  type="text"
                  required
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="如：客房装修、管道维修等"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">施工位置 *</label>
                <input
                  v-model="formData.constructionLocation"
                  type="text"
                  required
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="如：3楼走廊、地下室等"
                />
              </div>
            </div>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">施工单位</label>
                <input
                  v-model="formData.contractor"
                  type="text"
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="施工单位名称"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">负责人</label>
                <input
                  v-model="formData.supervisor"
                  type="text"
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="现场负责人姓名"
                />
              </div>
            </div>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">开始时间</label>
                <input
                  v-model="formData.startTime"
                  type="datetime-local"
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">预计结束时间</label>
                <input
                  v-model="formData.endTime"
                  type="datetime-local"
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">施工内容描述 *</label>
              <textarea
                v-model="formData.constructionDescription"
                required
                :disabled="loading"
                rows="4"
                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                placeholder="详细描述施工内容、注意事项、安全措施等..."
              ></textarea>
            </div>
          </div>

          <!-- 图片上传 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              相关图片
            </label>
            
            <!-- 已上传的图片 -->
            <div v-if="formData.images.length > 0" class="mb-4">
              <div class="grid grid-cols-3 gap-2">
                <div 
                  v-for="(image, index) in formData.images" 
                  :key="index"
                  class="relative group"
                >
                  <img 
                    :src="image" 
                    :alt="`图片 ${index + 1}`"
                    class="w-full h-20 object-cover rounded border border-gray-200"
                  />
                  <button 
                    type="button"
                    @click="removeImage(index)"
                    class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600 opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    ×
                  </button>
                </div>
              </div>
            </div>
            
            <!-- 上传按钮 -->
            <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors">
              <input 
                ref="fileInput"
                type="file" 
                multiple 
                accept="image/*"
                class="hidden"
                @change="handleFileSelect"
              />
              
              <button
                type="button"
                @click="fileInput?.click()"
                :disabled="loading || uploading"
                class="text-blue-600 hover:text-blue-700 font-medium disabled:opacity-50"
              >
                <Icon name="mdi:camera" size="24" class="mx-auto mb-2" />
                {{ uploading ? '上传中...' : '选择图片' }}
              </button>
              
              <p class="text-xs text-gray-500 mt-1">
                支持 JPG、PNG 格式，最多 9 张图片
              </p>
            </div>
          </div>
        </form>
      </div>

      <!-- 底部按钮 -->
      <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
        <button 
          type="button"
          @click="handleClose"
          :disabled="loading"
          class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50"
        >
          取消
        </button>
        <button 
          @click="handleSubmit"
          :disabled="loading || !isFormValid"
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center"
        >
          <Icon v-if="loading" name="mdi:loading" size="16" class="mr-1 animate-spin" />
          {{ loading ? (isEditing ? '更新中...' : '创建中...') : (isEditing ? '更新' : '创建') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { WorkLog } from '~/types'

interface Props {
  show: boolean
  editLog?: WorkLog | null
}

const props = withDefaults(defineProps<Props>(), {
  editLog: null
})

const emit = defineEmits<{
  'update:show': [value: boolean]
  created: []
  updated: []
}>()

const workLogStore = useWorkLogStore()

// 响应式数据
const loading = ref(false)
const uploading = ref(false)
const fileInput = ref<HTMLInputElement>()

const formData = reactive({
  // 通用字段
  content: '',
  pageType: '',
  images: [] as string[],
  notes: '',

  // 变电站字段 - 根据powerstation.html
  indoorTemp: '',
  indoorHumidity: '',
  transformer2000A: '',
  transformer2000B: '',
  transformer2000C: '',
  transformer1250A: '',
  transformer1250B: '',
  transformer1250C: '',
  transformerStatus: '',
  fireStatus: '',

  // 净水器字段 - 根据waterfilter.html
  tapWaterLevel: '',
  purifiedWaterLevel: '',
  rdBuildingPump: '',
  highZonePump: '',
  lowZonePump: '',

  // 空调字段 - 根据aircondition.html
  chilledWaterInletTemp: '',
  chilledWaterOutletTemp: '',
  highTempGeneratorTemp: '',
  coolingWaterInletTemp: '',
  coolingWaterOutletTemp: '',
  vacuumPressure: '',
  highZoneWaterTemp: '',
  lowZoneWaterTemp: '',
  chilledWaterPump: false,
  coolingWaterPump: false,

  // 施工登记字段
  projectName: '',
  constructionLocation: '',
  contractor: '',
  supervisor: '',
  startTime: '',
  endTime: '',
  constructionDescription: ''
})

// 计算属性
const isEditing = computed(() => !!props.editLog)

const isFormValid = computed(() => {
  if (!formData.pageType) return false

  switch (formData.pageType) {
    case 'main':
      return formData.content.trim().length > 0 && formData.content.length <= 1000

    case 'powerstation':
      return formData.indoorTemp && formData.indoorHumidity

    case 'waterfilter':
      return formData.tapWaterLevel || formData.purifiedWaterLevel || formData.rdBuildingPump

    case 'aircondition':
      return formData.chilledWaterInletTemp || formData.chilledWaterOutletTemp

    case 'construction':
      return formData.projectName.trim().length > 0 &&
             formData.constructionLocation.trim().length > 0 &&
             formData.constructionDescription.trim().length > 0

    default:
      return false
  }
})

// 方法
const resetForm = () => {
  // 通用字段
  formData.content = ''
  formData.pageType = ''
  formData.images = []
  formData.notes = ''

  // 变电站字段
  formData.indoorTemp = ''
  formData.indoorHumidity = ''
  formData.transformer2000A = ''
  formData.transformer2000B = ''
  formData.transformer2000C = ''
  formData.transformer1250A = ''
  formData.transformer1250B = ''
  formData.transformer1250C = ''
  formData.transformerStatus = ''
  formData.fireStatus = ''

  // 净水器字段
  formData.tapWaterLevel = ''
  formData.purifiedWaterLevel = ''
  formData.rdBuildingPump = ''
  formData.highZonePump = ''
  formData.lowZonePump = ''

  // 空调字段
  formData.chilledWaterInletTemp = ''
  formData.chilledWaterOutletTemp = ''
  formData.highTempGeneratorTemp = ''
  formData.coolingWaterInletTemp = ''
  formData.coolingWaterOutletTemp = ''
  formData.vacuumPressure = ''
  formData.highZoneWaterTemp = ''
  formData.lowZoneWaterTemp = ''
  formData.chilledWaterPump = false
  formData.coolingWaterPump = false

  // 施工登记字段
  formData.projectName = ''
  formData.constructionLocation = ''
  formData.contractor = ''
  formData.supervisor = ''
  formData.startTime = ''
  formData.endTime = ''
  formData.constructionDescription = ''
}

const loadEditData = () => {
  if (props.editLog) {
    formData.content = props.editLog.content
    formData.pageType = props.editLog.pageType
    formData.images = [...(props.editLog.images || [])]
  }
}

const handleClose = () => {
  if (!loading.value) {
    emit('update:show', false)
    resetForm()
  }
}

const handleBackdropClick = () => {
  handleClose()
}

// 压缩图片函数
const compressImage = (file: File, quality = 0.8): Promise<Blob> => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!
    const img = new Image()

    img.onload = () => {
      // 计算压缩后的尺寸
      const maxWidth = 1200
      const maxHeight = 1200
      let { width, height } = img

      if (width > height) {
        if (width > maxWidth) {
          height = (height * maxWidth) / width
          width = maxWidth
        }
      } else {
        if (height > maxHeight) {
          width = (width * maxHeight) / height
          height = maxHeight
        }
      }

      canvas.width = width
      canvas.height = height

      // 绘制并压缩
      ctx.drawImage(img, 0, 0, width, height)
      canvas.toBlob((blob) => {
        resolve(blob || file)
      }, 'image/jpeg', quality)
    }

    img.src = URL.createObjectURL(file)
  })
}

const handleFileSelect = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files

  if (!files || files.length === 0) return

  // 检查图片数量限制
  if (formData.images.length + files.length > 9) {
    alert('最多只能上传9张图片')
    return
  }

  uploading.value = true

  try {
    for (const fileItem of Array.from(files)) {
      // 检查文件类型
      if (!fileItem.type.startsWith('image/')) {
        alert(`文件 ${fileItem.name} 不是有效的图片格式`)
        continue
      }

      // 检查文件大小（5MB限制）
      if (fileItem.size > 5 * 1024 * 1024) {
        alert(`文件 ${fileItem.name} 大小超过5MB限制`)
        continue
      }

      // 压缩图片
      const compressedBlob = await compressImage(fileItem)

      // 创建新的文件名
      const originalName = fileItem.name
      const nameWithoutExt = originalName.substring(0, originalName.lastIndexOf('.')) || originalName
      const compressedFileName = `worklog_${nameWithoutExt}_${Date.now()}.jpg`

      // 构造FormData
      const uploadFormData = new FormData()
      uploadFormData.append('token', '8e7057ee0ba0be565301980fb3e52763')
      uploadFormData.append('image', new File([compressedBlob], compressedFileName, { type: 'image/jpeg' }))

      // 上传到自建API
      const response = await fetch('https://www.junwei.bid:89/web/11/index.php', {
        method: 'POST',
        body: uploadFormData
      })

      if (response.ok) {
        const result = await response.json()
        // 兼容不同返回格式
        if (result.url) {
          formData.images.push(result.url)
        } else if (result.data && result.data.url) {
          formData.images.push(result.data.url)
        } else if (typeof result === 'string' && result.startsWith('http')) {
          formData.images.push(result)
        } else {
          alert(`上传文件 ${fileItem.name} 失败: 返回格式不正确`)
        }
      } else {
        const errorText = await response.text()
        alert(`上传文件 ${fileItem.name} 失败: ${errorText}`)
      }
    }
  } catch (error) {
    console.error('文件上传失败:', error)
    alert('文件上传失败，请稍后重试')
  } finally {
    uploading.value = false
    // 清空文件输入
    if (fileInput.value) {
      fileInput.value.value = ''
    }
  }
}

const removeImage = (index: number) => {
  formData.images.splice(index, 1)
}

const handleSubmit = async () => {
  if (!isFormValid.value || loading.value) return
  
  loading.value = true
  
  try {
    const logData = {
      content: formData.content.trim(),
      pageType: formData.pageType,
      images: formData.images
    }
    
    let result
    
    if (isEditing.value) {
      // 编辑逻辑（需要在store中实现updateLog方法）
      // result = await workLogStore.updateLog(props.editLog!.id, logData)
      // 暂时使用创建逻辑
      result = await workLogStore.createLog(logData)
      if (result.success) {
        emit('updated')
      }
    } else {
      result = await workLogStore.createLog(logData)
      if (result.success) {
        emit('created')
      }
    }
    
    if (!result.success) {
      alert(result.error || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    alert('提交失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 监听props变化
watch(() => props.show, (newValue) => {
  if (newValue) {
    if (props.editLog) {
      loadEditData()
    } else {
      resetForm()
    }
  }
})

// 键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.show) {
    handleClose()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>
