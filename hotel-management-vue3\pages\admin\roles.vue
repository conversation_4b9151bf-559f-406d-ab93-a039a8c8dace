<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">角色管理</h1>
          <p class="mt-1 text-sm text-gray-600">
            管理用户角色和权限配置
          </p>
        </div>
        
        <div class="mt-4 sm:mt-0 flex space-x-3">
          <button 
            @click="showCreateModal = true"
            class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center"
          >
            <Icon name="mdi:plus" size="16" class="mr-1" />
            新增角色
          </button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:shield-account" size="24" class="text-blue-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">总角色数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ roleStats.total }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:cog" size="24" class="text-green-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">系统角色</p>
            <p class="text-2xl font-semibold text-gray-900">{{ roleStats.system }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:account-group" size="24" class="text-purple-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">用户总数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ totalUsers }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 角色列表 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">角色列表</h3>
      </div>
      
      <div v-if="loading" class="p-8 text-center">
        <Icon name="mdi:loading" size="32" class="text-gray-400 animate-spin mx-auto mb-4" />
        <p class="text-gray-600">加载中...</p>
      </div>
      
      <div v-else-if="roles.length === 0" class="p-8 text-center">
        <Icon name="mdi:shield-account-outline" size="64" class="text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无角色</h3>
        <p class="text-gray-600 mb-6">创建你的第一个角色吧</p>
        <button 
          @click="showCreateModal = true"
          class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          新增角色
        </button>
      </div>
      
      <div v-else class="divide-y divide-gray-200">
        <div 
          v-for="role in roles" 
          :key="role.id"
          class="p-6 hover:bg-gray-50 transition-colors"
        >
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-3 mb-2">
                <h4 class="text-lg font-medium text-gray-900">{{ role.name }}</h4>
                <span class="text-sm text-gray-500">({{ role.code }})</span>
                <span 
                  v-if="role.isSystem"
                  class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full"
                >
                  系统角色
                </span>
                <span 
                  v-else
                  class="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full"
                >
                  自定义
                </span>
              </div>
              
              <p v-if="role.description" class="text-gray-600 mb-3">{{ role.description }}</p>
              
              <!-- 权限标签 -->
              <div class="flex flex-wrap gap-2 mb-3">
                <span 
                  v-for="permission in role.permissions" 
                  :key="permission"
                  class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-md"
                >
                  {{ getPermissionName(permission) }}
                </span>
              </div>
              
              <div class="flex items-center space-x-6 text-sm text-gray-500">
                <div class="flex items-center">
                  <Icon name="mdi:account-group" size="16" class="mr-1" />
                  {{ role.userCount }} 个用户
                </div>
                <div class="flex items-center">
                  <Icon name="mdi:calendar" size="16" class="mr-1" />
                  创建于 {{ formatDate(role.createdAt) }}
                </div>
              </div>
            </div>
            
            <div class="flex items-center space-x-2">
              <button 
                @click="editRole(role)"
                class="text-blue-600 hover:text-blue-700 p-2 rounded-md hover:bg-blue-50 transition-colors"
                title="编辑"
              >
                <Icon name="mdi:pencil" size="16" />
              </button>
              
              <button 
                v-if="!role.isSystem"
                @click="deleteRole(role)"
                class="text-red-600 hover:text-red-700 p-2 rounded-md hover:bg-red-50 transition-colors"
                title="删除"
              >
                <Icon name="mdi:delete" size="16" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建/编辑角色模态框 -->
    <CreateRoleModal
      v-model:show="showCreateModal"
      :edit-role="editingRole"
      @created="handleRoleCreated"
      @updated="handleRoleUpdated"
    />
  </div>
</template>

<script setup lang="ts">
import type { Role } from '~/types'
import CreateRoleModal from '~/components/admin/CreateRoleModal.vue'

// 页面元数据
definePageMeta({
  title: '角色管理',
  middleware: 'auth',
  requiresAdmin: true
})

// Store
const adminStore = useAdminStore()

// 响应式数据
const showCreateModal = ref(false)
const editingRole = ref<Role | null>(null)

// 计算属性
const roles = computed(() => adminStore.roles)
const roleStats = computed(() => adminStore.roleStats)
const loading = computed(() => adminStore.loading)

const totalUsers = computed(() => 
  roles.value.reduce((sum, role) => sum + role.userCount, 0)
)

// 权限名称映射
const permissionNames: Record<string, string> = {
  'user_management': '用户管理',
  'system_settings': '系统设置',
  'reports': '报表查看',
  'repair_management': '维修管理',
  'inventory_view': '库存查看',
  'repair_submit': '提交报修',
  'worklog_create': '创建日志'
}

// 方法
const formatDate = (date: Date) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

const getPermissionName = (permission: string) => {
  return permissionNames[permission] || permission
}

const editRole = (role: Role) => {
  editingRole.value = role
  showCreateModal.value = true
}

const deleteRole = async (role: Role) => {
  if (role.isSystem) {
    alert('系统角色不能删除')
    return
  }
  
  if (role.userCount > 0) {
    alert('该角色还有用户，请先转移用户后再删除')
    return
  }
  
  if (confirm(`确定要删除角色"${role.name}"吗？`)) {
    // 这里应该调用删除API
    alert('删除功能待实现')
  }
}

const handleRoleCreated = () => {
  showCreateModal.value = false
  editingRole.value = null
  adminStore.fetchRoles()
}

const handleRoleUpdated = () => {
  showCreateModal.value = false
  editingRole.value = null
  adminStore.fetchRoles()
}

// 生命周期
onMounted(async () => {
  await adminStore.fetchRoles()
})

// 页面标题
useHead({
  title: '角色管理 - 酒店管理系统'
})
</script>
