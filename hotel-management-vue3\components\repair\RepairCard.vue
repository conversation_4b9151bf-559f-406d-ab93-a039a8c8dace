<template>
  <div class="bg-white rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
    <!-- 头部 -->
    <div class="p-4 border-b border-gray-100">
      <div class="flex items-start justify-between">
        <div class="flex-1">
          <h3 class="text-lg font-semibold text-gray-900 mb-1">{{ repair.title }}</h3>
          <div class="flex items-center space-x-2 text-sm text-gray-600">
            <Icon name="mdi:map-marker" size="14" />
            <span>{{ repair.location }}</span>
          </div>
        </div>
        
        <!-- 优先级标识 -->
        <span 
          class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
          :class="getPriorityStyle(repair.priority)"
        >
          <Icon :name="getPriorityIcon(repair.priority)" size="12" class="mr-1" />
          {{ getPriorityText(repair.priority) }}
        </span>
      </div>
    </div>

    <!-- 内容 -->
    <div class="p-4">
      <!-- 状态和分类 -->
      <div class="flex items-center justify-between mb-3">
        <span 
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
          :class="getStatusStyle(repair.status)"
        >
          <Icon :name="getStatusIcon(repair.status)" size="12" class="mr-1" />
          {{ getStatusText(repair.status) }}
        </span>
        
        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
          {{ repair.category }}
        </span>
      </div>

      <!-- 描述 -->
      <p class="text-sm text-gray-600 mb-4 line-clamp-2">{{ repair.description }}</p>

      <!-- 时间信息 -->
      <div class="space-y-2 mb-4">
        <div class="flex items-center justify-between text-xs text-gray-500">
          <span>创建时间</span>
          <span>{{ formatTime(repair.createdAt) }}</span>
        </div>
        
        <div v-if="repair.assignedAt" class="flex items-center justify-between text-xs text-gray-500">
          <span>分配时间</span>
          <span>{{ formatTime(repair.assignedAt) }}</span>
        </div>
        
        <div v-if="repair.startedAt" class="flex items-center justify-between text-xs text-gray-500">
          <span>开始时间</span>
          <span>{{ formatTime(repair.startedAt) }}</span>
        </div>
        
        <div v-if="repair.completedAt" class="flex items-center justify-between text-xs text-gray-500">
          <span>完成时间</span>
          <span>{{ formatTime(repair.completedAt) }}</span>
        </div>
      </div>

      <!-- 人员信息 -->
      <div class="space-y-2 mb-4">
        <div v-if="repair.reporter" class="flex items-center text-sm">
          <Icon name="mdi:account" size="14" class="text-gray-400 mr-2" />
          <span class="text-gray-600">报修人:</span>
          <span class="ml-1 text-gray-900">{{ repair.reporter.realName || repair.reporter.username }}</span>
        </div>
        
        <div v-if="repair.assignee" class="flex items-center text-sm">
          <Icon name="mdi:account-wrench" size="14" class="text-gray-400 mr-2" />
          <span class="text-gray-600">处理人:</span>
          <span class="ml-1 text-gray-900">{{ repair.assignee.realName || repair.assignee.username }}</span>
        </div>
      </div>

      <!-- 预计时间和成本 -->
      <div v-if="repair.estimatedTime || repair.cost" class="grid grid-cols-2 gap-4 mb-4 text-sm">
        <div v-if="repair.estimatedTime" class="text-center p-2 bg-blue-50 rounded">
          <div class="text-blue-600 font-medium">{{ repair.estimatedTime }}h</div>
          <div class="text-xs text-gray-500">预计时长</div>
        </div>
        
        <div v-if="repair.cost" class="text-center p-2 bg-green-50 rounded">
          <div class="text-green-600 font-medium">¥{{ repair.cost }}</div>
          <div class="text-xs text-gray-500">维修成本</div>
        </div>
      </div>

      <!-- 图片预览 -->
      <div v-if="repair.images && repair.images.length > 0" class="mb-4">
        <div class="flex space-x-2">
          <img 
            v-for="(image, index) in repair.images.slice(0, 3)" 
            :key="index"
            :src="image" 
            :alt="`图片 ${index + 1}`"
            class="w-16 h-16 object-cover rounded border border-gray-200 cursor-pointer"
            @click="$emit('view', repair)"
          />
          <div 
            v-if="repair.images.length > 3"
            class="w-16 h-16 bg-gray-100 rounded border border-gray-200 flex items-center justify-center text-xs text-gray-500 cursor-pointer"
            @click="$emit('view', repair)"
          >
            +{{ repair.images.length - 3 }}
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="px-4 py-3 border-t border-gray-100 bg-gray-50">
      <div class="flex items-center justify-between">
        <button 
          @click="$emit('view', repair)"
          class="text-blue-600 hover:text-blue-700 text-sm font-medium"
        >
          查看详情
        </button>
        
        <div class="flex space-x-2">
          <!-- 根据状态显示不同的操作按钮 -->
          <template v-if="repair.status === 'pending'">
            <button 
              v-if="canAssign"
              @click="$emit('assign', repair)"
              class="bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700 transition-colors"
            >
              分配
            </button>
            <button 
              v-if="canEdit"
              @click="$emit('edit', repair)"
              class="border border-gray-300 text-gray-700 px-3 py-1 rounded text-xs hover:bg-gray-50 transition-colors"
            >
              编辑
            </button>
          </template>
          
          <template v-else-if="repair.status === 'assigned'">
            <button 
              v-if="canStart"
              @click="$emit('start', repair)"
              class="bg-green-600 text-white px-3 py-1 rounded text-xs hover:bg-green-700 transition-colors"
            >
              开始处理
            </button>
          </template>
          
          <template v-else-if="repair.status === 'in_progress'">
            <button 
              v-if="canComplete"
              @click="$emit('complete', repair)"
              class="bg-green-600 text-white px-3 py-1 rounded text-xs hover:bg-green-700 transition-colors"
            >
              完成
            </button>
          </template>
          
          <!-- 取消按钮 -->
          <button 
            v-if="canCancel"
            @click="$emit('cancel', repair)"
            class="border border-red-300 text-red-600 px-3 py-1 rounded text-xs hover:bg-red-50 transition-colors"
          >
            取消
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Repair } from '~/types'

interface Props {
  repair: Repair
}

const props = defineProps<Props>()
const emit = defineEmits<{
  view: [repair: Repair]
  edit: [repair: Repair]
  assign: [repair: Repair]
  start: [repair: Repair]
  complete: [repair: Repair]
  cancel: [repair: Repair]
}>()

const authStore = useAuthStore()

// 计算属性
const canEdit = computed(() => {
  if (!authStore.user) return false
  if (authStore.isAdmin) return true
  return authStore.user.id === props.repair.reporter?.id && 
         ['pending', 'assigned'].includes(props.repair.status)
})

const canAssign = computed(() => {
  if (!authStore.user) return false
  return authStore.isAdmin || authStore.user.roles.includes('engineer')
})

const canStart = computed(() => {
  if (!authStore.user) return false
  return authStore.user.id === props.repair.assignee?.id
})

const canComplete = computed(() => {
  if (!authStore.user) return false
  return authStore.user.id === props.repair.assignee?.id
})

const canCancel = computed(() => {
  if (!authStore.user) return false
  if (authStore.isAdmin) return true
  return authStore.user.id === props.repair.reporter?.id && 
         !['completed', 'cancelled'].includes(props.repair.status)
})

// 方法
const getPriorityStyle = (priority: string) => {
  const styles = {
    'low': 'bg-gray-100 text-gray-800',
    'medium': 'bg-yellow-100 text-yellow-800',
    'high': 'bg-orange-100 text-orange-800',
    'urgent': 'bg-red-100 text-red-800'
  }
  return styles[priority as keyof typeof styles] || styles.medium
}

const getPriorityIcon = (priority: string) => {
  const icons = {
    'low': 'mdi:arrow-down',
    'medium': 'mdi:minus',
    'high': 'mdi:arrow-up',
    'urgent': 'mdi:alert'
  }
  return icons[priority as keyof typeof icons] || icons.medium
}

const getPriorityText = (priority: string) => {
  const texts = {
    'low': '低',
    'medium': '中',
    'high': '高',
    'urgent': '紧急'
  }
  return texts[priority as keyof typeof texts] || '中'
}

const getStatusStyle = (status: string) => {
  const styles = {
    'pending': 'bg-yellow-100 text-yellow-800',
    'assigned': 'bg-blue-100 text-blue-800',
    'in_progress': 'bg-purple-100 text-purple-800',
    'completed': 'bg-green-100 text-green-800',
    'cancelled': 'bg-gray-100 text-gray-800',
    'rejected': 'bg-red-100 text-red-800'
  }
  return styles[status as keyof typeof styles] || styles.pending
}

const getStatusIcon = (status: string) => {
  const icons = {
    'pending': 'mdi:clock-outline',
    'assigned': 'mdi:account-check',
    'in_progress': 'mdi:progress-wrench',
    'completed': 'mdi:check-circle',
    'cancelled': 'mdi:close-circle',
    'rejected': 'mdi:close-circle-outline'
  }
  return icons[status as keyof typeof icons] || icons.pending
}

const getStatusText = (status: string) => {
  const texts = {
    'pending': '待处理',
    'assigned': '已分配',
    'in_progress': '处理中',
    'completed': '已完成',
    'cancelled': '已取消',
    'rejected': '已拒绝'
  }
  return texts[status as keyof typeof texts] || '待处理'
}

const formatTime = (date: Date) => {
  const now = new Date()
  const targetDate = new Date(date)
  const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000)
  
  if (diffInSeconds < 60) {
    return '刚刚'
  } else if (diffInSeconds < 3600) {
    return `${Math.floor(diffInSeconds / 60)}分钟前`
  } else if (diffInSeconds < 86400) {
    return `${Math.floor(diffInSeconds / 3600)}小时前`
  } else if (diffInSeconds < 604800) {
    return `${Math.floor(diffInSeconds / 86400)}天前`
  } else {
    return targetDate.toLocaleDateString('zh-CN')
  }
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
