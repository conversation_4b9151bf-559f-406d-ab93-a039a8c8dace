<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <!-- 微信浏览器兼容性设置 -->
    <meta name="x5-orientation" content="portrait">
    <meta name="x5-fullscreen" content="true">
    <meta name="x5-page-mode" content="app">
    <title>出库管理</title>
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- LeanCloud SDK -->
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <!-- 备用 CDN 源 -->
    <script>
        // 检测 LeanCloud SDK 是否加载成功，如果失败则使用备用源
        window.addEventListener('load', function() {
            if (typeof AV === 'undefined') {
                console.log('主 CDN 加载失败，尝试备用源...');
                var script = document.createElement('script');
                script.src = 'https://cdn.bootcdn.net/ajax/libs/leancloud-storage/4.15.2/av-min.js';
                script.onerror = function() {
                    console.error('所有 CDN 源都加载失败，请检查网络连接');
                    alert('网络连接异常，请检查网络后刷新页面');
                };
                document.head.appendChild(script);
            }
        });
    </script>
    <style>
        /* 微信浏览器兼容性修复 */
        * {
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
        }
        
        body {
            -webkit-overflow-scrolling: touch;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        
        .btn-fix {
            -webkit-appearance: none;
            appearance: none;
            border-radius: 8px;
            border: none;
            outline: none;
        }
        
        .fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .loading {
            border-top-color: #3498db;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .status-draft { @apply bg-gray-100 text-gray-800; }
        .status-confirmed { @apply bg-blue-100 text-blue-800; }
        .status-completed { @apply bg-green-100 text-green-800; }
        .status-cancelled { @apply bg-red-100 text-red-800; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
        <div class="max-w-7xl mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-4">
                    <a href="index.html" class="text-gray-600 hover:text-gray-800">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                    </a>
                    <h1 class="text-xl font-semibold text-gray-800">出库管理</h1>
                </div>
                <div id="userInfo" class="hidden flex items-center space-x-2">
                    <span id="realName" class="text-gray-800 font-medium text-sm"></span>
                    <button id="logoutBtn" class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded-lg text-sm transition-colors btn-fix">
                        退出
                    </button>
                </div>
                <button id="loginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm transition-colors btn-fix">
                    登录
                </button>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="max-w-7xl mx-auto px-4 py-6">
        <!-- 未登录提示 -->
        <div id="loginPrompt" class="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg shadow-sm border border-blue-200 p-8 mb-6 text-center">
            <div class="mb-6">
                <h2 class="text-2xl font-bold text-gray-800 mb-4">请先登录</h2>
                <p class="text-gray-600 text-lg mb-6">
                    需要登录后才能进行出库管理操作
                </p>
                <button id="promptLoginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors font-medium btn-fix">
                    立即登录
                </button>
            </div>
        </div>

        <!-- 出库管理主界面 -->
        <div id="inventoryOutSection" class="hidden">
            <!-- 统计卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">今日出库单</p>
                            <p id="todayOutboundCount" class="text-2xl font-bold text-blue-600">0</p>
                        </div>
                        <div class="bg-blue-100 rounded-full p-3">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">待确认</p>
                            <p id="pendingOutboundCount" class="text-2xl font-bold text-orange-600">0</p>
                        </div>
                        <div class="bg-orange-100 rounded-full p-3">
                            <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">本月出库</p>
                            <p id="monthOutboundCount" class="text-2xl font-bold text-green-600">0</p>
                        </div>
                        <div class="bg-green-100 rounded-full p-3">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">总出库金额</p>
                            <p id="totalOutboundAmount" class="text-2xl font-bold text-purple-600">¥0</p>
                        </div>
                        <div class="bg-purple-100 rounded-full p-3">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作栏 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-6 mb-6">
                <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div class="flex flex-col sm:flex-row gap-3">
                        <input type="text" id="searchInput" placeholder="搜索出库单号、部门..."
                               class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent text-sm">
                        <select id="statusFilter" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent text-sm">
                            <option value="">全部状态</option>
                            <option value="draft">草稿</option>
                            <option value="confirmed">已确认</option>
                            <option value="completed">已完成</option>
                            <option value="cancelled">已取消</option>
                        </select>
                        <input type="date" id="dateFilter" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent text-sm">
                    </div>
                    <div class="flex gap-3">
                        <button id="refreshBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors text-sm font-medium btn-fix">
                            刷新
                        </button>
                        <button id="createOutboundBtn" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors text-sm font-medium btn-fix">
                            新建出库单
                        </button>
                    </div>
                </div>
            </div>

            <!-- 出库单列表 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-100">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-800">出库单列表</h2>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">出库单号</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">部门/收货人</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">仓库</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">金额</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody id="outboundTableBody" class="bg-white divide-y divide-gray-200">
                            <!-- 出库单数据将在这里动态加载 -->
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <div id="pagination" class="px-6 py-3 border-t border-gray-200 flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span id="pageInfo">0-0</span> 条，共 <span id="totalCount">0</span> 条
                    </div>
                    <div class="flex space-x-2">
                        <button id="prevPageBtn" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed btn-fix">
                            上一页
                        </button>
                        <button id="nextPageBtn" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed btn-fix">
                            下一页
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载指示器 -->
        <div id="loadingIndicator" class="hidden flex justify-center py-8">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 border-4 border-gray-300 border-t-green-500 rounded-full loading"></div>
                <span class="text-gray-600">正在加载...</span>
            </div>
        </div>
    </main>

    <!-- 登录弹窗 -->
    <div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md fade-in">
            <div class="p-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-xl font-semibold text-gray-800">用户登录</h2>
                    <button id="closeLoginModal" class="text-gray-400 hover:text-gray-600 text-2xl btn-fix">&times;</button>
                </div>
            </div>
            <div class="p-4">
                <!-- 登录表单 -->
                <form id="loginForm" class="space-y-4">
                    <div>
                        <label for="loginUsername" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                        <input type="text" id="loginUsername" autocomplete="username"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm" required>
                    </div>
                    <div>
                        <label for="loginPassword" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                        <input type="password" id="loginPassword" autocomplete="current-password"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm" required>
                    </div>
                    <button type="submit" class="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                        登录
                    </button>
                </form>

                <!-- 注册表单 -->
                <form id="registerForm" class="hidden space-y-4">
                    <div>
                        <label for="registerUsername" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                        <input type="text" id="registerUsername" autocomplete="username"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm" required>
                    </div>
                    <div>
                        <label for="registerRealName" class="block text-sm font-medium text-gray-700 mb-1">真实姓名</label>
                        <input type="text" id="registerRealName" autocomplete="name"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm" required>
                    </div>
                    <div>
                        <label for="registerPhone" class="block text-sm font-medium text-gray-700 mb-1">联系电话</label>
                        <input type="tel" id="registerPhone" autocomplete="tel"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm" required>
                    </div>
                    <div>
                        <label for="registerDepartment" class="block text-sm font-medium text-gray-700 mb-1">所属部门</label>
                        <select id="registerDepartment" required
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm">
                            <option value="">请选择部门</option>
                            <option value="前厅部">前厅部</option>
                            <option value="客房部">客房部</option>
                            <option value="餐饮部">餐饮部</option>
                            <option value="工程部">工程部</option>
                            <option value="保安部">保安部</option>
                            <option value="财务部">财务部</option>
                            <option value="人事部">人事部</option>
                            <option value="销售部">销售部</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>
                    <div>
                        <label for="registerPassword" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                        <input type="password" id="registerPassword" autocomplete="new-password"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm" required>
                    </div>
                    <div>
                        <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-1">确认密码</label>
                        <input type="password" id="confirmPassword" autocomplete="new-password"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm" required>
                    </div>
                    <button type="submit" class="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                        注册
                    </button>
                </form>

                <!-- 表单切换 -->
                <div class="mt-4 text-center">
                    <p id="loginSwitchText" class="text-sm text-gray-600">
                        还没有账号？
                        <a href="#" id="showRegisterBtn" class="text-blue-500 hover:text-blue-600 font-medium">立即注册</a>
                    </p>
                    <p id="registerSwitchText" class="hidden text-sm text-gray-600">
                        已有账号？
                        <a href="#" id="showLoginBtn" class="text-blue-500 hover:text-blue-600 font-medium">立即登录</a>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- 出库单编辑弹窗 -->
    <div id="outboundOrderModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto fade-in">
            <div class="p-4 border-b border-gray-200 sticky top-0 bg-white">
                <div class="flex justify-between items-center">
                    <h2 id="outboundOrderModalTitle" class="text-xl font-semibold text-gray-800">新建出库单</h2>
                    <button id="closeOutboundOrderModal" class="text-gray-400 hover:text-gray-600 text-2xl btn-fix">&times;</button>
                </div>
            </div>
            <div class="p-6">
                <form id="outboundOrderForm" class="space-y-6">
                    <input type="hidden" id="editOutboundOrderId">

                    <!-- 基本信息 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="outboundOrderNo" class="block text-sm font-medium text-gray-700 mb-1">出库单号 <span class="text-red-500">*</span></label>
                            <input type="text" id="outboundOrderNo" required readonly
                                class="w-full p-3 border border-gray-300 rounded-lg bg-gray-50 text-sm">
                        </div>
                        <div>
                            <label for="outboundType" class="block text-sm font-medium text-gray-700 mb-1">出库类型 <span class="text-red-500">*</span></label>
                            <select id="outboundType" required
                                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent text-sm">
                                <option value="">请选择出库类型</option>
                                <option value="sale">销售出库</option>
                                <option value="use">领用出库</option>
                                <option value="transfer">调拨出库</option>
                                <option value="other">其他出库</option>
                            </select>
                        </div>
                        <div>
                            <label for="outboundDepartment" class="block text-sm font-medium text-gray-700 mb-1">领用部门</label>
                            <select id="outboundDepartment"
                                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent text-sm">
                                <option value="">请选择部门</option>
                                <option value="前厅部">前厅部</option>
                                <option value="客房部">客房部</option>
                                <option value="餐饮部">餐饮部</option>
                                <option value="工程部">工程部</option>
                                <option value="保安部">保安部</option>
                                <option value="财务部">财务部</option>
                                <option value="人事部">人事部</option>
                                <option value="销售部">销售部</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        <div>
                            <label for="outboundRecipient" class="block text-sm font-medium text-gray-700 mb-1">收货人/领用人</label>
                            <input type="text" id="outboundRecipient"
                                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent text-sm">
                        </div>
                        <div>
                            <label for="outboundWarehouse" class="block text-sm font-medium text-gray-700 mb-1">出库仓库 <span class="text-red-500">*</span></label>
                            <select id="outboundWarehouse" required
                                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent text-sm">
                                <option value="">请选择仓库</option>
                                <!-- 仓库选项将动态加载 -->
                            </select>
                        </div>
                    </div>

                    <div>
                        <label for="outboundRemark" class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                        <textarea id="outboundRemark" rows="3"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent text-sm"></textarea>
                    </div>

                    <!-- 出库明细 -->
                    <div>
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-800">出库明细</h3>
                            <button type="button" id="addOutboundItem" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                                添加商品
                            </button>
                        </div>

                        <div class="overflow-x-auto">
                            <table class="min-w-full border border-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">商品</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">库存</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">出库数量</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">单价</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">金额</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">批次号</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="outboundItemsTable">
                                    <!-- 出库明细将在这里动态添加 -->
                                </tbody>
                            </table>
                        </div>

                        <div class="mt-4 text-right">
                            <span class="text-lg font-semibold">总金额: ¥<span id="totalOutboundAmount">0.00</span></span>
                        </div>
                    </div>

                    <div class="flex gap-3 pt-4">
                        <button type="button" id="cancelOutboundOrderEdit" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                            取消
                        </button>
                        <button type="button" id="saveOutboundOrderDraft" class="flex-1 bg-yellow-500 hover:bg-yellow-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                            保存草稿
                        </button>
                        <button type="submit" class="flex-1 bg-red-500 hover:bg-red-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                            确认出库
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 引入模块化JS文件 -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/base-app.js"></script>
    <script>
        // 初始化出库管理应用
        let inventoryOutApp;

        // 统一的初始化函数
        function initInventoryOutApp() {
            if (typeof AV !== 'undefined') {
                try {
                    inventoryOutApp = new InventoryOutApp();
                    inventoryOutApp.init();
                    console.log('出库管理应用初始化成功');
                } catch (error) {
                    console.error('出库管理应用初始化失败:', error);
                    alert('应用初始化失败: ' + error.message);
                }
            } else {
                setTimeout(initInventoryOutApp, 100);
            }
        }

        // 出库管理应用类
        class InventoryOutApp {
            constructor() {
                this.currentPage = 1;
                this.pageSize = 20;
                this.totalCount = 0;
                this.elements = {};
            }

            /**
             * 初始化应用
             */
            init() {
                // 初始化DOM元素
                this.initElements();

                // 绑定事件
                this.bindPageEvents();

                // 完成初始化
                this.onInitComplete();
            }

            /**
             * 初始化DOM元素
             */
            initElements() {
                const pageElements = this.getPageElements();
                for (const [key, id] of Object.entries(pageElements)) {
                    this.elements[key] = document.getElementById(id);
                }
            }

            /**
             * 获取页面特定的DOM元素
             */
            getPageElements() {
                return {
                    // 主要区域
                    loginPrompt: 'loginPrompt',
                    inventoryOutSection: 'inventoryOutSection',

                    // 统计元素
                    todayOutboundCount: 'todayOutboundCount',
                    pendingOutboundCount: 'pendingOutboundCount',
                    monthOutboundCount: 'monthOutboundCount',
                    totalOutboundAmount: 'totalOutboundAmount',

                    // 操作元素
                    searchInput: 'searchInput',
                    statusFilter: 'statusFilter',
                    dateFilter: 'dateFilter',
                    refreshBtn: 'refreshBtn',
                    createOutboundBtn: 'createOutboundBtn',

                    // 列表和分页
                    outboundTableBody: 'outboundTableBody',
                    pageInfo: 'pageInfo',
                    totalCount: 'totalCount',
                    prevPageBtn: 'prevPageBtn',
                    nextPageBtn: 'nextPageBtn',

                    // 登录相关
                    promptLoginBtn: 'promptLoginBtn',

                    // 用户信息
                    realName: 'realName',
                    userInfo: 'userInfo',
                    loginBtn: 'loginBtn',
                    loadingIndicator: 'loadingIndicator'
                };
            }

            /**
             * 绑定页面特定事件
             */
            bindPageEvents() {
                // 登录提示按钮
                if (this.elements.promptLoginBtn) {
                    this.elements.promptLoginBtn.addEventListener('click', () => {
                        this.showLoginModal();
                    });
                }

                // 刷新按钮
                if (this.elements.refreshBtn) {
                    this.elements.refreshBtn.addEventListener('click', () => {
                        this.loadInboundOrders();
                    });
                }

                // 新建出库单按钮
                if (this.elements.createOutboundBtn) {
                    this.elements.createOutboundBtn.addEventListener('click', () => {
                        this.createOutboundOrder();
                    });
                }

                // 搜索和筛选
                if (this.elements.searchInput) {
                    this.elements.searchInput.addEventListener('input', this.debounce(() => {
                        this.currentPage = 1;
                        this.loadInboundOrders();
                    }, 500));
                }

                if (this.elements.statusFilter) {
                    this.elements.statusFilter.addEventListener('change', () => {
                        this.currentPage = 1;
                        this.loadInboundOrders();
                    });
                }

                if (this.elements.dateFilter) {
                    this.elements.dateFilter.addEventListener('change', () => {
                        this.currentPage = 1;
                        this.loadInboundOrders();
                    });
                }

                // 分页按钮
                if (this.elements.prevPageBtn) {
                    this.elements.prevPageBtn.addEventListener('click', () => {
                        if (this.currentPage > 1) {
                            this.currentPage--;
                            this.loadOutboundOrders();
                        }
                    });
                }

                if (this.elements.nextPageBtn) {
                    this.elements.nextPageBtn.addEventListener('click', () => {
                        const maxPage = Math.ceil(this.totalCount / this.pageSize);
                        if (this.currentPage < maxPage) {
                            this.currentPage++;
                            this.loadOutboundOrders();
                        }
                    });
                }
            }

            /**
             * 页面特定的初始化后处理
             */
            onInitComplete() {
                // 检查登录状态
                this.checkLoginStatus();
            }

            /**
             * 检查登录状态
             */
            checkLoginStatus() {
                const currentUser = AV.User.current();

                // 直接获取DOM元素，避免依赖this.elements
                const loginPrompt = document.getElementById('loginPrompt');
                const inventoryOutSection = document.getElementById('inventoryOutSection');
                const realName = document.getElementById('realName');
                const userInfo = document.getElementById('userInfo');
                const loginBtn = document.getElementById('loginBtn');

                if (currentUser) {
                    // 用户已登录
                    if (loginPrompt) loginPrompt.style.display = 'none';
                    if (inventoryOutSection) inventoryOutSection.style.display = 'block';

                    // 更新用户信息显示
                    if (realName) realName.textContent = currentUser.get('realName') || currentUser.get('username');
                    if (userInfo) userInfo.style.display = 'flex';
                    if (loginBtn) loginBtn.style.display = 'none';

                    // 加载数据
                    this.loadStatistics();
                    this.loadOutboundOrders();
                } else {
                    // 用户未登录
                    if (loginPrompt) loginPrompt.style.display = 'block';
                    if (inventoryOutSection) inventoryOutSection.style.display = 'none';
                    if (userInfo) userInfo.style.display = 'none';
                    if (loginBtn) loginBtn.style.display = 'block';
                }
            }

            /**
             * 加载统计数据
             */
            async loadStatistics() {
                try {
                    const today = new Date();
                    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
                    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

                    // 查询今日出库单数量
                    const todayQuery = new AV.Query('OutboundOrder');
                    todayQuery.greaterThanOrEqualTo('createdAt', startOfDay);
                    const todayCount = await todayQuery.count();

                    // 查询待确认出库单数量
                    const pendingQuery = new AV.Query('OutboundOrder');
                    pendingQuery.equalTo('status', 'draft');
                    const pendingCount = await pendingQuery.count();

                    // 查询本月出库单数量
                    const monthQuery = new AV.Query('OutboundOrder');
                    monthQuery.greaterThanOrEqualTo('createdAt', startOfMonth);
                    const monthCount = await monthQuery.count();

                    // 查询总出库金额（本月）
                    const amountQuery = new AV.Query('OutboundOrder');
                    amountQuery.greaterThanOrEqualTo('createdAt', startOfMonth);
                    amountQuery.equalTo('status', 'completed');
                    const orders = await amountQuery.find();
                    const totalAmount = orders.reduce((sum, order) => sum + (order.get('totalAmount') || 0), 0);

                    // 更新显示
                    if (this.elements.todayOutboundCount) {
                        this.elements.todayOutboundCount.textContent = todayCount;
                    }
                    if (this.elements.pendingOutboundCount) {
                        this.elements.pendingOutboundCount.textContent = pendingCount;
                    }
                    if (this.elements.monthOutboundCount) {
                        this.elements.monthOutboundCount.textContent = monthCount;
                    }
                    if (this.elements.totalOutboundAmount) {
                        this.elements.totalOutboundAmount.textContent = '¥' + totalAmount.toFixed(2);
                    }
                } catch (error) {
                    console.error('加载统计数据失败:', error);
                }
            }

            /**
             * 加载出库单列表
             */
            async loadOutboundOrders() {
                try {
                    this.showLoading();

                    const query = new AV.Query('OutboundOrder');

                    // 应用搜索条件
                    const searchText = this.elements.searchInput?.value?.trim();
                    if (searchText) {
                        const orderNoQuery = new AV.Query('OutboundOrder');
                        orderNoQuery.contains('orderNo', searchText);

                        const deptQuery = new AV.Query('OutboundOrder');
                        deptQuery.contains('department', searchText);

                        const recipientQuery = new AV.Query('OutboundOrder');
                        recipientQuery.contains('recipient', searchText);

                        query._orQuery([orderNoQuery, deptQuery, recipientQuery]);
                    }

                    // 应用状态筛选
                    const statusFilter = this.elements.statusFilter?.value;
                    if (statusFilter) {
                        query.equalTo('status', statusFilter);
                    }

                    // 应用日期筛选
                    const dateFilter = this.elements.dateFilter?.value;
                    if (dateFilter) {
                        const filterDate = new Date(dateFilter);
                        const nextDay = new Date(filterDate);
                        nextDay.setDate(nextDay.getDate() + 1);
                        query.greaterThanOrEqualTo('createdAt', filterDate);
                        query.lessThan('createdAt', nextDay);
                    }

                    // 包含关联数据
                    query.include('warehouseId');
                    query.include('createdBy');

                    // 排序和分页
                    query.descending('createdAt');
                    query.limit(this.pageSize);
                    query.skip((this.currentPage - 1) * this.pageSize);

                    const results = await query.find();
                    const total = await query.count();

                    this.totalCount = total;
                    this.renderOutboundOrderList(results);
                    this.updatePagination();

                    this.hideLoading();
                } catch (error) {
                    console.error('加载出库单列表失败:', error);
                    this.hideLoading();
                    alert('加载出库单列表失败: ' + error.message);
                }
            }

            /**
             * 渲染出库单列表
             */
            renderOutboundOrderList(orders) {
                if (!this.elements.outboundTableBody) return;

                if (orders.length === 0) {
                    this.elements.outboundTableBody.innerHTML = `
                        <tr>
                            <td colspan="8" class="px-6 py-8 text-center text-gray-500">
                                暂无出库单数据
                            </td>
                        </tr>
                    `;
                    return;
                }

                const html = orders.map(order => {
                    const warehouse = order.get('warehouseId');
                    const creator = order.get('createdBy');
                    const status = order.get('status');
                    const statusText = this.getStatusText(status);
                    const statusClass = this.getStatusClass(status);
                    const department = order.get('department') || '-';
                    const recipient = order.get('recipient') || '-';

                    return `
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                ${order.get('orderNo')}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${department}<br><small class="text-gray-400">${recipient}</small>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${warehouse ? warehouse.get('name') : '-'}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${this.getTypeText(order.get('type'))}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ¥${(order.get('totalAmount') || 0).toFixed(2)}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium rounded-full ${statusClass}">
                                    ${statusText}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${order.createdAt.toLocaleDateString()}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="inventoryOutApp.viewOutboundOrder('${order.id}')"
                                            class="text-blue-600 hover:text-blue-900">查看</button>
                                    ${status === 'draft' ? `
                                        <button onclick="inventoryOutApp.editOutboundOrder('${order.id}')"
                                                class="text-green-600 hover:text-green-900">编辑</button>
                                        <button onclick="inventoryOutApp.deleteOutboundOrder('${order.id}')"
                                                class="text-red-600 hover:text-red-900">删除</button>
                                    ` : ''}
                                </div>
                            </td>
                        </tr>
                    `;
                }).join('');

                this.elements.outboundTableBody.innerHTML = html;
            }

            /**
             * 获取状态文本
             */
            getStatusText(status) {
                const statusMap = {
                    'draft': '草稿',
                    'confirmed': '已确认',
                    'completed': '已完成',
                    'cancelled': '已取消'
                };
                return statusMap[status] || status;
            }

            /**
             * 获取状态样式类
             */
            getStatusClass(status) {
                const classMap = {
                    'draft': 'status-draft',
                    'confirmed': 'status-confirmed',
                    'completed': 'status-completed',
                    'cancelled': 'status-cancelled'
                };
                return classMap[status] || 'status-draft';
            }

            /**
             * 获取类型文本
             */
            getTypeText(type) {
                const typeMap = {
                    'sale': '销售出库',
                    'use': '领用出库',
                    'transfer': '调拨出库',
                    'other': '其他出库'
                };
                return typeMap[type] || type;
            }

            /**
             * 更新分页信息
             */
            updatePagination() {
                const start = (this.currentPage - 1) * this.pageSize + 1;
                const end = Math.min(this.currentPage * this.pageSize, this.totalCount);

                if (this.elements.pageInfo) {
                    this.elements.pageInfo.textContent = `${start}-${end}`;
                }
                if (this.elements.totalCount) {
                    this.elements.totalCount.textContent = this.totalCount;
                }

                // 更新分页按钮状态
                if (this.elements.prevPageBtn) {
                    this.elements.prevPageBtn.disabled = this.currentPage <= 1;
                }
                if (this.elements.nextPageBtn) {
                    const maxPage = Math.ceil(this.totalCount / this.pageSize);
                    this.elements.nextPageBtn.disabled = this.currentPage >= maxPage;
                }
            }

            /**
             * 创建新出库单
             */
            createOutboundOrder() {
                this.showOutboundOrderModal();
            }

            /**
             * 防抖函数
             */
            debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }

            /**
             * 显示加载状态
             */
            showLoading() {
                this.elements.loadingIndicator.style.display = 'flex';
            }

            /**
             * 隐藏加载状态
             */
            hideLoading() {
                this.elements.loadingIndicator.style.display = 'none';
            }

            /**
             * 显示登录弹窗
             */
            showLoginModal() {
                document.getElementById('loginModal').style.display = 'flex';
            }

            /**
             * 显示出库单弹窗
             */
            async showOutboundOrderModal(orderId = null) {
                const modal = document.getElementById('outboundOrderModal');
                const title = document.getElementById('outboundOrderModalTitle');
                const form = document.getElementById('outboundOrderForm');

                // 重置表单状态
                form.reset();
                const inputs = form.querySelectorAll('input, select, textarea, button');
                inputs.forEach(input => {
                    input.disabled = false;
                });
                document.getElementById('saveOutboundOrderDraft').style.display = 'inline-block';
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn) submitBtn.style.display = 'inline-block';

                if (orderId) {
                    title.textContent = '查看出库单';
                    await this.loadOutboundOrderForEdit(orderId);
                } else {
                    title.textContent = '新建出库单';
                    document.getElementById('editOutboundOrderId').value = '';
                    this.generateOutboundOrderNo();
                    this.clearOutboundItems();
                }

                await this.loadWarehouses();
                modal.style.display = 'flex';
            }

            /**
             * 生成出库单号
             */
            generateOutboundOrderNo() {
                const now = new Date();
                const dateStr = now.getFullYear().toString() +
                              (now.getMonth() + 1).toString().padStart(2, '0') +
                              now.getDate().toString().padStart(2, '0');
                const timeStr = now.getHours().toString().padStart(2, '0') +
                               now.getMinutes().toString().padStart(2, '0') +
                               now.getSeconds().toString().padStart(2, '0');
                const orderNo = `OUT${dateStr}${timeStr}`;
                document.getElementById('outboundOrderNo').value = orderNo;
            }

            /**
             * 加载仓库列表
             */
            async loadWarehouses() {
                try {
                    const query = new AV.Query('Warehouse');
                    query.equalTo('status', 'active');
                    query.ascending('name');
                    const warehouses = await query.find();

                    const select = document.getElementById('outboundWarehouse');
                    select.innerHTML = '<option value="">请选择仓库</option>';

                    warehouses.forEach(warehouse => {
                        const option = document.createElement('option');
                        option.value = warehouse.id;
                        option.textContent = warehouse.get('name');
                        select.appendChild(option);
                    });
                } catch (error) {
                    console.error('加载仓库失败:', error);
                }
            }

            /**
             * 清空出库明细
             */
            clearOutboundItems() {
                const tbody = document.getElementById('outboundItemsTable');
                tbody.innerHTML = '';
                this.updateTotalOutboundAmount();
            }

            /**
             * 更新总金额
             */
            updateTotalOutboundAmount() {
                const rows = document.querySelectorAll('#outboundItemsTable tr');
                let total = 0;

                rows.forEach(row => {
                    const amountCell = row.querySelector('.item-amount');
                    if (amountCell) {
                        total += parseFloat(amountCell.textContent) || 0;
                    }
                });

                document.getElementById('totalOutboundAmount').textContent = total.toFixed(2);
            }

            /**
             * 加载出库单数据用于编辑
             */
            async loadOutboundOrderForEdit(orderId) {
                try {
                    const query = new AV.Query('OutboundOrder');
                    query.include('warehouseId');
                    const order = await query.get(orderId);

                    // 填充基本信息
                    document.getElementById('editOutboundOrderId').value = orderId;
                    document.getElementById('outboundOrderNo').value = order.get('orderNo');
                    document.getElementById('outboundType').value = order.get('type') || '';
                    document.getElementById('outboundDepartment').value = order.get('department') || '';
                    document.getElementById('outboundRecipient').value = order.get('recipient') || '';
                    document.getElementById('outboundRemark').value = order.get('remark') || '';

                    // 设置仓库
                    const warehouse = order.get('warehouseId');
                    if (warehouse) {
                        document.getElementById('outboundWarehouse').value = warehouse.id;
                    }

                    // 加载出库明细
                    await this.loadOutboundOrderItems(orderId);

                } catch (error) {
                    console.error('加载出库单数据失败:', error);
                    alert('加载出库单数据失败: ' + error.message);
                }
            }

            /**
             * 加载出库单明细
             */
            async loadOutboundOrderItems(orderId) {
                try {
                    const query = new AV.Query('OutboundOrderItem');
                    query.equalTo('orderId', AV.Object.createWithoutData('OutboundOrder', orderId));
                    query.include('productId');
                    const items = await query.find();

                    const tbody = document.getElementById('outboundItemsTable');
                    tbody.innerHTML = '';

                    items.forEach(item => {
                        this.addOutboundItemRow(item);
                    });

                    this.updateTotalOutboundAmount();
                } catch (error) {
                    console.error('加载出库明细失败:', error);
                    // 如果是表不存在的错误，清空明细表格
                    if (error.message.includes('Class or object doesn\'t exists')) {
                        console.log('OutboundOrderItem 表不存在，将在保存时自动创建');
                        const tbody = document.getElementById('outboundItemsTable');
                        tbody.innerHTML = '';
                        this.updateTotalOutboundAmount();
                    }
                }
            }

            /**
             * 查看出库单
             */
            async viewOutboundOrder(orderId) {
                await this.showOutboundOrderModal(orderId);
                // 设置为只读模式
                setTimeout(() => {
                    const form = document.getElementById('outboundOrderForm');
                    const inputs = form.querySelectorAll('input, select, textarea, button');
                    inputs.forEach(input => {
                        if (input.type !== 'button' && input.id !== 'closeOutboundOrderModal' && input.id !== 'cancelOutboundOrderEdit') {
                            input.disabled = true;
                        }
                    });

                    // 隐藏操作按钮
                    document.getElementById('saveOutboundOrderDraft').style.display = 'none';
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) submitBtn.style.display = 'none';
                }, 100);
            }

            /**
             * 编辑出库单
             */
            editOutboundOrder(orderId) {
                this.showOutboundOrderModal(orderId);
            }

            /**
             * 删除出库单
             */
            async deleteOutboundOrder(orderId) {
                if (!confirm('确定要删除这个出库单吗？')) {
                    return;
                }

                try {
                    const order = AV.Object.createWithoutData('OutboundOrder', orderId);
                    await order.destroy();
                    alert('删除成功');
                    this.loadOutboundOrders();
                    this.loadStatistics();
                } catch (error) {
                    console.error('删除失败:', error);
                    alert('删除失败: ' + error.message);
                }
            }
            /**
             * 添加出库明细行
             */
            addOutboundItemRow(item = null) {
                const tbody = document.getElementById('outboundItemsTable');
                const row = document.createElement('tr');
                row.className = 'border-b border-gray-200';

                const product = item ? item.get('productId') : null;
                const quantity = item ? item.get('quantity') : 0;
                const unitPrice = item ? item.get('unitPrice') : 0;
                const amount = quantity * unitPrice;
                const batchNo = item ? item.get('batchNo') : '';

                row.innerHTML = `
                    <td class="px-4 py-2">
                        <select class="item-product w-full p-2 border rounded text-sm" required>
                            <option value="">选择商品</option>
                            ${product ? `<option value="${product.id}" selected>${product.get('name')} (${product.get('code')})</option>` : ''}
                        </select>
                    </td>
                    <td class="px-4 py-2">
                        <span class="item-stock text-sm font-medium text-green-600">-</span>
                    </td>
                    <td class="px-4 py-2">
                        <input type="number" class="item-quantity w-full p-2 border rounded text-sm"
                               value="${quantity}" min="0" step="0.01" required>
                    </td>
                    <td class="px-4 py-2">
                        <input type="number" class="item-price w-full p-2 border rounded text-sm"
                               value="${unitPrice}" min="0" step="0.01" required>
                    </td>
                    <td class="px-4 py-2">
                        <span class="item-amount font-medium">${amount.toFixed(2)}</span>
                    </td>
                    <td class="px-4 py-2">
                        <input type="text" class="item-batch w-full p-2 border rounded text-sm"
                               value="${batchNo}" placeholder="批次号">
                    </td>
                    <td class="px-4 py-2">
                        <button type="button" class="remove-item bg-red-500 hover:bg-red-600 text-white px-2 py-1 rounded text-xs">
                            删除
                        </button>
                    </td>
                `;

                tbody.appendChild(row);

                // 绑定事件
                const productSelect = row.querySelector('.item-product');
                const quantityInput = row.querySelector('.item-quantity');
                const priceInput = row.querySelector('.item-price');
                const removeBtn = row.querySelector('.remove-item');
                const stockSpan = row.querySelector('.item-stock');

                const updateAmount = () => {
                    const qty = parseFloat(quantityInput.value) || 0;
                    const price = parseFloat(priceInput.value) || 0;
                    const amountSpan = row.querySelector('.item-amount');
                    amountSpan.textContent = (qty * price).toFixed(2);
                    this.updateTotalOutboundAmount();

                    // 检查库存
                    this.checkStock(row);
                };

                // 商品选择变化时更新库存信息
                productSelect.addEventListener('change', async () => {
                    await this.updateProductStock(row);
                    updateAmount();
                });

                quantityInput.addEventListener('input', updateAmount);
                priceInput.addEventListener('input', updateAmount);

                removeBtn.addEventListener('click', () => {
                    row.remove();
                    this.updateTotalOutboundAmount();
                });

                // 如果没有商品数据，加载商品选项
                if (!product) {
                    this.loadProductOptions(productSelect);
                } else {
                    this.updateProductStock(row);
                }
            }

            /**
             * 更新商品库存信息
             */
            async updateProductStock(row) {
                const productSelect = row.querySelector('.item-product');
                const stockSpan = row.querySelector('.item-stock');
                const warehouseSelect = document.getElementById('outboundWarehouse');

                const productId = productSelect.value;
                const warehouseId = warehouseSelect.value;

                if (!productId || !warehouseId) {
                    stockSpan.textContent = '-';
                    return;
                }

                try {
                    // 查询库存
                    const query = new AV.Query('Inventory');
                    query.equalTo('productId', AV.Object.createWithoutData('Product', productId));
                    query.equalTo('warehouseId', AV.Object.createWithoutData('Warehouse', warehouseId));
                    const inventory = await query.first();

                    if (inventory) {
                        const availableQty = inventory.get('availableQuantity') || 0;
                        stockSpan.textContent = availableQty.toString();
                        stockSpan.className = availableQty > 0 ? 'item-stock text-sm font-medium text-green-600' : 'item-stock text-sm font-medium text-red-600';
                    } else {
                        stockSpan.textContent = '0';
                        stockSpan.className = 'item-stock text-sm font-medium text-red-600';
                    }
                } catch (error) {
                    console.error('查询库存失败:', error);
                    stockSpan.textContent = '-';
                }
            }

            /**
             * 检查库存是否充足
             */
            checkStock(row) {
                const quantityInput = row.querySelector('.item-quantity');
                const stockSpan = row.querySelector('.item-stock');

                const requestQty = parseFloat(quantityInput.value) || 0;
                const stockQty = parseFloat(stockSpan.textContent) || 0;

                if (requestQty > stockQty) {
                    quantityInput.style.borderColor = '#ef4444';
                    quantityInput.title = `库存不足，当前库存：${stockQty}`;
                } else {
                    quantityInput.style.borderColor = '#d1d5db';
                    quantityInput.title = '';
                }
            }

            /**
             * 加载商品选项
             */
            async loadProductOptions(selectElement) {
                try {
                    const query = new AV.Query('Product');
                    query.equalTo('status', 'active');
                    query.include('categoryId');
                    query.ascending('name');
                    const products = await query.find();

                    products.forEach(product => {
                        const option = document.createElement('option');
                        option.value = product.id;
                        option.textContent = `${product.get('name')} (${product.get('code')})`;
                        selectElement.appendChild(option);
                    });
                } catch (error) {
                    console.error('加载商品选项失败:', error);
                }
            }

            /**
             * 保存出库单
             */
            async saveOutboundOrder(status = 'draft') {
                try {
                    // 获取表单数据
                    const orderNo = document.getElementById('outboundOrderNo').value;
                    const type = document.getElementById('outboundType').value;
                    const department = document.getElementById('outboundDepartment').value;
                    const recipient = document.getElementById('outboundRecipient').value;
                    const warehouseId = document.getElementById('outboundWarehouse').value;
                    const remark = document.getElementById('outboundRemark').value;
                    const editId = document.getElementById('editOutboundOrderId').value;

                    // 验证必填字段
                    if (!orderNo || !type || !warehouseId) {
                        alert('请填写必填字段');
                        return;
                    }

                    // 获取出库明细
                    const items = this.getOutboundItems();
                    if (items.length === 0) {
                        alert('请至少添加一个商品');
                        return;
                    }

                    // 检查库存
                    const stockCheck = await this.validateStock(items, warehouseId);
                    if (!stockCheck.valid) {
                        alert(`库存不足：${stockCheck.message}`);
                        return;
                    }

                    // 计算总金额
                    const totalAmount = items.reduce((sum, item) => sum + item.amount, 0);

                    // 保存出库单
                    let order;
                    if (editId) {
                        order = AV.Object.createWithoutData('OutboundOrder', editId);
                    } else {
                        order = new AV.Object('OutboundOrder');
                        order.set('orderNo', orderNo);
                        order.set('createdBy', AV.User.current());
                    }

                    order.set('type', type);
                    order.set('status', status);
                    order.set('department', department);
                    order.set('recipient', recipient);
                    order.set('totalAmount', totalAmount);
                    order.set('remark', remark);
                    order.set('warehouseId', AV.Object.createWithoutData('Warehouse', warehouseId));

                    await order.save();

                    // 保存出库明细
                    if (editId) {
                        // 删除原有明细
                        const itemQuery = new AV.Query('OutboundOrderItem');
                        itemQuery.equalTo('orderId', order);
                        const oldItems = await itemQuery.find();
                        await AV.Object.destroyAll(oldItems);
                    }

                    // 创建新明细
                    for (const itemData of items) {
                        const item = new AV.Object('OutboundOrderItem');
                        item.set('orderId', order);
                        item.set('productId', AV.Object.createWithoutData('Product', itemData.productId));
                        item.set('quantity', itemData.quantity);
                        item.set('unitPrice', itemData.unitPrice);
                        item.set('amount', itemData.amount);
                        item.set('batchNo', itemData.batchNo);
                        await item.save();
                    }

                    alert(status === 'draft' ? '草稿保存成功' : '出库单确认成功');
                    document.getElementById('outboundOrderModal').style.display = 'none';
                    this.loadOutboundOrders();
                    this.loadStatistics();

                } catch (error) {
                    console.error('保存出库单失败:', error);
                    alert('保存失败: ' + error.message);
                }
            }

            /**
             * 获取出库明细数据
             */
            getOutboundItems() {
                const rows = document.querySelectorAll('#outboundItemsTable tr');
                const items = [];

                rows.forEach(row => {
                    const productSelect = row.querySelector('.item-product');
                    const quantityInput = row.querySelector('.item-quantity');
                    const priceInput = row.querySelector('.item-price');
                    const batchInput = row.querySelector('.item-batch');

                    if (productSelect && quantityInput && priceInput) {
                        const productId = productSelect.value;
                        const quantity = parseFloat(quantityInput.value) || 0;
                        const unitPrice = parseFloat(priceInput.value) || 0;
                        const batchNo = batchInput ? batchInput.value : '';

                        if (productId && quantity > 0) {
                            items.push({
                                productId,
                                quantity,
                                unitPrice,
                                amount: quantity * unitPrice,
                                batchNo
                            });
                        }
                    }
                });

                return items;
            }

            /**
             * 验证库存是否充足
             */
            async validateStock(items, warehouseId) {
                try {
                    for (const item of items) {
                        const query = new AV.Query('Inventory');
                        query.equalTo('productId', AV.Object.createWithoutData('Product', item.productId));
                        query.equalTo('warehouseId', AV.Object.createWithoutData('Warehouse', warehouseId));
                        const inventory = await query.first();

                        const availableQty = inventory ? inventory.get('availableQuantity') || 0 : 0;
                        if (item.quantity > availableQty) {
                            // 获取商品名称
                            const productQuery = new AV.Query('Product');
                            const product = await productQuery.get(item.productId);
                            return {
                                valid: false,
                                message: `${product.get('name')} 库存不足，需要 ${item.quantity}，可用 ${availableQty}`
                            };
                        }
                    }
                    return { valid: true };
                } catch (error) {
                    console.error('验证库存失败:', error);
                    return { valid: false, message: '验证库存时发生错误' };
                }
            }
        }

        // 页面加载完成后的统一初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化应用
            initInventoryOutApp();

            // 绑定出库单弹窗事件
            document.getElementById('closeOutboundOrderModal').addEventListener('click', function() {
                document.getElementById('outboundOrderModal').style.display = 'none';
            });

            document.getElementById('cancelOutboundOrderEdit').addEventListener('click', function() {
                document.getElementById('outboundOrderModal').style.display = 'none';
            });

            document.getElementById('addOutboundItem').addEventListener('click', function() {
                if (inventoryOutApp) {
                    inventoryOutApp.addOutboundItemRow();
                }
            });

            // 绑定出库单表单提交
            document.getElementById('outboundOrderForm').addEventListener('submit', async function(e) {
                e.preventDefault();
                if (inventoryOutApp) {
                    await inventoryOutApp.saveOutboundOrder('confirmed');
                }
            });

            document.getElementById('saveOutboundOrderDraft').addEventListener('click', async function() {
                if (inventoryOutApp) {
                    await inventoryOutApp.saveOutboundOrder('draft');
                }
            });

            // 绑定登录相关事件
            // 绑定登录按钮
            document.getElementById('loginBtn').addEventListener('click', function() {
                document.getElementById('loginModal').style.display = 'flex';
            });

            document.getElementById('promptLoginBtn').addEventListener('click', function() {
                document.getElementById('loginModal').style.display = 'flex';
            });

            // 绑定登录弹窗关闭按钮
            document.getElementById('closeLoginModal').addEventListener('click', function() {
                document.getElementById('loginModal').style.display = 'none';
            });

            // 绑定表单切换
            document.getElementById('showRegisterBtn').addEventListener('click', function(e) {
                e.preventDefault();
                document.getElementById('loginForm').style.display = 'none';
                document.getElementById('registerForm').style.display = 'block';
                document.getElementById('loginSwitchText').style.display = 'none';
                document.getElementById('registerSwitchText').style.display = 'block';
            });

            document.getElementById('showLoginBtn').addEventListener('click', function(e) {
                e.preventDefault();
                document.getElementById('loginForm').style.display = 'block';
                document.getElementById('registerForm').style.display = 'none';
                document.getElementById('loginSwitchText').style.display = 'block';
                document.getElementById('registerSwitchText').style.display = 'none';
            });

            // 登录功能
            document.getElementById('loginForm').addEventListener('submit', async function(e) {
                e.preventDefault();

                const username = document.getElementById('loginUsername').value;
                const password = document.getElementById('loginPassword').value;

                if (!username || !password) {
                    alert('请输入用户名和密码');
                    return;
                }

                try {
                    const user = await AV.User.logIn(username, password);
                    console.log('登录成功:', user);

                    // 关闭登录弹窗
                    document.getElementById('loginModal').style.display = 'none';

                    // 重新检查登录状态
                    if (inventoryOutApp) {
                        inventoryOutApp.checkLoginStatus();
                    }

                    alert('登录成功！');
                } catch (error) {
                    console.error('登录失败:', error);
                    alert('登录失败: ' + error.message);
                }
            });

            // 注册功能
            document.getElementById('registerForm').addEventListener('submit', async function(e) {
                e.preventDefault();

                const username = document.getElementById('registerUsername').value;
                const realName = document.getElementById('registerRealName').value;
                const phone = document.getElementById('registerPhone').value;
                const department = document.getElementById('registerDepartment').value;
                const password = document.getElementById('registerPassword').value;
                const confirmPassword = document.getElementById('confirmPassword').value;

                if (!username || !realName || !phone || !department || !password) {
                    alert('请填写所有必填字段');
                    return;
                }

                if (password !== confirmPassword) {
                    alert('两次输入的密码不一致');
                    return;
                }

                if (password.length < 6) {
                    alert('密码长度至少6位');
                    return;
                }

                try {
                    const user = new AV.User();
                    user.setUsername(username);
                    user.setPassword(password);
                    user.set('realName', realName);
                    user.set('phone', phone);
                    user.set('department', department);
                    user.set('roles', []); // 默认无特殊角色

                    await user.signUp();
                    console.log('注册成功:', user);

                    // 关闭登录弹窗
                    document.getElementById('loginModal').style.display = 'none';

                    // 重新检查登录状态
                    if (inventoryOutApp) {
                        inventoryOutApp.checkLoginStatus();
                    }

                    alert('注册成功！');
                } catch (error) {
                    console.error('注册失败:', error);
                    alert('注册失败: ' + error.message);
                }
            });

            // 退出登录功能
            document.getElementById('logoutBtn').addEventListener('click', async function() {
                try {
                    await AV.User.logOut();
                    console.log('退出登录成功');

                    // 重新检查登录状态
                    if (inventoryOutApp) {
                        inventoryOutApp.checkLoginStatus();
                    }

                    alert('已退出登录');
                } catch (error) {
                    console.error('退出登录失败:', error);
                    alert('退出登录失败: ' + error.message);
                }
            });
        });
    </script>
</body>
</html>
