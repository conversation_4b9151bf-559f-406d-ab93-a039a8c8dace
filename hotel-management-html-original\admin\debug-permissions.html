<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限调试 - 系统管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
</head>
<body class="bg-gray-50 min-h-screen p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">权限系统调试</h1>
        
        <!-- 用户信息 -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-lg font-medium mb-4">当前用户信息</h2>
            <div id="userInfo" class="space-y-2">
                <!-- 用户信息将在这里显示 -->
            </div>
        </div>

        <!-- 权限检查结果 -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-lg font-medium mb-4">权限检查结果</h2>
            <div id="permissionResults" class="space-y-2">
                <!-- 权限检查结果将在这里显示 -->
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-medium mb-4">测试操作</h2>
            <div class="space-x-4">
                <button id="refreshBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                    刷新权限
                </button>
                <button id="testPermissionBtn" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">
                    测试权限检查
                </button>
                <a href="index.html" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded inline-block">
                    返回管理首页
                </a>
            </div>
        </div>
    </div>

    <!-- 引入模块化JS文件 -->
    <script src="../js/config.js"></script>
    <script src="../js/permission-manager.js"></script>
    <script>
        class PermissionDebugApp {
            constructor() {
                this.userInfoContainer = document.getElementById('userInfo');
                this.permissionResultsContainer = document.getElementById('permissionResults');
            }

            init() {
                this.bindEvents();
                this.loadUserInfo();
                this.testPermissions();
            }

            bindEvents() {
                document.getElementById('refreshBtn').addEventListener('click', () => {
                    this.refreshPermissions();
                });

                document.getElementById('testPermissionBtn').addEventListener('click', () => {
                    this.testPermissions();
                });
            }

            loadUserInfo() {
                const currentUser = AV.User.current();
                
                if (currentUser) {
                    const roles = currentUser.get('roles');
                    const userInfo = `
                        <div><strong>用户名:</strong> ${currentUser.get('username')}</div>
                        <div><strong>真实姓名:</strong> ${currentUser.get('realName') || '未设置'}</div>
                        <div><strong>角色 (原始):</strong> ${JSON.stringify(roles)}</div>
                        <div><strong>角色类型:</strong> ${typeof roles}</div>
                        <div><strong>是否数组:</strong> ${Array.isArray(roles)}</div>
                        <div><strong>部门:</strong> ${currentUser.get('department') || '未设置'}</div>
                        <div><strong>用户ID:</strong> ${currentUser.id}</div>
                    `;
                    this.userInfoContainer.innerHTML = userInfo;
                } else {
                    this.userInfoContainer.innerHTML = '<div class="text-red-500">用户未登录</div>';
                }
            }

            async refreshPermissions() {
                if (window.permissionManager) {
                    await window.permissionManager.refreshPermissions();
                    this.testPermissions();
                } else {
                    alert('权限管理器未初始化');
                }
            }

            async testPermissions() {
                const currentUser = AV.User.current();
                if (!currentUser) {
                    this.permissionResultsContainer.innerHTML = '<div class="text-red-500">用户未登录，无法测试权限</div>';
                    return;
                }

                let results = [];

                // 测试基本角色检查
                const roles = currentUser.get('roles') || [];
                let basicAdminCheck = false;
                
                if (typeof roles === 'string') {
                    try {
                        const roleArray = JSON.parse(roles);
                        basicAdminCheck = roleArray.includes('admin') || roleArray.includes('super_admin');
                    } catch (e) {
                        basicAdminCheck = roles === 'admin' || roles === 'super_admin';
                    }
                } else if (Array.isArray(roles)) {
                    basicAdminCheck = roles.includes('admin') || roles.includes('super_admin');
                }

                results.push(`<div><strong>基本管理员检查:</strong> <span class="${basicAdminCheck ? 'text-green-600' : 'text-red-600'}">${basicAdminCheck}</span></div>`);

                // 测试权限管理器
                if (window.permissionManager) {
                    try {
                        await window.permissionManager.init();
                        
                        const isAdmin = window.permissionManager.isAdmin();
                        const isSuperAdmin = window.permissionManager.isSuperAdmin();
                        const userRoleCodes = window.permissionManager.getUserRoleCodes();
                        const userRoles = window.permissionManager.getUserRoles();
                        const permissions = window.permissionManager.getUserPermissions();

                        results.push(`<div><strong>权限管理器 - 是管理员:</strong> <span class="${isAdmin ? 'text-green-600' : 'text-red-600'}">${isAdmin}</span></div>`);
                        results.push(`<div><strong>权限管理器 - 是超级管理员:</strong> <span class="${isSuperAdmin ? 'text-green-600' : 'text-red-600'}">${isSuperAdmin}</span></div>`);
                        results.push(`<div><strong>权限管理器 - 角色代码:</strong> ${JSON.stringify(userRoleCodes)}</div>`);
                        results.push(`<div><strong>权限管理器 - 角色对象数量:</strong> ${userRoles.length}</div>`);

                        // 测试具体权限
                        const testPermissions = [
                            { module: 'system', subModule: 'user_management', action: 'view' },
                            { module: 'system', subModule: 'user_management', action: 'create' },
                            { module: 'system', subModule: 'user_management', action: 'delete' },
                            { module: 'system', subModule: 'role_management', action: 'view' },
                            { module: 'system', subModule: 'system_settings', action: 'view' }
                        ];

                        results.push('<div class="mt-4"><strong>具体权限检查:</strong></div>');
                        for (const perm of testPermissions) {
                            try {
                                const hasPermission = window.permissionManager.hasPermission(perm.module, perm.subModule, perm.action);
                                results.push(`<div class="ml-4">• ${perm.module}.${perm.subModule}.${perm.action}: <span class="${hasPermission ? 'text-green-600' : 'text-red-600'}">${hasPermission}</span></div>`);
                            } catch (error) {
                                results.push(`<div class="ml-4">• ${perm.module}.${perm.subModule}.${perm.action}: <span class="text-red-600">错误: ${error.message}</span></div>`);
                            }
                        }

                        // 显示权限对象
                        results.push(`<div class="mt-4"><strong>权限对象:</strong></div>`);
                        results.push(`<div class="ml-4 text-xs bg-gray-100 p-2 rounded"><pre>${JSON.stringify(permissions, null, 2)}</pre></div>`);

                    } catch (error) {
                        results.push(`<div class="text-red-500"><strong>权限管理器错误:</strong> ${error.message}</div>`);
                    }
                } else {
                    results.push('<div class="text-yellow-600"><strong>权限管理器:</strong> 未初始化</div>');
                }

                this.permissionResultsContainer.innerHTML = results.join('');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            function initApp() {
                if (typeof AV !== 'undefined') {
                    // 先初始化权限管理器
                    if (window.permissionManager) {
                        window.permissionManager.init().then(() => {
                            console.log('权限管理器初始化完成');
                        }).catch(error => {
                            console.error('权限管理器初始化失败:', error);
                        });
                    }
                    
                    // 初始化调试应用
                    const debugApp = new PermissionDebugApp();
                    debugApp.init();
                } else {
                    setTimeout(initApp, 100);
                }
            }
            initApp();
        });
    </script>
</body>
</html>
