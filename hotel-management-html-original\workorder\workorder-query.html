<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <!-- 微信浏览器兼容性设置 -->
    <meta name="x5-orientation" content="portrait">
    <meta name="x5-fullscreen" content="true">
    <meta name="x5-page-mode" content="app">
    <title>工单查询</title>
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- LeanCloud SDK -->
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <style>
        /* 微信浏览器兼容性修复 */
        * {
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
        }
        
        body {
            -webkit-overflow-scrolling: touch;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        
        .btn-fix {
            -webkit-appearance: none;
            appearance: none;
            border-radius: 8px;
            border: none;
            outline: none;
        }
        
        .fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* 状态样式 */
        .status-pending { @apply bg-yellow-100 text-yellow-800; }
        .status-accepted { @apply bg-blue-100 text-blue-800; }
        .status-processing { @apply bg-purple-100 text-purple-800; }
        .status-completed { @apply bg-green-100 text-green-800; }
        .status-cancelled { @apply bg-red-100 text-red-800; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航 -->
    <nav class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-10">
        <div class="max-w-4xl mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-3">
                    <a href="index.html" class="text-gray-600 hover:text-gray-800 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                    </a>
                    <h1 class="text-xl font-semibold text-gray-800">工单查询</h1>
                </div>
                <div class="flex items-center space-x-3">
                    <button id="myOrdersBtn" class="bg-green-500 hover:bg-green-600 text-white px-3 py-1.5 rounded-lg text-sm transition-colors btn-fix">
                        我的工单
                    </button>
                    <button id="refreshBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1.5 rounded-lg text-sm transition-colors btn-fix">
                        刷新
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="max-w-4xl mx-auto px-4 py-6">
        <!-- 加载状态 -->
        <div id="loadingSection" class="text-center py-12">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">加载中...</p>
        </div>

        <!-- 错误状态 -->
        <div id="errorSection" class="hidden text-center py-12">
            <div class="mb-4">
                <svg class="mx-auto h-12 w-12 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">工单不存在</h3>
            <p class="text-gray-500 mb-4">请检查工单号是否正确</p>
            <a href="index.html" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg">
                返回工单管理
            </a>
        </div>

        <!-- 我的工单列表 -->
        <div id="myOrdersSection" class="hidden">
            <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-gray-800">我的工单</h2>
                    <button id="backToQueryBtn" class="text-blue-600 hover:text-blue-800 text-sm">
                        返回工单查询
                    </button>
                </div>
                <div id="myOrdersList">
                    <!-- 工单列表将通过JS动态填充 -->
                </div>
            </div>
        </div>

        <!-- 工单详情 -->
        <div id="orderSection" class="hidden space-y-6">
            <!-- 工单状态卡片 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-gray-800">工单状态</h2>
                    <span id="orderStatus" class="px-3 py-1 rounded-full text-sm font-medium">
                        <!-- 状态将通过JS动态填充 -->
                    </span>
                </div>
                
                <!-- 进度条 -->
                <div class="mb-4">
                    <div class="flex justify-between text-sm text-gray-600 mb-2">
                        <span>进度</span>
                        <span id="progressText">0%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div id="progressBar" class="bg-blue-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                </div>
                
                <!-- 时间信息 -->
                <div id="timeInfo" class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <!-- 时间信息将通过JS动态填充 -->
                </div>
            </div>

            <!-- 工单详情 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">工单详情</h3>
                <div id="orderDetails">
                    <!-- 详情将通过JS动态填充 -->
                </div>
            </div>

            <!-- 处理信息 -->
            <div id="processInfo" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">处理信息</h3>
                <div id="processDetails">
                    <!-- 处理信息将通过JS动态填充 -->
                </div>
            </div>
        </div>
    </main>

    <script src="../js/config.js?v=2.1"></script>
    <script>
        class WorkOrderQueryApp {
            constructor() {
                this.orderNumber = null;
                this.order = null;
                this.currentUser = null;
                
                this.elements = {
                    loadingSection: 'loadingSection',
                    errorSection: 'errorSection',
                    orderSection: 'orderSection',
                    myOrdersSection: 'myOrdersSection',
                    myOrdersList: 'myOrdersList',
                    orderStatus: 'orderStatus',
                    progressBar: 'progressBar',
                    progressText: 'progressText',
                    timeInfo: 'timeInfo',
                    orderDetails: 'orderDetails',
                    processInfo: 'processInfo',
                    processDetails: 'processDetails',
                    refreshBtn: 'refreshBtn',
                    myOrdersBtn: 'myOrdersBtn',
                    backToQueryBtn: 'backToQueryBtn'
                };

                // 获取DOM元素
                Object.keys(this.elements).forEach(key => {
                    this.elements[key] = document.getElementById(this.elements[key]);
                });
            }

            async init() {
                try {
                    console.log('=== 工单查询页面初始化 ===');
                    
                    // 初始化LeanCloud
                    if (typeof AV !== 'undefined' && window.LeanCloudConfig) {
                        AV.init(window.LeanCloudConfig);
                        console.log('LeanCloud初始化完成');
                    }

                    // 检查用户登录状态
                    this.currentUser = AV.User.current();
                    if (!this.currentUser) {
                        console.log('用户未登录');
                        this.showLoginRequired();
                        return;
                    }
                    console.log('用户已登录:', this.currentUser.get('username'));

                    // 获取URL参数中的工单号
                    this.orderNumber = this.getOrderNumberFromURL();
                    console.log('URL工单号:', this.orderNumber);
                    
                    if (!this.orderNumber) {
                        // 如果没有指定工单号，显示我的工单列表
                        console.log('没有指定工单号，显示工单列表');
                        await this.showMyOrders();
                        this.bindEvents();
                        return;
                    }

                    // 加载工单信息
                    console.log('加载指定工单信息');
                    await this.loadOrder();

                    // 绑定事件
                    this.bindEvents();

                } catch (error) {
                    console.error('初始化失败:', error);
                    this.showError();
                }
            }

            getOrderNumberFromURL() {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get('order');
            }

            async loadOrder() {
                try {
                    console.log('=== 加载工单详情 ===');
                    console.log('工单号:', this.orderNumber);
                    console.log('使用表名: RepairOrder');
                    
                    // 使用RepairOrder表名，保持与现有数据的兼容性
                    const RepairOrder = AV.Object.extend('RepairOrder');
                    const query = new AV.Query(RepairOrder);
                    query.equalTo('orderNumber', this.orderNumber);
                    
                    // 重要：只查询当前用户的工单
                    query.equalTo('reporter', this.currentUser);
                    console.log('查询条件已设置');
                    
                    console.log('开始执行查询...');
                    const orders = await query.find();
                    console.log('查询完成，找到', orders.length, '条记录');
                    
                    if (orders.length === 0) {
                        console.log('没有找到匹配的工单');
                        this.showAccessDenied();
                        return;
                    }

                    this.order = orders[0];
                    console.log('工单加载成功:', this.order.get('orderNumber'));
                    this.renderOrder();
                    this.showOrder();

                } catch (error) {
                    console.error('=== 加载工单失败 ===');
                    console.error('错误详情:', error);
                    this.showError();
                }
            }

            async showMyOrders() {
                try {
                    console.log('=== 加载我的工单列表 ===');
                    console.log('使用表名: RepairOrder');
                    this.showLoading();
                    
                    // 使用RepairOrder表名，保持与现有数据的兼容性
                    const RepairOrder = AV.Object.extend('RepairOrder');
                    console.log('RepairOrder类已创建');
                    
                    const query = new AV.Query(RepairOrder);
                    query.equalTo('reporter', this.currentUser);
                    query.descending('createdAt');
                    query.limit(20); // 限制显示最近20条
                    console.log('查询条件已设置');
                    
                    console.log('开始执行查询...');
                    const orders = await query.find();
                    console.log('查询成功！找到', orders.length, '条工单');
                    
                    this.renderMyOrdersList(orders);
                    this.showMyOrdersSection();
                    
                } catch (error) {
                    console.error('=== 加载我的工单失败 ===');
                    console.error('错误详情:', error);
                    this.showError();
                }
            }

            renderMyOrdersList(orders) {
                if (!this.elements.myOrdersList) return;
                
                if (orders.length === 0) {
                    this.elements.myOrdersList.innerHTML = `
                        <div class="text-center py-8">
                            <div class="text-gray-400 text-4xl mb-4">📋</div>
                            <p class="text-gray-500">暂无工单</p>
                            <a href="workorder.html" class="mt-4 inline-block bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                                提交新工单
                            </a>
                        </div>
                    `;
                    return;
                }
                
                const ordersHtml = orders.map(order => {
                    const status = order.get('status');
                    const statusText = this.getStatusText(status);
                    const urgency = order.get('urgency');
                    const location = order.get('location');
                    const reportTime = order.get('reportTime');
                    const orderNumber = order.get('orderNumber');
                    
                    return `
                        <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer" 
                             onclick="window.location.href='workorder-query.html?order=${orderNumber}'">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-medium text-gray-900">${order.get('title') || '工单'}</h4>
                                <span class="px-2 py-1 rounded-full text-xs font-medium status-${status}">
                                    ${statusText}
                                </span>
                            </div>
                            <div class="text-sm text-gray-600 space-y-1">
                                <div>工单号：${orderNumber}</div>
                                <div>位置：${location}</div>
                                <div>紧急程度：${urgency}</div>
                                <div>提交时间：${reportTime ? new Date(reportTime).toLocaleString() : '-'}</div>
                            </div>
                        </div>
                    `;
                }).join('');
                
                this.elements.myOrdersList.innerHTML = ordersHtml;
            }

            renderOrder() {
                if (!this.order) return;

                // 渲染状态
                this.renderStatus();
                
                // 渲染详情
                this.renderDetails();
                
                // 渲染处理信息
                this.renderProcessInfo();
            }

            renderStatus() {
                const status = this.order.get('status');
                const statusText = this.getStatusText(status);
                
                if (this.elements.orderStatus) {
                    this.elements.orderStatus.textContent = statusText;
                    this.elements.orderStatus.className = `px-3 py-1 rounded-full text-sm font-medium status-${status}`;
                }

                // 更新进度条
                const progress = this.getProgress(status);
                if (this.elements.progressBar) {
                    this.elements.progressBar.style.width = `${progress}%`;
                }
                
                if (this.elements.progressText) {
                    this.elements.progressText.textContent = `${progress}%`;
                }

                // 渲染时间信息
                this.renderTimeInfo();
            }

            renderTimeInfo() {
                if (!this.elements.timeInfo) return;

                const reportTime = this.order.get('reportTime');
                const acceptTime = this.order.get('acceptTime');
                const completeTime = this.order.get('completeTime');

                this.elements.timeInfo.innerHTML = `
                    <div>
                        <div class="font-medium text-gray-700">提交时间</div>
                        <div class="text-gray-600">${reportTime ? new Date(reportTime).toLocaleString() : '-'}</div>
                    </div>
                    <div>
                        <div class="font-medium text-gray-700">接单时间</div>
                        <div class="text-gray-600">${acceptTime ? new Date(acceptTime).toLocaleString() : '-'}</div>
                    </div>
                    <div>
                        <div class="font-medium text-gray-700">完成时间</div>
                        <div class="text-gray-600">${completeTime ? new Date(completeTime).toLocaleString() : '-'}</div>
                    </div>
                `;
            }

            renderDetails() {
                if (!this.elements.orderDetails) return;

                const images = this.order.get('images') || [];
                const imagesHtml = images.length > 0 ? `
                    <div class="mt-4">
                        <div class="font-medium text-gray-700 mb-2">相关图片</div>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                            ${images.map(url => `
                                <img src="${url}" alt="工单图片" class="w-full h-24 object-cover rounded-lg cursor-pointer" 
                                     onclick="window.open('${url}', '_blank')">
                            `).join('')}
                        </div>
                    </div>
                ` : '';

                this.elements.orderDetails.innerHTML = `
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="font-medium text-gray-700">工单号</div>
                            <div class="text-gray-900">${this.order.get('orderNumber')}</div>
                        </div>
                        <div>
                            <div class="font-medium text-gray-700">工单类别</div>
                            <div class="text-gray-900">${this.order.get('category')}</div>
                        </div>
                        <div>
                            <div class="font-medium text-gray-700">紧急程度</div>
                            <div class="text-gray-900">${this.order.get('urgency')}</div>
                        </div>
                        <div>
                            <div class="font-medium text-gray-700">位置</div>
                            <div class="text-gray-900">${this.order.get('location')}</div>
                        </div>
                        <div class="md:col-span-2">
                            <div class="font-medium text-gray-700">工单描述</div>
                            <div class="text-gray-900 mt-1">${this.order.get('description')}</div>
                        </div>
                    </div>
                    ${imagesHtml}
                `;
            }

            renderProcessInfo() {
                if (!this.elements.processDetails) return;

                const processor = this.order.get('processor');
                const processNote = this.order.get('processNote');
                const completeNote = this.order.get('completeNote');

                this.elements.processDetails.innerHTML = `
                    <div class="space-y-4">
                        <div>
                            <div class="font-medium text-gray-700">处理人员</div>
                            <div class="text-gray-900">${processor || '暂未分配'}</div>
                        </div>
                        ${processNote ? `
                            <div>
                                <div class="font-medium text-gray-700">处理说明</div>
                                <div class="text-gray-900">${processNote}</div>
                            </div>
                        ` : ''}
                        ${completeNote ? `
                            <div>
                                <div class="font-medium text-gray-700">完成说明</div>
                                <div class="text-gray-900">${completeNote}</div>
                            </div>
                        ` : ''}
                    </div>
                `;
            }

            getStatusText(status) {
                const statusMap = {
                    'pending': '待处理',
                    'accepted': '已接单',
                    'processing': '处理中',
                    'completed': '已完成',
                    'cancelled': '已取消'
                };
                return statusMap[status] || status;
            }

            getProgress(status) {
                const progressMap = {
                    'pending': 25,
                    'accepted': 50,
                    'processing': 75,
                    'completed': 100,
                    'cancelled': 0
                };
                return progressMap[status] || 0;
            }

            bindEvents() {
                if (this.elements.refreshBtn) {
                    this.elements.refreshBtn.addEventListener('click', () => {
                        if (this.orderNumber) {
                            this.loadOrder();
                        } else {
                            this.showMyOrders();
                        }
                    });
                }

                if (this.elements.myOrdersBtn) {
                    this.elements.myOrdersBtn.addEventListener('click', () => {
                        this.showMyOrders();
                    });
                }

                if (this.elements.backToQueryBtn) {
                    this.elements.backToQueryBtn.addEventListener('click', () => {
                        this.showQueryMode();
                    });
                }
            }

            showLoading() {
                this.elements.loadingSection?.classList.remove('hidden');
                this.elements.errorSection?.classList.add('hidden');
                this.elements.orderSection?.classList.add('hidden');
                this.elements.myOrdersSection?.classList.add('hidden');
            }

            showError() {
                this.elements.loadingSection?.classList.add('hidden');
                this.elements.orderSection?.classList.add('hidden');
                this.elements.myOrdersSection?.classList.add('hidden');
                this.elements.errorSection?.classList.remove('hidden');
            }

            showOrder() {
                this.elements.loadingSection?.classList.add('hidden');
                this.elements.errorSection?.classList.add('hidden');
                this.elements.myOrdersSection?.classList.add('hidden');
                this.elements.orderSection?.classList.remove('hidden');
            }

            showMyOrdersSection() {
                this.elements.loadingSection?.classList.add('hidden');
                this.elements.errorSection?.classList.add('hidden');
                this.elements.orderSection?.classList.add('hidden');
                this.elements.myOrdersSection?.classList.remove('hidden');
            }

            showQueryMode() {
                this.elements.myOrdersSection?.classList.add('hidden');
                if (this.order) {
                    this.showOrder();
                } else {
                    this.showLoading();
                    this.loadOrder();
                }
            }

            showLoginRequired() {
                this.elements.loadingSection?.classList.add('hidden');
                this.elements.orderSection?.classList.add('hidden');
                
                // 显示登录提示
                if (this.elements.errorSection) {
                    this.elements.errorSection.innerHTML = `
                        <div class="text-center py-12">
                            <div class="mb-4">
                                <svg class="mx-auto h-12 w-12 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                </svg>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">需要登录</h3>
                            <p class="text-gray-500 mb-4">请先登录后再查看工单</p>
                            <a href="../index.html" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg">
                                返回登录
                            </a>
                        </div>
                    `;
                    this.elements.errorSection.classList.remove('hidden');
                }
            }

            showAccessDenied() {
                this.elements.loadingSection?.classList.add('hidden');
                this.elements.orderSection?.classList.add('hidden');
                
                // 显示访问拒绝提示
                if (this.elements.errorSection) {
                    this.elements.errorSection.innerHTML = `
                        <div class="text-center py-12">
                            <div class="mb-4">
                                <svg class="mx-auto h-12 w-12 text-orange-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 0h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                </svg>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">工单不存在或无权访问</h3>
                            <p class="text-gray-500 mb-4">您只能查看自己提交的工单</p>
                            <div class="space-x-4">
                                <a href="index.html" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg">
                                    返回工单管理
                                </a>
                                <a href="workorder.html" class="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg">
                                    提交新工单
                                </a>
                            </div>
                        </div>
                    `;
                    this.elements.errorSection.classList.remove('hidden');
                }
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            function initApp() {
                if (typeof AV !== 'undefined') {
                    const workOrderQueryApp = new WorkOrderQueryApp();
                    workOrderQueryApp.init();
                } else {
                    setTimeout(initApp, 100);
                }
            }
            initApp();
        });
    </script>
</body>
</html>
