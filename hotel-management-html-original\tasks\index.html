<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>任务管理</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- LeanCloud SDK -->
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <style>
        .status-pending { @apply bg-yellow-100 text-yellow-800; }
        .status-in-progress { @apply bg-blue-100 text-blue-800; }
        .status-completed { @apply bg-green-100 text-green-800; }
        .status-overdue { @apply bg-red-100 text-red-800; }
        .status-cancelled { @apply bg-gray-100 text-gray-800; }
        
        .priority-高 { @apply bg-red-100 text-red-800; }
        .priority-中 { @apply bg-yellow-100 text-yellow-800; }
        .priority-低 { @apply bg-green-100 text-green-800; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-3">
                    <a href="../index.html" class="text-gray-600 hover:text-gray-800">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                    </a>
                    <h1 class="text-xl font-semibold text-gray-800">任务管理</h1>
                </div>
                <div id="userInfo" class="hidden items-center space-x-4">
                    <span id="realName" class="text-gray-800 font-medium"></span>
                    <button id="logoutBtn" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm">
                        退出登录
                    </button>
                </div>
                <button id="loginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm">
                    登录
                </button>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="max-w-7xl mx-auto px-4 py-6">
        <!-- 欢迎页面（未登录时显示） -->
        <div id="welcomePage" class="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg shadow-sm border border-blue-200 p-8 text-center">
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-800 mb-4">任务管理系统</h1>
                <div class="text-xl text-blue-600 font-semibold mb-2">
                    相润金鹏酒店任务协作平台
                </div>
                <p class="text-gray-600 text-lg mb-8">
                    高效的部门任务管理，让团队协作更简单
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <div class="text-blue-500 text-3xl mb-3">📋</div>
                    <h3 class="font-semibold text-gray-800 mb-2">任务发布</h3>
                    <p class="text-gray-600 text-sm">创建任务、分配执行人、设置截止时间</p>
                </div>
                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <div class="text-green-500 text-3xl mb-3">👥</div>
                    <h3 class="font-semibold text-gray-800 mb-2">团队协作</h3>
                    <p class="text-gray-600 text-sm">部门内任务共享，实时跟踪进度</p>
                </div>
                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <div class="text-purple-500 text-3xl mb-3">📊</div>
                    <h3 class="font-semibold text-gray-800 mb-2">数据统计</h3>
                    <p class="text-gray-600 text-sm">任务完成率、效率分析、绩效统计</p>
                </div>
            </div>
            
            <div class="bg-white rounded-lg p-6 shadow-sm">
                <h3 class="font-semibold text-gray-800 mb-3">开始使用</h3>
                <p class="text-gray-600 mb-4">请先登录账号，然后即可使用任务管理功能</p>
                <button id="promptLoginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors font-medium">
                    立即登录
                </button>
            </div>
        </div>

        <!-- 任务管理界面 -->
        <div id="taskSection" class="hidden">
            <!-- 统计面板 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="text-2xl font-bold text-blue-600" id="totalTasks">0</div>
                        <div class="ml-3">
                            <div class="text-sm font-medium text-gray-500">总任务</div>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="text-2xl font-bold text-yellow-600" id="pendingTasks">0</div>
                        <div class="ml-3">
                            <div class="text-sm font-medium text-gray-500">待开始</div>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="text-2xl font-bold text-purple-600" id="inProgressTasks">0</div>
                        <div class="ml-3">
                            <div class="text-sm font-medium text-gray-500">进行中</div>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="text-2xl font-bold text-green-600" id="completedTasks">0</div>
                        <div class="ml-3">
                            <div class="text-sm font-medium text-gray-500">已完成</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速操作 -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <a href="task-create.html" class="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white p-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-md">
                    <div class="flex items-center space-x-3">
                        <div class="text-2xl">➕</div>
                        <div>
                            <h4 class="font-semibold">发布任务</h4>
                            <p class="text-blue-100 text-sm">创建新的任务</p>
                        </div>
                    </div>
                </a>
                
                <a href="task-list.html" class="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white p-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-md">
                    <div class="flex items-center space-x-3">
                        <div class="text-2xl">📋</div>
                        <div>
                            <h4 class="font-semibold">任务列表</h4>
                            <p class="text-green-100 text-sm">查看和管理任务</p>
                        </div>
                    </div>
                </a>
                
                <a href="task-stats.html" class="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white p-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-md">
                    <div class="flex items-center space-x-3">
                        <div class="text-2xl">📊</div>
                        <div>
                            <h4 class="font-semibold">数据统计</h4>
                            <p class="text-purple-100 text-sm">查看统计报表</p>
                        </div>
                    </div>
                </a>
            </div>

            <!-- 最近任务 -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-800">最近任务</h3>
                        <a href="task-list.html" class="text-blue-600 hover:text-blue-800 text-sm">查看全部</a>
                    </div>
                </div>
                <div id="recentTasks" class="divide-y divide-gray-200">
                    <!-- 最近任务列表将通过JS动态填充 -->
                </div>
            </div>
        </div>
    </main>

    <!-- 登录弹窗 -->
    <div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-800">用户登录</h2>
            </div>
            <div class="p-6">
                <form id="loginForm" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                        <input type="text" id="loginUsername" required
                            class="w-full p-3 border border-gray-300 rounded-lg">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                        <input type="password" id="loginPassword" required
                            class="w-full p-3 border border-gray-300 rounded-lg">
                    </div>
                    <div class="flex gap-3 pt-4">
                        <button type="button" id="cancelLogin" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-3 rounded-lg">
                            取消
                        </button>
                        <button type="submit" class="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg">
                            登录
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="../js/config.js?v=2.1"></script>
    <script src="../js/core/error-handler.js?v=2.1"></script>
    <script src="../js/utils.js?v=2.1"></script>
    <script src="../js/auth.js?v=2.1"></script>
    <script src="../js/base-app.js?v=2.1"></script>
    <script>
        class TaskIndexApp extends BaseWorkLogApp {
            constructor() {
                super({
                    pageType: 'task-index',
                    requiredElements: ['taskSection']
                });
            }

            getPageElements() {
                return {
                    // 头部元素
                    userInfo: 'userInfo',
                    realName: 'realName',
                    loginBtn: 'loginBtn',
                    logoutBtn: 'logoutBtn',
                    // 登录弹窗元素
                    loginModal: 'loginModal',
                    loginForm: 'loginForm',
                    loginUsername: 'loginUsername',
                    loginPassword: 'loginPassword',
                    cancelLogin: 'cancelLogin',
                    // 页面内容元素
                    welcomePage: 'welcomePage',
                    taskSection: 'taskSection',
                    promptLoginBtn: 'promptLoginBtn',
                    totalTasks: 'totalTasks',
                    pendingTasks: 'pendingTasks',
                    inProgressTasks: 'inProgressTasks',
                    completedTasks: 'completedTasks',
                    recentTasks: 'recentTasks'
                };
            }

            bindPageEvents() {
                // 提示登录
                if (this.elements.promptLoginBtn) {
                    this.elements.promptLoginBtn.addEventListener('click', () => this.showLoginModal());
                }
            }

            onUserLoggedIn() {
                console.log('=== 任务管理页面：用户登录成功 ===');
                console.log('当前用户:', this.currentUser?.get('username'));
                console.log('用户部门:', this.currentUser?.get('department'));
                
                this.showUserInterface();
                this.loadStatistics();
                this.loadRecentTasks();
            }

            onUserLoggedOut() {
                this.showWelcomePage();
            }

            showUserInterface() {
                // 调用父类方法更新头部显示
                super.showUserInterface();
                
                // 更新页面内容显示
                this.elements.welcomePage.style.display = 'none';
                this.elements.taskSection.style.display = 'block';
            }

            showWelcomePage() {
                // 调用父类方法更新头部显示
                super.showLoginPrompt();
                
                // 更新页面内容显示
                this.elements.welcomePage.style.display = 'block';
                this.elements.taskSection.style.display = 'none';
            }

            async loadStatistics() {
                if (!this.currentUser) return;

                try {
                    const Task = AV.Object.extend('Task');
                    const userDept = this.currentUser.get('department');
                    
                    // 构建查询条件：部门任务 + 分配给自己的任务 + 自己创建的任务
                    const deptQuery = new AV.Query(Task);
                    deptQuery.equalTo('creatorDept', userDept);
                    
                    const assignedQuery = new AV.Query(Task);
                    assignedQuery.equalTo('assignee', this.currentUser);
                    
                    const createdQuery = new AV.Query(Task);
                    createdQuery.equalTo('creator', this.currentUser);
                    
                    const mainQuery = AV.Query.or(deptQuery, assignedQuery, createdQuery);
                    
                    // 总任务数
                    const total = await mainQuery.count();
                    this.elements.totalTasks.textContent = total;

                    // 各状态任务数
                    const statuses = ['pending', 'in-progress', 'completed'];
                    for (const status of statuses) {
                        const statusQuery = AV.Query.or(deptQuery, assignedQuery, createdQuery);
                        statusQuery.equalTo('status', status);
                        const count = await statusQuery.count();
                        
                        const elementMap = {
                            'pending': 'pendingTasks',
                            'in-progress': 'inProgressTasks',
                            'completed': 'completedTasks'
                        };
                        
                        if (this.elements[elementMap[status]]) {
                            this.elements[elementMap[status]].textContent = count;
                        }
                    }

                } catch (error) {
                    console.error('加载统计数据失败:', error);
                    // 显示默认值
                    ['totalTasks', 'pendingTasks', 'inProgressTasks', 'completedTasks'].forEach(id => {
                        if (this.elements[id]) {
                            this.elements[id].textContent = '--';
                        }
                    });
                }
            }

            async loadRecentTasks() {
                if (!this.currentUser) return;

                try {
                    const Task = AV.Object.extend('Task');
                    const userDept = this.currentUser.get('department');
                    
                    // 构建查询条件
                    const deptQuery = new AV.Query(Task);
                    deptQuery.equalTo('creatorDept', userDept);
                    
                    const assignedQuery = new AV.Query(Task);
                    assignedQuery.equalTo('assignee', this.currentUser);
                    
                    const createdQuery = new AV.Query(Task);
                    createdQuery.equalTo('creator', this.currentUser);
                    
                    const mainQuery = AV.Query.or(deptQuery, assignedQuery, createdQuery);
                    mainQuery.descending('createdAt');
                    mainQuery.limit(5);
                    
                    const tasks = await mainQuery.find();
                    this.renderRecentTasks(tasks);

                } catch (error) {
                    console.error('加载最近任务失败:', error);
                    this.elements.recentTasks.innerHTML = `
                        <div class="p-8 text-center text-gray-500">
                            加载失败，请稍后重试
                        </div>
                    `;
                }
            }

            renderRecentTasks(tasks) {
                if (tasks.length === 0) {
                    this.elements.recentTasks.innerHTML = `
                        <div class="p-8 text-center text-gray-500">
                            暂无任务数据
                            <div class="mt-4">
                                <a href="task-create.html" class="text-blue-600 hover:text-blue-800">创建第一个任务</a>
                            </div>
                        </div>
                    `;
                    return;
                }

                const tasksHtml = tasks.map(task => {
                    const status = task.get('status');
                    const priority = task.get('priority');
                    const dueDate = task.get('dueDate');
                    
                    return `
                        <div class="p-4 hover:bg-gray-50">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3 mb-2">
                                        <h4 class="font-medium text-gray-900">${task.get('title')}</h4>
                                        <span class="px-2 py-1 rounded-full text-xs font-medium status-${status}">
                                            ${this.getStatusText(status)}
                                        </span>
                                        <span class="px-2 py-1 rounded-full text-xs font-medium priority-${priority}">
                                            ${priority}
                                        </span>
                                    </div>
                                    <div class="text-sm text-gray-600">
                                        <span>执行人: ${task.get('assigneeName')}</span>
                                        <span class="ml-4">截止: ${dueDate ? new Date(dueDate).toLocaleDateString() : '-'}</span>
                                        ${task.get('baseTaskId') ? `<span class="ml-4 text-xs text-blue-600">组任务</span>` : ''}
                                    </div>
                                </div>
                                <div>
                                    <a href="task-detail.html?id=${task.id}" class="text-blue-600 hover:text-blue-800 text-sm">查看详情</a>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');

                this.elements.recentTasks.innerHTML = tasksHtml;
            }

            getStatusText(status) {
                const statusMap = {
                    'pending': '待开始',
                    'in-progress': '进行中',
                    'completed': '已完成',
                    'overdue': '已逾期',
                    'cancelled': '已取消'
                };
                return statusMap[status] || status;
            }
        }

        // 全局变量
        let taskIndexApp;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始状态：显示欢迎页面，隐藏功能界面
            const welcomePage = document.getElementById('welcomePage');
            const taskSection = document.getElementById('taskSection');
            
            if (welcomePage) welcomePage.style.display = 'block';
            if (taskSection) taskSection.style.display = 'none';
            
            function initApp() {
                if (typeof AV !== 'undefined') {
                    taskIndexApp = new TaskIndexApp();
                    taskIndexApp.init();
                } else {
                    setTimeout(initApp, 100);
                }
            }
            initApp();
        });
    </script>
</body>
</html>
