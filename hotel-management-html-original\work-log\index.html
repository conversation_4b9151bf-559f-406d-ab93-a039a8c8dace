<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作日志管理 - 酒店管理系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <style>
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .btn-fix {
            touch-action: manipulation;
        }
        .module-card {
            transition: all 0.3s ease;
        }
        .module-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        /* 统计模块样式 */
        .stats-item {
            transition: all 0.3s ease;
        }
        .stats-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .stats-count {
            position: relative;
        }
        .stats-count.loading::after {
            content: "";
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 2px;
            background: linear-gradient(90deg, transparent, currentColor, transparent);
            animation: loading 1.5s infinite;
        }
        @keyframes loading {
            0% { transform: translateX(-50%) scaleX(0); }
            50% { transform: translateX(-50%) scaleX(1); }
            100% { transform: translateX(-50%) scaleX(0); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="../index.html" class="text-blue-600 hover:text-blue-800 mr-4">
                        ← 返回主页
                    </a>
                    <h1 class="text-xl font-semibold text-gray-900">工作日志管理</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- 用户信息 -->
                    <div id="userInfo" class="flex items-center space-x-2" style="display: none;">
                        <span class="text-sm text-gray-700">用户：</span>
                        <span id="realName" class="text-sm font-medium text-gray-900"></span>
                        <button id="logoutBtn" class="text-sm text-red-600 hover:text-red-800 btn-fix">退出</button>
                    </div>
                    <button id="loginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                        登录
                    </button>
                </div>
            </div>
        </div>
    </nav>
    <!-- 权限提示 -->
    <div id="accessDenied" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8" style="display: none;">
        <div class="bg-white rounded-lg shadow p-8 text-center">
            <div class="mb-4">
                <svg class="mx-auto h-12 w-12 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">访问受限</h3>
            <p class="text-gray-500 mb-4">您需要登录才能访问工作日志功能</p>
            <button id="loginPromptBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg btn-fix">
                立即登录
            </button>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div id="workLogSection" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8" style="display: none;">
        <!-- 页面标题 -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">工作日志管理</h2>
                <p class="text-gray-500 mt-1">记录和管理日常工作内容</p>
            </div>
            <a href="work-log.html" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg btn-fix">
                新建日志
            </a>
        </div>

        <!-- 工作统计 -->
        <div id="statisticsSection" class="bg-white rounded-lg shadow p-6 mb-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900 mb-2 sm:mb-0">工作统计</h3>
                <a href="all-stats.html" class="text-blue-600 text-sm hover:underline">查看全员统计 →</a>
            </div>

            <!-- 移动端：2行布局 -->
            <div class="block sm:hidden">
                <div class="grid grid-cols-3 gap-3 mb-3">
                    <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-3 text-center stats-item">
                        <div class="text-green-600 text-xs mb-1">今日</div>
                        <div class="today-count stats-count text-lg font-bold text-green-600">--</div>
                    </div>
                    <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-3 text-center stats-item">
                        <div class="text-purple-600 text-xs mb-1">本周</div>
                        <div class="week-count stats-count text-lg font-bold text-purple-600">--</div>
                    </div>
                    <div class="bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg p-3 text-center stats-item">
                        <div class="text-orange-600 text-xs mb-1">本月</div>
                        <div class="month-count stats-count text-lg font-bold text-orange-500">--</div>
                    </div>
                </div>
                <div class="grid grid-cols-2 gap-3">
                    <div class="bg-gradient-to-br from-red-50 to-red-100 rounded-lg p-3 text-center stats-item">
                        <div class="text-red-600 text-xs mb-1">本年</div>
                        <div class="year-count stats-count text-lg font-bold text-red-500">--</div>
                    </div>
                    <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-3 text-center stats-item">
                        <div class="text-blue-600 text-xs mb-1">总工作</div>
                        <div class="total-count stats-count text-lg font-bold text-blue-600">--</div>
                    </div>
                </div>
            </div>

            <!-- 桌面端：1行布局 -->
            <div class="hidden sm:block">
                <div class="grid grid-cols-5 gap-4">
                    <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-4 text-center stats-item">
                        <div class="text-green-600 text-sm mb-2">今日</div>
                        <div id="todayPostCount" class="today-count stats-count text-2xl font-bold text-green-600">--</div>
                    </div>
                    <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-4 text-center stats-item">
                        <div class="text-purple-600 text-sm mb-2">本周</div>
                        <div id="weekPostCount" class="week-count stats-count text-2xl font-bold text-purple-600">--</div>
                    </div>
                    <div class="bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg p-4 text-center stats-item">
                        <div class="text-orange-600 text-sm mb-2">本月</div>
                        <div id="monthPostCount" class="month-count stats-count text-2xl font-bold text-orange-500">--</div>
                    </div>
                    <div class="bg-gradient-to-br from-red-50 to-red-100 rounded-lg p-4 text-center stats-item">
                        <div class="text-red-600 text-sm mb-2">本年</div>
                        <div id="yearPostCount" class="year-count stats-count text-2xl font-bold text-red-500">--</div>
                    </div>
                    <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4 text-center stats-item">
                        <div class="text-blue-600 text-sm mb-2">总工作</div>
                        <div id="myPostCount" class="total-count stats-count text-2xl font-bold text-blue-600">--</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 工作日志类型 -->
        <div class="mb-8">
            <h3 class="text-lg font-medium text-gray-900 mb-4">工作日志类型</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- 日常工作日志 -->
                <a href="work-log.html" class="module-card block bg-white rounded-lg shadow p-6 hover:shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <h4 class="ml-4 text-lg font-medium text-gray-900">日常工作日志</h4>
                    </div>
                    <p class="text-gray-500 text-sm">记录日常工作内容、进度和问题</p>
                </a>

                <!-- 空调维护日志 -->
                <a href="aircondition.html" class="module-card block bg-white rounded-lg shadow p-6 hover:shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-cyan-500 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                            </svg>
                        </div>
                        <h4 class="ml-4 text-lg font-medium text-gray-900">空调维护日志</h4>
                    </div>
                    <p class="text-gray-500 text-sm">空调设备检查、维护和故障记录</p>
                </a>

                <!-- 配电房日志 -->
                <a href="powerstation.html" class="module-card block bg-white rounded-lg shadow p-6 hover:shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <h4 class="ml-4 text-lg font-medium text-gray-900">配电房日志</h4>
                    </div>
                    <p class="text-gray-500 text-sm">配电设备巡检、维护和安全记录</p>
                </a>

                <!-- 净水器日志 -->
                <a href="waterfilter.html" class="module-card block bg-white rounded-lg shadow p-6 hover:shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                            </svg>
                        </div>
                        <h4 class="ml-4 text-lg font-medium text-gray-900">净水器日志</h4>
                    </div>
                    <p class="text-gray-500 text-sm">净水设备维护、滤芯更换记录</p>
                </a>

                <!-- 工程施工日志 -->
                <a href="construction.html" class="module-card block bg-white rounded-lg shadow p-6 hover:shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gray-500 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                            </svg>
                        </div>
                        <h4 class="ml-4 text-lg font-medium text-gray-900">工程施工日志</h4>
                    </div>
                    <p class="text-gray-500 text-sm">工程项目进度、质量和安全记录</p>
                </a>

                <!-- 工作报告 -->
                <a href="work-report.html" class="module-card block bg-white rounded-lg shadow p-6 hover:shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                        <h4 class="ml-4 text-lg font-medium text-gray-900">工作报告</h4>
                    </div>
                    <p class="text-gray-500 text-sm">生成和查看工作总结报告</p>
                </a>
            </div>
        </div>
    </div>

    <!-- 登录弹窗 -->
    <div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-40" style="display: none;">
        <div class="bg-white rounded-lg p-6 w-96 max-w-md mx-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">用户登录</h3>
            <form id="loginForm">
                <div class="mb-4">
                    <label for="loginUsername" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                    <input type="text" id="loginUsername" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                <div class="mb-6">
                    <label for="loginPassword" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                    <input type="password" id="loginPassword" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" id="cancelLoginBtn" class="px-4 py-2 text-gray-600 hover:text-gray-800">取消</button>
                    <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-md">登录</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 引入模块化JS文件 -->
    <script src="../js/config.js"></script>
    <script>
        // 工作日志应用类
        class WorkLogApp {
            constructor() {
                this.elements = {};
            }

            init() {
                this.initElements();
                this.bindEvents();
                this.checkAccess();
            }

            initElements() {
                this.elements = {
                    accessDenied: document.getElementById('accessDenied'),
                    workLogSection: document.getElementById('workLogSection'),
                    realName: document.getElementById('realName'),
                    userInfo: document.getElementById('userInfo'),
                    loginBtn: document.getElementById('loginBtn'),
                    logoutBtn: document.getElementById('logoutBtn'),
                    loginPromptBtn: document.getElementById('loginPromptBtn'),
                    loginModal: document.getElementById('loginModal'),
                    loginForm: document.getElementById('loginForm'),
                    cancelLoginBtn: document.getElementById('cancelLoginBtn')
                };
            }

            bindEvents() {
                // 登录相关事件
                if (this.elements.loginBtn) {
                    this.elements.loginBtn.addEventListener('click', () => {
                        this.showLoginModal();
                    });
                }

                if (this.elements.loginPromptBtn) {
                    this.elements.loginPromptBtn.addEventListener('click', () => {
                        this.showLoginModal();
                    });
                }

                if (this.elements.logoutBtn) {
                    this.elements.logoutBtn.addEventListener('click', async () => {
                        try {
                            await AV.User.logOut();
                            this.checkAccess();
                        } catch (error) {
                            console.error('退出登录失败:', error);
                        }
                    });
                }

                // 登录表单事件
                this.elements.loginForm.addEventListener('submit', async (e) => {
                    e.preventDefault();
                    await this.handleLogin();
                });

                // 关闭登录弹窗
                document.addEventListener('click', (e) => {
                    if (e.target.id === 'loginModal' || e.target.id === 'cancelLoginBtn') {
                        this.elements.loginModal.style.display = 'none';
                    }
                });
            }

            async checkAccess() {
                const currentUser = AV.User.current();

                if (currentUser) {
                    this.elements.accessDenied.style.display = 'none';
                    this.elements.workLogSection.style.display = 'block';

                    // 更新用户信息显示
                    this.elements.realName.textContent = currentUser.get('realName') || currentUser.get('username');
                    this.elements.userInfo.style.display = 'flex';
                    this.elements.loginBtn.style.display = 'none';

                    // 加载工作统计
                    this.loadWorkStats();
                } else {
                    // 用户未登录
                    this.elements.accessDenied.style.display = 'block';
                    this.elements.workLogSection.style.display = 'none';
                    this.elements.userInfo.style.display = 'none';
                    this.elements.loginBtn.style.display = 'block';
                }
            }

            showLoginModal() {
                this.elements.loginModal.style.display = 'flex';
            }

            async handleLogin() {
                const username = document.getElementById('loginUsername').value;
                const password = document.getElementById('loginPassword').value;

                try {
                    await AV.User.logIn(username, password);
                    this.elements.loginModal.style.display = 'none';
                    this.checkAccess();
                    alert('登录成功');
                } catch (error) {
                    console.error('登录失败:', error);
                    alert('登录失败: ' + error.message);
                }
            }

            // 加载工作统计数据
            async loadWorkStats() {
                const currentUser = AV.User.current();
                if (!currentUser) return;

                try {
                    // 添加加载状态
                    document.querySelectorAll('.total-count, .today-count, .week-count, .month-count, .year-count').forEach(el => {
                        el.classList.add('loading');
                    });

                    const query = new AV.Query('WorkLog');
                    query.equalTo('user', currentUser);

                    // 总数
                    const total = await query.count();
                    document.querySelectorAll('.total-count').forEach(el => el.textContent = total);

                    // 今日
                    const today = new Date();
                    today.setHours(0, 0, 0, 0);
                    const tomorrow = new Date(today);
                    tomorrow.setDate(today.getDate() + 1);
                    const todayQuery = new AV.Query('WorkLog');
                    todayQuery.equalTo('user', currentUser);
                    todayQuery.greaterThanOrEqualTo('createdAt', today);
                    todayQuery.lessThan('createdAt', tomorrow);
                    const todayCount = await todayQuery.count();
                    document.querySelectorAll('.today-count').forEach(el => el.textContent = todayCount);

                    // 本周
                    const weekStart = new Date(today);
                    weekStart.setDate(today.getDate() - today.getDay() + 1); // 周一开始
                    const weekEnd = new Date(weekStart);
                    weekEnd.setDate(weekStart.getDate() + 7);
                    const weekQuery = new AV.Query('WorkLog');
                    weekQuery.equalTo('user', currentUser);
                    weekQuery.greaterThanOrEqualTo('createdAt', weekStart);
                    weekQuery.lessThan('createdAt', weekEnd);
                    const weekCount = await weekQuery.count();
                    document.querySelectorAll('.week-count').forEach(el => el.textContent = weekCount);

                    // 本月
                    const monthStart = new Date();
                    monthStart.setHours(0, 0, 0, 0);
                    monthStart.setDate(1);
                    const monthEnd = new Date(monthStart);
                    monthEnd.setMonth(monthStart.getMonth() + 1);
                    const monthQuery = new AV.Query('WorkLog');
                    monthQuery.equalTo('user', currentUser);
                    monthQuery.greaterThanOrEqualTo('createdAt', monthStart);
                    monthQuery.lessThan('createdAt', monthEnd);
                    const monthCount = await monthQuery.count();
                    document.querySelectorAll('.month-count').forEach(el => el.textContent = monthCount);

                    // 本年
                    const yearStart = new Date();
                    yearStart.setHours(0, 0, 0, 0);
                    yearStart.setMonth(0, 1);
                    const yearEnd = new Date(yearStart);
                    yearEnd.setFullYear(yearStart.getFullYear() + 1);
                    const yearQuery = new AV.Query('WorkLog');
                    yearQuery.equalTo('user', currentUser);
                    yearQuery.greaterThanOrEqualTo('createdAt', yearStart);
                    yearQuery.lessThan('createdAt', yearEnd);
                    const yearCount = await yearQuery.count();
                    document.querySelectorAll('.year-count').forEach(el => el.textContent = yearCount);

                    // 移除加载状态
                    document.querySelectorAll('.total-count, .today-count, .week-count, .month-count, .year-count').forEach(el => {
                        el.classList.remove('loading');
                    });

                } catch (error) {
                    console.error('加载工作统计失败:', error);
                    // 出错时显示默认值
                    document.querySelectorAll('.total-count, .today-count, .week-count, .month-count, .year-count').forEach(el => {
                        el.textContent = '--';
                        el.classList.remove('loading');
                    });
                }
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            function initApp() {
                if (typeof AV !== 'undefined') {
                    const workLogApp = new WorkLogApp();
                    workLogApp.init();
                } else {
                    setTimeout(initApp, 100);
                }
            }
            initApp();
        });
    </script>
</body>
</html>
