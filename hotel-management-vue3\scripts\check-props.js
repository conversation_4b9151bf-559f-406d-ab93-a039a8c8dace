// scripts/check-props.js
// 检查Vue组件中的props定义问题

const fs = require('fs')
const path = require('path')

function findVueFiles(dir) {
  const files = []
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir)
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        traverse(fullPath)
      } else if (item.endsWith('.vue')) {
        files.push(fullPath)
      }
    }
  }
  
  traverse(dir)
  return files
}

function checkPropsUsage(filePath) {
  const content = fs.readFileSync(filePath, 'utf-8')
  const lines = content.split('\n')
  
  let hasDefineProps = false
  let hasPropsVariable = false
  let usesPropsInCode = false
  let definePropsLine = -1
  let propsUsageLines = []
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]
    
    // 检查是否有defineProps
    if (line.includes('defineProps<') && !line.includes('//')) {
      hasDefineProps = true
      definePropsLine = i + 1
      
      // 检查是否赋值给变量
      if (line.includes('const props =') || line.includes('const props=')) {
        hasPropsVariable = true
      }
    }
    
    // 检查是否在代码中使用了props
    if (line.includes('props.') && !line.includes('//') && !line.includes('defineProps')) {
      usesPropsInCode = true
      propsUsageLines.push(i + 1)
    }
  }
  
  return {
    hasDefineProps,
    hasPropsVariable,
    usesPropsInCode,
    definePropsLine,
    propsUsageLines
  }
}

function main() {
  const componentsDir = path.join(__dirname, '..', 'components')
  const vueFiles = findVueFiles(componentsDir)
  
  console.log('🔍 检查Vue组件中的props定义问题...\n')
  
  let issuesFound = 0
  
  for (const filePath of vueFiles) {
    const relativePath = path.relative(process.cwd(), filePath)
    const result = checkPropsUsage(filePath)
    
    if (result.hasDefineProps && result.usesPropsInCode && !result.hasPropsVariable) {
      issuesFound++
      console.log(`❌ ${relativePath}`)
      console.log(`   defineProps在第${result.definePropsLine}行，但没有赋值给变量`)
      console.log(`   props被使用在第${result.propsUsageLines.join(', ')}行`)
      console.log(`   建议修复: const props = defineProps<Props>()`)
      console.log('')
    }
  }
  
  if (issuesFound === 0) {
    console.log('✅ 所有组件的props定义都正确！')
  } else {
    console.log(`⚠️  发现 ${issuesFound} 个组件有props定义问题`)
  }
  
  console.log(`\n📊 检查了 ${vueFiles.length} 个Vue组件`)
}

if (require.main === module) {
  main()
}

module.exports = { checkPropsUsage, findVueFiles }
