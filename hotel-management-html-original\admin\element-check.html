<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面元素检查 - 系统管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 min-h-screen p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">管理主页元素检查</h1>
        
        <!-- 检查结果 -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-lg font-medium mb-4">检查结果</h2>
            <div id="checkResults" class="space-y-2 text-sm">
                <!-- 检查结果将在这里显示 -->
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="space-x-4">
            <button id="checkBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded">
                检查管理主页元素
            </button>
            <a href="index.html" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded inline-block">
                返回管理首页
            </a>
        </div>

        <!-- 说明 -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mt-6">
            <h3 class="text-lg font-medium text-blue-900 mb-2">使用说明</h3>
            <div class="text-blue-800 text-sm space-y-2">
                <p>• 此工具通过iframe加载管理主页来检查其元素</p>
                <p>• 如果发现元素缺失，请检查管理主页的HTML结构</p>
                <p>• 所有关键元素都应该存在才能保证权限系统正常工作</p>
            </div>
        </div>
    </div>

    <!-- 隐藏的iframe用于加载管理主页 -->
    <iframe id="adminFrame" src="index.html" style="display: none;"></iframe>

    <script>
        class ElementChecker {
            constructor() {
                this.resultsContainer = document.getElementById('checkResults');
                this.adminFrame = document.getElementById('adminFrame');
            }

            init() {
                this.bindEvents();
                this.log('点击"检查管理主页元素"按钮开始检查', 'info');
            }

            bindEvents() {
                document.getElementById('checkBtn').addEventListener('click', () => {
                    this.checkElements();
                });
            }

            log(message, type = 'info') {
                const colors = {
                    'success': 'text-green-600',
                    'error': 'text-red-600',
                    'warning': 'text-yellow-600',
                    'info': 'text-blue-600'
                };

                const div = document.createElement('div');
                div.className = colors[type] || 'text-gray-600';
                div.textContent = message;
                this.resultsContainer.appendChild(div);
            }

            clearResults() {
                this.resultsContainer.innerHTML = '';
            }

            async checkElements() {
                this.clearResults();
                this.log('🔍 开始检查管理主页元素...', 'info');

                try {
                    // 等待iframe加载完成
                    await this.waitForFrameLoad();
                    
                    const frameDoc = this.adminFrame.contentDocument || this.adminFrame.contentWindow.document;
                    
                    if (!frameDoc) {
                        this.log('❌ 无法访问管理主页内容，可能存在跨域限制', 'error');
                        this.log('💡 建议直接在管理主页使用"检查元素"按钮', 'warning');
                        return;
                    }

                    this.log('✅ 成功加载管理主页', 'success');

                    // 检查关键元素
                    const elements = {
                        'accessDenied': '访问受限提示',
                        'adminSection': '管理功能区域',
                        'userInfo': '用户信息区域',
                        'loginBtn': '登录按钮',
                        'logoutBtn': '退出按钮',
                        'realName': '用户姓名显示',
                        'totalUsers': '总用户数统计',
                        'activeUsers': '活跃用户统计',
                        'todayOperations': '今日操作统计',
                        'systemWarnings': '系统警告统计',
                        'loginPromptBtn': '登录提示按钮'
                    };

                    let existingElements = 0;
                    let missingElements = 0;

                    Object.entries(elements).forEach(([id, name]) => {
                        const element = frameDoc.getElementById(id);
                        if (element) {
                            this.log(`✅ ${name} (${id}) 存在`, 'success');
                            existingElements++;
                        } else {
                            this.log(`❌ ${name} (${id}) 不存在`, 'error');
                            missingElements++;
                        }
                    });

                    // 检查管理模块链接
                    const moduleLinks = frameDoc.querySelectorAll('.admin-module-link');
                    this.log(`📋 找到 ${moduleLinks.length} 个管理模块链接`, 'info');

                    // 检查管理卡片
                    const adminCards = frameDoc.querySelectorAll('.admin-card');
                    this.log(`📋 找到 ${adminCards.length} 个管理卡片`, 'info');

                    // 显示总结
                    this.log('', 'info');
                    this.log('📊 检查总结:', 'info');
                    this.log(`✅ 存在的元素: ${existingElements}`, existingElements > 0 ? 'success' : 'warning');
                    this.log(`❌ 缺失的元素: ${missingElements}`, missingElements === 0 ? 'success' : 'error');
                    this.log(`📋 管理模块链接: ${moduleLinks.length}`, moduleLinks.length > 0 ? 'success' : 'warning');
                    this.log(`📋 管理卡片: ${adminCards.length}`, adminCards.length > 0 ? 'success' : 'warning');

                    if (missingElements === 0) {
                        this.log('🎉 所有关键元素都存在！权限系统应该能正常工作。', 'success');
                    } else {
                        this.log('⚠️ 发现缺失的元素，这可能导致权限系统无法正常工作。', 'error');
                        this.log('💡 请检查管理主页的HTML结构，确保所有必需的元素都存在。', 'warning');
                    }

                } catch (error) {
                    this.log(`❌ 检查过程中出现错误: ${error.message}`, 'error');
                    this.log('💡 建议直接在管理主页使用"检查元素"按钮进行检查', 'warning');
                }
            }

            waitForFrameLoad() {
                return new Promise((resolve, reject) => {
                    if (this.adminFrame.contentDocument && this.adminFrame.contentDocument.readyState === 'complete') {
                        resolve();
                        return;
                    }

                    this.adminFrame.onload = () => {
                        // 等待一小段时间确保内容完全加载
                        setTimeout(resolve, 500);
                    };

                    this.adminFrame.onerror = () => {
                        reject(new Error('管理主页加载失败'));
                    };

                    // 设置超时
                    setTimeout(() => {
                        reject(new Error('管理主页加载超时'));
                    }, 10000);
                });
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            const checker = new ElementChecker();
            checker.init();
        });
    </script>
</body>
</html>
