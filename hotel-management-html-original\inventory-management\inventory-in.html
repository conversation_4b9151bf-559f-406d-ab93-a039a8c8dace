<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <!-- 微信浏览器兼容性设置 -->
    <meta name="x5-orientation" content="portrait">
    <meta name="x5-fullscreen" content="true">
    <meta name="x5-page-mode" content="app">
    <title>入库管理</title>
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- LeanCloud SDK -->
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <!-- 备用 CDN 源 -->
    <script>
        // 检测 LeanCloud SDK 是否加载成功，如果失败则使用备用源
        window.addEventListener('load', function() {
            if (typeof AV === 'undefined') {
                console.log('主 CDN 加载失败，尝试备用源...');
                var script = document.createElement('script');
                script.src = 'https://cdn.bootcdn.net/ajax/libs/leancloud-storage/4.15.2/av-min.js';
                script.onerror = function() {
                    console.error('所有 CDN 源都加载失败，请检查网络连接');
                    alert('网络连接异常，请检查网络后刷新页面');
                };
                document.head.appendChild(script);
            }
        });
    </script>
    <style>
        /* 微信浏览器兼容性修复 */
        * {
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
        }
        
        body {
            -webkit-overflow-scrolling: touch;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        
        .btn-fix {
            -webkit-appearance: none;
            appearance: none;
            border-radius: 8px;
            border: none;
            outline: none;
        }
        
        .fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .loading {
            border-top-color: #3498db;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .status-draft { @apply bg-gray-100 text-gray-800; }
        .status-confirmed { @apply bg-blue-100 text-blue-800; }
        .status-completed { @apply bg-green-100 text-green-800; }
        .status-cancelled { @apply bg-red-100 text-red-800; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
        <div class="max-w-7xl mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-4">
                    <a href="index.html" class="text-gray-600 hover:text-gray-800">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                    </a>
                    <h1 class="text-xl font-semibold text-gray-800">入库管理</h1>
                </div>
                <div id="userInfo" class="hidden flex items-center space-x-2">
                    <span id="realName" class="text-gray-800 font-medium text-sm"></span>
                    <button id="logoutBtn" class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded-lg text-sm transition-colors btn-fix">
                        退出
                    </button>
                </div>
                <button id="loginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm transition-colors btn-fix">
                    登录
                </button>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="max-w-7xl mx-auto px-4 py-6">
        <!-- 未登录提示 -->
        <div id="loginPrompt" class="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg shadow-sm border border-blue-200 p-8 mb-6 text-center">
            <div class="mb-6">
                <h2 class="text-2xl font-bold text-gray-800 mb-4">请先登录</h2>
                <p class="text-gray-600 text-lg mb-6">
                    需要登录后才能进行入库管理操作
                </p>
                <button id="promptLoginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors font-medium btn-fix">
                    立即登录
                </button>
            </div>
        </div>

        <!-- 入库管理主界面 -->
        <div id="inventoryInSection" class="hidden">
            <!-- 统计卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">今日入库单</p>
                            <p id="todayInboundCount" class="text-2xl font-bold text-blue-600">0</p>
                        </div>
                        <div class="bg-blue-100 rounded-full p-3">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">待确认</p>
                            <p id="pendingInboundCount" class="text-2xl font-bold text-orange-600">0</p>
                        </div>
                        <div class="bg-orange-100 rounded-full p-3">
                            <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">本月入库</p>
                            <p id="monthInboundCount" class="text-2xl font-bold text-green-600">0</p>
                        </div>
                        <div class="bg-green-100 rounded-full p-3">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">总入库金额</p>
                            <p id="totalInboundAmount" class="text-2xl font-bold text-purple-600">¥0</p>
                        </div>
                        <div class="bg-purple-100 rounded-full p-3">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作栏 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-6 mb-6">
                <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div class="flex flex-col sm:flex-row gap-3">
                        <input type="text" id="searchInput" placeholder="搜索入库单号、供应商..."
                               class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm">
                        <select id="statusFilter" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm">
                            <option value="">全部状态</option>
                            <option value="draft">草稿</option>
                            <option value="confirmed">已确认</option>
                            <option value="completed">已完成</option>
                            <option value="cancelled">已取消</option>
                        </select>
                        <input type="date" id="dateFilter" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm">
                    </div>
                    <div class="flex gap-3">
                        <button id="refreshBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors text-sm font-medium btn-fix">
                            刷新
                        </button>
                        <button id="createInboundBtn" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors text-sm font-medium btn-fix">
                            新建入库单
                        </button>
                    </div>
                </div>
            </div>

            <!-- 入库单列表 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-100">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-800">入库单列表</h2>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">入库单号</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">供应商</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">仓库</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">金额</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody id="inboundTableBody" class="bg-white divide-y divide-gray-200">
                            <!-- 入库单数据将在这里动态加载 -->
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <div id="pagination" class="px-6 py-3 border-t border-gray-200 flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span id="pageInfo">0-0</span> 条，共 <span id="totalCount">0</span> 条
                    </div>
                    <div class="flex space-x-2">
                        <button id="prevPageBtn" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed btn-fix">
                            上一页
                        </button>
                        <button id="nextPageBtn" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed btn-fix">
                            下一页
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载指示器 -->
        <div id="loadingIndicator" class="hidden flex justify-center py-8">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 border-4 border-gray-300 border-t-green-500 rounded-full loading"></div>
                <span class="text-gray-600">正在加载...</span>
            </div>
        </div>
    </main>

    <!-- 登录弹窗 -->
    <div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md fade-in">
            <div class="p-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-xl font-semibold text-gray-800">用户登录</h2>
                    <button id="closeLoginModal" class="text-gray-400 hover:text-gray-600 text-2xl btn-fix">&times;</button>
                </div>
            </div>
            <div class="p-4">
                <!-- 登录表单 -->
                <form id="loginForm" class="space-y-4">
                    <div>
                        <label for="loginUsername" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                        <input type="text" id="loginUsername" autocomplete="username"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm" required>
                    </div>
                    <div>
                        <label for="loginPassword" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                        <input type="password" id="loginPassword" autocomplete="current-password"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm" required>
                    </div>
                    <button type="submit" class="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                        登录
                    </button>
                </form>

                <!-- 注册表单 -->
                <form id="registerForm" class="hidden space-y-4">
                    <div>
                        <label for="registerUsername" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                        <input type="text" id="registerUsername" autocomplete="username"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm" required>
                    </div>
                    <div>
                        <label for="registerRealName" class="block text-sm font-medium text-gray-700 mb-1">真实姓名</label>
                        <input type="text" id="registerRealName" autocomplete="name"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm" required>
                    </div>
                    <div>
                        <label for="registerPhone" class="block text-sm font-medium text-gray-700 mb-1">联系电话</label>
                        <input type="tel" id="registerPhone" autocomplete="tel"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm" required>
                    </div>
                    <div>
                        <label for="registerDepartment" class="block text-sm font-medium text-gray-700 mb-1">所属部门</label>
                        <select id="registerDepartment" required
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm">
                            <option value="">请选择部门</option>
                            <option value="前厅部">前厅部</option>
                            <option value="客房部">客房部</option>
                            <option value="餐饮部">餐饮部</option>
                            <option value="工程部">工程部</option>
                            <option value="保安部">保安部</option>
                            <option value="财务部">财务部</option>
                            <option value="人事部">人事部</option>
                            <option value="销售部">销售部</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>
                    <div>
                        <label for="registerPassword" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                        <input type="password" id="registerPassword" autocomplete="new-password"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm" required>
                    </div>
                    <div>
                        <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-1">确认密码</label>
                        <input type="password" id="confirmPassword" autocomplete="new-password"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm" required>
                    </div>
                    <button type="submit" class="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                        注册
                    </button>
                </form>

                <!-- 表单切换 -->
                <div class="mt-4 text-center">
                    <p id="loginSwitchText" class="text-sm text-gray-600">
                        还没有账号？
                        <a href="#" id="showRegisterBtn" class="text-blue-500 hover:text-blue-600 font-medium">立即注册</a>
                    </p>
                    <p id="registerSwitchText" class="hidden text-sm text-gray-600">
                        已有账号？
                        <a href="#" id="showLoginBtn" class="text-blue-500 hover:text-blue-600 font-medium">立即登录</a>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- 入库单编辑弹窗 -->
    <div id="inboundOrderModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto fade-in">
            <div class="p-4 border-b border-gray-200 sticky top-0 bg-white">
                <div class="flex justify-between items-center">
                    <h2 id="inboundOrderModalTitle" class="text-xl font-semibold text-gray-800">新建入库单</h2>
                    <button id="closeInboundOrderModal" class="text-gray-400 hover:text-gray-600 text-2xl btn-fix">&times;</button>
                </div>
            </div>
            <div class="p-6">
                <form id="inboundOrderForm" class="space-y-6">
                    <input type="hidden" id="editInboundOrderId">

                    <!-- 基本信息 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="inboundOrderNo" class="block text-sm font-medium text-gray-700 mb-1">入库单号 <span class="text-red-500">*</span></label>
                            <input type="text" id="inboundOrderNo" required readonly
                                class="w-full p-3 border border-gray-300 rounded-lg bg-gray-50 text-sm">
                        </div>
                        <div>
                            <label for="inboundType" class="block text-sm font-medium text-gray-700 mb-1">入库类型 <span class="text-red-500">*</span></label>
                            <select id="inboundType" required
                                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm">
                                <option value="">请选择入库类型</option>
                                <option value="purchase">采购入库</option>
                                <option value="return">退货入库</option>
                                <option value="transfer">调拨入库</option>
                                <option value="other">其他入库</option>
                            </select>
                        </div>
                        <div>
                            <label for="inboundSupplier" class="block text-sm font-medium text-gray-700 mb-1">供应商</label>
                            <select id="inboundSupplier"
                                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm">
                                <option value="">请选择供应商</option>
                                <!-- 供应商选项将动态加载 -->
                            </select>
                        </div>
                        <div>
                            <label for="inboundWarehouse" class="block text-sm font-medium text-gray-700 mb-1">入库仓库 <span class="text-red-500">*</span></label>
                            <select id="inboundWarehouse" required
                                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm">
                                <option value="">请选择仓库</option>
                                <!-- 仓库选项将动态加载 -->
                            </select>
                        </div>
                    </div>

                    <div>
                        <label for="inboundRemark" class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                        <textarea id="inboundRemark" rows="3"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"></textarea>
                    </div>

                    <!-- 入库明细 -->
                    <div>
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-800">入库明细</h3>
                            <button type="button" id="addInboundItem" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                                添加商品
                            </button>
                        </div>

                        <div class="overflow-x-auto">
                            <table class="min-w-full border border-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">商品</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">数量</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">单价</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">金额</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">批次号</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="inboundItemsTable">
                                    <!-- 入库明细将在这里动态添加 -->
                                </tbody>
                            </table>
                        </div>

                        <div class="mt-4 text-right">
                            <span class="text-lg font-semibold">总金额: ¥<span id="totalAmount">0.00</span></span>
                        </div>
                    </div>

                    <div class="flex gap-3 pt-4">
                        <button type="button" id="cancelInboundOrderEdit" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                            取消
                        </button>
                        <button type="button" id="saveInboundOrderDraft" class="flex-1 bg-yellow-500 hover:bg-yellow-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                            保存草稿
                        </button>
                        <button type="submit" class="flex-1 bg-green-500 hover:bg-green-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                            确认入库
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 商品选择弹窗 -->
    <div id="productSelectModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[80vh] overflow-y-auto fade-in">
            <div class="p-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-xl font-semibold text-gray-800">选择商品</h2>
                    <button id="closeProductSelectModal" class="text-gray-400 hover:text-gray-600 text-2xl btn-fix">&times;</button>
                </div>
            </div>
            <div class="p-6">
                <div class="mb-4">
                    <input type="text" id="productSearchInput" placeholder="搜索商品名称或编码..."
                           class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm">
                </div>

                <div class="max-h-96 overflow-y-auto">
                    <table class="min-w-full">
                        <thead class="bg-gray-50 sticky top-0">
                            <tr>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">商品信息</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">分类</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">单位</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">操作</th>
                            </tr>
                        </thead>
                        <tbody id="productSelectTable">
                            <!-- 商品列表将在这里动态加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入模块化JS文件 -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/base-app.js"></script>
    <script>
        // 初始化入库管理应用
        let inventoryInApp;

        // 统一的初始化函数
        function initInventoryInApp() {
            if (typeof AV !== 'undefined') {
                try {
                    inventoryInApp = new InventoryInApp();
                    inventoryInApp.init();
                    console.log('入库管理应用初始化成功');
                } catch (error) {
                    console.error('入库管理应用初始化失败:', error);
                    alert('应用初始化失败: ' + error.message);
                }
            } else {
                setTimeout(initInventoryInApp, 100);
            }
        }

        // 入库管理应用类
        class InventoryInApp {
            constructor() {
                this.currentPage = 1;
                this.pageSize = 20;
                this.totalCount = 0;
                this.elements = {};
            }

            /**
             * 初始化应用
             */
            init() {
                // 初始化DOM元素
                this.initElements();

                // 绑定事件
                this.bindPageEvents();

                // 完成初始化
                this.onInitComplete();
            }

            /**
             * 初始化DOM元素
             */
            initElements() {
                const pageElements = this.getPageElements();
                for (const [key, id] of Object.entries(pageElements)) {
                    this.elements[key] = document.getElementById(id);
                }
            }

            /**
             * 获取页面特定的DOM元素
             */
            getPageElements() {
                return {
                    // 主要区域
                    loginPrompt: 'loginPrompt',
                    inventoryInSection: 'inventoryInSection',

                    // 统计元素
                    todayInboundCount: 'todayInboundCount',
                    pendingInboundCount: 'pendingInboundCount',
                    monthInboundCount: 'monthInboundCount',
                    totalInboundAmount: 'totalInboundAmount',

                    // 操作元素
                    searchInput: 'searchInput',
                    statusFilter: 'statusFilter',
                    dateFilter: 'dateFilter',
                    refreshBtn: 'refreshBtn',
                    createInboundBtn: 'createInboundBtn',

                    // 列表和分页
                    inboundTableBody: 'inboundTableBody',
                    pageInfo: 'pageInfo',
                    totalCount: 'totalCount',
                    prevPageBtn: 'prevPageBtn',
                    nextPageBtn: 'nextPageBtn',

                    // 登录相关
                    promptLoginBtn: 'promptLoginBtn',

                    // 用户信息
                    realName: 'realName',
                    userInfo: 'userInfo',
                    loginBtn: 'loginBtn',
                    loadingIndicator: 'loadingIndicator'
                };
            }

            /**
             * 绑定页面特定事件
             */
            bindPageEvents() {
                // 登录提示按钮
                if (this.elements.promptLoginBtn) {
                    this.elements.promptLoginBtn.addEventListener('click', () => {
                        this.showLoginModal();
                    });
                }

                // 刷新按钮
                if (this.elements.refreshBtn) {
                    this.elements.refreshBtn.addEventListener('click', () => {
                        this.loadInboundOrders();
                    });
                }

                // 新建入库单按钮
                if (this.elements.createInboundBtn) {
                    this.elements.createInboundBtn.addEventListener('click', () => {
                        this.createInboundOrder();
                    });
                }

                // 搜索和筛选
                if (this.elements.searchInput) {
                    this.elements.searchInput.addEventListener('input', this.debounce(() => {
                        this.currentPage = 1;
                        this.loadInboundOrders();
                    }, 500));
                }

                if (this.elements.statusFilter) {
                    this.elements.statusFilter.addEventListener('change', () => {
                        this.currentPage = 1;
                        this.loadInboundOrders();
                    });
                }

                if (this.elements.dateFilter) {
                    this.elements.dateFilter.addEventListener('change', () => {
                        this.currentPage = 1;
                        this.loadInboundOrders();
                    });
                }

                // 分页按钮
                if (this.elements.prevPageBtn) {
                    this.elements.prevPageBtn.addEventListener('click', () => {
                        if (this.currentPage > 1) {
                            this.currentPage--;
                            this.loadInboundOrders();
                        }
                    });
                }

                if (this.elements.nextPageBtn) {
                    this.elements.nextPageBtn.addEventListener('click', () => {
                        const maxPage = Math.ceil(this.totalCount / this.pageSize);
                        if (this.currentPage < maxPage) {
                            this.currentPage++;
                            this.loadInboundOrders();
                        }
                    });
                }
            }

            /**
             * 页面特定的初始化后处理
             */
            onInitComplete() {
                // 检查登录状态
                this.checkLoginStatus();
            }

            /**
             * 检查登录状态
             */
            checkLoginStatus() {
                const currentUser = AV.User.current();

                // 直接获取DOM元素，避免依赖this.elements
                const loginPrompt = document.getElementById('loginPrompt');
                const inventoryInSection = document.getElementById('inventoryInSection');
                const realName = document.getElementById('realName');
                const userInfo = document.getElementById('userInfo');
                const loginBtn = document.getElementById('loginBtn');

                if (currentUser) {
                    // 用户已登录
                    if (loginPrompt) loginPrompt.style.display = 'none';
                    if (inventoryInSection) inventoryInSection.style.display = 'block';

                    // 更新用户信息显示
                    if (realName) realName.textContent = currentUser.get('realName') || currentUser.get('username');
                    if (userInfo) userInfo.style.display = 'flex';
                    if (loginBtn) loginBtn.style.display = 'none';

                    // 加载数据
                    this.loadStatistics();
                    this.loadInboundOrders();
                } else {
                    // 用户未登录
                    if (loginPrompt) loginPrompt.style.display = 'block';
                    if (inventoryInSection) inventoryInSection.style.display = 'none';
                    if (userInfo) userInfo.style.display = 'none';
                    if (loginBtn) loginBtn.style.display = 'block';
                }
            }

            /**
             * 加载统计数据
             */
            async loadStatistics() {
                try {
                    const today = new Date();
                    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
                    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

                    // 查询今日入库单数量
                    const todayQuery = new AV.Query('InboundOrder');
                    todayQuery.greaterThanOrEqualTo('createdAt', startOfDay);
                    const todayCount = await todayQuery.count();

                    // 查询待确认入库单数量
                    const pendingQuery = new AV.Query('InboundOrder');
                    pendingQuery.equalTo('status', 'draft');
                    const pendingCount = await pendingQuery.count();

                    // 查询本月入库单数量
                    const monthQuery = new AV.Query('InboundOrder');
                    monthQuery.greaterThanOrEqualTo('createdAt', startOfMonth);
                    const monthCount = await monthQuery.count();

                    // 查询总入库金额（本月）
                    const amountQuery = new AV.Query('InboundOrder');
                    amountQuery.greaterThanOrEqualTo('createdAt', startOfMonth);
                    amountQuery.equalTo('status', 'completed');
                    const orders = await amountQuery.find();
                    const totalAmount = orders.reduce((sum, order) => sum + (order.get('totalAmount') || 0), 0);

                    // 更新显示
                    if (this.elements.todayInboundCount) {
                        this.elements.todayInboundCount.textContent = todayCount;
                    }
                    if (this.elements.pendingInboundCount) {
                        this.elements.pendingInboundCount.textContent = pendingCount;
                    }
                    if (this.elements.monthInboundCount) {
                        this.elements.monthInboundCount.textContent = monthCount;
                    }
                    if (this.elements.totalInboundAmount) {
                        this.elements.totalInboundAmount.textContent = '¥' + totalAmount.toFixed(2);
                    }
                } catch (error) {
                    console.error('加载统计数据失败:', error);
                }
            }

            /**
             * 加载入库单列表
             */
            async loadInboundOrders() {
                try {
                    this.showLoading();

                    const query = new AV.Query('InboundOrder');

                    // 应用搜索条件
                    const searchText = this.elements.searchInput?.value?.trim();
                    if (searchText) {
                        const orderNoQuery = new AV.Query('InboundOrder');
                        orderNoQuery.contains('orderNo', searchText);

                        const supplierQuery = new AV.Query('InboundOrder');
                        const supplierSubQuery = new AV.Query('Supplier');
                        supplierSubQuery.contains('name', searchText);
                        supplierQuery.matchesQuery('supplierId', supplierSubQuery);

                        query._orQuery([orderNoQuery, supplierQuery]);
                    }

                    // 应用状态筛选
                    const statusFilter = this.elements.statusFilter?.value;
                    if (statusFilter) {
                        query.equalTo('status', statusFilter);
                    }

                    // 应用日期筛选
                    const dateFilter = this.elements.dateFilter?.value;
                    if (dateFilter) {
                        const filterDate = new Date(dateFilter);
                        const nextDay = new Date(filterDate);
                        nextDay.setDate(nextDay.getDate() + 1);
                        query.greaterThanOrEqualTo('createdAt', filterDate);
                        query.lessThan('createdAt', nextDay);
                    }

                    // 包含关联数据
                    query.include('supplierId');
                    query.include('warehouseId');
                    query.include('createdBy');

                    // 排序和分页
                    query.descending('createdAt');
                    query.limit(this.pageSize);
                    query.skip((this.currentPage - 1) * this.pageSize);

                    const results = await query.find();
                    const total = await query.count();

                    this.totalCount = total;
                    this.renderInboundOrderList(results);
                    this.updatePagination();

                    this.hideLoading();
                } catch (error) {
                    console.error('加载入库单列表失败:', error);
                    this.hideLoading();
                    alert('加载入库单列表失败: ' + error.message);
                }
            }

            /**
             * 渲染入库单列表
             */
            renderInboundOrderList(orders) {
                if (!this.elements.inboundTableBody) return;

                if (orders.length === 0) {
                    this.elements.inboundTableBody.innerHTML = `
                        <tr>
                            <td colspan="8" class="px-6 py-8 text-center text-gray-500">
                                暂无入库单数据
                            </td>
                        </tr>
                    `;
                    return;
                }

                const html = orders.map(order => {
                    const supplier = order.get('supplierId');
                    const warehouse = order.get('warehouseId');
                    const creator = order.get('createdBy');
                    const status = order.get('status');
                    const statusText = this.getStatusText(status);
                    const statusClass = this.getStatusClass(status);

                    return `
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                ${order.get('orderNo')}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${supplier ? supplier.get('name') : '-'}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${warehouse ? warehouse.get('name') : '-'}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${this.getTypeText(order.get('type'))}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ¥${(order.get('totalAmount') || 0).toFixed(2)}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium rounded-full ${statusClass}">
                                    ${statusText}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${order.createdAt.toLocaleDateString()}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="inventoryInApp.viewInboundOrder('${order.id}')"
                                            class="text-blue-600 hover:text-blue-900">查看</button>
                                    ${status === 'draft' ? `
                                        <button onclick="inventoryInApp.editInboundOrder('${order.id}')"
                                                class="text-green-600 hover:text-green-900">编辑</button>
                                        <button onclick="inventoryInApp.deleteInboundOrder('${order.id}')"
                                                class="text-red-600 hover:text-red-900">删除</button>
                                    ` : ''}
                                </div>
                            </td>
                        </tr>
                    `;
                }).join('');

                this.elements.inboundTableBody.innerHTML = html;
            }

            /**
             * 获取状态文本
             */
            getStatusText(status) {
                const statusMap = {
                    'draft': '草稿',
                    'confirmed': '已确认',
                    'completed': '已完成',
                    'cancelled': '已取消'
                };
                return statusMap[status] || status;
            }

            /**
             * 获取状态样式类
             */
            getStatusClass(status) {
                const classMap = {
                    'draft': 'status-draft',
                    'confirmed': 'status-confirmed',
                    'completed': 'status-completed',
                    'cancelled': 'status-cancelled'
                };
                return classMap[status] || 'status-draft';
            }

            /**
             * 获取类型文本
             */
            getTypeText(type) {
                const typeMap = {
                    'purchase': '采购入库',
                    'return': '退货入库',
                    'transfer': '调拨入库',
                    'other': '其他入库'
                };
                return typeMap[type] || type;
            }

            /**
             * 更新分页信息
             */
            updatePagination() {
                const start = (this.currentPage - 1) * this.pageSize + 1;
                const end = Math.min(this.currentPage * this.pageSize, this.totalCount);

                if (this.elements.pageInfo) {
                    this.elements.pageInfo.textContent = `${start}-${end}`;
                }
                if (this.elements.totalCount) {
                    this.elements.totalCount.textContent = this.totalCount;
                }

                // 更新分页按钮状态
                if (this.elements.prevPageBtn) {
                    this.elements.prevPageBtn.disabled = this.currentPage <= 1;
                }
                if (this.elements.nextPageBtn) {
                    const maxPage = Math.ceil(this.totalCount / this.pageSize);
                    this.elements.nextPageBtn.disabled = this.currentPage >= maxPage;
                }
            }

            /**
             * 创建新入库单
             */
            createInboundOrder() {
                this.showInboundOrderModal();
            }

            /**
             * 防抖函数
             */
            debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }

            /**
             * 显示加载状态
             */
            showLoading() {
                this.elements.loadingIndicator.style.display = 'flex';
            }

            /**
             * 隐藏加载状态
             */
            hideLoading() {
                this.elements.loadingIndicator.style.display = 'none';
            }

            /**
             * 显示登录弹窗
             */
            showLoginModal() {
                document.getElementById('loginModal').style.display = 'flex';
            }

            /**
             * 显示入库单弹窗
             */
            async showInboundOrderModal(orderId = null) {
                const modal = document.getElementById('inboundOrderModal');
                const title = document.getElementById('inboundOrderModalTitle');
                const form = document.getElementById('inboundOrderForm');

                // 重置表单状态
                form.reset();
                const inputs = form.querySelectorAll('input, select, textarea, button');
                inputs.forEach(input => {
                    input.disabled = false;
                });
                document.getElementById('saveInboundOrderDraft').style.display = 'inline-block';
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn) submitBtn.style.display = 'inline-block';

                if (orderId) {
                    title.textContent = '查看入库单';
                    await this.loadInboundOrderForEdit(orderId);
                } else {
                    title.textContent = '新建入库单';
                    document.getElementById('editInboundOrderId').value = '';
                    this.generateOrderNo();
                    this.clearInboundItems();
                }

                await this.loadSuppliers();
                await this.loadWarehouses();
                modal.style.display = 'flex';
            }

            /**
             * 生成入库单号
             */
            generateOrderNo() {
                const now = new Date();
                const dateStr = now.getFullYear().toString() +
                              (now.getMonth() + 1).toString().padStart(2, '0') +
                              now.getDate().toString().padStart(2, '0');
                const timeStr = now.getHours().toString().padStart(2, '0') +
                               now.getMinutes().toString().padStart(2, '0') +
                               now.getSeconds().toString().padStart(2, '0');
                const orderNo = `IN${dateStr}${timeStr}`;
                document.getElementById('inboundOrderNo').value = orderNo;
            }

            /**
             * 加载供应商列表
             */
            async loadSuppliers() {
                try {
                    const query = new AV.Query('Supplier');
                    query.equalTo('status', 'active');
                    query.ascending('name');
                    const suppliers = await query.find();

                    const select = document.getElementById('inboundSupplier');
                    select.innerHTML = '<option value="">请选择供应商</option>';

                    suppliers.forEach(supplier => {
                        const option = document.createElement('option');
                        option.value = supplier.id;
                        option.textContent = supplier.get('name');
                        select.appendChild(option);
                    });
                } catch (error) {
                    console.error('加载供应商失败:', error);
                }
            }

            /**
             * 加载仓库列表
             */
            async loadWarehouses() {
                try {
                    const query = new AV.Query('Warehouse');
                    query.equalTo('status', 'active');
                    query.ascending('name');
                    const warehouses = await query.find();

                    const select = document.getElementById('inboundWarehouse');
                    select.innerHTML = '<option value="">请选择仓库</option>';

                    warehouses.forEach(warehouse => {
                        const option = document.createElement('option');
                        option.value = warehouse.id;
                        option.textContent = warehouse.get('name');
                        select.appendChild(option);
                    });
                } catch (error) {
                    console.error('加载仓库失败:', error);
                }
            }

            /**
             * 清空入库明细
             */
            clearInboundItems() {
                const tbody = document.getElementById('inboundItemsTable');
                tbody.innerHTML = '';
                this.updateTotalAmount();
            }

            /**
             * 更新总金额
             */
            updateTotalAmount() {
                const rows = document.querySelectorAll('#inboundItemsTable tr');
                let total = 0;

                rows.forEach(row => {
                    const amountCell = row.querySelector('.item-amount');
                    if (amountCell) {
                        total += parseFloat(amountCell.textContent) || 0;
                    }
                });

                document.getElementById('totalAmount').textContent = total.toFixed(2);
            }

            /**
             * 查看入库单
             */
            async viewInboundOrder(orderId) {
                await this.showInboundOrderModal(orderId);
                // 设置为只读模式
                setTimeout(() => {
                    const form = document.getElementById('inboundOrderForm');
                    const inputs = form.querySelectorAll('input, select, textarea, button');
                    inputs.forEach(input => {
                        if (input.type !== 'button' && input.id !== 'closeInboundOrderModal' && input.id !== 'cancelInboundOrderEdit') {
                            input.disabled = true;
                        }
                    });

                    // 隐藏操作按钮
                    document.getElementById('saveInboundOrderDraft').style.display = 'none';
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) submitBtn.style.display = 'none';
                }, 100);
            }

            /**
             * 编辑入库单
             */
            editInboundOrder(orderId) {
                this.showInboundOrderModal(orderId);
            }

            /**
             * 加载入库单数据用于编辑
             */
            async loadInboundOrderForEdit(orderId) {
                try {
                    const query = new AV.Query('InboundOrder');
                    query.include('supplierId');
                    query.include('warehouseId');
                    const order = await query.get(orderId);

                    // 填充基本信息
                    document.getElementById('editInboundOrderId').value = orderId;
                    document.getElementById('inboundOrderNo').value = order.get('orderNo');
                    document.getElementById('inboundType').value = order.get('type') || '';
                    document.getElementById('inboundRemark').value = order.get('remark') || '';

                    // 设置供应商
                    const supplier = order.get('supplierId');
                    if (supplier) {
                        document.getElementById('inboundSupplier').value = supplier.id;
                    }

                    // 设置仓库
                    const warehouse = order.get('warehouseId');
                    if (warehouse) {
                        document.getElementById('inboundWarehouse').value = warehouse.id;
                    }

                    // 加载入库明细
                    await this.loadInboundOrderItems(orderId);

                } catch (error) {
                    console.error('加载入库单数据失败:', error);
                    alert('加载入库单数据失败: ' + error.message);
                }
            }

            /**
             * 加载入库单明细
             */
            async loadInboundOrderItems(orderId) {
                try {
                    const query = new AV.Query('InboundOrderItem');
                    query.equalTo('orderId', AV.Object.createWithoutData('InboundOrder', orderId));
                    query.include('productId');
                    const items = await query.find();

                    const tbody = document.getElementById('inboundItemsTable');
                    tbody.innerHTML = '';

                    items.forEach(item => {
                        this.addInboundItemRow(item);
                    });

                    this.updateTotalAmount();
                } catch (error) {
                    console.error('加载入库明细失败:', error);
                    // 如果是表不存在的错误，清空明细表格
                    if (error.message.includes('Class or object doesn\'t exists')) {
                        console.log('InboundOrderItem 表不存在，将在保存时自动创建');
                        const tbody = document.getElementById('inboundItemsTable');
                        tbody.innerHTML = '';
                        this.updateTotalAmount();
                    }
                }
            }

            /**
             * 添加入库明细行
             */
            addInboundItemRow(item = null) {
                const tbody = document.getElementById('inboundItemsTable');
                const row = document.createElement('tr');
                row.className = 'border-b border-gray-200';

                const product = item ? item.get('productId') : null;
                const quantity = item ? item.get('quantity') : 0;
                const unitPrice = item ? item.get('unitPrice') : 0;
                const amount = quantity * unitPrice;
                const batchNo = item ? item.get('batchNo') : '';

                row.innerHTML = `
                    <td class="px-4 py-2">
                        <select class="item-product w-full p-2 border rounded text-sm" required>
                            <option value="">选择商品</option>
                            ${product ? `<option value="${product.id}" selected>${product.get('name')} (${product.get('code')})</option>` : ''}
                        </select>
                    </td>
                    <td class="px-4 py-2">
                        <input type="number" class="item-quantity w-full p-2 border rounded text-sm"
                               value="${quantity}" min="0" step="0.01" required>
                    </td>
                    <td class="px-4 py-2">
                        <input type="number" class="item-price w-full p-2 border rounded text-sm"
                               value="${unitPrice}" min="0" step="0.01" required>
                    </td>
                    <td class="px-4 py-2">
                        <span class="item-amount font-medium">${amount.toFixed(2)}</span>
                    </td>
                    <td class="px-4 py-2">
                        <input type="text" class="item-batch w-full p-2 border rounded text-sm"
                               value="${batchNo}" placeholder="批次号">
                    </td>
                    <td class="px-4 py-2">
                        <button type="button" class="remove-item bg-red-500 hover:bg-red-600 text-white px-2 py-1 rounded text-xs">
                            删除
                        </button>
                    </td>
                `;

                tbody.appendChild(row);

                // 绑定事件
                const quantityInput = row.querySelector('.item-quantity');
                const priceInput = row.querySelector('.item-price');
                const removeBtn = row.querySelector('.remove-item');

                const updateAmount = () => {
                    const qty = parseFloat(quantityInput.value) || 0;
                    const price = parseFloat(priceInput.value) || 0;
                    const amountSpan = row.querySelector('.item-amount');
                    amountSpan.textContent = (qty * price).toFixed(2);
                    this.updateTotalAmount();
                };

                quantityInput.addEventListener('input', updateAmount);
                priceInput.addEventListener('input', updateAmount);

                removeBtn.addEventListener('click', () => {
                    row.remove();
                    this.updateTotalAmount();
                });

                // 如果没有商品数据，加载商品选项
                if (!product) {
                    this.loadProductOptions(row.querySelector('.item-product'));
                }
            }

            /**
             * 加载商品选项
             */
            async loadProductOptions(selectElement) {
                try {
                    const query = new AV.Query('Product');
                    query.equalTo('status', 'active');
                    query.include('categoryId');
                    query.ascending('name');
                    const products = await query.find();

                    products.forEach(product => {
                        const option = document.createElement('option');
                        option.value = product.id;
                        option.textContent = `${product.get('name')} (${product.get('code')})`;
                        selectElement.appendChild(option);
                    });
                } catch (error) {
                    console.error('加载商品选项失败:', error);
                }
            }

            /**
             * 保存入库单
             */
            async saveInboundOrder(status = 'draft') {
                try {
                    // 获取表单数据
                    const orderNo = document.getElementById('inboundOrderNo').value;
                    const type = document.getElementById('inboundType').value;
                    const supplierId = document.getElementById('inboundSupplier').value;
                    const warehouseId = document.getElementById('inboundWarehouse').value;
                    const remark = document.getElementById('inboundRemark').value;
                    const editId = document.getElementById('editInboundOrderId').value;

                    // 验证必填字段
                    if (!orderNo || !type || !warehouseId) {
                        alert('请填写必填字段');
                        return;
                    }

                    // 获取入库明细
                    const items = this.getInboundItems();
                    if (items.length === 0) {
                        alert('请至少添加一个商品');
                        return;
                    }

                    // 计算总金额
                    const totalAmount = items.reduce((sum, item) => sum + item.amount, 0);

                    // 保存入库单
                    let order;
                    if (editId) {
                        order = AV.Object.createWithoutData('InboundOrder', editId);
                    } else {
                        order = new AV.Object('InboundOrder');
                        order.set('orderNo', orderNo);
                        order.set('createdBy', AV.User.current());
                    }

                    order.set('type', type);
                    order.set('status', status);
                    order.set('totalAmount', totalAmount);
                    order.set('remark', remark);

                    if (supplierId) {
                        order.set('supplierId', AV.Object.createWithoutData('Supplier', supplierId));
                    }
                    if (warehouseId) {
                        order.set('warehouseId', AV.Object.createWithoutData('Warehouse', warehouseId));
                    }

                    await order.save();

                    // 保存入库明细
                    if (editId) {
                        // 删除原有明细
                        const itemQuery = new AV.Query('InboundOrderItem');
                        itemQuery.equalTo('orderId', order);
                        const oldItems = await itemQuery.find();
                        await AV.Object.destroyAll(oldItems);
                    }

                    // 创建新明细
                    for (const itemData of items) {
                        const item = new AV.Object('InboundOrderItem');
                        item.set('orderId', order);
                        item.set('productId', AV.Object.createWithoutData('Product', itemData.productId));
                        item.set('quantity', itemData.quantity);
                        item.set('unitPrice', itemData.unitPrice);
                        item.set('amount', itemData.amount);
                        item.set('batchNo', itemData.batchNo);
                        await item.save();
                    }

                    alert(status === 'draft' ? '草稿保存成功' : '入库单确认成功');
                    document.getElementById('inboundOrderModal').style.display = 'none';
                    this.loadInboundOrders();
                    this.loadStatistics();

                } catch (error) {
                    console.error('保存入库单失败:', error);
                    alert('保存失败: ' + error.message);
                }
            }

            /**
             * 获取入库明细数据
             */
            getInboundItems() {
                const rows = document.querySelectorAll('#inboundItemsTable tr');
                const items = [];

                rows.forEach(row => {
                    const productSelect = row.querySelector('.item-product');
                    const quantityInput = row.querySelector('.item-quantity');
                    const priceInput = row.querySelector('.item-price');
                    const batchInput = row.querySelector('.item-batch');

                    if (productSelect && quantityInput && priceInput) {
                        const productId = productSelect.value;
                        const quantity = parseFloat(quantityInput.value) || 0;
                        const unitPrice = parseFloat(priceInput.value) || 0;
                        const batchNo = batchInput ? batchInput.value : '';

                        if (productId && quantity > 0) {
                            items.push({
                                productId,
                                quantity,
                                unitPrice,
                                amount: quantity * unitPrice,
                                batchNo
                            });
                        }
                    }
                });

                return items;
            }

            /**
             * 删除入库单
             */
            async deleteInboundOrder(orderId) {
                if (!confirm('确定要删除这个入库单吗？')) {
                    return;
                }

                try {
                    const order = AV.Object.createWithoutData('InboundOrder', orderId);
                    await order.destroy();
                    alert('删除成功');
                    this.loadInboundOrders();
                    this.loadStatistics();
                } catch (error) {
                    console.error('删除失败:', error);
                    alert('删除失败: ' + error.message);
                }
            }
        }

        // 页面加载完成后的统一初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化应用
            initInventoryInApp();

            // 绑定入库单弹窗事件
            document.getElementById('closeInboundOrderModal').addEventListener('click', function() {
                document.getElementById('inboundOrderModal').style.display = 'none';
            });

            document.getElementById('cancelInboundOrderEdit').addEventListener('click', function() {
                document.getElementById('inboundOrderModal').style.display = 'none';
            });

            document.getElementById('addInboundItem').addEventListener('click', function() {
                if (inventoryInApp) {
                    inventoryInApp.addInboundItemRow();
                }
            });

            document.getElementById('closeProductSelectModal').addEventListener('click', function() {
                document.getElementById('productSelectModal').style.display = 'none';
            });

            // 绑定入库单表单提交
            document.getElementById('inboundOrderForm').addEventListener('submit', async function(e) {
                e.preventDefault();
                if (inventoryInApp) {
                    await inventoryInApp.saveInboundOrder('confirmed');
                }
            });

            document.getElementById('saveInboundOrderDraft').addEventListener('click', async function() {
                if (inventoryInApp) {
                    await inventoryInApp.saveInboundOrder('draft');
                }
            });

            // 绑定登录相关事件
            // 绑定登录按钮
            document.getElementById('loginBtn').addEventListener('click', function() {
                document.getElementById('loginModal').style.display = 'flex';
            });

            document.getElementById('promptLoginBtn').addEventListener('click', function() {
                document.getElementById('loginModal').style.display = 'flex';
            });

            // 绑定登录弹窗关闭按钮
            document.getElementById('closeLoginModal').addEventListener('click', function() {
                document.getElementById('loginModal').style.display = 'none';
            });

            // 绑定表单切换
            document.getElementById('showRegisterBtn').addEventListener('click', function(e) {
                e.preventDefault();
                document.getElementById('loginForm').style.display = 'none';
                document.getElementById('registerForm').style.display = 'block';
                document.getElementById('loginSwitchText').style.display = 'none';
                document.getElementById('registerSwitchText').style.display = 'block';
            });

            document.getElementById('showLoginBtn').addEventListener('click', function(e) {
                e.preventDefault();
                document.getElementById('loginForm').style.display = 'block';
                document.getElementById('registerForm').style.display = 'none';
                document.getElementById('loginSwitchText').style.display = 'block';
                document.getElementById('registerSwitchText').style.display = 'none';
            });

            // 登录功能
            document.getElementById('loginForm').addEventListener('submit', async function(e) {
                e.preventDefault();

                const username = document.getElementById('loginUsername').value;
                const password = document.getElementById('loginPassword').value;

                if (!username || !password) {
                    alert('请输入用户名和密码');
                    return;
                }

                try {
                    const user = await AV.User.logIn(username, password);
                    console.log('登录成功:', user);

                    // 关闭登录弹窗
                    document.getElementById('loginModal').style.display = 'none';

                    // 重新检查登录状态
                    if (inventoryInApp) {
                        inventoryInApp.checkLoginStatus();
                    }

                    alert('登录成功！');
                } catch (error) {
                    console.error('登录失败:', error);
                    alert('登录失败: ' + error.message);
                }
            });

            // 注册功能
            document.getElementById('registerForm').addEventListener('submit', async function(e) {
                e.preventDefault();

                const username = document.getElementById('registerUsername').value;
                const realName = document.getElementById('registerRealName').value;
                const phone = document.getElementById('registerPhone').value;
                const department = document.getElementById('registerDepartment').value;
                const password = document.getElementById('registerPassword').value;
                const confirmPassword = document.getElementById('confirmPassword').value;

                if (!username || !realName || !phone || !department || !password) {
                    alert('请填写所有必填字段');
                    return;
                }

                if (password !== confirmPassword) {
                    alert('两次输入的密码不一致');
                    return;
                }

                if (password.length < 6) {
                    alert('密码长度至少6位');
                    return;
                }

                try {
                    const user = new AV.User();
                    user.setUsername(username);
                    user.setPassword(password);
                    user.set('realName', realName);
                    user.set('phone', phone);
                    user.set('department', department);
                    user.set('roles', []); // 默认无特殊角色

                    await user.signUp();
                    console.log('注册成功:', user);

                    // 关闭登录弹窗
                    document.getElementById('loginModal').style.display = 'none';

                    // 重新检查登录状态
                    if (inventoryInApp) {
                        inventoryInApp.checkLoginStatus();
                    }

                    alert('注册成功！');
                } catch (error) {
                    console.error('注册失败:', error);
                    alert('注册失败: ' + error.message);
                }
            });

            // 退出登录功能
            document.getElementById('logoutBtn').addEventListener('click', async function() {
                try {
                    await AV.User.logOut();
                    console.log('退出登录成功');

                    // 重新检查登录状态
                    if (inventoryInApp) {
                        inventoryInApp.checkLoginStatus();
                    }

                    alert('已退出登录');
                } catch (error) {
                    console.error('退出登录失败:', error);
                    alert('退出登录失败: ' + error.message);
                }
            });
        });
    </script>
</body>
</html>
