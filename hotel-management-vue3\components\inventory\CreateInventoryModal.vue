<template>
  <div 
    v-if="show" 
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    @click="handleBackdropClick"
  >
    <div 
      class="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden"
      @click.stop
    >
      <!-- 头部 -->
      <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-900">
          {{ isEditing ? '编辑库存物品' : '新增库存物品' }}
        </h3>
        <button 
          @click="handleClose"
          class="text-gray-400 hover:text-gray-600"
        >
          <Icon name="mdi:close" size="24" />
        </button>
      </div>

      <!-- 内容 -->
      <div class="px-6 py-4 max-h-[calc(90vh-140px)] overflow-y-auto">
        <form @submit.prevent="handleSubmit">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 左侧：基本信息 -->
            <div class="space-y-6">
              <h4 class="text-md font-medium text-gray-900">基本信息</h4>
              
              <!-- 物品名称 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  物品名称 *
                </label>
                <input 
                  v-model="formData.name"
                  type="text"
                  required
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="请输入物品名称"
                />
              </div>

              <!-- 分类 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  分类 *
                </label>
                <select 
                  v-model="formData.category"
                  required
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">请选择分类</option>
                  <option value="客房用品">客房用品</option>
                  <option value="清洁用品">清洁用品</option>
                  <option value="办公用品">办公用品</option>
                  <option value="维修工具">维修工具</option>
                  <option value="餐饮用品">餐饮用品</option>
                  <option value="电子设备">电子设备</option>
                  <option value="其他">其他</option>
                </select>
              </div>

              <!-- 单位 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  单位 *
                </label>
                <select 
                  v-model="formData.unit"
                  required
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">请选择单位</option>
                  <option value="个">个</option>
                  <option value="件">件</option>
                  <option value="套">套</option>
                  <option value="瓶">瓶</option>
                  <option value="包">包</option>
                  <option value="盒">盒</option>
                  <option value="条">条</option>
                  <option value="张">张</option>
                  <option value="台">台</option>
                  <option value="公斤">公斤</option>
                  <option value="升">升</option>
                </select>
              </div>

              <!-- 位置 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  存放位置 *
                </label>
                <input 
                  v-model="formData.location"
                  type="text"
                  required
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="如：仓库A、1楼储物间等"
                />
              </div>

              <!-- 供应商 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  供应商
                </label>
                <input 
                  v-model="formData.supplier"
                  type="text"
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="请输入供应商名称"
                />
              </div>

              <!-- 条码 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  条码
                </label>
                <input 
                  v-model="formData.barcode"
                  type="text"
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="请输入条码"
                />
              </div>
            </div>

            <!-- 右侧：库存和价格信息 -->
            <div class="space-y-6">
              <h4 class="text-md font-medium text-gray-900">库存信息</h4>
              
              <!-- 当前库存 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  当前库存 *
                </label>
                <input 
                  v-model.number="formData.quantity"
                  type="number"
                  min="0"
                  required
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="请输入当前库存数量"
                />
              </div>

              <!-- 最低库存 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  最低库存 *
                </label>
                <input 
                  v-model.number="formData.minStock"
                  type="number"
                  min="0"
                  required
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="低于此数量时会发出警告"
                />
              </div>

              <!-- 最高库存 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  最高库存
                </label>
                <input 
                  v-model.number="formData.maxStock"
                  type="number"
                  min="0"
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="建议的最大库存量"
                />
              </div>

              <!-- 单价 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  单价 (元)
                </label>
                <input 
                  v-model.number="formData.price"
                  type="number"
                  min="0"
                  step="0.01"
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="请输入单价"
                />
              </div>

              <!-- 状态 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  状态 *
                </label>
                <select 
                  v-model="formData.status"
                  required
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="active">正常</option>
                  <option value="inactive">停用</option>
                  <option value="discontinued">停产</option>
                </select>
              </div>

              <!-- 描述 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  描述
                </label>
                <textarea 
                  v-model="formData.description"
                  rows="4"
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                  placeholder="请输入物品描述、规格等信息..."
                ></textarea>
              </div>
            </div>
          </div>

          <!-- 图片上传 -->
          <div class="mt-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              物品图片
            </label>
            
            <!-- 已上传的图片 -->
            <div v-if="formData.images.length > 0" class="mb-4">
              <div class="grid grid-cols-4 gap-2">
                <div 
                  v-for="(image, index) in formData.images" 
                  :key="index"
                  class="relative group"
                >
                  <img 
                    :src="image" 
                    :alt="`图片 ${index + 1}`"
                    class="w-full h-20 object-cover rounded border border-gray-200"
                  />
                  <button 
                    type="button"
                    @click="removeImage(index)"
                    class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600 opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    ×
                  </button>
                </div>
              </div>
            </div>
            
            <!-- 上传按钮 -->
            <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors">
              <input 
                ref="fileInput"
                type="file" 
                multiple 
                accept="image/*"
                class="hidden"
                @change="handleFileSelect"
              />
              
              <button 
                type="button"
                @click="$refs.fileInput?.click()"
                :disabled="loading || uploading"
                class="text-blue-600 hover:text-blue-700 font-medium disabled:opacity-50"
              >
                <Icon name="mdi:camera" size="24" class="mx-auto mb-2" />
                {{ uploading ? '上传中...' : '选择图片' }}
              </button>
              
              <p class="text-xs text-gray-500 mt-1">
                支持 JPG、PNG 格式，最多 6 张图片
              </p>
            </div>
          </div>
        </form>
      </div>

      <!-- 底部按钮 -->
      <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
        <button 
          type="button"
          @click="handleClose"
          :disabled="loading"
          class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50"
        >
          取消
        </button>
        <button 
          @click="handleSubmit"
          :disabled="loading || !isFormValid"
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center"
        >
          <Icon v-if="loading" name="mdi:loading" size="16" class="mr-1 animate-spin" />
          {{ loading ? (isEditing ? '更新中...' : '创建中...') : (isEditing ? '更新' : '创建') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { InventoryItem } from '~/types'

interface Props {
  show: boolean
  editItem?: InventoryItem | null
}

const props = withDefaults(defineProps<Props>(), {
  editItem: null
})

const emit = defineEmits<{
  'update:show': [value: boolean]
  created: []
  updated: []
}>()

const inventoryStore = useInventoryStore()

// 响应式数据
const loading = ref(false)
const uploading = ref(false)
const fileInput = ref<HTMLInputElement>()

const formData = reactive({
  name: '',
  category: '',
  unit: '',
  quantity: 0,
  minStock: 0,
  maxStock: undefined as number | undefined,
  location: '',
  supplier: '',
  price: undefined as number | undefined,
  barcode: '',
  description: '',
  status: 'active' as 'active' | 'inactive' | 'discontinued',
  images: [] as string[]
})

// 计算属性
const isEditing = computed(() => !!props.editItem)

const isFormValid = computed(() => {
  return formData.name.trim().length > 0 && 
         formData.category.length > 0 &&
         formData.unit.length > 0 &&
         formData.location.trim().length > 0 &&
         formData.quantity >= 0 &&
         formData.minStock >= 0
})

// 方法
const resetForm = () => {
  formData.name = ''
  formData.category = ''
  formData.unit = ''
  formData.quantity = 0
  formData.minStock = 0
  formData.maxStock = undefined
  formData.location = ''
  formData.supplier = ''
  formData.price = undefined
  formData.barcode = ''
  formData.description = ''
  formData.status = 'active'
  formData.images = []
}

const loadEditData = () => {
  if (props.editItem) {
    formData.name = props.editItem.name
    formData.category = props.editItem.category
    formData.unit = props.editItem.unit
    formData.quantity = props.editItem.quantity
    formData.minStock = props.editItem.minStock
    formData.maxStock = props.editItem.maxStock
    formData.location = props.editItem.location
    formData.supplier = props.editItem.supplier || ''
    formData.price = props.editItem.price
    formData.barcode = props.editItem.barcode || ''
    formData.description = props.editItem.description || ''
    formData.status = props.editItem.status
    formData.images = [...(props.editItem.images || [])]
  }
}

const handleClose = () => {
  if (!loading.value) {
    emit('update:show', false)
    resetForm()
  }
}

const handleBackdropClick = () => {
  handleClose()
}

const handleFileSelect = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files
  
  if (!files || files.length === 0) return
  
  // 检查图片数量限制
  if (formData.images.length + files.length > 6) {
    alert('最多只能上传6张图片')
    return
  }
  
  uploading.value = true
  
  try {
    for (const fileItem of Array.from(files)) {
      // 检查文件类型
      if (!fileItem.type.startsWith('image/')) {
        alert(`文件 ${fileItem.name} 不是有效的图片格式`)
        continue
      }

      // 检查文件大小（5MB限制）
      if (fileItem.size > 5 * 1024 * 1024) {
        alert(`文件 ${fileItem.name} 大小超过5MB限制`)
        continue
      }

      // 压缩图片（简化版，直接使用原文件）
      const compressedBlob = fileItem

      // 创建新的文件名
      const originalName = fileItem.name
      const nameWithoutExt = originalName.substring(0, originalName.lastIndexOf('.')) || originalName
      const compressedFileName = `inventory_${nameWithoutExt}_${Date.now()}.jpg`

      // 构造FormData
      const uploadFormData = new FormData()
      uploadFormData.append('token', '8e7057ee0ba0be565301980fb3e52763')
      uploadFormData.append('image', new File([compressedBlob], compressedFileName, { type: 'image/jpeg' }))

      // 上传到自建API
      const response = await fetch('https://www.junwei.bid:89/web/11/index.php', {
        method: 'POST',
        body: uploadFormData
      })

      if (response.ok) {
        const result = await response.json()
        // 兼容不同返回格式
        if (result.url) {
          formData.images.push(result.url)
        } else if (result.data && result.data.url) {
          formData.images.push(result.data.url)
        } else if (typeof result === 'string' && result.startsWith('http')) {
          formData.images.push(result)
        } else {
          alert(`上传文件 ${fileItem.name} 失败: 返回格式不正确`)
        }
      } else {
        const errorText = await response.text()
        alert(`上传文件 ${fileItem.name} 失败: ${errorText}`)
      }
    }
  } catch (error) {
    console.error('文件上传失败:', error)
    alert('文件上传失败，请稍后重试')
  } finally {
    uploading.value = false
    // 清空文件输入
    if (fileInput.value) {
      fileInput.value.value = ''
    }
  }
}

const removeImage = (index: number) => {
  formData.images.splice(index, 1)
}

const handleSubmit = async () => {
  if (!isFormValid.value || loading.value) return
  
  loading.value = true
  
  try {
    const itemData = {
      name: formData.name.trim(),
      category: formData.category,
      unit: formData.unit,
      quantity: formData.quantity,
      minStock: formData.minStock,
      maxStock: formData.maxStock,
      location: formData.location.trim(),
      supplier: formData.supplier.trim() || undefined,
      price: formData.price,
      barcode: formData.barcode.trim() || undefined,
      description: formData.description.trim() || undefined,
      status: formData.status,
      images: formData.images
    }
    
    let result
    
    if (isEditing.value) {
      result = await inventoryStore.updateItem(props.editItem!.id, itemData)
      if (result.success) {
        emit('updated')
      }
    } else {
      result = await inventoryStore.createItem(itemData)
      if (result.success) {
        emit('created')
      }
    }
    
    if (!result.success) {
      alert(result.error || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    alert('提交失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 监听props变化
watch(() => props.show, (newValue) => {
  if (newValue) {
    if (props.editItem) {
      loadEditData()
    } else {
      resetForm()
    }
  }
})

// 键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.show) {
    handleClose()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>
