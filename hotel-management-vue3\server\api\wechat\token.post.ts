// server/api/wechat/token.post.ts
export default defineEventHandler(async (event) => {
  const config = useRuntimeConfig()
  
  try {
    // 企业微信获取access_token的API
    const response = await $fetch('https://qyapi.weixin.qq.com/cgi-bin/gettoken', {
      method: 'GET',
      query: {
        corpid: config.public.wechatCorpId,
        corpsecret: process.env.WECHAT_CORP_SECRET // 这个应该在服务端环境变量中
      }
    })

    if (response.errcode === 0) {
      return {
        access_token: response.access_token,
        expires_in: response.expires_in
      }
    } else {
      throw createError({
        statusCode: 400,
        statusMessage: `企业微信API错误: ${response.errmsg}`
      })
    }
  } catch (error: any) {
    throw createError({
      statusCode: 500,
      statusMessage: error.message || '获取企业微信访问令牌失败'
    })
  }
})
