<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 系统管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <style>
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .btn-fix {
            touch-action: manipulation;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="index.html" class="text-blue-600 hover:text-blue-800 mr-4">
                        ← 返回管理首页
                    </a>
                    <h1 class="text-xl font-semibold text-gray-900">用户管理</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="addUserBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                        添加用户
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 搜索和筛选 -->
        <div class="bg-white rounded-lg shadow mb-6 p-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <input type="text" id="searchInput" placeholder="搜索用户名或姓名..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                </div>
                <div>
                    <select id="departmentFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                        <option value="">全部部门</option>
                        <option value="工程部">工程部</option>
                        <option value="前厅部">前厅部</option>
                        <option value="客房部">客房部</option>
                        <option value="餐饮部">餐饮部</option>
                        <option value="保安部">保安部</option>
                        <option value="财务部">财务部</option>
                        <option value="人事部">人事部</option>
                        <option value="销售部">销售部</option>
                    </select>
                </div>
                <div>
                    <select id="roleFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                        <option value="">全部角色</option>
                        <option value="super_admin">超级管理员</option>
                        <option value="admin">管理员</option>
                        <option value="user">普通用户</option>
                    </select>
                </div>
                <div>
                    <button id="searchBtn" class="w-full bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md text-sm btn-fix">
                        搜索
                    </button>
                <button id="fixRolesBtn" class="w-full bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md text-sm btn-fix">
                    修复角色数据
                </button>
                </div>
            </div>
        </div>

        <!-- 角色权限说明 -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h3 class="text-sm font-medium text-blue-900 mb-2">角色权限说明</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-xs text-blue-800">
                <div>
                    <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-purple-100 text-purple-800 mb-1">超级管理员</span>
                    <p>• 拥有系统所有权限</p>
                    <p>• 可以管理所有用户和角色</p>
                    <p>• 可以修改系统设置</p>
                </div>
                <div>
                    <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800 mb-1">管理员</span>
                    <p>• 拥有大部分管理权限</p>
                    <p>• 可以管理用户（不能删除）</p>
                    <p>• 可以查看系统设置</p>
                </div>
                <div>
                    <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800 mb-1">普通用户</span>
                    <p>• 基本操作权限</p>
                    <p>• 可以管理自己的工作日志</p>
                    <p>• 可以创建维修单</p>
                </div>
            </div>
        </div>

        <!-- 用户列表 -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-medium text-gray-900">用户列表</h2>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户信息</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">部门</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">角色</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">注册时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后登录</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody id="userTableBody" class="bg-white divide-y divide-gray-200">
                        <!-- 用户列表将在这里动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span id="pageInfo">1-20</span> 条，共 <span id="totalCount">0</span> 条
                    </div>
                    <div class="flex space-x-2">
                        <button id="prevPageBtn" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 btn-fix">上一页</button>
                        <button id="nextPageBtn" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 btn-fix">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户编辑弹窗 -->
    <div id="userModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 fade-in">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 id="userModalTitle" class="text-xl font-semibold text-gray-800">添加用户</h2>
                    <button id="closeUserModal" class="text-gray-400 hover:text-gray-600 text-2xl btn-fix">&times;</button>
                </div>
            </div>
            <div class="p-6">
                <form id="userForm" class="space-y-4">
                    <input type="hidden" id="editUserId">
                    <div>
                        <label for="userUsername" class="block text-sm font-medium text-gray-700 mb-1">用户名 <span class="text-red-500">*</span></label>
                        <input type="text" id="userUsername" required
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                    </div>
                    <div>
                        <label for="userRealName" class="block text-sm font-medium text-gray-700 mb-1">真实姓名 <span class="text-red-500">*</span></label>
                        <input type="text" id="userRealName" required
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                    </div>
                    <div>
                        <label for="userPhone" class="block text-sm font-medium text-gray-700 mb-1">电话</label>
                        <input type="tel" id="userPhone"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                    </div>
                    <div>
                        <label for="userDepartment" class="block text-sm font-medium text-gray-700 mb-1">部门</label>
                        <select id="userDepartment"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                            <option value="">请选择部门</option>
                            <option value="工程部">工程部</option>
                            <option value="前厅部">前厅部</option>
                            <option value="客房部">客房部</option>
                            <option value="餐饮部">餐饮部</option>
                            <option value="保安部">保安部</option>
                            <option value="财务部">财务部</option>
                            <option value="人事部">人事部</option>
                            <option value="销售部">销售部</option>
                        </select>
                    </div>
                    <div>
                        <label for="userRoles" class="block text-sm font-medium text-gray-700 mb-1">角色</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" id="roleSuperAdmin" value="super_admin" class="rounded">
                                <span class="ml-2 text-sm text-gray-700">超级管理员</span>
                                <span class="ml-2 text-xs text-red-500">(拥有所有权限)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" id="roleAdmin" value="admin" class="rounded">
                                <span class="ml-2 text-sm text-gray-700">管理员</span>
                                <span class="ml-2 text-xs text-blue-500">(大部分管理权限)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" id="roleUser" value="user" class="rounded" checked>
                                <span class="ml-2 text-sm text-gray-700">普通用户</span>
                                <span class="ml-2 text-xs text-gray-500">(基本操作权限)</span>
                            </label>
                        </div>
                        <div class="mt-2 text-xs text-gray-500">
                            <p>• 用户可以拥有多个角色</p>
                            <p>• 超级管理员拥有最高权限，请谨慎分配</p>
                        </div>
                    </div>
                    <div id="passwordSection">
                        <label for="userPassword" class="block text-sm font-medium text-gray-700 mb-1">密码 <span class="text-red-500">*</span></label>
                        <input type="password" id="userPassword" required minlength="6"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                    </div>
                    <div class="flex gap-3 pt-4">
                        <button type="button" id="cancelUserEdit" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                            取消
                        </button>
                        <button type="submit" class="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                            保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 加载指示器 -->
    <div id="loadingIndicator" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-40" style="display: none;">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <span class="text-gray-700">加载中...</span>
        </div>
    </div>

    <!-- 引入模块化JS文件 -->
    <script src="../js/config.js"></script>
    <script src="../js/utils.js"></script>
    <script src="../js/permission-manager.js"></script>
    <script>
        // 用户管理应用类
        class UserManagementApp {
            constructor() {
                this.currentPage = 1;
                this.pageSize = 20;
                this.totalCount = 0;
            }

            async init() {
                // 显示当前用户信息
                this.showCurrentUserInfo();

                // 检查权限
                if (!await this.checkPermissions()) {
                    return;
                }

                this.bindEvents();
                this.loadUsers();
                this.updateUIByPermissions();
            }

            showCurrentUserInfo() {
                const currentUser = AV.User.current();
                if (currentUser) {
                    const roles = currentUser.get('roles') || [];
                    console.log('当前用户信息:');
                    console.log('- 用户名:', currentUser.get('username'));
                    console.log('- 真实姓名:', currentUser.get('realName'));
                    console.log('- 角色:', roles);
                    console.log('- 角色类型:', typeof roles);
                    console.log('- 是否数组:', Array.isArray(roles));
                    console.log('- 用户ID:', currentUser.id);
                }
            }

            async checkPermissions() {
                if (!window.permissionManager) {
                    console.warn('权限管理器未初始化，使用降级权限检查');
                    return this.checkLegacyPermissions();
                }

                await window.permissionManager.init();

                if (!window.permissionManager.hasPermission('system', 'user_management', 'view')) {
                    this.showAccessDenied();
                    return false;
                }

                return true;
            }

            checkLegacyPermissions() {
                const currentUser = AV.User.current();
                if (!currentUser) {
                    this.showAccessDenied('请先登录');
                    return false;
                }

                const roles = currentUser.get('roles') || [];
                let isAdmin = false;

                if (typeof roles === 'string') {
                    try {
                        const roleArray = JSON.parse(roles);
                        isAdmin = roleArray.includes('admin') || roleArray.includes('super_admin');
                    } catch (e) {
                        isAdmin = roles === 'admin' || roles === 'super_admin';
                    }
                } else if (Array.isArray(roles)) {
                    isAdmin = roles.includes('admin') || roles.includes('super_admin');
                }

                if (!isAdmin) {
                    this.showAccessDenied('您没有用户管理权限');
                    return false;
                }

                return true;
            }

            showAccessDenied(message = '您没有权限访问此页面') {
                document.body.innerHTML = `
                    <div class="min-h-screen bg-gray-50 flex items-center justify-center">
                        <div class="bg-white rounded-lg shadow p-8 text-center max-w-md">
                            <div class="mb-4">
                                <svg class="mx-auto h-12 w-12 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                </svg>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">访问受限</h3>
                            <p class="text-gray-500 mb-4">${message}</p>
                            <a href="index.html" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg">
                                返回管理首页
                            </a>
                        </div>
                    </div>
                `;
            }

            updateUIByPermissions() {
                if (!window.permissionManager) return;

                // 根据权限显示/隐藏操作按钮
                const permissions = [
                    { selector: '#addUserBtn', module: 'system', subModule: 'user_management', action: 'create' },
                    { selector: '.edit-user-btn', module: 'system', subModule: 'user_management', action: 'edit' },
                    { selector: '.delete-user-btn', module: 'system', subModule: 'user_management', action: 'delete' },
                    { selector: '.reset-password-btn', module: 'system', subModule: 'user_management', action: 'edit' }
                ];

                permissions.forEach(({ selector, module, subModule, action }) => {
                    window.permissionManager.toggleElementByPermission(selector, module, subModule, action);
                });
            }

            normalizeRoles(roles) {
                // 标准化角色数据为数组格式
                let roleArray = [];

                if (!roles) {
                    return [];
                }

                if (typeof roles === 'string') {
                    try {
                        // 尝试解析JSON字符串
                        const parsed = JSON.parse(roles);
                        if (Array.isArray(parsed)) {
                            roleArray = parsed;
                        } else {
                            roleArray = [roles]; // 如果解析后不是数组，作为单个角色处理
                        }
                    } catch (e) {
                        // JSON解析失败，作为单个角色处理
                        roleArray = [roles];
                    }
                } else if (Array.isArray(roles)) {
                    roleArray = roles;
                } else {
                    // 其他类型，转换为字符串后作为单个角色处理
                    roleArray = [String(roles)];
                }

                // 过滤掉空值和无效角色
                return roleArray.filter(role => role && typeof role === 'string' && role.trim() !== '');
            }

            formatUserRoles(roles) {
                const roleArray = this.normalizeRoles(roles);

                if (roleArray.length === 0) {
                    return '<span class="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">无角色</span>';
                }

                const roleConfig = {
                    'super_admin': { label: '超级管理员', color: 'bg-purple-100 text-purple-800' },
                    'admin': { label: '管理员', color: 'bg-red-100 text-red-800' },
                    'user': { label: '普通用户', color: 'bg-green-100 text-green-800' }
                };

                return roleArray.map(role => {
                    const config = roleConfig[role] || { label: role, color: 'bg-gray-100 text-gray-800' };
                    return `<span class="px-2 py-1 text-xs font-medium rounded-full ${config.color} mr-1">${config.label}</span>`;
                }).join('');
            }

            bindEvents() {
                // 搜索按钮
                document.getElementById('searchBtn').addEventListener('click', () => {
                    this.currentPage = 1;
                    this.loadUsers();
                });

                document.getElementById('fixRolesBtn').addEventListener('click', () => {
                    this.fixUserRoles();
                });

                // 搜索输入框回车
                document.getElementById('searchInput').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.currentPage = 1;
                        this.loadUsers();
                    }
                });

                // 筛选器变化
                document.getElementById('departmentFilter').addEventListener('change', () => {
                    this.currentPage = 1;
                    this.loadUsers();
                });

                document.getElementById('roleFilter').addEventListener('change', () => {
                    this.currentPage = 1;
                    this.loadUsers();
                });

                // 分页按钮
                document.getElementById('prevPageBtn').addEventListener('click', () => {
                    if (this.currentPage > 1) {
                        this.currentPage--;
                        this.loadUsers();
                    }
                });

                document.getElementById('nextPageBtn').addEventListener('click', () => {
                    const maxPage = Math.ceil(this.totalCount / this.pageSize);
                    if (this.currentPage < maxPage) {
                        this.currentPage++;
                        this.loadUsers();
                    }
                });

                // 添加用户按钮
                document.getElementById('addUserBtn').addEventListener('click', () => {
                    this.showUserModal();
                });

                // 用户弹窗事件
                document.getElementById('closeUserModal').addEventListener('click', () => {
                    document.getElementById('userModal').style.display = 'none';
                });

                document.getElementById('cancelUserEdit').addEventListener('click', () => {
                    document.getElementById('userModal').style.display = 'none';
                });

                // 用户表单提交
                document.getElementById('userForm').addEventListener('submit', async (e) => {
                    e.preventDefault();
                    await this.saveUser();
                });
            }

            async loadUsers() {
                try {
                    this.showLoading();

                    const query = new AV.Query('_User');

                    // 应用搜索条件
                    const searchText = document.getElementById('searchInput').value.trim();
                    if (searchText) {
                        const usernameQuery = new AV.Query('_User');
                        usernameQuery.contains('username', searchText);

                        const realNameQuery = new AV.Query('_User');
                        realNameQuery.contains('realName', searchText);

                        query._orQuery([usernameQuery, realNameQuery]);
                    }

                    // 应用部门筛选
                    const departmentFilter = document.getElementById('departmentFilter').value;
                    if (departmentFilter) {
                        query.equalTo('department', departmentFilter);
                    }

                    // 应用角色筛选
                    const roleFilter = document.getElementById('roleFilter').value;
                    if (roleFilter) {
                        query.contains('roles', roleFilter);
                    }

                    // 排序和分页
                    query.descending('createdAt');
                    query.limit(this.pageSize);
                    query.skip((this.currentPage - 1) * this.pageSize);

                    const results = await query.find();
                    const total = await query.count();

                    this.totalCount = total;
                    this.renderUserList(results);
                    this.updatePagination();

                    this.hideLoading();
                } catch (error) {
                    console.error('加载用户列表失败:', error);
                    console.error('错误堆栈:', error.stack);
                    this.hideLoading();

                    // 显示更详细的错误信息
                    const tbody = document.getElementById('userTableBody');
                    if (tbody) {
                        tbody.innerHTML = `
                            <tr>
                                <td colspan="6" class="px-6 py-4 text-center text-red-500">
                                    <div class="space-y-2">
                                        <div>加载用户列表失败: ${error.message}</div>
                                        <div class="text-xs text-gray-500">请查看浏览器控制台获取详细错误信息</div>
                                    </div>
                                </td>
                            </tr>
                        `;
                    }

                    alert('加载用户列表失败: ' + error.message);
                }
            }

            renderUserList(users) {
                const tbody = document.getElementById('userTableBody');

                if (users.length === 0) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="7" class="px-6 py-8 text-center text-gray-500">
                                暂无用户数据
                            </td>
                        </tr>
                    `;
                    return;
                }

                const html = users.map(user => {
                    const roles = user.get('roles') || [];

                    // 调试信息
                    console.log(`用户 ${user.get('username')} 的角色数据:`, roles, '类型:', typeof roles, '是否数组:', Array.isArray(roles));

                    // 检查是否需要修复
                    const normalizedRoles = this.normalizeRoles(roles);
                    if (JSON.stringify(roles) !== JSON.stringify(normalizedRoles)) {
                        console.log(`用户 ${user.get('username')} 需要修复角色数据:`, roles, '->', normalizedRoles);
                    }

                    const roleLabels = this.formatUserRoles(roles);

                    return `
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">${user.get('realName') || '-'}</div>
                                <div class="text-sm text-gray-500">${user.get('username')}</div>
                                <div class="text-sm text-gray-500">${user.get('phone') || '-'}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${user.get('department') || '-'}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                ${roleLabels}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${user.createdAt.toLocaleDateString()}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${user.updatedAt.toLocaleDateString()}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                                    正常
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="userApp.editUser('${user.id}')"
                                            class="text-blue-600 hover:text-blue-900">编辑</button>
                                    <button onclick="userApp.resetPassword('${user.id}')"
                                            class="text-yellow-600 hover:text-yellow-900">重置密码</button>
                                    <button onclick="userApp.deleteUser('${user.id}')"
                                            class="text-red-600 hover:text-red-900">删除</button>
                                </div>
                            </td>
                        </tr>
                    `;
                }).join('');

                tbody.innerHTML = html;
            }

            updatePagination() {
                const start = (this.currentPage - 1) * this.pageSize + 1;
                const end = Math.min(this.currentPage * this.pageSize, this.totalCount);

                document.getElementById('pageInfo').textContent = `${start}-${end}`;
                document.getElementById('totalCount').textContent = this.totalCount;

                // 更新分页按钮状态
                document.getElementById('prevPageBtn').disabled = this.currentPage <= 1;
                const maxPage = Math.ceil(this.totalCount / this.pageSize);
                document.getElementById('nextPageBtn').disabled = this.currentPage >= maxPage;
            }

            showUserModal(userId = null) {
                const modal = document.getElementById('userModal');
                const title = document.getElementById('userModalTitle');
                const form = document.getElementById('userForm');
                const passwordSection = document.getElementById('passwordSection');

                form.reset();
                document.getElementById('editUserId').value = userId || '';

                if (userId) {
                    title.textContent = '编辑用户';
                    passwordSection.style.display = 'none';
                    document.getElementById('userPassword').required = false;
                    this.loadUserForEdit(userId);
                } else {
                    title.textContent = '添加用户';
                    passwordSection.style.display = 'block';
                    document.getElementById('userPassword').required = true;
                }

                modal.style.display = 'flex';
            }

            async loadUserForEdit(userId) {
                try {
                    const query = new AV.Query('_User');
                    const user = await query.get(userId);

                    document.getElementById('userUsername').value = user.get('username');
                    document.getElementById('userRealName').value = user.get('realName') || '';
                    document.getElementById('userPhone').value = user.get('phone') || '';
                    document.getElementById('userDepartment').value = user.get('department') || '';

                    // 设置角色
                    const roles = user.get('roles') || [];
                    document.getElementById('roleSuperAdmin').checked = roles.includes('super_admin');
                    document.getElementById('roleAdmin').checked = roles.includes('admin');
                    document.getElementById('roleUser').checked = roles.includes('user');

                } catch (error) {
                    console.error('加载用户数据失败:', error);
                    alert('加载用户数据失败: ' + error.message);
                }
            }

            async saveUser() {
                try {
                    const userId = document.getElementById('editUserId').value;
                    const action = userId ? 'edit' : 'create';

                    // 权限检查
                    if (window.permissionManager && !window.permissionManager.checkPermissionMiddleware('system', 'user_management', action)) {
                        return;
                    }

                    const username = document.getElementById('userUsername').value;
                    const realName = document.getElementById('userRealName').value;
                    const phone = document.getElementById('userPhone').value;
                    const department = document.getElementById('userDepartment').value;
                    const password = document.getElementById('userPassword').value;

                    // 获取角色
                    const roles = [];
                    if (document.getElementById('roleSuperAdmin').checked) roles.push('super_admin');
                    if (document.getElementById('roleAdmin').checked) roles.push('admin');
                    if (document.getElementById('roleUser').checked) roles.push('user');

                    let user;
                    if (userId) {
                        // 编辑用户
                        console.log('编辑用户，ID:', userId, '角色:', roles);

                        // 尝试使用master key进行操作
                        try {
                            user = AV.Object.createWithoutData('_User', userId);
                            user.set('realName', realName);
                            user.set('phone', phone);
                            user.set('department', department);
                            user.set('roles', roles);

                            // 尝试使用master key保存
                            await user.save(null, { useMasterKey: true });
                        } catch (masterKeyError) {
                            console.log('Master key保存失败，尝试普通保存:', masterKeyError.message);

                            // 如果master key失败，尝试普通保存
                            user = AV.Object.createWithoutData('_User', userId);
                            user.set('realName', realName);
                            user.set('phone', phone);
                            user.set('department', department);
                            user.set('roles', roles);
                            await user.save();
                        }
                    } else {
                        // 新建用户
                        console.log('创建新用户，用户名:', username, '角色:', roles);

                        user = new AV.User();
                        user.setUsername(username);
                        user.setPassword(password);
                        user.set('realName', realName);
                        user.set('phone', phone);
                        user.set('department', department);
                        user.set('roles', roles);

                        try {
                            await user.signUp();
                        } catch (signUpError) {
                            console.error('用户注册失败:', signUpError);
                            throw signUpError;
                        }
                    }

                    alert(userId ? '用户更新成功' : '用户创建成功');
                    document.getElementById('userModal').style.display = 'none';
                    this.loadUsers();

                } catch (error) {
                    console.error('保存用户失败:', error);
                    console.error('错误详情:', {
                        code: error.code,
                        message: error.message,
                        rawMessage: error.rawMessage
                    });

                    let errorMessage = '保存用户失败: ' + error.message;

                    // 针对不同错误提供具体建议
                    if (error.code === 403 || error.message.includes('Forbidden')) {
                        errorMessage += '\n\n可能的原因：\n1. 当前用户权限不足\n2. LeanCloud ACL权限设置限制\n3. 需要超级管理员权限';
                    } else if (error.code === 202 || error.message.includes('username')) {
                        errorMessage += '\n\n用户名可能已存在，请使用其他用户名';
                    }

                    alert(errorMessage);
                }
            }

            async editUser(userId) {
                this.showUserModal(userId);
            }

            async resetPassword(userId) {
                if (!confirm('确定要重置该用户的密码吗？新密码将设置为 123456')) {
                    return;
                }

                try {
                    // 注意：在实际应用中，重置密码需要特殊的权限和安全措施
                    alert('密码重置功能需要后端支持，请联系系统管理员');
                } catch (error) {
                    console.error('重置密码失败:', error);
                    alert('重置密码失败: ' + error.message);
                }
            }

            async deleteUser(userId) {
                // 权限检查
                if (window.permissionManager && !window.permissionManager.checkPermissionMiddleware('system', 'user_management', 'delete')) {
                    return;
                }

                if (!confirm('确定要删除该用户吗？此操作不可恢复！')) {
                    return;
                }

                try {
                    const user = AV.Object.createWithoutData('_User', userId);
                    await user.destroy();
                    alert('用户删除成功');
                    this.loadUsers();
                } catch (error) {
                    console.error('删除用户失败:', error);
                    alert('删除用户失败: ' + error.message);
                }
            }

            async fixUserRoles() {
                if (!confirm('确定要修复所有用户的角色数据吗？这将标准化所有用户的角色格式。')) {
                    return;
                }

                try {
                    this.showLoading();

                    // 获取所有用户
                    const query = new AV.Query('_User');
                    query.limit(1000); // 限制数量，避免一次性加载太多
                    const users = await query.find();

                    let fixedCount = 0;
                    let errorCount = 0;

                    for (const user of users) {
                        try {
                            const currentRoles = user.get('roles');
                            const normalizedRoles = this.normalizeRoles(currentRoles);

                            // 如果角色数据需要修复
                            if (JSON.stringify(currentRoles) !== JSON.stringify(normalizedRoles)) {
                                user.set('roles', normalizedRoles);
                                await user.save();
                                fixedCount++;
                                console.log(`修复用户 ${user.get('username')} 的角色数据:`, currentRoles, '->', normalizedRoles);
                            }
                        } catch (error) {
                            console.error(`修复用户 ${user.get('username')} 失败:`, error);
                            errorCount++;
                        }
                    }

                    this.hideLoading();

                    const message = `角色数据修复完成！
修复用户数: ${fixedCount}
错误数: ${errorCount}
总用户数: ${users.length}`;

                    alert(message);
                    console.log(message);

                    // 重新加载用户列表
                    this.loadUsers();

                } catch (error) {
                    this.hideLoading();
                    console.error('修复角色数据失败:', error);
                    alert('修复角色数据失败: ' + error.message);
                }
            }

            showLoading() {
                document.getElementById('loadingIndicator').style.display = 'flex';
            }

            hideLoading() {
                document.getElementById('loadingIndicator').style.display = 'none';
            }
        }

        // 全局变量
        let userApp;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            function initApp() {
                if (typeof AV !== 'undefined') {
                    userApp = new UserManagementApp();
                    userApp.init();
                } else {
                    setTimeout(initApp, 100);
                }
            }
            initApp();
        });
    </script>
</body>
</html>