# 后台管理系统配置说明

## 问题说明

由于LeanCloud的安全机制，默认情况下普通用户（包括管理员）无法直接查询 `_User` 表。这导致后台管理系统无法获取完整的用户列表。

## 解决方案

### 方案一：LeanCloud控制台权限配置（推荐）

1. **登录LeanCloud控制台**
   - 访问 https://console.leancloud.cn/
   - 使用您的LeanCloud账号登录

2. **进入应用管理**
   - 选择您的应用
   - 进入"数据存储"模块

3. **配置_User表权限**
   - 点击 `_User` 表
   - 切换到"权限"标签
   - 在"find"权限中，选择以下之一：
     - **公开读取**：允许所有用户查询（不推荐）
     - **指定角色**：只允许admin角色查询（推荐）
     - **登录用户**：允许所有登录用户查询

4. **推荐配置**
   ```
   find权限：仅限admin角色
   ```

### 方案二：部署云函数

1. **准备云函数代码**
   - 项目中已包含 `cloud-functions.js` 文件
   - 包含用户管理所需的所有云函数

2. **创建云引擎应用**
   - 在LeanCloud控制台创建云引擎
   - 选择Node.js运行环境

3. **部署云函数**
   - 将 `cloud-functions.js` 上传到云引擎
   - 部署并启动云函数服务

4. **云函数列表**
   - `getUserList`：获取所有用户列表
   - `updateUser`：更新用户信息
   - `searchUser`：搜索特定用户
   - `getUserStats`：获取用户统计
   - `initAdmin`：初始化管理员账户

### 方案三：当前可用功能

即使没有完整配置，您仍可以使用以下功能：

1. **管理当前用户**
   - 编辑自己的个人信息
   - 设置联系电话和部门

2. **搜索特定用户**
   - 通过用户名搜索用户
   - 编辑找到的用户信息

## 使用说明

### 管理员权限

确保您的账户具有管理员权限：
```javascript
// 用户的roles字段应包含"admin"
roles: ["admin"]
```

### 用户信息字段

系统管理的用户信息包括：
- `username`：用户名（不可修改）
- `realName`：真实姓名
- `phone`：联系电话
- `department`：所属部门
- `roles`：用户角色数组

### 角色说明

- **admin**：管理员，可以管理用户和系统
- **engineer**：工程师，可以处理工单
- **无角色**：普通用户，只能提交报修

## 测试步骤

1. **访问后台管理**
   ```
   http://localhost:8080/admin.html
   ```

2. **使用管理员账号登录**
   - 确保账号具有admin角色

3. **测试功能**
   - 点击"编辑我的信息"管理当前用户
   - 使用搜索功能查找特定用户
   - 编辑用户信息并保存

## 常见问题

### Q: 提示"权限不足"怎么办？
A: 需要在LeanCloud控制台配置_User表的查询权限，或部署云函数。

### Q: 云函数返回400错误？
A: 云函数未部署或部署失败，请检查云引擎配置。

### Q: 只能编辑自己的信息？
A: 这是LeanCloud的安全限制，需要配置权限或使用云函数。

### Q: 如何添加新的管理员？
A: 可以通过以下方式：
1. 在LeanCloud控制台直接修改用户的roles字段
2. 使用现有管理员账号编辑其他用户的角色
3. 使用云函数的initAdmin功能

## 安全建议

1. **最小权限原则**
   - 只给必要的用户分配admin角色
   - 定期审查用户权限

2. **数据保护**
   - 不要在前端暴露敏感信息
   - 使用HTTPS传输数据

3. **访问控制**
   - 定期更换管理员密码
   - 监控管理员操作日志

## 技术支持

如果遇到配置问题，请：
1. 检查LeanCloud控制台的错误日志
2. 确认应用ID和密钥配置正确
3. 验证网络连接和防火墙设置

---

**注意**：本系统为演示项目，生产环境使用时请根据实际需求调整安全配置。
