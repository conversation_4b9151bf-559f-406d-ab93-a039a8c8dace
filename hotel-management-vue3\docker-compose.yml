version: '3.8'

services:
  # 主应用服务
  app:
    build:
      context: .
      target: production
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NUXT_PUBLIC_APP_ENV=production
    env_file:
      - .env
    volumes:
      - ./uploads:/app/uploads
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - hotel-network

  # Redis 缓存服务
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    networks:
      - hotel-network

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./uploads:/var/www/uploads:ro
    depends_on:
      - app
    restart: unless-stopped
    networks:
      - hotel-network

  # 数据库备份服务（可选）
  backup:
    image: alpine:latest
    volumes:
      - ./backups:/backups
      - redis_data:/data:ro
    command: |
      sh -c "
        apk add --no-cache tar gzip
        while true; do
          tar -czf /backups/backup-$$(date +%Y%m%d_%H%M%S).tar.gz /data
          find /backups -name '*.tar.gz' -mtime +7 -delete
          sleep 86400
        done
      "
    restart: unless-stopped
    networks:
      - hotel-network

volumes:
  redis_data:
    driver: local

networks:
  hotel-network:
    driver: bridge
