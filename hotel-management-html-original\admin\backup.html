<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据备份 - 系统管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <style>
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .btn-fix {
            touch-action: manipulation;
        }
        .progress-bar {
            transition: width 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="index.html" class="text-blue-600 hover:text-blue-800 mr-4">
                        ← 返回管理首页
                    </a>
                    <h1 class="text-xl font-semibold text-gray-900">数据备份</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="createBackupBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                        创建备份
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 备份概览 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">总备份数</dt>
                            <dd class="text-lg font-medium text-gray-900" id="totalBackups">0</dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">最近备份</dt>
                            <dd class="text-lg font-medium text-gray-900" id="lastBackup">-</dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">备份大小</dt>
                            <dd class="text-lg font-medium text-gray-900" id="backupSize">0 MB</dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">自动备份</dt>
                            <dd class="text-lg font-medium text-gray-900" id="autoBackupStatus">已启用</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- 备份设置 -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-medium text-gray-900">备份设置</h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">备份类型</label>
                        <select id="backupType" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                            <option value="full">完整备份</option>
                            <option value="incremental">增量备份</option>
                            <option value="differential">差异备份</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">备份频率</label>
                        <select id="backupFrequency" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                            <option value="daily">每日</option>
                            <option value="weekly">每周</option>
                            <option value="monthly">每月</option>
                            <option value="manual">手动</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">保留天数</label>
                        <input type="number" id="retentionDays" value="30" min="1" max="365"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                    </div>
                </div>
                <div class="mt-6 space-y-4">
                    <label class="flex items-center">
                        <input type="checkbox" id="enableAutoBackup" checked class="rounded">
                        <span class="ml-2 text-sm text-gray-700">启用自动备份</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" id="enableCompression" checked class="rounded">
                        <span class="ml-2 text-sm text-gray-700">启用压缩</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" id="enableEncryption" class="rounded">
                        <span class="ml-2 text-sm text-gray-700">启用加密</span>
                    </label>
                </div>
                <div class="mt-6">
                    <button id="saveBackupSettings" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                        保存设置
                    </button>
                </div>
            </div>
        </div>

        <!-- 备份进度 -->
        <div id="backupProgress" class="bg-white rounded-lg shadow mb-6 hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-medium text-gray-900">备份进度</h2>
            </div>
            <div class="p-6">
                <div class="mb-4">
                    <div class="flex justify-between text-sm text-gray-600 mb-2">
                        <span id="progressText">正在备份...</span>
                        <span id="progressPercent">0%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div id="progressBar" class="bg-blue-500 h-2 rounded-full progress-bar" style="width: 0%"></div>
                    </div>
                </div>
                <div id="progressDetails" class="text-sm text-gray-500">
                    <!-- 进度详情将在这里显示 -->
                </div>
            </div>
        </div>

        <!-- 备份列表 -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-medium text-gray-900">备份历史</h2>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备份名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">大小</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody id="backupTableBody" class="bg-white divide-y divide-gray-200">
                        <!-- 备份列表将在这里动态加载 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 恢复确认弹窗 -->
    <div id="restoreModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 fade-in">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-800">确认恢复</h2>
            </div>
            <div class="p-6">
                <div class="mb-4">
                    <p class="text-gray-700">您确定要恢复到此备份吗？</p>
                    <p class="text-sm text-red-600 mt-2">⚠️ 此操作将覆盖当前数据，请谨慎操作！</p>
                </div>
                <div id="restoreBackupInfo" class="bg-gray-50 p-3 rounded mb-4">
                    <!-- 备份信息将在这里显示 -->
                </div>
                <div class="flex gap-3">
                    <button id="cancelRestore" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                        取消
                    </button>
                    <button id="confirmRestore" class="flex-1 bg-red-500 hover:bg-red-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                        确认恢复
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载指示器 -->
    <div id="loadingIndicator" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-40" style="display: none;">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <span class="text-gray-700">处理中...</span>
        </div>
    </div>

    <!-- 引入模块化JS文件 -->
    <script src="../js/config.js"></script>
    <script src="../js/permission-manager.js"></script>
    <script>
        // 数据备份管理应用类
        class BackupManagementApp {
            constructor() {
                this.selectedBackupId = null;
                this.backupInProgress = false;
            }

            init() {
                this.bindEvents();
                this.loadBackupList();
                this.loadBackupSettings();
                this.createSampleBackups(); // 创建示例备份记录
            }

            bindEvents() {
                // 创建备份按钮
                document.getElementById('createBackupBtn').addEventListener('click', () => {
                    this.createBackup();
                });

                // 保存设置按钮
                document.getElementById('saveBackupSettings').addEventListener('click', () => {
                    this.saveBackupSettings();
                });

                // 恢复弹窗事件
                document.getElementById('cancelRestore').addEventListener('click', () => {
                    document.getElementById('restoreModal').style.display = 'none';
                });

                document.getElementById('confirmRestore').addEventListener('click', () => {
                    this.performRestore();
                });
            }

            async createSampleBackups() {
                try {
                    // 检查是否已有备份记录
                    const query = new AV.Query('BackupRecord');
                    const count = await query.count();

                    if (count > 0) return; // 已有数据，不创建示例

                    const sampleBackups = [
                        {
                            name: 'backup_20241206_001',
                            type: 'full',
                            size: 15.6,
                            status: 'completed',
                            description: '完整系统备份',
                            compressed: true,
                            encrypted: false
                        },
                        {
                            name: 'backup_20241205_001',
                            type: 'incremental',
                            size: 3.2,
                            status: 'completed',
                            description: '增量备份',
                            compressed: true,
                            encrypted: false
                        },
                        {
                            name: 'backup_20241204_001',
                            type: 'full',
                            size: 14.8,
                            status: 'completed',
                            description: '完整系统备份',
                            compressed: true,
                            encrypted: true
                        }
                    ];

                    for (const backupData of sampleBackups) {
                        const backup = new AV.Object('BackupRecord');
                        Object.keys(backupData).forEach(key => {
                            backup.set(key, backupData[key]);
                        });
                        await backup.save();
                    }

                    console.log('示例备份记录创建完成');
                } catch (error) {
                    console.error('创建示例备份记录失败:', error);
                }
            }

            async loadBackupList() {
                try {
                    this.showLoading();

                    const query = new AV.Query('BackupRecord');
                    query.descending('createdAt');
                    const backups = await query.find();

                    this.renderBackupList(backups);
                    this.updateBackupStats(backups);

                    this.hideLoading();
                } catch (error) {
                    console.error('加载备份列表失败:', error);
                    this.hideLoading();

                    // 如果表不存在，显示空列表
                    if (error.message.includes('Class or object doesn\'t exists')) {
                        this.renderBackupList([]);
                        this.updateBackupStats([]);
                    } else {
                        alert('加载备份列表失败: ' + error.message);
                    }
                }
            }

            renderBackupList(backups) {
                const tbody = document.getElementById('backupTableBody');

                if (backups.length === 0) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="6" class="px-6 py-8 text-center text-gray-500">
                                暂无备份记录
                            </td>
                        </tr>
                    `;
                    return;
                }

                const html = backups.map(backup => {
                    const status = backup.get('status');
                    const statusClass = this.getStatusClass(status);
                    const statusText = this.getStatusText(status);
                    const typeText = this.getTypeText(backup.get('type'));

                    return `
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">${backup.get('name')}</div>
                                <div class="text-sm text-gray-500">${backup.get('description') || ''}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${typeText}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${backup.get('size')} MB
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${backup.createdAt.toLocaleString()}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium rounded-full ${statusClass}">
                                    ${statusText}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="backupApp.downloadBackup('${backup.id}')"
                                            class="text-blue-600 hover:text-blue-900">下载</button>
                                    <button onclick="backupApp.showRestoreModal('${backup.id}')"
                                            class="text-green-600 hover:text-green-900">恢复</button>
                                    <button onclick="backupApp.deleteBackup('${backup.id}')"
                                            class="text-red-600 hover:text-red-900">删除</button>
                                </div>
                            </td>
                        </tr>
                    `;
                }).join('');

                tbody.innerHTML = html;
            }

            updateBackupStats(backups) {
                const totalBackups = backups.length;
                const totalSize = backups.reduce((sum, backup) => sum + (backup.get('size') || 0), 0);
                const lastBackup = backups.length > 0 ? backups[0].createdAt.toLocaleDateString() : '-';

                document.getElementById('totalBackups').textContent = totalBackups;
                document.getElementById('backupSize').textContent = totalSize.toFixed(1) + ' MB';
                document.getElementById('lastBackup').textContent = lastBackup;
            }

            getStatusClass(status) {
                const classMap = {
                    'completed': 'bg-green-100 text-green-800',
                    'failed': 'bg-red-100 text-red-800',
                    'in_progress': 'bg-yellow-100 text-yellow-800'
                };
                return classMap[status] || 'bg-gray-100 text-gray-800';
            }

            getStatusText(status) {
                const textMap = {
                    'completed': '已完成',
                    'failed': '失败',
                    'in_progress': '进行中'
                };
                return textMap[status] || status;
            }

            getTypeText(type) {
                const textMap = {
                    'full': '完整备份',
                    'incremental': '增量备份',
                    'differential': '差异备份'
                };
                return textMap[type] || type;
            }

            async createBackup() {
                if (this.backupInProgress) {
                    alert('备份正在进行中，请稍候...');
                    return;
                }

                try {
                    this.backupInProgress = true;
                    this.showBackupProgress();

                    const backupType = document.getElementById('backupType').value;
                    const enableCompression = document.getElementById('enableCompression').checked;
                    const enableEncryption = document.getElementById('enableEncryption').checked;

                    // 模拟备份过程
                    await this.simulateBackupProcess();

                    // 创建备份记录
                    const backup = new AV.Object('BackupRecord');
                    backup.set('name', `backup_${new Date().toISOString().split('T')[0]}_${Date.now()}`);
                    backup.set('type', backupType);
                    backup.set('size', Math.random() * 20 + 5); // 模拟大小
                    backup.set('status', 'completed');
                    backup.set('description', `${this.getTypeText(backupType)} - ${new Date().toLocaleString()}`);
                    backup.set('compressed', enableCompression);
                    backup.set('encrypted', enableEncryption);

                    await backup.save();

                    this.hideBackupProgress();
                    this.backupInProgress = false;
                    alert('备份创建成功！');
                    this.loadBackupList();
                } catch (error) {
                    console.error('创建备份失败:', error);
                    this.hideBackupProgress();
                    this.backupInProgress = false;
                    alert('创建备份失败: ' + error.message);
                }
            }

            async simulateBackupProcess() {
                const steps = [
                    { text: '正在准备备份...', progress: 10 },
                    { text: '正在备份用户数据...', progress: 30 },
                    { text: '正在备份库存数据...', progress: 50 },
                    { text: '正在备份工作日志...', progress: 70 },
                    { text: '正在压缩备份文件...', progress: 90 },
                    { text: '备份完成', progress: 100 }
                ];

                for (const step of steps) {
                    this.updateBackupProgress(step.text, step.progress);
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }

            showBackupProgress() {
                document.getElementById('backupProgress').classList.remove('hidden');
                this.updateBackupProgress('正在初始化...', 0);
            }

            hideBackupProgress() {
                document.getElementById('backupProgress').classList.add('hidden');
            }

            updateBackupProgress(text, progress) {
                document.getElementById('progressText').textContent = text;
                document.getElementById('progressPercent').textContent = progress + '%';
                document.getElementById('progressBar').style.width = progress + '%';

                const details = document.getElementById('progressDetails');
                details.innerHTML += `<div>${new Date().toLocaleTimeString()}: ${text}</div>`;
                details.scrollTop = details.scrollHeight;
            }

            async showRestoreModal(backupId) {
                try {
                    const query = new AV.Query('BackupRecord');
                    const backup = await query.get(backupId);

                    this.selectedBackupId = backupId;

                    const info = document.getElementById('restoreBackupInfo');
                    info.innerHTML = `
                        <div><strong>备份名称:</strong> ${backup.get('name')}</div>
                        <div><strong>备份类型:</strong> ${this.getTypeText(backup.get('type'))}</div>
                        <div><strong>备份大小:</strong> ${backup.get('size')} MB</div>
                        <div><strong>创建时间:</strong> ${backup.createdAt.toLocaleString()}</div>
                    `;

                    document.getElementById('restoreModal').style.display = 'flex';
                } catch (error) {
                    console.error('加载备份信息失败:', error);
                    alert('加载备份信息失败: ' + error.message);
                }
            }

            async performRestore() {
                if (!this.selectedBackupId) return;

                try {
                    this.showLoading();
                    document.getElementById('restoreModal').style.display = 'none';

                    // 模拟恢复过程
                    await new Promise(resolve => setTimeout(resolve, 3000));

                    this.hideLoading();
                    alert('数据恢复成功！');
                    this.selectedBackupId = null;
                } catch (error) {
                    console.error('数据恢复失败:', error);
                    this.hideLoading();
                    alert('数据恢复失败: ' + error.message);
                }
            }

            downloadBackup(backupId) {
                // 实现备份下载功能
                alert('备份下载功能开发中...');
            }

            async deleteBackup(backupId) {
                if (!confirm('确定要删除此备份吗？此操作不可恢复！')) {
                    return;
                }

                try {
                    const backup = AV.Object.createWithoutData('BackupRecord', backupId);
                    await backup.destroy();
                    alert('备份删除成功');
                    this.loadBackupList();
                } catch (error) {
                    console.error('删除备份失败:', error);
                    alert('删除备份失败: ' + error.message);
                }
            }

            async loadBackupSettings() {
                try {
                    const query = new AV.Query('BackupSettings');
                    const settings = await query.first();

                    if (settings) {
                        document.getElementById('backupType').value = settings.get('type') || 'full';
                        document.getElementById('backupFrequency').value = settings.get('frequency') || 'daily';
                        document.getElementById('retentionDays').value = settings.get('retentionDays') || 30;
                        document.getElementById('enableAutoBackup').checked = settings.get('autoBackup') !== false;
                        document.getElementById('enableCompression').checked = settings.get('compression') !== false;
                        document.getElementById('enableEncryption').checked = settings.get('encryption') === true;
                    }
                } catch (error) {
                    console.error('加载备份设置失败:', error);
                }
            }

            async saveBackupSettings() {
                try {
                    const settings = {
                        type: document.getElementById('backupType').value,
                        frequency: document.getElementById('backupFrequency').value,
                        retentionDays: parseInt(document.getElementById('retentionDays').value),
                        autoBackup: document.getElementById('enableAutoBackup').checked,
                        compression: document.getElementById('enableCompression').checked,
                        encryption: document.getElementById('enableEncryption').checked
                    };

                    // 查找现有设置
                    const query = new AV.Query('BackupSettings');
                    let settingsObj = await query.first();

                    if (!settingsObj) {
                        settingsObj = new AV.Object('BackupSettings');
                    }

                    Object.keys(settings).forEach(key => {
                        settingsObj.set(key, settings[key]);
                    });

                    await settingsObj.save();
                    alert('备份设置保存成功');
                } catch (error) {
                    console.error('保存备份设置失败:', error);
                    alert('保存备份设置失败: ' + error.message);
                }
            }

            showLoading() {
                document.getElementById('loadingIndicator').style.display = 'flex';
            }

            hideLoading() {
                document.getElementById('loadingIndicator').style.display = 'none';
            }
        }

        // 全局变量
        let backupApp;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            function initApp() {
                if (typeof AV !== 'undefined') {
                    backupApp = new BackupManagementApp();
                    backupApp.init();
                } else {
                    setTimeout(initApp, 100);
                }
            }
            initApp();
        });
    </script>
</body>
</html>