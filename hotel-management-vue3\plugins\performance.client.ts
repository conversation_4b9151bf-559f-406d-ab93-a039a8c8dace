// plugins/performance.client.ts
export default defineNuxtPlugin(() => {
  // 页面性能监控
  if (typeof window !== 'undefined' && 'performance' in window) {
    // 监控页面加载性能
    window.addEventListener('load', () => {
      setTimeout(() => {
        const perfData = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
        
        if (perfData) {
          const metrics = {
            // DNS查询时间
            dnsTime: perfData.domainLookupEnd - perfData.domainLookupStart,
            // TCP连接时间
            tcpTime: perfData.connectEnd - perfData.connectStart,
            // 请求响应时间
            requestTime: perfData.responseEnd - perfData.requestStart,
            // DOM解析时间
            domParseTime: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
            // 页面完全加载时间
            loadTime: perfData.loadEventEnd - perfData.loadEventStart,
            // 首次内容绘制时间
            fcp: getFCP(),
            // 最大内容绘制时间
            lcp: getLCP()
          }
          
          console.log('页面性能指标:', metrics)
          
          // 发送性能数据到监控服务
          sendPerformanceData(metrics)
        }
      }, 0)
    })

    // 监控资源加载性能
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'resource') {
          const resourceEntry = entry as PerformanceResourceTiming
          
          // 监控慢资源
          if (resourceEntry.duration > 1000) {
            console.warn('慢资源加载:', {
              name: resourceEntry.name,
              duration: resourceEntry.duration,
              size: resourceEntry.transferSize
            })
          }
        }
      }
    })
    
    observer.observe({ entryTypes: ['resource'] })
  }
})

// 获取首次内容绘制时间
function getFCP(): number {
  const fcpEntry = performance.getEntriesByName('first-contentful-paint')[0]
  return fcpEntry ? fcpEntry.startTime : 0
}

// 获取最大内容绘制时间
function getLCP(): number {
  return new Promise((resolve) => {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const lastEntry = entries[entries.length - 1]
      resolve(lastEntry.startTime)
      observer.disconnect()
    })
    
    observer.observe({ entryTypes: ['largest-contentful-paint'] })
    
    // 超时处理
    setTimeout(() => {
      observer.disconnect()
      resolve(0)
    }, 5000)
  })
}

// 发送性能数据
function sendPerformanceData(metrics: any) {
  // 这里可以发送到性能监控服务
  // 如 Google Analytics, New Relic, DataDog 等
  
  const performanceData = {
    ...metrics,
    url: window.location.href,
    userAgent: navigator.userAgent,
    timestamp: new Date().toISOString(),
    // 设备信息
    deviceInfo: {
      memory: (navigator as any).deviceMemory,
      cores: navigator.hardwareConcurrency,
      connection: (navigator as any).connection?.effectiveType
    }
  }
  
  console.log('性能数据:', performanceData)
  
  // 实际项目中可以发送到监控服务
  // fetch('/api/performance', {
  //   method: 'POST',
  //   headers: { 'Content-Type': 'application/json' },
  //   body: JSON.stringify(performanceData)
  // })
}
