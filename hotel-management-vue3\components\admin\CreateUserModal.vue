<template>
  <div 
    v-if="show" 
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    @click="handleBackdropClick"
  >
    <div 
      class="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-hidden"
      @click.stop
    >
      <!-- 头部 -->
      <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-900">
          {{ isEditing ? '编辑用户' : '新增用户' }}
        </h3>
        <button 
          @click="handleClose"
          class="text-gray-400 hover:text-gray-600"
        >
          <Icon name="mdi:close" size="24" />
        </button>
      </div>

      <!-- 内容 -->
      <div class="px-6 py-4 max-h-[calc(90vh-140px)] overflow-y-auto">
        <form @submit.prevent="handleSubmit">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 左侧：基本信息 -->
            <div class="space-y-6">
              <h4 class="text-md font-medium text-gray-900">基本信息</h4>
              
              <!-- 用户名 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  用户名 *
                </label>
                <input 
                  v-model="formData.username"
                  type="text"
                  required
                  :disabled="loading || isEditing"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
                  placeholder="请输入用户名"
                />
                <div class="mt-1 text-xs text-gray-500">
                  {{ isEditing ? '用户名不可修改' : '用户名用于登录，不可修改' }}
                </div>
              </div>

              <!-- 真实姓名 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  真实姓名 *
                </label>
                <input 
                  v-model="formData.realName"
                  type="text"
                  required
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="请输入真实姓名"
                />
              </div>

              <!-- 手机号码 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  手机号码
                </label>
                <input 
                  v-model="formData.phone"
                  type="tel"
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="请输入手机号码"
                />
              </div>

              <!-- 邮箱 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  邮箱地址
                </label>
                <input 
                  v-model="formData.email"
                  type="email"
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="请输入邮箱地址"
                />
              </div>

              <!-- 密码 -->
              <div v-if="!isEditing">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  初始密码 *
                </label>
                <input 
                  v-model="formData.password"
                  type="password"
                  required
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="请输入初始密码"
                />
                <div class="mt-1 text-xs text-gray-500">
                  密码长度至少8位，建议包含数字和特殊字符
                </div>
              </div>
            </div>

            <!-- 右侧：部门和权限 -->
            <div class="space-y-6">
              <h4 class="text-md font-medium text-gray-900">部门和权限</h4>
              
              <!-- 部门 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  所属部门 *
                </label>
                <select 
                  v-model="formData.department"
                  required
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">请选择部门</option>
                  <option value="前厅部">前厅部</option>
                  <option value="客房部">客房部</option>
                  <option value="餐饮部">餐饮部</option>
                  <option value="工程部">工程部</option>
                  <option value="保安部">保安部</option>
                  <option value="财务部">财务部</option>
                  <option value="人事部">人事部</option>
                  <option value="信息部">信息部</option>
                </select>
              </div>

              <!-- 职位 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  职位
                </label>
                <input 
                  v-model="formData.position"
                  type="text"
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="如：前台接待、工程师、主管等"
                />
              </div>

              <!-- 角色权限 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-3">
                  角色权限 *
                </label>
                <div class="space-y-3">
                  <label class="flex items-start">
                    <input 
                      v-model="formData.roles"
                      type="checkbox"
                      value="user"
                      :disabled="loading"
                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-0.5"
                    />
                    <div class="ml-3">
                      <div class="text-sm font-medium text-gray-700">普通用户</div>
                      <div class="text-xs text-gray-500">可以使用基本功能，如工作日志、报修等</div>
                    </div>
                  </label>
                  
                  <label class="flex items-start">
                    <input 
                      v-model="formData.roles"
                      type="checkbox"
                      value="engineer"
                      :disabled="loading"
                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-0.5"
                    />
                    <div class="ml-3">
                      <div class="text-sm font-medium text-gray-700">工程师</div>
                      <div class="text-xs text-gray-500">可以处理报修工单，管理库存等</div>
                    </div>
                  </label>
                  
                  <label class="flex items-start">
                    <input 
                      v-model="formData.roles"
                      type="checkbox"
                      value="admin"
                      :disabled="loading"
                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-0.5"
                    />
                    <div class="ml-3">
                      <div class="text-sm font-medium text-gray-700">管理员</div>
                      <div class="text-xs text-gray-500">拥有所有权限，可以管理用户和系统设置</div>
                    </div>
                  </label>
                </div>
              </div>

              <!-- 账户状态 -->
              <div v-if="isEditing">
                <label class="block text-sm font-medium text-gray-700 mb-3">
                  账户状态
                </label>
                <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                  <div>
                    <div class="text-sm font-medium text-gray-700">启用账户</div>
                    <div class="text-xs text-gray-500">禁用后用户将无法登录系统</div>
                  </div>
                  <label class="relative inline-flex items-center cursor-pointer">
                    <input 
                      v-model="formData.isActive"
                      type="checkbox" 
                      class="sr-only peer"
                      :disabled="loading"
                    />
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
              </div>

              <!-- 备注 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  备注
                </label>
                <textarea 
                  v-model="formData.notes"
                  rows="3"
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                  placeholder="可以添加用户相关的备注信息..."
                ></textarea>
              </div>
            </div>
          </div>
        </form>
      </div>

      <!-- 底部按钮 -->
      <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
        <button 
          type="button"
          @click="handleClose"
          :disabled="loading"
          class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50"
        >
          取消
        </button>
        <button 
          @click="handleSubmit"
          :disabled="loading || !isFormValid"
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center"
        >
          <Icon v-if="loading" name="mdi:loading" size="16" class="mr-1 animate-spin" />
          {{ loading ? (isEditing ? '更新中...' : '创建中...') : (isEditing ? '更新用户' : '创建用户') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { User } from '~/types'

interface Props {
  show: boolean
  editUser?: User | null
}

const props = withDefaults(defineProps<Props>(), {
  editUser: null
})

const emit = defineEmits<{
  'update:show': [value: boolean]
  created: []
  updated: []
}>()

// 响应式数据
const loading = ref(false)

const formData = reactive({
  username: '',
  realName: '',
  phone: '',
  email: '',
  password: '',
  department: '',
  position: '',
  roles: [] as string[],
  isActive: true,
  notes: ''
})

// 计算属性
const isEditing = computed(() => !!props.editUser)

const isFormValid = computed(() => {
  const hasBasicInfo = formData.username.trim().length > 0 && 
                      formData.realName.trim().length > 0 &&
                      formData.department.length > 0 &&
                      formData.roles.length > 0
  
  if (isEditing.value) {
    return hasBasicInfo
  } else {
    return hasBasicInfo && formData.password.length >= 8
  }
})

// 方法
const resetForm = () => {
  formData.username = ''
  formData.realName = ''
  formData.phone = ''
  formData.email = ''
  formData.password = ''
  formData.department = ''
  formData.position = ''
  formData.roles = ['user']
  formData.isActive = true
  formData.notes = ''
}

const loadEditData = () => {
  if (props.editUser) {
    formData.username = props.editUser.username
    formData.realName = props.editUser.realName || ''
    formData.phone = props.editUser.phone || ''
    formData.email = props.editUser.email || ''
    formData.password = ''
    formData.department = props.editUser.department || ''
    formData.position = props.editUser.position || ''
    formData.roles = [...props.editUser.roles]
    formData.isActive = (props.editUser as any).isActive !== false
    formData.notes = (props.editUser as any).notes || ''
  }
}

const handleClose = () => {
  if (!loading.value) {
    emit('update:show', false)
    resetForm()
  }
}

const handleBackdropClick = () => {
  handleClose()
}

const handleSubmit = async () => {
  if (!isFormValid.value || loading.value) return
  
  loading.value = true
  
  try {
    const userData = {
      username: formData.username.trim(),
      realName: formData.realName.trim(),
      phone: formData.phone.trim() || undefined,
      email: formData.email.trim() || undefined,
      department: formData.department,
      position: formData.position.trim() || undefined,
      roles: formData.roles,
      notes: formData.notes.trim() || undefined
    }
    
    if (isEditing.value) {
      // 更新用户
      console.log('更新用户:', userData)
      emit('updated')
    } else {
      // 创建用户
      const newUserData = {
        ...userData,
        password: formData.password
      }
      console.log('创建用户:', newUserData)
      emit('created')
    }
    
    handleClose()
  } catch (error) {
    console.error('操作失败:', error)
    alert('操作失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 监听props变化
watch(() => props.show, (newValue) => {
  if (newValue) {
    if (props.editUser) {
      loadEditData()
    } else {
      resetForm()
    }
  }
})

// 键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.show) {
    handleClose()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>
