@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义样式 */
body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* 微信浏览器兼容性 */
* {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
}

/* 加载动画 */
.loading {
  @apply animate-pulse bg-gray-200;
}

/* Naive UI 主题定制 */
.n-button {
  border-radius: 6px;
}

.n-card {
  border-radius: 8px;
}

/* 自定义工具类 */
.glass-effect {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.8);
}

.shadow-soft {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 移动端优化 */
@media (max-width: 768px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}
