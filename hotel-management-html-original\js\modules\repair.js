/**
 * 报修工单模块 - 实现报修提交功能
 * 继承自BaseWorkLogApp，实现报修工单特定功能
 */

class RepairApp extends BaseWorkLogApp {
    constructor() {
        super({
            pageType: 'repair',
            requiredElements: ['repairSection', 'submitBtn']
        });
        
        // 绑定方法上下文
        this.handleSubmit = this.handleSubmit.bind(this);
        this.handleImageUpload = this.handleImageUpload.bind(this);
        this.handleCancel = this.handleCancel.bind(this);
        
        // 上传的图片列表
        this.uploadedImages = [];
    }

    /**
     * 获取页面特定的DOM元素
     */
    getPageElements() {
        return {
            // 页面区域
            welcomeSection: 'welcomeSection',
            repairSection: 'repairSection',
            loginPrompt: 'loginPrompt',
            promptLoginBtn: 'promptLoginBtn',

            // 表单字段
            repairForm: 'repairForm',
            category: 'category',
            urgency: 'urgency',
            location: 'location',
            description: 'description',

            // 图片上传
            imageInput: 'imageInput',
            uploadArea: 'uploadArea',
            imagePreview: 'imagePreview',

            // 按钮
            submitBtn: 'submitBtn',
            cancelBtn: 'cancelBtn',
            welcomeLoginBtn: 'welcomeLoginBtn',

            // 用户信息
            realName: 'realName',
            userInfo: 'userInfo',
            loginBtn: 'loginBtn',
            logoutBtn: 'logoutBtn',
            username: 'username',

            // 登录弹窗
            loginModal: 'loginModal',
            closeModal: 'closeModal',
            loginForm: 'loginForm',
            registerForm: 'registerForm',
            modalTitle: 'modalTitle',
            showRegisterBtn: 'showRegisterBtn',
            showLoginBtn: 'showLoginBtn',
            loginSwitchText: 'loginSwitchText',
            registerSwitchText: 'registerSwitchText',

            // 登录表单字段
            loginUsername: 'loginUsername',
            loginPassword: 'loginPassword',
            registerUsername: 'registerUsername',
            registerRealName: 'registerRealName',
            registerPassword: 'registerPassword',
            confirmPassword: 'confirmPassword'
        };
    }

    /**
     * 绑定页面特定事件
     */
    bindPageEvents() {
        // 表单提交事件
        if (this.elements.repairForm) {
            this.elements.repairForm.addEventListener('submit', this.handleSubmit);
        }
        
        // 提交按钮事件
        if (this.elements.submitBtn) {
            this.elements.submitBtn.addEventListener('click', this.handleSubmit);
        }
        
        // 取消按钮事件
        if (this.elements.cancelBtn) {
            this.elements.cancelBtn.addEventListener('click', this.handleCancel);
        }
        
        // 欢迎页面登录按钮
        if (this.elements.welcomeLoginBtn) {
            this.elements.welcomeLoginBtn.addEventListener('click', () => {
                this.showLoginModal();
            });
        }

        // 登录提示按钮
        if (this.elements.promptLoginBtn) {
            this.elements.promptLoginBtn.addEventListener('click', () => {
                this.showLoginModal();
            });
        }

        // 登录相关事件绑定
        this.bindLoginEvents();

        // 图片上传相关事件
        this.bindImageUploadEvents();
    }

    /**
     * 绑定登录相关事件
     */
    bindLoginEvents() {
        // 登录按钮
        if (this.elements.loginBtn) {
            this.elements.loginBtn.addEventListener('click', () => {
                this.showLoginModal();
            });
        }

        // 登出按钮
        if (this.elements.logoutBtn) {
            this.elements.logoutBtn.addEventListener('click', () => {
                WorkLogAuth.logout();
            });
        }

        // 关闭弹窗
        if (this.elements.closeModal) {
            this.elements.closeModal.addEventListener('click', () => {
                this.hideLoginModal();
            });
        }

        // 点击弹窗外部关闭
        if (this.elements.loginModal) {
            this.elements.loginModal.addEventListener('click', (e) => {
                if (e.target === this.elements.loginModal) {
                    this.hideLoginModal();
                }
            });
        }

        // 登录表单提交
        if (this.elements.loginForm) {
            this.elements.loginForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                await this.handleLogin();
            });
        }

        // 注册表单提交
        if (this.elements.registerForm) {
            this.elements.registerForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                await this.handleRegister();
            });
        }

        // 显示注册表单
        if (this.elements.showRegisterBtn) {
            this.elements.showRegisterBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.showRegisterForm();
            });
        }

        // 显示登录表单
        if (this.elements.showLoginBtn) {
            this.elements.showLoginBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.showLoginForm();
            });
        }
    }

    /**
     * 绑定图片上传事件
     */
    bindImageUploadEvents() {
        // 点击上传区域
        if (this.elements.uploadArea) {
            this.elements.uploadArea.addEventListener('click', () => {
                this.elements.imageInput?.click();
            });
        }
        
        // 文件选择事件
        if (this.elements.imageInput) {
            this.elements.imageInput.addEventListener('change', this.handleImageUpload);
        }
        
        // 拖拽上传
        if (this.elements.uploadArea) {
            this.elements.uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                this.elements.uploadArea.classList.add('border-red-500', 'bg-red-50');
            });
            
            this.elements.uploadArea.addEventListener('dragleave', (e) => {
                e.preventDefault();
                this.elements.uploadArea.classList.remove('border-red-500', 'bg-red-50');
            });
            
            this.elements.uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                this.elements.uploadArea.classList.remove('border-red-500', 'bg-red-50');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    this.handleImageFiles(files);
                }
            });
        }
    }

    /**
     * 用户登录后的回调
     */
    onUserLoggedIn() {
        this.showUserInterface();
    }

    /**
     * 用户登出后的回调
     */
    onUserLoggedOut() {
        this.showLoginInterface();
    }

    /**
     * 显示用户界面
     */
    showUserInterface() {
        super.showUserInterface();

        if (this.elements.repairSection) {
            this.elements.repairSection.classList.remove('hidden');
        }

        if (this.elements.welcomeSection) {
            this.elements.welcomeSection.classList.add('hidden');
        }

        if (this.elements.loginPrompt) {
            this.elements.loginPrompt.classList.add('hidden');
        }
    }

    /**
     * 显示登录界面
     */
    showLoginInterface() {
        super.showLoginPrompt();

        if (this.elements.repairSection) {
            this.elements.repairSection.classList.add('hidden');
        }

        if (this.elements.welcomeSection) {
            this.elements.welcomeSection.classList.remove('hidden');
        }

        if (this.elements.loginPrompt) {
            this.elements.loginPrompt.classList.remove('hidden');
        }
    }



    /**
     * 显示登录弹窗
     */
    showLoginModal() {
        if (this.elements.loginModal) {
            this.elements.loginModal.classList.remove('hidden');
            this.showLoginForm();
        }
    }

    /**
     * 隐藏登录弹窗
     */
    hideLoginModal() {
        if (this.elements.loginModal) {
            this.elements.loginModal.classList.add('hidden');
        }
    }

    /**
     * 显示登录表单
     */
    showLoginForm() {
        if (this.elements.loginForm) {
            this.elements.loginForm.classList.remove('hidden');
        }
        if (this.elements.registerForm) {
            this.elements.registerForm.classList.add('hidden');
        }
        if (this.elements.modalTitle) {
            this.elements.modalTitle.textContent = '登录';
        }
        if (this.elements.loginSwitchText) {
            this.elements.loginSwitchText.classList.remove('hidden');
        }
        if (this.elements.registerSwitchText) {
            this.elements.registerSwitchText.classList.add('hidden');
        }
    }

    /**
     * 显示注册表单
     */
    showRegisterForm() {
        if (this.elements.loginForm) {
            this.elements.loginForm.classList.add('hidden');
        }
        if (this.elements.registerForm) {
            this.elements.registerForm.classList.remove('hidden');
        }
        if (this.elements.modalTitle) {
            this.elements.modalTitle.textContent = '注册';
        }
        if (this.elements.loginSwitchText) {
            this.elements.loginSwitchText.classList.add('hidden');
        }
        if (this.elements.registerSwitchText) {
            this.elements.registerSwitchText.classList.remove('hidden');
        }
    }

    /**
     * 处理登录
     */
    async handleLogin() {
        const username = this.elements.loginUsername?.value?.trim();
        const password = this.elements.loginPassword?.value;

        if (!username || !password) {
            WorkLogUtils.showMessage('请输入用户名和密码', 'warning');
            return;
        }

        try {
            const user = await WorkLogAuth.login(username, password);
            if (user) {
                this.hideLoginModal();
                WorkLogUtils.showMessage('登录成功', 'success');

                // 触发登录成功事件
                window.dispatchEvent(new CustomEvent('userLoggedIn', { detail: user }));
            }
        } catch (error) {
            console.error('登录失败:', error);
            WorkLogUtils.showMessage('登录失败: ' + error.message, 'error');
        }
    }

    /**
     * 处理注册
     */
    async handleRegister() {
        const username = this.elements.registerUsername?.value?.trim();
        const realName = this.elements.registerRealName?.value?.trim();
        const password = this.elements.registerPassword?.value;
        const confirmPassword = this.elements.confirmPassword?.value;

        if (!username || !realName || !password || !confirmPassword) {
            WorkLogUtils.showMessage('请填写所有字段', 'warning');
            return;
        }

        if (password !== confirmPassword) {
            WorkLogUtils.showMessage('两次输入的密码不一致', 'warning');
            return;
        }

        if (password.length < 6) {
            WorkLogUtils.showMessage('密码长度至少6位', 'warning');
            return;
        }

        try {
            const user = await WorkLogAuth.register(username, password, realName);
            if (user) {
                this.hideLoginModal();
                WorkLogUtils.showMessage('注册成功', 'success');

                // 触发登录成功事件
                window.dispatchEvent(new CustomEvent('userLoggedIn', { detail: user }));
            }
        } catch (error) {
            console.error('注册失败:', error);
            WorkLogUtils.showMessage('注册失败: ' + error.message, 'error');
        }
    }

    /**
     * 处理图片上传
     */
    handleImageUpload(event) {
        const files = event.target.files;
        if (files.length > 0) {
            this.handleImageFiles(files);
        }
    }

    /**
     * 处理图片文件
     */
    async handleImageFiles(files) {
        const maxFiles = 5;
        const currentCount = this.uploadedImages.length;
        
        if (currentCount + files.length > maxFiles) {
            WorkLogUtils.showMessage(`最多只能上传${maxFiles}张图片`, 'warning');
            return;
        }
        
        for (let file of files) {
            if (!file.type.startsWith('image/')) {
                WorkLogUtils.showMessage('只能上传图片文件', 'warning');
                continue;
            }
            
            if (file.size > 5 * 1024 * 1024) {
                WorkLogUtils.showMessage('图片大小不能超过5MB', 'warning');
                continue;
            }
            
            try {
                const imageUrl = await WorkLogUtils.uploadImages([file], 'repair');
                if (imageUrl && imageUrl.length > 0) {
                    this.uploadedImages.push(imageUrl[0]);
                    this.updateImagePreview();
                }
            } catch (error) {
                console.error('图片上传失败:', error);
                WorkLogUtils.showMessage('图片上传失败: ' + error.message, 'error');
            }
        }
    }

    /**
     * 更新图片预览
     */
    updateImagePreview() {
        if (!this.elements.imagePreview) return;
        
        if (this.uploadedImages.length === 0) {
            this.elements.imagePreview.classList.add('hidden');
            return;
        }
        
        this.elements.imagePreview.classList.remove('hidden');
        this.elements.imagePreview.innerHTML = this.uploadedImages.map((url, index) => `
            <div class="relative">
                <img src="${url}" alt="预览图片" class="w-full h-20 object-cover rounded-lg">
                <button type="button" onclick="repairApp.removeImage(${index})" 
                        class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600">
                    ×
                </button>
            </div>
        `).join('');
    }

    /**
     * 移除图片
     */
    removeImage(index) {
        this.uploadedImages.splice(index, 1);
        this.updateImagePreview();
    }

    /**
     * 处理取消操作
     */
    handleCancel() {
        if (confirm('确定要取消报修吗？已填写的信息将丢失。')) {
            this.resetForm();
            window.location.href = 'index.html';
        }
    }

    /**
     * 重置表单
     */
    resetForm() {
        if (this.elements.repairForm) {
            this.elements.repairForm.reset();
        }
        this.uploadedImages = [];
        this.updateImagePreview();
    }

    /**
     * 处理表单提交
     */
    async handleSubmit(event) {
        event.preventDefault();
        
        if (!this.validateForm()) {
            return;
        }
        
        const submitBtn = this.elements.submitBtn;
        const originalText = submitBtn?.textContent;
        
        try {
            if (submitBtn) {
                submitBtn.textContent = '提交中...';
                submitBtn.disabled = true;
            }
            
            // 生成工单号
            const orderNumber = this.generateOrderNumber();
            
            // 构建工单数据
            const orderData = this.buildOrderData(orderNumber);
            
            // 保存工单
            await this.saveRepairOrder(orderData);
            
            WorkLogUtils.showMessage('报修提交成功！工单号：' + orderNumber, 'success');
            
            // 重置表单
            this.resetForm();
            
            // 可选：跳转到工单查询页面
            setTimeout(() => {
                if (confirm('报修提交成功！是否查看工单状态？')) {
                    window.location.href = `repair-query.html?order=${orderNumber}`;
                }
            }, 1500);
            
        } catch (error) {
            globalErrorHandler.handle(error, '提交报修工单', {
                showToUser: true,
                allowRetry: true,
                retryCallback: () => this.handleSubmit(event)
            });
        } finally {
            if (submitBtn) {
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        }
    }

    /**
     * 验证表单（使用新的安全验证模块）
     */
    validateForm() {
        try {
            // 收集表单数据
            const formData = {
                category: this.elements.category?.value,
                urgency: this.elements.urgency?.value,
                location: this.elements.location?.value,
                description: this.elements.description?.value
            };

            // 定义验证规则
            const validationRules = {
                category: {
                    type: 'text',
                    required: true,
                    minLength: 1,
                    maxLength: 50
                },
                urgency: {
                    type: 'text',
                    required: true,
                    minLength: 1,
                    maxLength: 20
                },
                location: {
                    type: 'text',
                    required: true,
                    minLength: 2,
                    maxLength: 100
                },
                description: {
                    type: 'text',
                    required: true,
                    minLength: 5,
                    maxLength: 1000
                }
            };

            // 使用SecurityValidator进行验证
            const validation = SecurityValidator.validateForm(formData, validationRules);

            if (!validation.isValid) {
                // 显示第一个错误
                const firstError = Object.values(validation.errors)[0];
                const firstField = Object.keys(validation.errors)[0];

                globalErrorHandler.showUserError(firstError);

                // 聚焦到错误字段
                const fieldElement = this.elements[firstField];
                if (fieldElement) {
                    fieldElement.focus();
                }

                return false;
            }

            // 检查用户信息是否完整
            const phone = this.currentUser?.get('phone');
            const department = this.currentUser?.get('department');

            // 验证手机号
            const phoneValidation = SecurityValidator.validatePhone(phone, true);
            if (!phoneValidation.isValid) {
                globalErrorHandler.showUserError('您的联系电话未设置或格式不正确，请联系管理员完善账户信息');
                return false;
            }

            // 验证部门信息
            const deptValidation = SecurityValidator.validateText(department, {
                required: true,
                minLength: 2,
                maxLength: 50
            });
            if (!deptValidation.isValid) {
                globalErrorHandler.showUserError('您的部门信息未设置，请联系管理员完善账户信息');
                return false;
            }

            return true;

        } catch (error) {
            globalErrorHandler.handle(error, '表单验证');
            return false;
        }
    }

    /**
     * 生成工单号
     */
    generateOrderNumber() {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hour = String(now.getHours()).padStart(2, '0');
        const minute = String(now.getMinutes()).padStart(2, '0');
        const second = String(now.getSeconds()).padStart(2, '0');
        
        return `WO${year}${month}${day}${hour}${minute}${second}`;
    }

    /**
     * 构建工单数据
     */
    buildOrderData(orderNumber) {
        // 生成标题（基于类别和位置）
        const title = `${this.elements.category.value} - ${this.elements.location.value}`;

        return {
            orderNumber: orderNumber,
            title: title,
            category: this.elements.category.value,
            urgency: this.elements.urgency.value,
            location: this.elements.location.value.trim(),
            description: this.elements.description.value.trim(),
            reporterName: this.currentUser.get('realName') || this.currentUser.get('username'),
            reporterPhone: this.currentUser.get('phone'),
            reporterDept: this.currentUser.get('department'),
            images: this.uploadedImages,
            status: 'pending',
            priority: this.calculatePriority(),
            reportTime: new Date(),
            reporter: this.currentUser
        };
    }

    /**
     * 计算优先级
     */
    calculatePriority() {
        const urgencyMap = {
            '紧急': 5,
            '高': 4,
            '中': 3,
            '低': 2
        };
        return urgencyMap[this.elements.urgency.value] || 2;
    }

    /**
     * 保存报修工单
     */
    async saveRepairOrder(orderData) {
        const RepairOrder = AV.Object.extend('RepairOrder');
        const order = new RepairOrder();
        
        // 设置所有字段
        Object.keys(orderData).forEach(key => {
            order.set(key, orderData[key]);
        });
        
        // 保存到数据库
        await order.save();

        console.log('工单保存成功:', order.id);

        return order;
    }


}

// 导出模块
if (typeof window !== 'undefined') {
    window.RepairApp = RepairApp;
}
