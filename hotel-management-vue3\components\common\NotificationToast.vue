<template>
  <Teleport to="body">
    <div class="fixed top-4 right-4 z-50 space-y-2">
      <Transition
        v-for="notification in notifications"
        :key="notification.id"
        name="notification"
        appear
      >
        <div 
          class="bg-white rounded-lg shadow-lg border-l-4 p-4 max-w-sm"
          :class="getBorderColor(notification.type)"
        >
          <div class="flex items-start">
            <!-- 图标 -->
            <div class="flex-shrink-0 mr-3">
              <Icon 
                :name="getIcon(notification.type)" 
                size="20" 
                :class="getIconColor(notification.type)"
              />
            </div>
            
            <!-- 内容 -->
            <div class="flex-1 min-w-0">
              <h4 
                v-if="notification.title"
                class="text-sm font-medium text-gray-900 mb-1"
              >
                {{ notification.title }}
              </h4>
              <p class="text-sm text-gray-600">
                {{ notification.message }}
              </p>
            </div>
            
            <!-- 关闭按钮 -->
            <button 
              @click="removeNotification(notification.id)"
              class="flex-shrink-0 ml-2 text-gray-400 hover:text-gray-600"
            >
              <Icon name="mdi:close" size="16" />
            </button>
          </div>
        </div>
      </Transition>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title?: string
  message: string
  duration?: number
}

// 响应式数据
const notifications = ref<Notification[]>([])

// 方法
const addNotification = (notification: Omit<Notification, 'id'>) => {
  const id = Date.now().toString()
  const newNotification: Notification = {
    id,
    duration: 5000,
    ...notification
  }
  
  notifications.value.push(newNotification)
  
  // 自动移除
  if (newNotification.duration && newNotification.duration > 0) {
    setTimeout(() => {
      removeNotification(id)
    }, newNotification.duration)
  }
  
  return id
}

const removeNotification = (id: string) => {
  const index = notifications.value.findIndex(n => n.id === id)
  if (index > -1) {
    notifications.value.splice(index, 1)
  }
}

const clearAll = () => {
  notifications.value = []
}

// 样式方法
const getBorderColor = (type: string) => {
  const colorMap = {
    success: 'border-green-500',
    error: 'border-red-500',
    warning: 'border-yellow-500',
    info: 'border-blue-500'
  }
  return colorMap[type as keyof typeof colorMap]
}

const getIcon = (type: string) => {
  const iconMap = {
    success: 'mdi:check-circle',
    error: 'mdi:alert-circle',
    warning: 'mdi:alert',
    info: 'mdi:information'
  }
  return iconMap[type as keyof typeof iconMap]
}

const getIconColor = (type: string) => {
  const colorMap = {
    success: 'text-green-500',
    error: 'text-red-500',
    warning: 'text-yellow-500',
    info: 'text-blue-500'
  }
  return colorMap[type as keyof typeof colorMap]
}

// 便捷方法
const success = (message: string, title?: string, duration?: number) => {
  return addNotification({ type: 'success', message, title, duration })
}

const error = (message: string, title?: string, duration?: number) => {
  return addNotification({ type: 'error', message, title, duration })
}

const warning = (message: string, title?: string, duration?: number) => {
  return addNotification({ type: 'warning', message, title, duration })
}

const info = (message: string, title?: string, duration?: number) => {
  return addNotification({ type: 'info', message, title, duration })
}

// 暴露方法
defineExpose({
  addNotification,
  removeNotification,
  clearAll,
  success,
  error,
  warning,
  info
})
</script>

<style scoped>
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}
</style>
