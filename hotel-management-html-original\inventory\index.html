<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>库存管理 - 系统管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <style>
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .btn-fix {
            touch-action: manipulation;
        }
        .inventory-card {
            transition: all 0.3s ease;
        }
        .inventory-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="../index.html" class="text-blue-600 hover:text-blue-800 mr-4">
                        ← 返回主页
                    </a>
                    <h1 class="text-xl font-semibold text-gray-900">库存管理系统</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- 用户信息 -->
                    <div id="userInfo" class="flex items-center space-x-2" style="display: none;">
                        <span class="text-sm text-gray-700">用户：</span>
                        <span id="realName" class="text-sm font-medium text-gray-900"></span>
                        <button id="logoutBtn" class="text-sm text-red-600 hover:text-red-800 btn-fix">退出</button>
                    </div>
                    <button id="loginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                        登录
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 权限提示 -->
    <div id="accessDenied" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8" style="display: none;">
        <div class="bg-white rounded-lg shadow p-8 text-center">
            <div class="mb-4">
                <svg class="mx-auto h-12 w-12 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">访问受限</h3>
            <p class="text-gray-500 mb-4">您需要相应权限才能访问库存管理功能</p>
            <button id="loginPromptBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg btn-fix">
                立即登录
            </button>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div id="inventorySection" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8" style="display: none;">
        <!-- 系统概览 -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">库存概览</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">今日入库</dt>
                                <dd class="text-lg font-medium text-gray-900" id="todayInbound">0</dd>
                            </dl>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 13l-5 5m0 0l-5-5m5 5V6"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">今日出库</dt>
                                <dd class="text-lg font-medium text-gray-900" id="todayOutbound">0</dd>
                            </dl>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">今日盘点</dt>
                                <dd class="text-lg font-medium text-gray-900" id="todayCount">0</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 库存管理功能 -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">库存管理</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- 入库管理 -->
                <a href="inbound.html" class="inventory-card block bg-white rounded-lg shadow p-6 hover:shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                            </svg>
                        </div>
                        <h3 class="ml-4 text-lg font-medium text-gray-900">入库管理</h3>
                    </div>
                    <p class="text-gray-500 text-sm">商品入库登记和库存增加</p>
                </a>

                <!-- 出库管理 -->
                <a href="outbound.html" class="inventory-card block bg-white rounded-lg shadow p-6 hover:shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 13l-5 5m0 0l-5-5m5 5V6"></path>
                            </svg>
                        </div>
                        <h3 class="ml-4 text-lg font-medium text-gray-900">出库管理</h3>
                    </div>
                    <p class="text-gray-500 text-sm">商品出库登记和库存减少</p>
                </a>

                <!-- 库存盘点 -->
                <a href="count.html" class="inventory-card block bg-white rounded-lg shadow p-6 hover:shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 bg-yellow-500 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                            </svg>
                        </div>
                        <h3 class="ml-4 text-lg font-medium text-gray-900">库存盘点</h3>
                    </div>
                    <p class="text-gray-500 text-sm">定期盘点和库存核对</p>
                </a>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">快速操作</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button id="quickInboundBtn" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                    快速入库
                </button>
                <button id="quickOutboundBtn" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                    快速出库
                </button>
                <button id="quickCountBtn" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                    快速盘点
                </button>
            </div>
        </div>
    </div>

    <!-- 登录弹窗 -->
    <div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-40" style="display: none;">
        <div class="bg-white rounded-lg p-6 w-96 max-w-md mx-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">用户登录</h3>
            <form id="loginForm">
                <div class="mb-4">
                    <label for="loginUsername" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                    <input type="text" id="loginUsername" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                <div class="mb-6">
                    <label for="loginPassword" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                    <input type="password" id="loginPassword" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" id="cancelLoginBtn" class="px-4 py-2 text-gray-600 hover:text-gray-800">取消</button>
                    <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-md">登录</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 引入模块化JS文件 -->
    <script src="../js/config.js"></script>
    <script src="../js/permission-manager.js"></script>
    <script>
        // 库存管理应用类
        class InventoryApp {
            constructor() {
                this.elements = {};
            }

            init() {
                this.initElements();
                this.bindEvents();
                this.checkAccess();
            }

            initElements() {
                this.elements = {
                    accessDenied: document.getElementById('accessDenied'),
                    inventorySection: document.getElementById('inventorySection'),
                    realName: document.getElementById('realName'),
                    userInfo: document.getElementById('userInfo'),
                    loginBtn: document.getElementById('loginBtn'),
                    logoutBtn: document.getElementById('logoutBtn'),
                    loginPromptBtn: document.getElementById('loginPromptBtn'),
                    todayInbound: document.getElementById('todayInbound'),
                    todayOutbound: document.getElementById('todayOutbound'),
                    todayCount: document.getElementById('todayCount')
                };
            }

            bindEvents() {
                // 登录相关事件
                if (this.elements.loginBtn) {
                    this.elements.loginBtn.addEventListener('click', () => {
                        this.showLoginModal();
                    });
                }

                if (this.elements.loginPromptBtn) {
                    this.elements.loginPromptBtn.addEventListener('click', () => {
                        this.showLoginModal();
                    });
                }

                if (this.elements.logoutBtn) {
                    this.elements.logoutBtn.addEventListener('click', async () => {
                        try {
                            await AV.User.logOut();
                            this.checkAccess();
                        } catch (error) {
                            console.error('退出登录失败:', error);
                        }
                    });
                }

                // 快速操作事件
                document.getElementById('quickInboundBtn').addEventListener('click', () => {
                    window.location.href = 'inbound.html';
                });

                document.getElementById('quickOutboundBtn').addEventListener('click', () => {
                    window.location.href = 'outbound.html';
                });

                document.getElementById('quickCountBtn').addEventListener('click', () => {
                    window.location.href = 'count.html';
                });

                // 登录表单事件
                document.getElementById('loginForm').addEventListener('submit', async (e) => {
                    e.preventDefault();
                    await this.handleLogin();
                });

                // 关闭登录弹窗
                document.addEventListener('click', (e) => {
                    if (e.target.id === 'loginModal' || e.target.id === 'cancelLoginBtn') {
                        document.getElementById('loginModal').style.display = 'none';
                    }
                });
            }

            async checkAccess() {
                const currentUser = AV.User.current();
                
                if (currentUser) {
                    // 检查库存管理权限
                    const hasInventoryAccess = await this.checkInventoryPermission(currentUser);
                    
                    if (hasInventoryAccess) {
                        this.elements.accessDenied.style.display = 'none';
                        this.elements.inventorySection.style.display = 'block';
                        
                        // 更新用户信息显示
                        this.elements.realName.textContent = currentUser.get('realName') || currentUser.get('username');
                        this.elements.userInfo.style.display = 'flex';
                        this.elements.loginBtn.style.display = 'none';
                        
                        // 加载统计数据
                        this.loadStatistics();
                    } else {
                        this.elements.accessDenied.style.display = 'block';
                        this.elements.inventorySection.style.display = 'none';
                        this.elements.userInfo.style.display = 'none';
                        this.elements.loginBtn.style.display = 'block';
                    }
                } else {
                    // 用户未登录
                    this.elements.accessDenied.style.display = 'block';
                    this.elements.inventorySection.style.display = 'none';
                    this.elements.userInfo.style.display = 'none';
                    this.elements.loginBtn.style.display = 'block';
                }
            }

            async checkInventoryPermission(user) {
                // 检查用户是否有库存管理权限
                const roles = user.get('roles') || [];
                
                // 管理员和超级管理员有库存权限
                if (roles.includes('admin') || roles.includes('super_admin')) {
                    return true;
                }
                
                // 检查是否有特定的库存权限
                if (window.permissionManager) {
                    try {
                        await window.permissionManager.init();
                        return window.permissionManager.hasPermission('inventory', 'products', 'view');
                    } catch (error) {
                        console.error('权限检查失败:', error);
                    }
                }
                
                return false;
            }

            async loadStatistics() {
                try {
                    // 初始化默认值
                    let todayInbound = 0;
                    let todayOutbound = 0;
                    let todayCount = 0;

                    // 计算今日日期范围
                    const today = new Date();
                    today.setHours(0, 0, 0, 0);
                    const tomorrow = new Date(today);
                    tomorrow.setDate(tomorrow.getDate() + 1);

                    // 加载今日入库数量
                    try {
                        const inboundQuery = new AV.Query('InboundRecord');
                        inboundQuery.greaterThanOrEqualTo('createdAt', today);
                        inboundQuery.lessThan('createdAt', tomorrow);
                        todayInbound = await inboundQuery.count();
                    } catch (error) {
                        console.warn('InboundRecord表不存在:', error.message);
                        todayInbound = 0;
                    }

                    // 加载今日出库数量
                    try {
                        const outboundQuery = new AV.Query('OutboundRecord');
                        outboundQuery.greaterThanOrEqualTo('createdAt', today);
                        outboundQuery.lessThan('createdAt', tomorrow);
                        todayOutbound = await outboundQuery.count();
                    } catch (error) {
                        console.warn('OutboundRecord表不存在:', error.message);
                        todayOutbound = 0;
                    }

                    // 加载今日盘点数量
                    try {
                        const countQuery = new AV.Query('CountRecord');
                        countQuery.greaterThanOrEqualTo('createdAt', today);
                        countQuery.lessThan('createdAt', tomorrow);
                        todayCount = await countQuery.count();
                    } catch (error) {
                        console.warn('CountRecord表不存在:', error.message);
                        todayCount = 0;
                    }

                    // 更新显示
                    this.elements.todayInbound.textContent = todayInbound;
                    this.elements.todayOutbound.textContent = todayOutbound;
                    this.elements.todayCount.textContent = todayCount;

                    console.log('统计数据加载完成:', { todayInbound, todayOutbound, todayCount });

                } catch (error) {
                    console.error('加载统计数据失败:', error);
                    // 出错时显示默认值
                    this.elements.todayInbound.textContent = '0';
                    this.elements.todayOutbound.textContent = '0';
                    this.elements.todayCount.textContent = '0';
                }
            }

            showLoginModal() {
                document.getElementById('loginModal').style.display = 'flex';
            }

            async handleLogin() {
                const username = document.getElementById('loginUsername').value;
                const password = document.getElementById('loginPassword').value;

                try {
                    await AV.User.logIn(username, password);
                    document.getElementById('loginModal').style.display = 'none';
                    await this.checkAccess();
                    alert('登录成功');
                } catch (error) {
                    console.error('登录失败:', error);
                    alert('登录失败: ' + error.message);
                }
            }


        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            function initApp() {
                if (typeof AV !== 'undefined') {
                    const inventoryApp = new InventoryApp();
                    inventoryApp.init();
                } else {
                    setTimeout(initApp, 100);
                }
            }
            initApp();
        });
    </script>
</body>
</html>
