# 数据库字段类型说明

## 用户表 (_User)

### 字段定义

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| username | String | 用户名（系统字段） | "junwei" |
| password | String | 密码（系统字段，加密存储） | - |
| realName | String | 真实姓名 | "张三" |
| phone | String | 联系电话 | "138****8888" |
| department | String | 所属部门 | "工程部" |
| roles | String | 用户角色（JSON字符串格式） | `"[\"admin\"]"` |
| createdAt | Date | 创建时间（系统字段） | - |
| updatedAt | Date | 更新时间（系统字段） | - |

### 角色字段说明

`roles` 字段在LeanCloud中被定义为String类型，存储JSON格式的角色数组：

#### 存储格式
```javascript
// 管理员
roles: "[\"admin\"]"

// 工程师
roles: "[\"engineer\"]"

// 管理员+工程师
roles: "[\"admin\",\"engineer\"]"

// 普通用户（无特殊角色）
roles: "[]"
```

#### 解析逻辑
```javascript
// 从数据库读取时的解析
function parseUserRoles(roles) {
    if (!roles) return [];
    
    if (Array.isArray(roles)) {
        return roles;
    }
    
    if (typeof roles === 'string') {
        try {
            const parsed = JSON.parse(roles);
            return Array.isArray(parsed) ? parsed : [parsed];
        } catch (e) {
            return roles.trim() ? [roles] : [];
        }
    }
    
    return [];
}

// 保存到数据库时的转换
function stringifyUserRoles(rolesArray) {
    return JSON.stringify(rolesArray);
}
```

## 报修工单表 (RepairOrder)

### 字段定义

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| orderNumber | String | 工单号 | "WO20241205120001" |
| title | String | 工单标题 | "空调设备 - 3楼客房301" |
| category | String | 报修类别 | "空调" |
| urgency | String | 紧急程度 | "高" |
| location | String | 故障位置 | "3楼客房301" |
| description | String | 故障描述 | "空调不制冷" |
| reporterName | String | 报修人姓名 | "张三" |
| reporterPhone | String | 报修人电话 | "138****8888" |
| reporterDept | String | 报修人部门 | "客房部" |
| images | Array | 故障图片URL数组 | ["http://..."] |
| status | String | 工单状态 | "pending" |
| priority | Number | 优先级 | 4 |
| reportTime | Date | 报修时间 | - |
| acceptTime | Date | 接单时间 | - |
| startTime | Date | 开始处理时间 | - |
| completeTime | Date | 完成时间 | - |
| result | String | 处理结果 | "已更换空调滤网" |
| reporter | Pointer | 报修人用户对象 | - |
| assignee | Pointer | 处理人用户对象 | - |
| assigneeName | String | 处理人姓名 | "李四" |

### 状态流转

```
pending (待接单) → accepted (已接单) → processing (处理中) → completed (已完成)
                                                        ↓
                                                   cancelled (已取消)
```

### 优先级计算

```javascript
function calculatePriority() {
    const urgencyMap = { '低': 1, '中': 2, '高': 3, '紧急': 4 };
    const categoryMap = { '其他': 0, '电气': 1, '水暖': 1, '空调': 2, '电梯': 3, '消防': 3 };
    
    return urgencyMap[urgency] + categoryMap[category];
}
```

## 工作日志表 (WorkLog)

### 字段定义

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| pageType | String | 页面类型 | "repair" |
| content | String | 日志内容 | "提交报修工单" |
| timestamp | Date | 时间戳 | - |
| user | Pointer | 用户对象 | - |
| username | String | 用户名 | "junwei" |
| metadata | Object | 元数据 | {"orderId": "xxx"} |

## 数据一致性注意事项

### 1. 角色字段处理
- 所有涉及roles字段的操作都需要进行字符串↔数组转换
- 新用户注册时默认设置为 `"[]"`
- 编辑用户时将数组转换为JSON字符串保存

### 2. 用户信息同步
- 报修工单中的用户信息（姓名、电话、部门）从用户表自动获取
- 管理员可以通过后台管理系统统一维护用户信息

### 3. 权限控制
- admin角色：可以访问后台管理，管理所有用户
- engineer角色：可以处理工单，查看工单管理页面
- 无角色：普通用户，只能提交报修

### 4. 数据完整性
- 报修前检查用户的phone和department字段是否完整
- 工单创建时自动生成唯一的orderNumber
- 所有时间字段使用Date类型，便于排序和筛选

## 开发建议

### 1. 类型安全
```javascript
// 推荐：使用辅助函数处理角色
const roles = parseUserRoles(user.get('roles'));

// 避免：直接使用可能导致类型错误
const roles = user.get('roles'); // 可能是字符串
```

### 2. 错误处理
```javascript
// 推荐：优雅处理解析错误
try {
    const roles = JSON.parse(user.get('roles'));
} catch (e) {
    const roles = []; // 默认值
}
```

### 3. 数据验证
```javascript
// 保存前验证数据完整性
if (!user.get('phone') || !user.get('department')) {
    throw new Error('用户信息不完整');
}
```

---

**注意**：此文档基于当前系统的实际数据结构，如有变更请及时更新。
