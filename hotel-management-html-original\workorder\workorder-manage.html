<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>工单处理</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- LeanCloud SDK -->
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <style>
        .status-pending { @apply bg-yellow-100 text-yellow-800; }
        .status-accepted { @apply bg-blue-100 text-blue-800; }
        .status-processing { @apply bg-purple-100 text-purple-800; }
        .status-completed { @apply bg-green-100 text-green-800; }
        .status-cancelled { @apply bg-red-100 text-red-800; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-3">
                    <a href="index.html" class="text-gray-600 hover:text-gray-800">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                    </a>
                    <h1 class="text-xl font-semibold text-gray-800">工单处理</h1>
                </div>
                <div id="userInfo" class="hidden items-center space-x-4">
                    <span id="realName" class="text-gray-800 font-medium"></span>
                    <button id="logoutBtn" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm">
                        退出登录
                    </button>
                </div>
                <button id="loginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm">
                    登录
                </button>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="max-w-7xl mx-auto px-4 py-6">
        <!-- 未登录提示 -->
        <div id="accessDenied" class="bg-white rounded-lg shadow p-8 text-center">
            <h2 class="text-2xl font-bold text-gray-800 mb-4">需要登录</h2>
            <p class="text-gray-600 mb-6">请先登录后使用工单处理功能</p>
            <button id="promptLoginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg">
                立即登录
            </button>
        </div>

        <!-- 工单管理界面 -->
        <div id="manageSection" class="hidden">
            <!-- 统计卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="text-2xl font-bold text-yellow-600" id="pendingCount">0</div>
                        <div class="ml-3">
                            <div class="text-sm font-medium text-gray-500">待处理</div>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="text-2xl font-bold text-blue-600" id="acceptedCount">0</div>
                        <div class="ml-3">
                            <div class="text-sm font-medium text-gray-500">已接单</div>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="text-2xl font-bold text-purple-600" id="processingCount">0</div>
                        <div class="ml-3">
                            <div class="text-sm font-medium text-gray-500">处理中</div>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="text-2xl font-bold text-green-600" id="completedCount">0</div>
                        <div class="ml-3">
                            <div class="text-sm font-medium text-gray-500">已完成</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 筛选和操作 -->
            <div class="bg-white rounded-lg shadow p-6 mb-6">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                    <div class="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-4">
                        <select id="statusFilter" class="px-3 py-2 border border-gray-300 rounded-lg">
                            <option value="">全部状态</option>
                            <option value="pending">待处理</option>
                            <option value="accepted">已接单</option>
                            <option value="processing">处理中</option>
                            <option value="completed">已完成</option>
                        </select>
                        <select id="urgencyFilter" class="px-3 py-2 border border-gray-300 rounded-lg">
                            <option value="">全部紧急程度</option>
                            <option value="紧急">紧急</option>
                            <option value="高">高</option>
                            <option value="中">中</option>
                            <option value="低">低</option>
                        </select>
                    </div>
                    <div class="flex space-x-2">
                        <button id="refreshBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                            刷新
                        </button>
                    </div>
                </div>
            </div>

            <!-- 工单列表 -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-800">工单列表</h3>
                </div>
                <div id="ordersList" class="divide-y divide-gray-200">
                    <!-- 工单列表将通过JS动态填充 -->
                </div>
            </div>
        </div>
    </main>

    <!-- 工单详情弹窗 -->
    <div id="orderModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-screen overflow-y-auto">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-xl font-semibold text-gray-800">工单详情</h2>
                    <button id="closeModal" class="text-gray-400 hover:text-gray-600 text-2xl">&times;</button>
                </div>
            </div>
            <div class="p-6">
                <div id="modalContent">
                    <!-- 详情内容将通过JS动态填充 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 登录弹窗 -->
    <div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-800">用户登录</h2>
            </div>
            <div class="p-6">
                <form id="loginForm" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                        <input type="text" id="loginUsername" required
                            class="w-full p-3 border border-gray-300 rounded-lg">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                        <input type="password" id="loginPassword" required
                            class="w-full p-3 border border-gray-300 rounded-lg">
                    </div>
                    <div class="flex gap-3 pt-4">
                        <button type="button" id="cancelLogin" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-3 rounded-lg">
                            取消
                        </button>
                        <button type="submit" class="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg">
                            登录
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="../js/config.js?v=2.1"></script>
    <script src="../js/core/error-handler.js?v=2.1"></script>
    <script src="../js/utils.js?v=2.1"></script>
    <script src="../js/auth.js?v=2.1"></script>
    <script src="../js/base-app.js?v=2.1"></script>
    <script>
        class WorkOrderManageApp extends BaseWorkLogApp {
            constructor() {
                super({
                    pageType: 'workorder-manage',
                    requiredElements: ['manageSection']
                });
                
                this.orders = [];
                this.selectedOrder = null;
            }

            getPageElements() {
                return {
                    // 头部元素
                    userInfo: 'userInfo',
                    realName: 'realName',
                    loginBtn: 'loginBtn',
                    logoutBtn: 'logoutBtn',
                    // 登录弹窗元素
                    loginModal: 'loginModal',
                    loginForm: 'loginForm',
                    loginUsername: 'loginUsername',
                    loginPassword: 'loginPassword',
                    cancelLogin: 'cancelLogin',
                    // 页面内容元素
                    accessDenied: 'accessDenied',
                    manageSection: 'manageSection',
                    ordersList: 'ordersList',
                    statusFilter: 'statusFilter',
                    urgencyFilter: 'urgencyFilter',
                    refreshBtn: 'refreshBtn',
                    promptLoginBtn: 'promptLoginBtn',
                    pendingCount: 'pendingCount',
                    acceptedCount: 'acceptedCount',
                    processingCount: 'processingCount',
                    completedCount: 'completedCount',
                    // 弹窗元素
                    orderModal: 'orderModal',
                    modalContent: 'modalContent',
                    closeModal: 'closeModal'
                };
            }

            bindPageEvents() {
                // 筛选事件
                if (this.elements.statusFilter) {
                    this.elements.statusFilter.addEventListener('change', () => this.filterOrders());
                }
                if (this.elements.urgencyFilter) {
                    this.elements.urgencyFilter.addEventListener('change', () => this.filterOrders());
                }

                // 刷新按钮
                if (this.elements.refreshBtn) {
                    this.elements.refreshBtn.addEventListener('click', () => this.loadOrders());
                }

                // 提示登录
                if (this.elements.promptLoginBtn) {
                    this.elements.promptLoginBtn.addEventListener('click', () => this.showLoginModal());
                }

                // 弹窗关闭
                if (this.elements.closeModal) {
                    this.elements.closeModal.addEventListener('click', () => this.closeOrderModal());
                }
            }

            onUserLoggedIn() {
                console.log('=== 工单管理页面：用户登录成功 ===');
                console.log('当前用户:', this.currentUser?.get('username'));
                console.log('用户真实姓名:', this.currentUser?.get('realName'));
                
                this.showUserInterface();
                this.loadOrders();
            }

            onUserLoggedOut() {
                this.showAccessDenied();
            }

            showUserInterface() {
                // 调用父类方法更新头部显示
                super.showUserInterface();

                // 更新页面内容显示
                this.elements.accessDenied.style.display = 'none';
                this.elements.manageSection.style.display = 'block';
            }

            showAccessDenied() {
                // 调用父类方法更新头部显示
                super.showLoginPrompt();

                // 更新页面内容显示
                this.elements.accessDenied.style.display = 'block';
                this.elements.manageSection.style.display = 'none';
            }

            async loadOrders() {
                try {
                    console.log('=== 开始加载工单数据 ===');
                    console.log('使用表名: RepairOrder');
                    
                    // 使用RepairOrder表名，保持与现有数据的兼容性
                    const RepairOrder = AV.Object.extend('RepairOrder');
                    console.log('RepairOrder类已创建');
                    
                    const query = new AV.Query(RepairOrder);
                    console.log('查询对象已创建');
                    
                    query.descending('createdAt');
                    query.limit(100);
                    console.log('查询条件已设置');
                    
                    console.log('开始执行查询...');
                    this.orders = await query.find();
                    console.log('查询成功！找到', this.orders.length, '条工单');
                    
                    this.updateStatistics();
                    this.renderOrders();
                    
                } catch (error) {
                    console.error('=== 加载工单失败 ===');
                    console.error('错误详情:', error);
                    console.error('错误消息:', error.message);
                    console.error('错误代码:', error.code);
                    
                    // 显示更友好的错误信息
                    if (error.message.includes('Class or object doesn\'t exists')) {
                        alert('数据表不存在，请联系管理员初始化数据库');
                    } else {
                        alert('加载工单失败: ' + error.message);
                    }
                }
            }

            updateStatistics() {
                const stats = {
                    pending: 0,
                    accepted: 0,
                    processing: 0,
                    completed: 0
                };

                this.orders.forEach(order => {
                    const status = order.get('status');
                    if (stats.hasOwnProperty(status)) {
                        stats[status]++;
                    }
                });

                this.elements.pendingCount.textContent = stats.pending;
                this.elements.acceptedCount.textContent = stats.accepted;
                this.elements.processingCount.textContent = stats.processing;
                this.elements.completedCount.textContent = stats.completed;
            }

            renderOrders() {
                const filteredOrders = this.getFilteredOrders();
                
                if (filteredOrders.length === 0) {
                    this.elements.ordersList.innerHTML = `
                        <div class="p-8 text-center text-gray-500">
                            暂无工单数据
                        </div>
                    `;
                    return;
                }

                const ordersHtml = filteredOrders.map(order => {
                    const status = order.get('status');
                    const urgency = order.get('urgency');
                    const reportTime = order.get('reportTime');
                    
                    return `
                        <div class="p-6 hover:bg-gray-50 cursor-pointer" onclick="workOrderManageApp.showOrderDetail('${order.id}')">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3 mb-2">
                                        <h4 class="font-medium text-gray-900">${order.get('title') || '工单'}</h4>
                                        <span class="px-2 py-1 rounded-full text-xs font-medium status-${status}">
                                            ${this.getStatusText(status)}
                                        </span>
                                        <span class="px-2 py-1 rounded-full text-xs font-medium ${this.getUrgencyClass(urgency)}">
                                            ${urgency}
                                        </span>
                                    </div>
                                    <div class="text-sm text-gray-600 space-y-1">
                                        <div>工单号：${order.get('orderNumber')}</div>
                                        <div>位置：${order.get('location')}</div>
                                        <div>提交人：${order.get('reporterName')}</div>
                                        <div>提交时间：${reportTime ? new Date(reportTime).toLocaleString() : '-'}</div>
                                    </div>
                                </div>
                                <div class="flex space-x-2">
                                    ${this.getActionButtons(order)}
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');

                this.elements.ordersList.innerHTML = ordersHtml;
            }

            getFilteredOrders() {
                let filtered = this.orders;

                const statusFilter = this.elements.statusFilter.value;
                if (statusFilter) {
                    filtered = filtered.filter(order => order.get('status') === statusFilter);
                }

                const urgencyFilter = this.elements.urgencyFilter.value;
                if (urgencyFilter) {
                    filtered = filtered.filter(order => order.get('urgency') === urgencyFilter);
                }

                return filtered;
            }

            getStatusText(status) {
                const statusMap = {
                    'pending': '待处理',
                    'accepted': '已接单',
                    'processing': '处理中',
                    'completed': '已完成',
                    'cancelled': '已取消'
                };
                return statusMap[status] || status;
            }

            getUrgencyClass(urgency) {
                const classMap = {
                    '紧急': 'bg-red-100 text-red-800',
                    '高': 'bg-orange-100 text-orange-800',
                    '中': 'bg-yellow-100 text-yellow-800',
                    '低': 'bg-green-100 text-green-800'
                };
                return classMap[urgency] || 'bg-gray-100 text-gray-800';
            }

            filterOrders() {
                this.renderOrders();
            }

            getActionButtons(order) {
                const status = order.get('status');
                const currentUser = this.currentUser;
                const processor = order.get('processor');

                let buttons = [];

                if (status === 'pending') {
                    buttons.push(`<button onclick="event.stopPropagation(); workOrderManageApp.acceptOrder('${order.id}')" class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-xs">接单</button>`);
                } else if (status === 'accepted' && processor === currentUser?.get('realName')) {
                    buttons.push(`<button onclick="event.stopPropagation(); workOrderManageApp.startProcessing('${order.id}')" class="bg-purple-500 hover:bg-purple-600 text-white px-3 py-1 rounded text-xs">开始处理</button>`);
                } else if (status === 'processing' && processor === currentUser?.get('realName')) {
                    buttons.push(`<button onclick="event.stopPropagation(); workOrderManageApp.completeOrder('${order.id}')" class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-xs">完成</button>`);
                }

                return buttons.join('');
            }

            async acceptOrder(orderId) {
                try {
                    const order = this.orders.find(o => o.id === orderId);
                    if (!order) return;

                    order.set('status', 'accepted');
                    order.set('processor', this.currentUser.get('realName'));
                    order.set('acceptTime', new Date());

                    await order.save();

                    alert('接单成功！');
                    this.loadOrders();

                } catch (error) {
                    console.error('接单失败:', error);
                    alert('接单失败: ' + error.message);
                }
            }

            async startProcessing(orderId) {
                try {
                    const order = this.orders.find(o => o.id === orderId);
                    if (!order) return;

                    const note = prompt('请输入处理说明（可选）:');

                    order.set('status', 'processing');
                    order.set('startTime', new Date());
                    if (note) {
                        order.set('processNote', note);
                    }

                    await order.save();

                    alert('已开始处理！');
                    this.loadOrders();

                } catch (error) {
                    console.error('开始处理失败:', error);
                    alert('开始处理失败: ' + error.message);
                }
            }

            async completeOrder(orderId) {
                try {
                    const order = this.orders.find(o => o.id === orderId);
                    if (!order) return;

                    const note = prompt('请输入完成说明:');
                    if (!note) {
                        alert('请输入完成说明');
                        return;
                    }

                    const completeTime = new Date();

                    order.set('status', 'completed');
                    order.set('completeTime', completeTime);
                    order.set('completeNote', note);

                    await order.save();

                    // 同时写入工作日志
                    console.log('开始写入工单完成记录到工作日志');
                    await this.saveCompletionToWorkLog({
                        orderNumber: order.get('orderNumber'),
                        completeNote: note,
                        completeTime: completeTime,
                        category: order.get('category'),
                        location: order.get('location'),
                        description: order.get('description')
                    });

                    alert('工单已完成！');
                    this.loadOrders();

                } catch (error) {
                    console.error('完成工单失败:', error);
                    alert('完成工单失败: ' + error.message);
                }
            }

            /**
             * 保存工单完成记录到工作日志
             */
            async saveCompletionToWorkLog(orderData) {
                try {
                    const WorkLog = AV.Object.extend('WorkLog');
                    const workLog = new WorkLog();

                    // 设置基础信息
                    workLog.set('user', this.currentUser);
                    workLog.set('username', this.currentUser.get('username'));
                    workLog.set('pageType', 'workorder-manage');

                    // 设置完成记录内容
                    const content = `完成工单：${orderData.orderNumber}\n` +
                                   `类别：${orderData.category}\n` +
                                   `位置：${orderData.location}\n` +
                                   `完成说明：${orderData.completeNote}`;

                    const logData = {
                        action: 'complete',
                        orderNumber: orderData.orderNumber,
                        category: orderData.category,
                        location: orderData.location,
                        description: orderData.description,
                        completeNote: orderData.completeNote,
                        completeTime: orderData.completeTime
                    };

                    // 设置数据
                    workLog.set('operationData', logData);
                    workLog.set('images', []);
                    workLog.set('content', content);

                    // 保存到LeanCloud
                    await workLog.save();

                    console.log('工单完成记录已保存到工作日志:', orderData.orderNumber);

                } catch (error) {
                    console.error('保存工单完成记录到工作日志失败:', error);
                    console.error('错误详情:', error.message);
                    // 不影响主流程，但要记录详细错误信息
                    alert('工作日志保存失败: ' + error.message);
                }
            }

            showOrderDetail(orderId) {
                const order = this.orders.find(o => o.id === orderId);
                if (!order) return;

                this.selectedOrder = order;
                this.renderOrderDetail();
                this.elements.orderModal.classList.remove('hidden');
            }

            closeOrderModal() {
                this.elements.orderModal.classList.add('hidden');
                this.selectedOrder = null;
            }

            renderOrderDetail() {
                if (!this.selectedOrder) return;

                const order = this.selectedOrder;
                const images = order.get('images') || [];

                this.elements.modalContent.innerHTML = `
                    <div class="space-y-6">
                        <!-- 基本信息 -->
                        <div>
                            <h3 class="text-lg font-semibold mb-4">基本信息</h3>
                            <div class="grid grid-cols-2 gap-4">
                                <div><span class="font-medium">工单号：</span>${order.get('orderNumber')}</div>
                                <div><span class="font-medium">类别：</span>${order.get('category')}</div>
                                <div><span class="font-medium">紧急程度：</span>${order.get('urgency')}</div>
                                <div><span class="font-medium">位置：</span>${order.get('location')}</div>
                                <div><span class="font-medium">状态：</span>${this.getStatusText(order.get('status'))}</div>
                                <div><span class="font-medium">提交人：</span>${order.get('reporterName')}</div>
                            </div>
                            <div class="mt-4">
                                <span class="font-medium">描述：</span>
                                <p class="mt-1 text-gray-700">${order.get('description')}</p>
                            </div>
                        </div>

                        <!-- 图片 -->
                        ${images.length > 0 ? `
                            <div>
                                <h3 class="text-lg font-semibold mb-4">相关图片</h3>
                                <div class="grid grid-cols-3 gap-4">
                                    ${images.map(url => `
                                        <img src="${url}" alt="工单图片" class="w-full h-32 object-cover rounded-lg cursor-pointer"
                                             onclick="window.open('${url}', '_blank')">
                                    `).join('')}
                                </div>
                            </div>
                        ` : ''}

                        <!-- 处理信息 -->
                        <div>
                            <h3 class="text-lg font-semibold mb-4">处理信息</h3>
                            <div class="space-y-2">
                                <div><span class="font-medium">处理人：</span>${order.get('processor') || '未分配'}</div>
                                <div><span class="font-medium">接单时间：</span>${order.get('acceptTime') ? new Date(order.get('acceptTime')).toLocaleString() : '-'}</div>
                                <div><span class="font-medium">完成时间：</span>${order.get('completeTime') ? new Date(order.get('completeTime')).toLocaleString() : '-'}</div>
                                ${order.get('processNote') ? `<div><span class="font-medium">处理说明：</span>${order.get('processNote')}</div>` : ''}
                                ${order.get('completeNote') ? `<div><span class="font-medium">完成说明：</span>${order.get('completeNote')}</div>` : ''}
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="flex justify-end space-x-4 pt-4 border-t">
                            ${this.getModalActionButtons(order)}
                        </div>
                    </div>
                `;
            }

            getModalActionButtons(order) {
                const status = order.get('status');
                const currentUser = this.currentUser;
                const processor = order.get('processor');

                let buttons = ['<button onclick="workOrderManageApp.closeOrderModal()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded">关闭</button>'];

                if (status === 'pending') {
                    buttons.unshift(`<button onclick="workOrderManageApp.acceptOrder('${order.id}')" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">接单</button>`);
                } else if (status === 'accepted' && processor === currentUser?.get('realName')) {
                    buttons.unshift(`<button onclick="workOrderManageApp.startProcessing('${order.id}')" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded">开始处理</button>`);
                } else if (status === 'processing' && processor === currentUser?.get('realName')) {
                    buttons.unshift(`<button onclick="workOrderManageApp.completeOrder('${order.id}')" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">完成</button>`);
                }

                return buttons.join('');
            }
        }

        // 全局变量
        let workOrderManageApp;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            function initApp() {
                if (typeof AV !== 'undefined') {
                    workOrderManageApp = new WorkOrderManageApp();
                    workOrderManageApp.init();
                } else {
                    setTimeout(initApp, 100);
                }
            }
            initApp();
        });
    </script>
</body>
</html>
