// composables/useLeanCloud.ts
import AV from 'leancloud-storage'
import type { User, WorkLog, ApiResponse } from '~/types'

// 检查LeanCloud是否已初始化
const checkLeanCloudInit = () => {
  try {
    // 尝试访问AV的内部属性来检查是否已初始化
    if (!AV._config || !AV._config.applicationId) {
      throw new Error('LeanCloud未初始化')
    }
    return true
  } catch (error) {
    console.error('LeanCloud初始化检查失败:', error)
    return false
  }
}

export const useLeanCloud = () => {
  // 在使用前检查初始化状态
  if (!checkLeanCloudInit()) {
    console.warn('LeanCloud未正确初始化，某些功能可能无法使用')
  }
  // 用户认证相关
  const auth = {
    // 登录
    async login(username: string, password: string): Promise<ApiResponse<User>> {
      try {
        const user = await AV.User.logIn(username, password)
        const userData: User = {
          id: user.id,
          username: user.getUsername(),
          realName: user.get('realName'),
          phone: user.get('phone'),
          department: user.get('department'),
          roles: user.get('roles') || ['user'],
          createdAt: user.createdAt
        }
        return { success: true, data: userData }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    },

    // 注册
    async register(userData: {
      username: string
      password: string
      realName?: string
      phone?: string
      department?: string
    }): Promise<ApiResponse<User>> {
      try {
        const user = new AV.User()
        user.setUsername(userData.username)
        user.setPassword(userData.password)
        
        if (userData.realName) user.set('realName', userData.realName)
        if (userData.phone) user.set('phone', userData.phone)
        if (userData.department) user.set('department', userData.department)
        user.set('roles', ['user'])
        
        await user.signUp()
        
        const newUser: User = {
          id: user.id,
          username: user.getUsername(),
          realName: userData.realName,
          phone: userData.phone,
          department: userData.department,
          roles: ['user'],
          createdAt: user.createdAt
        }
        
        return { success: true, data: newUser }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    },

    // 登出
    async logout(): Promise<ApiResponse> {
      try {
        await AV.User.logOut()
        return { success: true }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    },

    // 获取当前用户
    getCurrentUser(): User | null {
      try {
        const currentUser = AV.User.current()
        if (!currentUser) return null
      
      return {
        id: currentUser.id,
        username: currentUser.getUsername(),
        realName: currentUser.get('realName'),
        phone: currentUser.get('phone'),
        department: currentUser.get('department'),
        roles: currentUser.get('roles') || ['user'],
        createdAt: currentUser.createdAt
      }
      } catch (error) {
        console.error('获取当前用户失败，LeanCloud可能未初始化:', error)
        return null
      }
    }
  }

  // 工作日志相关
  const workLog = {
    // 创建日志
    async create(logData: {
      content: string
      images?: string[]
      pageType: string
    }): Promise<ApiResponse<WorkLog>> {
      try {
        const WorkLogClass = AV.Object.extend('WorkLog')
        const log = new WorkLogClass()
        
        log.set('content', logData.content)
        log.set('images', logData.images || [])
        log.set('pageType', logData.pageType)
        log.set('user', AV.User.current())
        
        await log.save()
        
        const newLog: WorkLog = {
          id: log.id,
          content: log.get('content'),
          images: log.get('images'),
          pageType: log.get('pageType'),
          createdAt: log.createdAt
        }
        
        return { success: true, data: newLog }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    },

    // 获取日志列表
    async getList(options: {
      pageType?: string
      page?: number
      limit?: number
      userId?: string
    } = {}): Promise<ApiResponse<WorkLog[]>> {
      try {
        const query = new AV.Query('WorkLog')
        
        if (options.pageType) {
          query.equalTo('pageType', options.pageType)
        }
        
        if (options.userId) {
          const user = AV.Object.createWithoutData('_User', options.userId)
          query.equalTo('user', user)
        }
        
        query.include('user')
        query.descending('createdAt')
        query.limit(options.limit || 10)
        query.skip((options.page || 0) * (options.limit || 10))
        
        const results = await query.find()
        
        const logs: WorkLog[] = results.map(log => ({
          id: log.id,
          content: log.get('content'),
          images: log.get('images') || [],
          pageType: log.get('pageType'),
          user: log.get('user') ? {
            id: log.get('user').id,
            username: log.get('user').getUsername(),
            realName: log.get('user').get('realName'),
            department: log.get('user').get('department'),
            roles: log.get('user').get('roles') || ['user'],
            createdAt: log.get('user').createdAt
          } : undefined,
          createdAt: log.createdAt,
          updatedAt: log.updatedAt
        }))
        
        return { success: true, data: logs }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    },

    // 删除日志
    async delete(logId: string): Promise<ApiResponse> {
      try {
        const log = AV.Object.createWithoutData('WorkLog', logId)
        await log.destroy()
        return { success: true }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    }
  }

  // 文件上传
  const file = {
    async upload(fileData: File | Blob, fileName?: string): Promise<ApiResponse<string>> {
      try {
        const avFile = new AV.File(fileName || 'upload', fileData)
        await avFile.save()
        return { success: true, data: avFile.url() }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    }
  }

  // 库存管理相关
  const inventory = {
    // 获取库存列表
    async getList(options: {
      page?: number
      limit?: number
      category?: string
      keyword?: string
      lowStock?: boolean
    } = {}) {
      try {
        const query = new AV.Query('InventoryItem')

        // 分页
        const page = options.page || 0
        const limit = options.limit || 20
        query.skip(page * limit)
        query.limit(limit)

        // 筛选条件
        if (options.category) {
          query.equalTo('category', options.category)
        }

        if (options.keyword) {
          query.contains('name', options.keyword)
        }

        if (options.lowStock) {
          // 查询低库存物品 - 简化查询
          query.lessThanOrEqualTo('quantity', 10)
        }

        // 排序
        query.descending('updatedAt')

        const results = await query.find()
        const items = results.map(item => ({
          id: item.id,
          name: item.get('name'),
          category: item.get('category'),
          quantity: item.get('quantity'),
          unit: item.get('unit'),
          minStock: item.get('minStock'),
          maxStock: item.get('maxStock'),
          price: item.get('price'),
          supplier: item.get('supplier'),
          location: item.get('location'),
          description: item.get('description'),
          images: item.get('images') || [],
          createdAt: item.createdAt,
          updatedAt: item.updatedAt
        }))

        return { success: true, items, total: results.length }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    },

    // 创建库存项目
    async create(itemData: {
      name: string
      category: string
      quantity: number
      unit: string
      minStock: number
      maxStock?: number
      price?: number
      supplier?: string
      location?: string
      description?: string
      images?: string[]
    }) {
      try {
        const InventoryItem = AV.Object.extend('InventoryItem')
        const item = new InventoryItem()

        item.set('name', itemData.name)
        item.set('category', itemData.category)
        item.set('quantity', itemData.quantity)
        item.set('unit', itemData.unit)
        item.set('minStock', itemData.minStock)
        item.set('maxStock', itemData.maxStock || 0)
        item.set('price', itemData.price || 0)
        item.set('supplier', itemData.supplier || '')
        item.set('location', itemData.location || '')
        item.set('description', itemData.description || '')
        item.set('images', itemData.images || [])

        const savedItem = await item.save()

        return {
          success: true,
          item: {
            id: savedItem.id,
            name: savedItem.get('name'),
            category: savedItem.get('category'),
            quantity: savedItem.get('quantity'),
            unit: savedItem.get('unit'),
            minStock: savedItem.get('minStock'),
            maxStock: savedItem.get('maxStock'),
            price: savedItem.get('price'),
            supplier: savedItem.get('supplier'),
            location: savedItem.get('location'),
            description: savedItem.get('description'),
            images: savedItem.get('images'),
            createdAt: savedItem.createdAt,
            updatedAt: savedItem.updatedAt
          }
        }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    },

    // 更新库存项目
    async update(id: string, itemData: any) {
      try {
        const item = AV.Object.createWithoutData('InventoryItem', id)

        Object.keys(itemData).forEach(key => {
          if (key !== 'id' && key !== 'createdAt' && key !== 'updatedAt') {
            item.set(key, itemData[key])
          }
        })

        const savedItem = await item.save()

        return {
          success: true,
          item: {
            id: savedItem.id,
            name: savedItem.get('name'),
            category: savedItem.get('category'),
            quantity: savedItem.get('quantity'),
            unit: savedItem.get('unit'),
            minStock: savedItem.get('minStock'),
            maxStock: savedItem.get('maxStock'),
            price: savedItem.get('price'),
            supplier: savedItem.get('supplier'),
            location: savedItem.get('location'),
            description: savedItem.get('description'),
            images: savedItem.get('images'),
            createdAt: savedItem.createdAt,
            updatedAt: savedItem.updatedAt
          }
        }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    },

    // 删除库存项目
    async delete(id: string) {
      try {
        const item = AV.Object.createWithoutData('InventoryItem', id)
        await item.destroy()
        return { success: true }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    },

    // 库存调整
    async adjustStock(id: string, adjustment: {
      type: 'in' | 'out' | 'adjust'
      quantity: number
      reason: string
      operator?: string
    }) {
      try {
        // 获取当前库存
        const item = await new AV.Query('InventoryItem').get(id)
        const currentQuantity = item.get('quantity')

        let newQuantity = currentQuantity
        switch (adjustment.type) {
          case 'in':
            newQuantity = currentQuantity + adjustment.quantity
            break
          case 'out':
            newQuantity = currentQuantity - adjustment.quantity
            break
          case 'adjust':
            newQuantity = adjustment.quantity
            break
        }

        // 更新库存数量
        item.set('quantity', newQuantity)
        await item.save()

        // 记录库存变动
        const Transaction = AV.Object.extend('InventoryTransaction')
        const transaction = new Transaction()

        transaction.set('itemId', id)
        transaction.set('type', adjustment.type)
        transaction.set('quantity', adjustment.quantity)
        transaction.set('beforeQuantity', currentQuantity)
        transaction.set('afterQuantity', newQuantity)
        transaction.set('reason', adjustment.reason)
        transaction.set('operator', adjustment.operator || '')

        await transaction.save()

        return { success: true, newQuantity }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    },

    // 创建盘点计划
    async createCountPlan(planData: {
      name: string
      description?: string
      startDate: Date
      endDate: Date
      items: string[]
      createdBy: string
    }) {
      try {
        const CountPlan = AV.Object.extend('StockCountPlan')
        const plan = new CountPlan()

        plan.set('name', planData.name)
        plan.set('description', planData.description || '')
        plan.set('startDate', planData.startDate)
        plan.set('endDate', planData.endDate)
        plan.set('items', planData.items)
        plan.set('status', 'pending')
        plan.set('createdBy', planData.createdBy)

        const savedPlan = await plan.save()

        return {
          success: true,
          plan: {
            id: savedPlan.id,
            name: savedPlan.get('name'),
            description: savedPlan.get('description'),
            startDate: savedPlan.get('startDate'),
            endDate: savedPlan.get('endDate'),
            items: savedPlan.get('items'),
            status: savedPlan.get('status'),
            createdBy: savedPlan.get('createdBy'),
            createdAt: savedPlan.createdAt,
            updatedAt: savedPlan.updatedAt
          }
        }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    },

    // 获取盘点计划列表
    async getCountPlans(options: { page?: number; limit?: number } = {}) {
      try {
        const query = new AV.Query('StockCountPlan')

        // 分页
        const page = options.page || 0
        const limit = options.limit || 20
        query.skip(page * limit)
        query.limit(limit)

        // 排序
        query.descending('createdAt')

        const results = await query.find()
        const plans = results.map(plan => ({
          id: plan.id,
          name: plan.get('name'),
          description: plan.get('description'),
          startDate: plan.get('startDate'),
          endDate: plan.get('endDate'),
          items: plan.get('items'),
          status: plan.get('status'),
          createdBy: plan.get('createdBy'),
          createdAt: plan.createdAt,
          updatedAt: plan.updatedAt
        }))

        return { success: true, plans }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    },

    // 提交盘点记录
    async submitCountRecord(recordData: {
      planId: string
      itemId: string
      systemQuantity: number
      actualQuantity: number
      remark?: string
      countBy: string
    }) {
      try {
        const CountRecord = AV.Object.extend('StockCountRecord')
        const record = new CountRecord()

        record.set('planId', recordData.planId)
        record.set('itemId', recordData.itemId)
        record.set('systemQuantity', recordData.systemQuantity)
        record.set('actualQuantity', recordData.actualQuantity)
        record.set('difference', recordData.actualQuantity - recordData.systemQuantity)
        record.set('remark', recordData.remark || '')
        record.set('countBy', recordData.countBy)
        record.set('countDate', new Date())

        const savedRecord = await record.save()

        return {
          success: true,
          record: {
            id: savedRecord.id,
            planId: savedRecord.get('planId'),
            itemId: savedRecord.get('itemId'),
            systemQuantity: savedRecord.get('systemQuantity'),
            actualQuantity: savedRecord.get('actualQuantity'),
            difference: savedRecord.get('difference'),
            remark: savedRecord.get('remark'),
            countBy: savedRecord.get('countBy'),
            countDate: savedRecord.get('countDate'),
            createdAt: savedRecord.createdAt
          }
        }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    }
  }

  // 报修管理相关
  const repair = {
    // 获取报修列表
    async getList(options: {
      page?: number
      limit?: number
      status?: string
      priority?: number
      category?: string
      assigneeId?: string
      reporterId?: string
    } = {}) {
      try {
        const query = new AV.Query('RepairOrder')

        // 分页
        const page = options.page || 0
        const limit = options.limit || 20
        query.skip(page * limit)
        query.limit(limit)

        // 筛选条件
        if (options.status) {
          query.equalTo('status', options.status)
        }

        if (options.priority) {
          query.equalTo('priority', options.priority)
        }

        if (options.category) {
          query.equalTo('category', options.category)
        }

        if (options.assigneeId) {
          query.equalTo('assigneeId', options.assigneeId)
        }

        if (options.reporterId) {
          query.equalTo('reporterId', options.reporterId)
        }

        // 包含关联数据
        query.include('reporter')
        query.include('assignee')

        // 排序
        query.descending('createdAt')

        const results = await query.find()
        const repairs = results.map(repair => ({
          id: repair.id,
          orderNumber: repair.get('orderNumber'),
          title: repair.get('title'),
          description: repair.get('description'),
          location: repair.get('location'),
          category: repair.get('category'),
          priority: repair.get('priority'),
          status: repair.get('status'),
          reporterId: repair.get('reporterId'),
          assigneeId: repair.get('assigneeId'),
          contactInfo: repair.get('contactInfo'),
          images: repair.get('images') || [],
          estimatedCost: repair.get('estimatedCost'),
          actualCost: repair.get('actualCost'),
          completedAt: repair.get('completedAt'),
          createdAt: repair.createdAt,
          updatedAt: repair.updatedAt
        }))

        return { success: true, repairs, total: results.length }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    },

    // 创建报修工单
    async create(repairData: {
      title: string
      description: string
      location: string
      category: string
      priority: number
      reporterId: string
      contactInfo?: string
      images?: string[]
    }) {
      try {
        const RepairOrder = AV.Object.extend('RepairOrder')
        const repair = new RepairOrder()

        // 生成工单号
        const orderNumber = `R${Date.now()}`

        repair.set('orderNumber', orderNumber)
        repair.set('title', repairData.title)
        repair.set('description', repairData.description)
        repair.set('location', repairData.location)
        repair.set('category', repairData.category)
        repair.set('priority', repairData.priority)
        repair.set('status', 'pending')
        repair.set('reporterId', repairData.reporterId)
        repair.set('contactInfo', repairData.contactInfo || '')
        repair.set('images', repairData.images || [])

        const savedRepair = await repair.save()

        return {
          success: true,
          repair: {
            id: savedRepair.id,
            orderNumber: savedRepair.get('orderNumber'),
            title: savedRepair.get('title'),
            description: savedRepair.get('description'),
            location: savedRepair.get('location'),
            category: savedRepair.get('category'),
            priority: savedRepair.get('priority'),
            status: savedRepair.get('status'),
            reporterId: savedRepair.get('reporterId'),
            contactInfo: savedRepair.get('contactInfo'),
            images: savedRepair.get('images'),
            createdAt: savedRepair.createdAt,
            updatedAt: savedRepair.updatedAt
          }
        }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    },

    // 更新报修工单
    async update(id: string, updateData: any) {
      try {
        const repair = AV.Object.createWithoutData('RepairOrder', id)

        Object.keys(updateData).forEach(key => {
          if (key !== 'id' && key !== 'createdAt' && key !== 'updatedAt') {
            repair.set(key, updateData[key])
          }
        })

        const savedRepair = await repair.save()

        return {
          success: true,
          repair: {
            id: savedRepair.id,
            orderNumber: savedRepair.get('orderNumber'),
            title: savedRepair.get('title'),
            description: savedRepair.get('description'),
            location: savedRepair.get('location'),
            category: savedRepair.get('category'),
            priority: savedRepair.get('priority'),
            status: savedRepair.get('status'),
            reporterId: savedRepair.get('reporterId'),
            assigneeId: savedRepair.get('assigneeId'),
            contactInfo: savedRepair.get('contactInfo'),
            images: savedRepair.get('images'),
            estimatedCost: savedRepair.get('estimatedCost'),
            actualCost: savedRepair.get('actualCost'),
            completedAt: savedRepair.get('completedAt'),
            createdAt: savedRepair.createdAt,
            updatedAt: savedRepair.updatedAt
          }
        }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    },

    // 分配工单
    async assign(id: string, assigneeId: string, estimatedCost?: number) {
      try {
        const repair = AV.Object.createWithoutData('RepairOrder', id)

        repair.set('assigneeId', assigneeId)
        repair.set('status', 'assigned')
        if (estimatedCost !== undefined) {
          repair.set('estimatedCost', estimatedCost)
        }

        await repair.save()

        return { success: true }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    },

    // 完成工单
    async complete(id: string, actualCost?: number, completionNotes?: string) {
      try {
        const repair = AV.Object.createWithoutData('RepairOrder', id)

        repair.set('status', 'completed')
        repair.set('completedAt', new Date())
        if (actualCost !== undefined) {
          repair.set('actualCost', actualCost)
        }
        if (completionNotes) {
          repair.set('completionNotes', completionNotes)
        }

        await repair.save()

        return { success: true }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    }
  }

  // 用户管理相关
  const userManagement = {
    // 获取用户列表
    async getUsers(options: {
      page?: number
      limit?: number
      department?: string
      role?: string
      keyword?: string
    } = {}) {
      try {
        const query = new AV.Query('_User')

        // 分页
        const page = options.page || 0
        const limit = options.limit || 20
        query.skip(page * limit)
        query.limit(limit)

        // 筛选条件
        if (options.department) {
          query.equalTo('department', options.department)
        }

        if (options.role) {
          query.contains('roles', options.role)
        }

        if (options.keyword) {
          query.contains('realName', options.keyword)
        }

        // 排序
        query.descending('createdAt')

        const results = await query.find()
        const users = results.map(user => ({
          id: user.id,
          username: user.get('username'),
          realName: user.get('realName'),
          email: user.get('email'),
          phone: user.get('phone'),
          department: user.get('department'),
          roles: user.get('roles') || [],
          status: user.get('status') || 'active',
          createdAt: user.createdAt,
          updatedAt: user.updatedAt
        }))

        return { success: true, users, total: results.length }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    },

    // 创建用户
    async createUser(userData: {
      username: string
      password: string
      realName: string
      email?: string
      phone?: string
      department: string
      roles: string[]
    }) {
      try {
        const user = new AV.User()

        user.set('username', userData.username)
        user.set('password', userData.password)
        user.set('realName', userData.realName)
        user.set('email', userData.email || '')
        user.set('phone', userData.phone || '')
        user.set('department', userData.department)
        user.set('roles', userData.roles)
        user.set('status', 'active')

        const savedUser = await user.signUp()

        return {
          success: true,
          user: {
            id: savedUser.id,
            username: savedUser.get('username'),
            realName: savedUser.get('realName'),
            email: savedUser.get('email'),
            phone: savedUser.get('phone'),
            department: savedUser.get('department'),
            roles: savedUser.get('roles'),
            status: savedUser.get('status'),
            createdAt: savedUser.createdAt,
            updatedAt: savedUser.updatedAt
          }
        }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    },

    // 更新用户
    async updateUser(id: string, userData: any) {
      try {
        const user = AV.Object.createWithoutData('_User', id)

        Object.keys(userData).forEach(key => {
          if (key !== 'id' && key !== 'username' && key !== 'createdAt' && key !== 'updatedAt') {
            user.set(key, userData[key])
          }
        })

        const savedUser = await user.save()

        return {
          success: true,
          user: {
            id: savedUser.id,
            username: savedUser.get('username'),
            realName: savedUser.get('realName'),
            email: savedUser.get('email'),
            phone: savedUser.get('phone'),
            department: savedUser.get('department'),
            roles: savedUser.get('roles'),
            status: savedUser.get('status'),
            createdAt: savedUser.createdAt,
            updatedAt: savedUser.updatedAt
          }
        }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    },

    // 删除用户
    async deleteUser(id: string) {
      try {
        const user = AV.Object.createWithoutData('_User', id)
        await user.destroy()
        return { success: true }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    }
  }

  // 部门管理相关
  const department = {
    // 获取部门列表
    async getList() {
      try {
        const query = new AV.Query('Department')
        query.ascending('name')

        const results = await query.find()
        const departments = results.map(dept => ({
          id: dept.id,
          name: dept.get('name'),
          description: dept.get('description'),
          parentId: dept.get('parentId'),
          createdAt: dept.createdAt,
          updatedAt: dept.updatedAt
        }))

        return { success: true, departments }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    },

    // 创建部门
    async create(deptData: {
      name: string
      description?: string
      parentId?: string
    }) {
      try {
        const Department = AV.Object.extend('Department')
        const dept = new Department()

        dept.set('name', deptData.name)
        dept.set('description', deptData.description || '')
        dept.set('parentId', deptData.parentId || '')

        const savedDept = await dept.save()

        return {
          success: true,
          department: {
            id: savedDept.id,
            name: savedDept.get('name'),
            description: savedDept.get('description'),
            parentId: savedDept.get('parentId'),
            createdAt: savedDept.createdAt,
            updatedAt: savedDept.updatedAt
          }
        }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    }
  }

  return {
    auth,
    workLog,
    file,
    inventory,
    repair,
    userManagement,
    department,
    AV
  }
}
