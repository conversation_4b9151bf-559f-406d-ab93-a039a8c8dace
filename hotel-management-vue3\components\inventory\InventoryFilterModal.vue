<template>
  <div 
    v-if="show" 
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    @click="handleBackdropClick"
  >
    <div 
      class="bg-white rounded-lg w-full max-w-md"
      @click.stop
    >
      <!-- 头部 -->
      <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-900">筛选条件</h3>
        <button 
          @click="handleClose"
          class="text-gray-400 hover:text-gray-600"
        >
          <Icon name="mdi:close" size="24" />
        </button>
      </div>

      <!-- 内容 -->
      <div class="px-6 py-4">
        <form @submit.prevent="handleApply">
          <!-- 分类筛选 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              分类
            </label>
            <select 
              v-model="filters.category"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">全部分类</option>
              <option value="客房用品">客房用品</option>
              <option value="清洁用品">清洁用品</option>
              <option value="办公用品">办公用品</option>
              <option value="维修工具">维修工具</option>
              <option value="餐饮用品">餐饮用品</option>
              <option value="电子设备">电子设备</option>
              <option value="其他">其他</option>
            </select>
          </div>

          <!-- 位置筛选 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              存放位置
            </label>
            <select 
              v-model="filters.location"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">全部位置</option>
              <option value="仓库A">仓库A</option>
              <option value="仓库B">仓库B</option>
              <option value="仓库C">仓库C</option>
              <option value="1楼储物间">1楼储物间</option>
              <option value="2楼储物间">2楼储物间</option>
              <option value="地下室">地下室</option>
              <option value="办公室">办公室</option>
            </select>
          </div>

          <!-- 状态筛选 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              状态
            </label>
            <select 
              v-model="filters.status"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">全部状态</option>
              <option value="active">正常</option>
              <option value="inactive">停用</option>
              <option value="discontinued">停产</option>
            </select>
          </div>

          <!-- 库存状态筛选 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-3">
              库存状态
            </label>
            <div class="space-y-3">
              <label class="flex items-center">
                <input 
                  v-model="filters.lowStock"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span class="ml-2 text-sm text-gray-700">只显示低库存物品</span>
              </label>
              
              <label class="flex items-center">
                <input 
                  v-model="filters.outOfStock"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span class="ml-2 text-sm text-gray-700">只显示缺货物品</span>
              </label>
            </div>
          </div>

          <!-- 价格范围筛选 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              单价范围 (元)
            </label>
            <div class="grid grid-cols-2 gap-3">
              <div>
                <input 
                  v-model.number="filters.minPrice"
                  type="number"
                  min="0"
                  step="0.01"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="最低价"
                />
              </div>
              <div>
                <input 
                  v-model.number="filters.maxPrice"
                  type="number"
                  min="0"
                  step="0.01"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="最高价"
                />
              </div>
            </div>
          </div>

          <!-- 供应商筛选 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              供应商
            </label>
            <input 
              v-model="filters.supplier"
              type="text"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请输入供应商名称"
            />
          </div>

          <!-- 关键词搜索 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              关键词搜索
            </label>
            <input 
              v-model="filters.keyword"
              type="text"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="搜索物品名称、描述、条码等"
            />
          </div>
        </form>
      </div>

      <!-- 底部按钮 -->
      <div class="px-6 py-4 border-t border-gray-200 flex justify-between">
        <button 
          type="button"
          @click="handleReset"
          class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
        >
          重置筛选
        </button>
        
        <div class="flex space-x-3">
          <button 
            type="button"
            @click="handleClose"
            class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
          >
            取消
          </button>
          <button 
            @click="handleApply"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            应用筛选
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  show: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:show': [value: boolean]
  apply: [filters: any]
}>()

const inventoryStore = useInventoryStore()

// 响应式数据
const filters = reactive({
  category: '',
  location: '',
  status: '',
  lowStock: false,
  outOfStock: false,
  minPrice: undefined as number | undefined,
  maxPrice: undefined as number | undefined,
  supplier: '',
  keyword: ''
})

// 方法
const resetFilters = () => {
  filters.category = ''
  filters.location = ''
  filters.status = ''
  filters.lowStock = false
  filters.outOfStock = false
  filters.minPrice = undefined
  filters.maxPrice = undefined
  filters.supplier = ''
  filters.keyword = ''
}

const loadCurrentFilters = () => {
  const currentFilters = inventoryStore.filters
  
  filters.category = currentFilters.category || ''
  filters.location = currentFilters.location || ''
  filters.status = currentFilters.status || ''
  filters.lowStock = currentFilters.lowStock || false
  filters.outOfStock = currentFilters.outOfStock || false
  filters.minPrice = currentFilters.minPrice
  filters.maxPrice = currentFilters.maxPrice
  filters.supplier = currentFilters.supplier || ''
  filters.keyword = currentFilters.keyword || ''
}

const handleClose = () => {
  emit('update:show', false)
}

const handleBackdropClick = () => {
  handleClose()
}

const handleReset = () => {
  resetFilters()
}

const handleApply = () => {
  // 构建筛选对象，过滤掉空值
  const appliedFilters: any = {}
  
  if (filters.category) appliedFilters.category = filters.category
  if (filters.location) appliedFilters.location = filters.location
  if (filters.status) appliedFilters.status = filters.status
  if (filters.lowStock) appliedFilters.lowStock = filters.lowStock
  if (filters.outOfStock) appliedFilters.outOfStock = filters.outOfStock
  if (filters.minPrice !== undefined && filters.minPrice >= 0) appliedFilters.minPrice = filters.minPrice
  if (filters.maxPrice !== undefined && filters.maxPrice >= 0) appliedFilters.maxPrice = filters.maxPrice
  if (filters.supplier.trim()) appliedFilters.supplier = filters.supplier.trim()
  if (filters.keyword.trim()) appliedFilters.keyword = filters.keyword.trim()
  
  emit('apply', appliedFilters)
  handleClose()
}

// 监听props变化
watch(() => props.show, (newValue) => {
  if (newValue) {
    loadCurrentFilters()
  }
})

// 键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.show) {
    handleClose()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>
