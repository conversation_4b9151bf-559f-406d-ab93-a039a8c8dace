<template>
  <div 
    v-if="show" 
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    @click="handleBackdropClick"
  >
    <div 
      class="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden"
      @click.stop
    >
      <!-- 头部 -->
      <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-900">
          {{ isCreating ? '创建盘点计划' : '库存盘点' }}
        </h3>
        <button 
          @click="handleClose"
          class="text-gray-400 hover:text-gray-600"
        >
          <Icon name="mdi:close" size="24" />
        </button>
      </div>

      <!-- 内容 -->
      <div class="px-6 py-4 max-h-[calc(90vh-140px)] overflow-y-auto">
        <!-- 创建盘点计划 -->
        <div v-if="isCreating">
          <form @submit.prevent="handleCreatePlan">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  计划名称 *
                </label>
                <input 
                  v-model="planForm.name"
                  type="text"
                  required
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="请输入盘点计划名称"
                />
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  开始日期 *
                </label>
                <input 
                  v-model="planForm.startDate"
                  type="date"
                  required
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            
            <div class="mt-6">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                计划描述
              </label>
              <textarea 
                v-model="planForm.description"
                :disabled="loading"
                rows="3"
                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                placeholder="请输入盘点计划描述"
              ></textarea>
            </div>
            
            <!-- 选择盘点物品 -->
            <div class="mt-6">
              <label class="block text-sm font-medium text-gray-700 mb-3">
                选择盘点物品 *
              </label>
              <div class="border border-gray-300 rounded-md max-h-60 overflow-y-auto">
                <div 
                  v-for="item in inventoryStore.items" 
                  :key="item.id"
                  class="flex items-center p-3 border-b border-gray-100 last:border-b-0 hover:bg-gray-50"
                >
                  <input 
                    :id="`item-${item.id}`"
                    v-model="planForm.items"
                    :value="item.id"
                    type="checkbox"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label 
                    :for="`item-${item.id}`"
                    class="ml-3 flex-1 cursor-pointer"
                  >
                    <div class="flex justify-between items-center">
                      <div>
                        <div class="font-medium text-gray-900">{{ item.name }}</div>
                        <div class="text-sm text-gray-500">{{ item.category }} | {{ item.location }}</div>
                      </div>
                      <div class="text-right">
                        <div class="font-medium text-gray-900">{{ item.quantity }} {{ item.unit }}</div>
                        <div class="text-sm text-gray-500">当前库存</div>
                      </div>
                    </div>
                  </label>
                </div>
              </div>
            </div>
          </form>
        </div>

        <!-- 盘点执行 -->
        <div v-else-if="currentPlan">
          <div class="mb-6">
            <h4 class="text-lg font-medium text-gray-900 mb-2">{{ currentPlan.name }}</h4>
            <p class="text-gray-600">{{ currentPlan.description }}</p>
          </div>
          
          <div class="space-y-4">
            <div 
              v-for="itemId in currentPlan.items" 
              :key="itemId"
              class="border border-gray-200 rounded-lg p-4"
            >
              <div class="flex justify-between items-start mb-4">
                <div>
                  <h5 class="font-medium text-gray-900">{{ getItemById(itemId)?.name }}</h5>
                  <p class="text-sm text-gray-500">{{ getItemById(itemId)?.category }} | {{ getItemById(itemId)?.location }}</p>
                </div>
                <div class="text-right">
                  <div class="font-medium text-gray-900">{{ getItemById(itemId)?.quantity }} {{ getItemById(itemId)?.unit }}</div>
                  <div class="text-sm text-gray-500">系统库存</div>
                </div>
              </div>
              
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    实际数量 *
                  </label>
                  <input 
                    v-model.number="countData[itemId].actualQuantity"
                    type="number"
                    min="0"
                    step="0.01"
                    required
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入实际数量"
                    @input="calculateDifference(itemId)"
                  />
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    差异
                  </label>
                  <input 
                    :value="countData[itemId].difference"
                    type="number"
                    readonly
                    class="w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-50 text-gray-600"
                  />
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    备注
                  </label>
                  <input 
                    v-model="countData[itemId].remark"
                    type="text"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="备注信息"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
        <button 
          type="button"
          @click="handleClose"
          class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
        >
          取消
        </button>
        
        <button 
          v-if="isCreating"
          @click="handleCreatePlan"
          :disabled="loading || !isFormValid"
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {{ loading ? '创建中...' : '创建计划' }}
        </button>
        
        <button 
          v-else-if="currentPlan"
          @click="handleSubmitCount"
          :disabled="loading || !isCountValid"
          class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {{ loading ? '提交中...' : '提交盘点' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { StockCountPlan } from '~/types'

interface Props {
  show: boolean
  plan?: StockCountPlan | null
}

const props = withDefaults(defineProps<Props>(), {
  plan: null
})

const emit = defineEmits<{
  'update:show': [value: boolean]
  created: [plan: StockCountPlan]
  completed: [plan: StockCountPlan]
}>()

// Store
const inventoryStore = useInventoryStore()
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const isCreating = computed(() => !props.plan)
const currentPlan = computed(() => props.plan)

// 创建计划表单
const planForm = reactive({
  name: '',
  description: '',
  startDate: '',
  status: 'draft' as const,
  items: [] as string[]
})

// 盘点数据
const countData = reactive<Record<string, {
  actualQuantity: number
  difference: number
  remark: string
}>>({})

// 计算属性
const isFormValid = computed(() => {
  return planForm.name.trim().length > 0 && 
         planForm.startDate && 
         planForm.items.length > 0
})

const isCountValid = computed(() => {
  if (!currentPlan.value) return false
  
  return currentPlan.value.items.every(itemId => {
    const data = countData[itemId]
    return data && typeof data.actualQuantity === 'number' && data.actualQuantity >= 0
  })
})

// 方法
const getItemById = (id: string) => {
  return inventoryStore.items.find(item => item.id === id)
}

const calculateDifference = (itemId: string) => {
  const item = getItemById(itemId)
  if (!item || !countData[itemId]) return
  
  const systemQuantity = item.quantity
  const actualQuantity = countData[itemId].actualQuantity || 0
  countData[itemId].difference = actualQuantity - systemQuantity
}

const initCountData = () => {
  if (!currentPlan.value) return
  
  currentPlan.value.items.forEach(itemId => {
    const item = getItemById(itemId)
    if (item) {
      countData[itemId] = {
        actualQuantity: item.quantity,
        difference: 0,
        remark: ''
      }
    }
  })
}

const resetForm = () => {
  planForm.name = ''
  planForm.description = ''
  planForm.startDate = ''
  planForm.items = []
  Object.keys(countData).forEach(key => delete countData[key])
}

const handleClose = () => {
  emit('update:show', false)
}

const handleBackdropClick = () => {
  handleClose()
}

const handleCreatePlan = async () => {
  if (!isFormValid.value) return
  
  loading.value = true
  
  try {
    const result = await inventoryStore.createCountPlan({
      name: planForm.name.trim(),
      description: planForm.description.trim(),
      status: 'draft',
      startDate: new Date(planForm.startDate),
      items: planForm.items,
      createdBy: authStore.user
    })
    
    if (result.success && result.data) {
      emit('created', result.data)
      handleClose()
    } else {
      alert(result.error || '创建盘点计划失败')
    }
  } catch (error) {
    console.error('创建盘点计划失败:', error)
    alert('创建盘点计划失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

const handleSubmitCount = async () => {
  if (!isCountValid.value || !currentPlan.value) return
  
  loading.value = true
  
  try {
    // 提交所有盘点记录
    for (const itemId of currentPlan.value.items) {
      const item = getItemById(itemId)
      const data = countData[itemId]
      
      if (item && data) {
        await inventoryStore.submitCountRecord({
          planId: currentPlan.value.id,
          itemId,
          systemQuantity: item.quantity,
          actualQuantity: data.actualQuantity,
          difference: data.difference,
          remark: data.remark,
          countBy: authStore.user,
          countDate: new Date()
        })
      }
    }
    
    // 完成盘点计划
    const result = await inventoryStore.completeCount(currentPlan.value.id)
    
    if (result.success && result.data) {
      emit('completed', result.data)
      handleClose()
    } else {
      alert(result.error || '完成盘点失败')
    }
  } catch (error) {
    console.error('提交盘点失败:', error)
    alert('提交盘点失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 监听器
watch(() => props.show, (newShow) => {
  if (newShow) {
    if (currentPlan.value) {
      initCountData()
    } else {
      resetForm()
    }
  }
})

// 键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.show) {
    handleClose()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>
