<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>初始化库存数据</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <script src="js/config.js"></script>
</head>
<body class="bg-gray-50 min-h-screen p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-800 mb-8">初始化库存数据</h1>
        
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">数据初始化</h2>
            <div class="space-y-4">
                <button id="initSuppliers" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded mr-4">
                    初始化供应商数据
                </button>
                <button id="initCategories" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded mr-4">
                    初始化商品分类
                </button>
                <button id="initWarehouses" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded mr-4">
                    初始化仓库数据
                </button>
                <button id="initProducts" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded mr-4">
                    初始化商品数据
                </button>
                <button id="initInboundOrders" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded mr-4">
                    初始化入库单数据
                </button>
                <button id="initInventory" class="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded mr-4">
                    初始化库存数据
                </button>
                <button id="initOutboundOrders" class="bg-pink-500 hover:bg-pink-600 text-white px-4 py-2 rounded mr-4">
                    初始化出库单数据
                </button>
                <button id="initTransferOrders" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded mr-4">
                    初始化调拨单数据
                </button>
                <button id="initCountPlans" class="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded mr-4">
                    初始化盘点数据
                </button>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">操作日志</h2>
            <div id="logOutput" class="bg-gray-100 p-4 rounded h-96 overflow-y-auto text-sm font-mono">
                <!-- 日志将在这里显示 -->
            </div>
        </div>
    </div>

    <script>
        // 日志函数
        function log(message) {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            logOutput.innerHTML += `[${timestamp}] ${message}\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
            console.log(message);
        }

        // 等待 LeanCloud 初始化
        function waitForAV() {
            return new Promise((resolve) => {
                function check() {
                    if (typeof AV !== 'undefined' && AV.applicationId) {
                        resolve();
                    } else {
                        setTimeout(check, 100);
                    }
                }
                check();
            });
        }

        // 初始化供应商数据
        async function initSuppliers() {
            try {
                log('开始初始化供应商数据...');
                
                const suppliers = [
                    { name: '北京建材有限公司', contact: '张经理', phone: '010-12345678', email: '<EMAIL>', address: '北京市朝阳区建材路123号', status: 'active' },
                    { name: '上海电器设备公司', contact: '李经理', phone: '021-87654321', email: '<EMAIL>', address: '上海市浦东新区电器街456号', status: 'active' },
                    { name: '广州清洁用品厂', contact: '王经理', phone: '020-11111111', email: '<EMAIL>', address: '广州市天河区清洁路789号', status: 'active' },
                    { name: '深圳办公用品商行', contact: '赵经理', phone: '0755-22222222', email: '<EMAIL>', address: '深圳市南山区办公大道101号', status: 'active' }
                ];

                for (const supplierData of suppliers) {
                    const supplier = new AV.Object('Supplier');
                    Object.keys(supplierData).forEach(key => {
                        supplier.set(key, supplierData[key]);
                    });
                    supplier.set('createdBy', AV.User.current());
                    await supplier.save();
                    log(`创建供应商: ${supplierData.name}`);
                }
                
                log('供应商数据初始化完成');
            } catch (error) {
                log('初始化供应商数据失败: ' + error.message);
            }
        }

        // 初始化商品分类
        async function initCategories() {
            try {
                log('开始初始化商品分类...');
                
                const categories = [
                    { name: '建材类', code: 'JC', level: 1, description: '建筑材料' },
                    { name: '电器类', code: 'DQ', level: 1, description: '电器设备' },
                    { name: '清洁用品', code: 'QJ', level: 1, description: '清洁用品' },
                    { name: '办公用品', code: 'BG', level: 1, description: '办公用品' }
                ];

                for (const categoryData of categories) {
                    const category = new AV.Object('Category');
                    Object.keys(categoryData).forEach(key => {
                        category.set(key, categoryData[key]);
                    });
                    category.set('createdBy', AV.User.current());
                    await category.save();
                    log(`创建分类: ${categoryData.name}`);
                }
                
                log('商品分类初始化完成');
            } catch (error) {
                log('初始化商品分类失败: ' + error.message);
            }
        }

        // 初始化仓库数据
        async function initWarehouses() {
            try {
                log('开始初始化仓库数据...');
                
                const warehouses = [
                    { name: '主仓库', code: 'WH001', type: 'main', manager: '仓库主管', phone: '010-88888888', address: '酒店地下一层', status: 'active' },
                    { name: '工程仓库', code: 'WH002', type: 'sub', manager: '工程部主管', phone: '010-99999999', address: '酒店地下二层', status: 'active' },
                    { name: '清洁用品仓', code: 'WH003', type: 'sub', manager: '清洁部主管', phone: '010-77777777', address: '酒店三层储物间', status: 'active' }
                ];

                for (const warehouseData of warehouses) {
                    const warehouse = new AV.Object('Warehouse');
                    Object.keys(warehouseData).forEach(key => {
                        warehouse.set(key, warehouseData[key]);
                    });
                    warehouse.set('createdBy', AV.User.current());
                    await warehouse.save();
                    log(`创建仓库: ${warehouseData.name}`);
                }
                
                log('仓库数据初始化完成');
            } catch (error) {
                log('初始化仓库数据失败: ' + error.message);
            }
        }

        // 初始化商品数据
        async function initProducts() {
            try {
                log('开始初始化商品数据...');
                
                // 先获取分类和供应商
                const categoryQuery = new AV.Query('Category');
                const categories = await categoryQuery.find();
                
                const supplierQuery = new AV.Query('Supplier');
                const suppliers = await supplierQuery.find();
                
                if (categories.length === 0 || suppliers.length === 0) {
                    log('请先初始化分类和供应商数据');
                    return;
                }

                const products = [
                    { name: '水泥', code: 'P001', unit: '袋', specification: '50kg/袋', minStock: 10, maxStock: 100 },
                    { name: '钢筋', code: 'P002', unit: '根', specification: '12mm*6m', minStock: 20, maxStock: 200 },
                    { name: '空调', code: 'P003', unit: '台', specification: '3匹挂式', minStock: 2, maxStock: 10 },
                    { name: '洗衣粉', code: 'P004', unit: '包', specification: '2kg/包', minStock: 5, maxStock: 50 },
                    { name: '打印纸', code: 'P005', unit: '箱', specification: 'A4 500张/包', minStock: 3, maxStock: 30 }
                ];

                for (let i = 0; i < products.length; i++) {
                    const productData = products[i];
                    const product = new AV.Object('Product');
                    Object.keys(productData).forEach(key => {
                        product.set(key, productData[key]);
                    });
                    product.set('categoryId', categories[i % categories.length]);
                    product.set('supplierId', suppliers[i % suppliers.length]);
                    product.set('status', 'active');
                    product.set('createdBy', AV.User.current());
                    await product.save();
                    log(`创建商品: ${productData.name}`);
                }
                
                log('商品数据初始化完成');
            } catch (error) {
                log('初始化商品数据失败: ' + error.message);
            }
        }

        // 初始化入库单数据
        async function initInboundOrders() {
            try {
                log('开始初始化入库单数据...');

                // 获取必要的数据
                const supplierQuery = new AV.Query('Supplier');
                const suppliers = await supplierQuery.find();

                const warehouseQuery = new AV.Query('Warehouse');
                const warehouses = await warehouseQuery.find();

                const productQuery = new AV.Query('Product');
                const products = await productQuery.find();

                if (suppliers.length === 0 || warehouses.length === 0) {
                    log('请先初始化供应商和仓库数据');
                    return;
                }

                if (products.length === 0) {
                    log('请先初始化商品数据');
                    return;
                }

                // 创建几个测试入库单
                for (let i = 1; i <= 5; i++) {
                    const order = new AV.Object('InboundOrder');
                    const now = new Date();
                    const orderNo = `IN${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}${i.toString().padStart(3, '0')}`;

                    order.set('orderNo', orderNo);
                    order.set('supplierId', suppliers[i % suppliers.length]);
                    order.set('warehouseId', warehouses[i % warehouses.length]);
                    order.set('type', ['purchase', 'return', 'transfer', 'other'][i % 4]);
                    order.set('status', ['draft', 'confirmed', 'completed'][i % 3]);
                    order.set('remark', `测试入库单 ${i}`);
                    order.set('createdBy', AV.User.current());

                    await order.save();
                    log(`创建入库单: ${orderNo}`);

                    // 为每个入库单创建2-3个明细
                    let totalAmount = 0;
                    const itemCount = Math.floor(Math.random() * 2) + 2; // 2-3个明细

                    for (let j = 0; j < itemCount; j++) {
                        const item = new AV.Object('InboundOrderItem');
                        const product = products[j % products.length];
                        const quantity = Math.floor(Math.random() * 50) + 1;
                        const unitPrice = Math.floor(Math.random() * 100) + 10;
                        const amount = quantity * unitPrice;

                        item.set('orderId', order);
                        item.set('productId', product);
                        item.set('quantity', quantity);
                        item.set('unitPrice', unitPrice);
                        item.set('amount', amount);
                        item.set('batchNo', `BATCH${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${i}${j}`);

                        await item.save();
                        totalAmount += amount;
                        log(`  - 创建明细: ${product.get('name')} x ${quantity}`);
                    }

                    // 更新入库单总金额
                    order.set('totalAmount', totalAmount);
                    await order.save();
                    log(`  - 更新总金额: ¥${totalAmount.toFixed(2)}`);
                }

                log('入库单数据初始化完成');
            } catch (error) {
                log('初始化入库单数据失败: ' + error.message);
            }
        }

        // 初始化库存数据
        async function initInventory() {
            try {
                log('开始初始化库存数据...');

                // 获取商品和仓库数据
                const productQuery = new AV.Query('Product');
                const products = await productQuery.find();

                const warehouseQuery = new AV.Query('Warehouse');
                const warehouses = await warehouseQuery.find();

                if (products.length === 0 || warehouses.length === 0) {
                    log('请先初始化商品和仓库数据');
                    return;
                }

                // 为每个商品在每个仓库创建库存记录
                for (const product of products) {
                    for (const warehouse of warehouses) {
                        const inventory = new AV.Object('Inventory');
                        const totalQty = Math.floor(Math.random() * 100) + 50; // 50-150的随机库存
                        const reservedQty = Math.floor(Math.random() * 10); // 0-10的预留库存
                        const availableQty = totalQty - reservedQty;

                        inventory.set('productId', product);
                        inventory.set('warehouseId', warehouse);
                        inventory.set('totalQuantity', totalQty);
                        inventory.set('availableQuantity', availableQty);
                        inventory.set('reservedQuantity', reservedQty);
                        inventory.set('minStock', product.get('minStock') || 10);
                        inventory.set('maxStock', product.get('maxStock') || 100);
                        inventory.set('lastUpdated', new Date());
                        inventory.set('createdBy', AV.User.current());

                        await inventory.save();
                        log(`创建库存: ${product.get('name')} @ ${warehouse.get('name')} = ${availableQty}`);
                    }
                }

                log('库存数据初始化完成');
            } catch (error) {
                log('初始化库存数据失败: ' + error.message);
            }
        }

        // 初始化出库单数据
        async function initOutboundOrders() {
            try {
                log('开始初始化出库单数据...');

                // 获取必要的数据
                const warehouseQuery = new AV.Query('Warehouse');
                const warehouses = await warehouseQuery.find();

                const productQuery = new AV.Query('Product');
                const products = await productQuery.find();

                if (warehouses.length === 0 || products.length === 0) {
                    log('请先初始化仓库和商品数据');
                    return;
                }

                const departments = ['前厅部', '客房部', '餐饮部', '工程部', '保安部'];
                const recipients = ['张三', '李四', '王五', '赵六', '钱七'];

                // 创建几个测试出库单
                for (let i = 1; i <= 5; i++) {
                    const order = new AV.Object('OutboundOrder');
                    const now = new Date();
                    const orderNo = `OUT${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}${i.toString().padStart(3, '0')}`;

                    order.set('orderNo', orderNo);
                    order.set('warehouseId', warehouses[i % warehouses.length]);
                    order.set('type', ['sale', 'use', 'transfer', 'other'][i % 4]);
                    order.set('status', ['draft', 'confirmed', 'completed'][i % 3]);
                    order.set('department', departments[i % departments.length]);
                    order.set('recipient', recipients[i % recipients.length]);
                    order.set('remark', `测试出库单 ${i}`);
                    order.set('createdBy', AV.User.current());

                    await order.save();
                    log(`创建出库单: ${orderNo}`);

                    // 为每个出库单创建2-3个明细
                    let totalAmount = 0;
                    const itemCount = Math.floor(Math.random() * 2) + 2; // 2-3个明细

                    for (let j = 0; j < itemCount; j++) {
                        const item = new AV.Object('OutboundOrderItem');
                        const product = products[j % products.length];
                        const quantity = Math.floor(Math.random() * 10) + 1; // 1-10的出库数量
                        const unitPrice = Math.floor(Math.random() * 100) + 10;
                        const amount = quantity * unitPrice;

                        item.set('orderId', order);
                        item.set('productId', product);
                        item.set('quantity', quantity);
                        item.set('unitPrice', unitPrice);
                        item.set('amount', amount);
                        item.set('batchNo', `BATCH${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${i}${j}`);

                        await item.save();
                        totalAmount += amount;
                        log(`  - 创建明细: ${product.get('name')} x ${quantity}`);
                    }

                    // 更新出库单总金额
                    order.set('totalAmount', totalAmount);
                    await order.save();
                    log(`  - 更新总金额: ¥${totalAmount.toFixed(2)}`);
                }

                log('出库单数据初始化完成');
            } catch (error) {
                log('初始化出库单数据失败: ' + error.message);
            }
        }

        // 初始化调拨单数据
        async function initTransferOrders() {
            try {
                log('开始初始化调拨单数据...');

                // 获取必要的数据
                const warehouseQuery = new AV.Query('Warehouse');
                const warehouses = await warehouseQuery.find();

                const productQuery = new AV.Query('Product');
                const products = await productQuery.find();

                if (warehouses.length < 2 || products.length === 0) {
                    log('请先初始化仓库（至少2个）和商品数据');
                    return;
                }

                // 创建几个测试调拨单
                for (let i = 1; i <= 5; i++) {
                    const order = new AV.Object('TransferOrder');
                    const now = new Date();
                    const orderNo = `TF${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}${i.toString().padStart(3, '0')}`;

                    // 随机选择不同的源仓库和目标仓库
                    const sourceWarehouse = warehouses[i % warehouses.length];
                    let targetWarehouse = warehouses[(i + 1) % warehouses.length];
                    // 确保源仓库和目标仓库不同
                    if (sourceWarehouse.id === targetWarehouse.id && warehouses.length > 1) {
                        targetWarehouse = warehouses[(i + 2) % warehouses.length];
                    }

                    order.set('orderNo', orderNo);
                    order.set('sourceWarehouseId', sourceWarehouse);
                    order.set('targetWarehouseId', targetWarehouse);
                    order.set('status', ['draft', 'confirmed', 'completed'][i % 3]);
                    order.set('transferDate', new Date().toISOString().split('T')[0]);
                    order.set('reason', `测试调拨单 ${i} - 从${sourceWarehouse.get('name')}调拨到${targetWarehouse.get('name')}`);
                    order.set('createdBy', AV.User.current());

                    await order.save();
                    log(`创建调拨单: ${orderNo}`);

                    // 为每个调拨单创建2-3个明细
                    let totalAmount = 0;
                    const itemCount = Math.floor(Math.random() * 2) + 2; // 2-3个明细

                    for (let j = 0; j < itemCount; j++) {
                        const item = new AV.Object('TransferOrderItem');
                        const product = products[j % products.length];
                        const quantity = Math.floor(Math.random() * 20) + 5; // 5-25的调拨数量
                        const unitPrice = Math.floor(Math.random() * 100) + 10;
                        const amount = quantity * unitPrice;

                        item.set('orderId', order);
                        item.set('productId', product);
                        item.set('quantity', quantity);
                        item.set('unitPrice', unitPrice);
                        item.set('amount', amount);
                        item.set('batchNo', `BATCH${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${i}${j}`);

                        await item.save();
                        totalAmount += amount;
                        log(`  - 创建明细: ${product.get('name')} x ${quantity}`);
                    }

                    // 更新调拨单总金额
                    order.set('totalAmount', totalAmount);
                    await order.save();
                    log(`  - 更新总金额: ¥${totalAmount.toFixed(2)}`);
                }

                log('调拨单数据初始化完成');
            } catch (error) {
                log('初始化调拨单数据失败: ' + error.message);
            }
        }

        // 初始化盘点数据
        async function initCountPlans() {
            try {
                log('开始初始化盘点数据...');

                // 获取必要的数据
                const warehouseQuery = new AV.Query('Warehouse');
                const warehouses = await warehouseQuery.find();

                const productQuery = new AV.Query('Product');
                const products = await productQuery.find();

                if (warehouses.length === 0 || products.length === 0) {
                    log('请先初始化仓库和商品数据');
                    return;
                }

                const countTypes = ['full', 'partial', 'cycle', 'spot'];
                const managers = ['张主管', '李经理', '王总监', '赵组长', '钱负责人'];

                // 创建几个测试盘点计划
                for (let i = 1; i <= 5; i++) {
                    const plan = new AV.Object('CountPlan');
                    const now = new Date();
                    const planNo = `COUNT${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}${i.toString().padStart(3, '0')}`;

                    // 计算计划日期（今天到未来7天内的随机日期）
                    const plannedDate = new Date();
                    plannedDate.setDate(plannedDate.getDate() + Math.floor(Math.random() * 7));

                    plan.set('planNo', planNo);
                    plan.set('name', `${now.getMonth() + 1}月第${i}次盘点`);
                    plan.set('warehouseId', warehouses[i % warehouses.length]);
                    plan.set('type', countTypes[i % countTypes.length]);
                    plan.set('status', ['draft', 'in-progress', 'completed'][i % 3]);
                    plan.set('plannedDate', plannedDate.toISOString().split('T')[0]);
                    plan.set('manager', managers[i % managers.length]);
                    plan.set('description', `测试盘点计划 ${i} - ${countTypes[i % countTypes.length]}盘点`);
                    plan.set('createdBy', AV.User.current());

                    await plan.save();
                    log(`创建盘点计划: ${planNo}`);

                    // 为每个盘点计划创建盘点范围（选择部分商品）
                    const selectedProducts = products.slice(0, Math.min(products.length, 3 + i)); // 选择3-8个商品

                    for (const product of selectedProducts) {
                        const scope = new AV.Object('CountScope');
                        scope.set('planId', plan);
                        scope.set('productId', product);
                        scope.set('warehouseId', warehouses[i % warehouses.length]);

                        await scope.save();
                        log(`  - 添加盘点商品: ${product.get('name')}`);
                    }

                    // 如果是已完成的盘点，创建盘点记录
                    if (plan.get('status') === 'completed') {
                        for (const product of selectedProducts) {
                            const record = new AV.Object('CountRecord');
                            const systemQty = Math.floor(Math.random() * 100) + 50; // 系统数量50-150
                            const actualQty = systemQty + Math.floor(Math.random() * 21) - 10; // 实际数量±10
                            const difference = actualQty - systemQty;

                            record.set('planId', plan);
                            record.set('productId', product);
                            record.set('warehouseId', warehouses[i % warehouses.length]);
                            record.set('systemQuantity', systemQty);
                            record.set('actualQuantity', actualQty);
                            record.set('difference', difference);
                            record.set('countDate', new Date());
                            record.set('countBy', AV.User.current());
                            record.set('remark', difference !== 0 ? `差异${difference > 0 ? '+' : ''}${difference}` : '数量一致');

                            await record.save();
                            log(`  - 创建盘点记录: ${product.get('name')} 系统${systemQty} 实际${actualQty} 差异${difference}`);
                        }
                    }
                }

                log('盘点数据初始化完成');
            } catch (error) {
                log('初始化盘点数据失败: ' + error.message);
            }
        }

        // 页面加载完成后绑定事件
        document.addEventListener('DOMContentLoaded', async function() {
            log('页面加载完成，等待 LeanCloud 初始化...');
            await waitForAV();
            log('LeanCloud 初始化完成');
            
            // 检查登录状态
            const currentUser = AV.User.current();
            if (!currentUser) {
                log('请先登录系统');
                alert('请先登录系统');
                return;
            }
            
            log(`当前用户: ${currentUser.get('username')}`);

            // 绑定按钮事件
            document.getElementById('initSuppliers').addEventListener('click', initSuppliers);
            document.getElementById('initCategories').addEventListener('click', initCategories);
            document.getElementById('initWarehouses').addEventListener('click', initWarehouses);
            document.getElementById('initProducts').addEventListener('click', initProducts);
            document.getElementById('initInboundOrders').addEventListener('click', initInboundOrders);
            document.getElementById('initInventory').addEventListener('click', initInventory);
            document.getElementById('initOutboundOrders').addEventListener('click', initOutboundOrders);
            document.getElementById('initTransferOrders').addEventListener('click', initTransferOrders);
            document.getElementById('initCountPlans').addEventListener('click', initCountPlans);
        });
    </script>
</body>
</html>
