// middleware/error-handler.global.ts
export default defineNuxtRouteMiddleware((to, from) => {
  // 全局错误处理
  if (process.client) {
    // 监听未捕获的错误
    window.addEventListener('error', (event) => {
      console.error('全局错误:', event.error)
      
      // 可以在这里发送错误到监控服务
      // sendErrorToMonitoring(event.error)
    })

    // 监听未捕获的Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      console.error('未处理的Promise拒绝:', event.reason)
      
      // 可以在这里发送错误到监控服务
      // sendErrorToMonitoring(event.reason)
    })
  }
})

// 错误监控服务（示例）
const sendErrorToMonitoring = (error: any) => {
  // 这里可以集成第三方错误监控服务
  // 如 Sentry, LogRocket, Bugsnag 等
  
  const errorData = {
    message: error.message || '未知错误',
    stack: error.stack,
    url: window.location.href,
    userAgent: navigator.userAgent,
    timestamp: new Date().toISOString()
  }
  
  // 发送到监控服务
  console.log('发送错误到监控服务:', errorData)
}
