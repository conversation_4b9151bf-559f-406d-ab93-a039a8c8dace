<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统管理 - 酒店管理系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <style>
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .btn-fix {
            touch-action: manipulation;
        }
        .admin-card {
            transition: all 0.3s ease;
        }
        .admin-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="../index.html" class="text-blue-600 hover:text-blue-800 mr-4">
                        ← 返回主页
                    </a>
                    <h1 class="text-xl font-semibold text-gray-900">系统管理</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- 用户信息 -->
                    <div id="userInfo" class="flex items-center space-x-2" style="display: none;">
                        <span class="text-sm text-gray-700">管理员：</span>
                        <span id="realName" class="text-sm font-medium text-gray-900"></span>
                        <button id="logoutBtn" class="text-sm text-red-600 hover:text-red-800 btn-fix">退出</button>
                    </div>
                    <button id="loginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                        登录
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 权限提示 -->
    <div id="accessDenied" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8" style="display: none;">
        <div class="bg-white rounded-lg shadow p-8 text-center">
            <div class="mb-4">
                <svg class="mx-auto h-12 w-12 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">访问受限</h3>
            <p class="text-gray-500 mb-4">您需要管理员权限才能访问此页面</p>
            <button id="loginPromptBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg btn-fix">
                立即登录
            </button>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div id="adminSection" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8" style="display: none;">
        <!-- 系统概览 -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">系统概览</h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">总用户数</dt>
                                <dd class="text-lg font-medium text-gray-900" id="totalUsers">0</dd>
                            </dl>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">活跃用户</dt>
                                <dd class="text-lg font-medium text-gray-900" id="activeUsers">0</dd>
                            </dl>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">今日操作</dt>
                                <dd class="text-lg font-medium text-gray-900" id="todayOperations">0</dd>
                            </dl>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">系统警告</dt>
                                <dd class="text-lg font-medium text-gray-900" id="systemWarnings">0</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 管理功能模块 -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">管理功能</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- 用户管理 -->
                <a href="users.html" class="admin-card admin-module-link block bg-white rounded-lg shadow p-6 hover:shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        </div>
                        <h3 class="ml-4 text-lg font-medium text-gray-900">用户管理</h3>
                    </div>
                    <p class="text-gray-500 text-sm">管理系统用户账号、权限和个人信息</p>
                </a>

                <!-- 角色权限 -->
                <a href="roles.html" class="admin-card admin-module-link block bg-white rounded-lg shadow p-6 hover:shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                            </svg>
                        </div>
                        <h3 class="ml-4 text-lg font-medium text-gray-900">角色权限</h3>
                    </div>
                    <p class="text-gray-500 text-sm">配置用户角色和权限控制</p>
                </a>

                <!-- 系统设置 -->
                <a href="system.html" class="admin-card admin-module-link block bg-white rounded-lg shadow p-6 hover:shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>
                        <h3 class="ml-4 text-lg font-medium text-gray-900">系统设置</h3>
                    </div>
                    <p class="text-gray-500 text-sm">系统参数配置和基础设置</p>
                </a>

                <!-- 操作日志 -->
                <a href="logs.html" class="admin-card admin-module-link block bg-white rounded-lg shadow p-6 hover:shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 bg-yellow-500 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <h3 class="ml-4 text-lg font-medium text-gray-900">操作日志</h3>
                    </div>
                    <p class="text-gray-500 text-sm">查看系统操作记录和审计日志</p>
                </a>

                <!-- 数据报表 -->
                <a href="reports.html" class="admin-card admin-module-link block bg-white rounded-lg shadow p-6 hover:shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 bg-indigo-500 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                        <h3 class="ml-4 text-lg font-medium text-gray-900">数据报表</h3>
                    </div>
                    <p class="text-gray-500 text-sm">生成和查看各类统计报表</p>
                </a>

                <!-- 数据备份 -->
                <a href="backup.html" class="admin-card admin-module-link block bg-white rounded-lg shadow p-6 hover:shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                            </svg>
                        </div>
                        <h3 class="ml-4 text-lg font-medium text-gray-900">数据备份</h3>
                    </div>
                    <p class="text-gray-500 text-sm">数据备份和恢复管理</p>
                </a>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">快速操作</h3>
            <div class="grid grid-cols-1 md:grid-cols-9 gap-4">
                <a href="init-data.html" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm btn-fix text-center">
                    数据初始化
                </a>
                <a href="permission-migration.html" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg text-sm btn-fix text-center">
                    权限迁移
                </a>
                <a href="debug-permissions.html" class="bg-pink-500 hover:bg-pink-600 text-white px-4 py-2 rounded-lg text-sm btn-fix text-center">
                    权限调试
                </a>
                <button id="checkElementsBtn" class="bg-teal-500 hover:bg-teal-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                    检查元素
                </button>
                <a href="element-check.html" class="bg-cyan-500 hover:bg-cyan-600 text-white px-4 py-2 rounded-lg text-sm btn-fix text-center">
                    元素检查
                </a>
                <a href="permission-diagnosis.html" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm btn-fix text-center">
                    权限诊断
                </a>
                <button id="clearCacheBtn" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                    清理缓存
                </button>
                <button id="systemCheckBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                    系统检查
                </button>
                <button id="exportDataBtn" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                    导出数据
                </button>
            </div>
        </div>
    </div>

    <!-- 登录弹窗 -->
    <div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style="display: none;">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 fade-in">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-800">管理员登录</h2>
            </div>
            <div class="p-6">
                <form id="loginForm" class="space-y-4">
                    <div>
                        <label for="loginUsername" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                        <input type="text" id="loginUsername" required
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                    </div>
                    <div>
                        <label for="loginPassword" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                        <input type="password" id="loginPassword" required
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                    </div>
                    <button type="submit" class="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                        登录
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- 引入模块化JS文件 -->
    <script src="../js/config.js"></script>
    <script src="../js/utils.js"></script>
    <script src="../js/auth.js"></script>
    <script src="../js/permission-manager.js"></script>
    <script>
        // 管理系统应用类
        class AdminApp {
            constructor() {
                this.elements = {};
            }

            init() {
                this.initElements();
                this.bindEvents();
                this.checkAdminAccess();
            }

            initElements() {
                this.elements = {
                    accessDenied: document.getElementById('accessDenied'),
                    adminSection: document.getElementById('adminSection'),
                    realName: document.getElementById('realName'),
                    userInfo: document.getElementById('userInfo'),
                    loginBtn: document.getElementById('loginBtn'),
                    logoutBtn: document.getElementById('logoutBtn'),
                    loginPromptBtn: document.getElementById('loginPromptBtn'),
                    totalUsers: document.getElementById('totalUsers'),
                    activeUsers: document.getElementById('activeUsers'),
                    todayOperations: document.getElementById('todayOperations'),
                    systemWarnings: document.getElementById('systemWarnings')
                };

                // 调试：检查关键元素是否存在
                console.log('元素初始化检查:');
                console.log('accessDenied:', !!this.elements.accessDenied);
                console.log('adminSection:', !!this.elements.adminSection);
                console.log('userInfo:', !!this.elements.userInfo);
                console.log('loginBtn:', !!this.elements.loginBtn);
            }

            bindEvents() {
                // 登录相关事件
                if (this.elements.loginBtn) {
                    this.elements.loginBtn.addEventListener('click', () => {
                        this.showLoginModal();
                    });
                }

                if (this.elements.loginPromptBtn) {
                    this.elements.loginPromptBtn.addEventListener('click', () => {
                        this.showLoginModal();
                    });
                }

                if (this.elements.logoutBtn) {
                    this.elements.logoutBtn.addEventListener('click', async () => {
                        try {
                            await AV.User.logOut();
                            this.checkAdminAccess();
                        } catch (error) {
                            console.error('退出登录失败:', error);
                        }
                    });
                }

                // 快速操作事件
                document.getElementById('clearCacheBtn').addEventListener('click', () => {
                    this.clearCache();
                });

                document.getElementById('systemCheckBtn').addEventListener('click', () => {
                    this.systemCheck();
                });

                document.getElementById('exportDataBtn').addEventListener('click', () => {
                    this.exportData();
                });

                document.getElementById('checkElementsBtn').addEventListener('click', () => {
                    this.checkPageElements();
                });

                // 登录表单事件
                document.getElementById('loginForm').addEventListener('submit', async (e) => {
                    e.preventDefault();
                    await this.handleLogin();
                });

                // 关闭登录弹窗
                document.addEventListener('click', (e) => {
                    if (e.target.id === 'loginModal') {
                        document.getElementById('loginModal').style.display = 'none';
                    }
                });
            }

            async checkAdminAccess() {
                const currentUser = AV.User.current();

                if (currentUser) {
                    console.log('当前用户:', currentUser.get('username'), '角色:', currentUser.get('roles'));

                    // 先使用降级处理确保基本功能
                    const roles = currentUser.get('roles') || [];
                    let isAdmin = false;

                    if (typeof roles === 'string') {
                        try {
                            const roleArray = JSON.parse(roles);
                            isAdmin = roleArray.includes('admin') || roleArray.includes('super_admin');
                        } catch (e) {
                            isAdmin = roles === 'admin' || roles === 'super_admin';
                        }
                    } else if (Array.isArray(roles)) {
                        isAdmin = roles.includes('admin') || roles.includes('super_admin');
                    }

                    console.log('权限检查结果:', isAdmin);

                    if (isAdmin) {
                        console.log('✅ 用户是管理员，显示管理界面');

                        // 检查元素是否存在
                        if (!this.elements.accessDenied || !this.elements.adminSection) {
                            console.error('❌ 关键元素不存在！');
                            console.log('accessDenied:', this.elements.accessDenied);
                            console.log('adminSection:', this.elements.adminSection);
                            return;
                        }

                        // 用户是管理员
                        this.elements.accessDenied.style.display = 'none';
                        this.elements.adminSection.style.display = 'block';

                        console.log('✅ 管理界面元素显示状态已更新');

                        // 更新用户信息显示
                        if (this.elements.realName && this.elements.userInfo && this.elements.loginBtn) {
                            this.elements.realName.textContent = currentUser.get('realName') || currentUser.get('username');
                            this.elements.userInfo.style.display = 'flex';
                            this.elements.loginBtn.style.display = 'none';
                            console.log('✅ 用户信息显示已更新');
                        } else {
                            console.error('❌ 用户信息元素不存在');
                        }

                        // 加载统计数据
                        this.loadStatistics();

                        // 尝试使用新权限系统进行更精细的控制
                        this.tryAdvancedPermissionCheck();
                    } else {
                        console.log('❌ 用户不是管理员，显示访问受限');

                        // 用户不是管理员
                        if (this.elements.accessDenied && this.elements.adminSection) {
                            this.elements.accessDenied.style.display = 'block';
                            this.elements.adminSection.style.display = 'none';
                        }

                        if (this.elements.userInfo && this.elements.loginBtn) {
                            this.elements.userInfo.style.display = 'none';
                            this.elements.loginBtn.style.display = 'block';
                        }
                    }
                } else {
                    // 用户未登录
                    this.elements.accessDenied.style.display = 'block';
                    this.elements.adminSection.style.display = 'none';
                    this.elements.userInfo.style.display = 'none';
                    this.elements.loginBtn.style.display = 'block';
                }
            }

            async tryAdvancedPermissionCheck() {
                try {
                    // 等待权限管理器初始化
                    if (window.permissionManager) {
                        await window.permissionManager.init();

                        // 根据权限显示/隐藏功能模块
                        this.updateModuleVisibility();

                        console.log('高级权限检查完成');
                    } else {
                        console.log('权限管理器未加载，使用基本权限检查');
                    }
                } catch (error) {
                    console.error('高级权限检查失败:', error);
                    // 失败时不影响基本功能
                }
            }

            async loadStatistics() {
                try {
                    // 加载用户统计
                    const userQuery = new AV.Query('_User');
                    const totalUsers = await userQuery.count();
                    this.elements.totalUsers.textContent = totalUsers;

                    // 加载活跃用户（最近7天登录）
                    const activeQuery = new AV.Query('_User');
                    const sevenDaysAgo = new Date();
                    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
                    activeQuery.greaterThan('updatedAt', sevenDaysAgo);
                    const activeUsers = await activeQuery.count();
                    this.elements.activeUsers.textContent = activeUsers;

                    // 模拟其他统计数据
                    this.elements.todayOperations.textContent = Math.floor(Math.random() * 100) + 50;
                    this.elements.systemWarnings.textContent = Math.floor(Math.random() * 5);
                } catch (error) {
                    console.error('加载统计数据失败:', error);
                }
            }

            showLoginModal() {
                document.getElementById('loginModal').style.display = 'flex';
            }

            updateModuleVisibility() {
                if (!window.permissionManager) {
                    console.log('权限管理器未初始化，显示所有模块');
                    // 如果权限管理器未初始化，显示所有模块（降级处理）
                    const allModules = document.querySelectorAll('.admin-module-link');
                    allModules.forEach(element => {
                        element.style.display = '';
                        if (element.parentElement) {
                            element.parentElement.style.display = '';
                        }
                    });
                    return;
                }

                try {
                    // 根据权限显示/隐藏功能模块
                    const modulePermissions = [
                        { selector: 'a[href="users.html"]', module: 'system', subModule: 'user_management', action: 'view' },
                        { selector: 'a[href="roles.html"]', module: 'system', subModule: 'role_management', action: 'view' },
                        { selector: 'a[href="system.html"]', module: 'system', subModule: 'system_settings', action: 'view' },
                        { selector: 'a[href="logs.html"]', module: 'system', subModule: 'logs', action: 'view' },
                        { selector: 'a[href="reports.html"]', module: 'system', subModule: 'reports', action: 'view' },
                        { selector: 'a[href="backup.html"]', module: 'system', subModule: 'backup', action: 'view' }
                    ];

                    modulePermissions.forEach(({ selector, module, subModule, action }) => {
                        const element = document.querySelector(selector);
                        if (element) {
                            try {
                                const hasPermission = window.permissionManager.hasPermission(module, subModule, action);
                                if (hasPermission) {
                                    element.style.display = '';
                                    if (element.parentElement) {
                                        element.parentElement.style.display = '';
                                    }
                                } else {
                                    element.style.display = 'none';
                                    if (element.parentElement) {
                                        element.parentElement.style.display = 'none';
                                    }
                                }
                            } catch (error) {
                                console.error('权限检查失败，显示模块:', selector, error);
                                // 权限检查失败时显示模块（降级处理）
                                element.style.display = '';
                                if (element.parentElement) {
                                    element.parentElement.style.display = '';
                                }
                            }
                        }
                    });
                } catch (error) {
                    console.error('模块可见性更新失败:', error);
                }
            }

            async handleLogin() {
                const username = document.getElementById('loginUsername').value;
                const password = document.getElementById('loginPassword').value;

                try {
                    await AV.User.logIn(username, password);
                    document.getElementById('loginModal').style.display = 'none';
                    await this.checkAdminAccess();
                    alert('登录成功');
                } catch (error) {
                    console.error('登录失败:', error);
                    alert('登录失败: ' + error.message);
                }
            }

            clearCache() {
                // 实现清理缓存功能
                if (confirm('确定要清理系统缓存吗？')) {
                    // 清理本地存储
                    localStorage.clear();
                    sessionStorage.clear();
                    alert('缓存清理完成');
                }
            }

            systemCheck() {
                // 实现系统检查功能
                alert('系统检查功能开发中...');
            }

            exportData() {
                // 实现数据导出功能
                alert('数据导出功能开发中...');
            }

            checkPageElements() {
                console.log('🔍 开始检查页面元素...');

                const elements = {
                    'accessDenied': '访问受限提示',
                    'adminSection': '管理功能区域',
                    'userInfo': '用户信息区域',
                    'loginBtn': '登录按钮',
                    'logoutBtn': '退出按钮',
                    'realName': '用户姓名显示',
                    'totalUsers': '总用户数统计',
                    'activeUsers': '活跃用户统计',
                    'todayOperations': '今日操作统计',
                    'systemWarnings': '系统警告统计',
                    'loginPromptBtn': '登录提示按钮'
                };

                let results = [];
                let missingElements = 0;
                let existingElements = 0;

                Object.entries(elements).forEach(([id, name]) => {
                    const element = document.getElementById(id);
                    if (element) {
                        console.log(`✅ ${name} (${id}) 存在`);
                        results.push(`✅ ${name} (${id}) 存在`);
                        existingElements++;
                    } else {
                        console.log(`❌ ${name} (${id}) 不存在`);
                        results.push(`❌ ${name} (${id}) 不存在`);
                        missingElements++;
                    }
                });

                // 检查管理模块链接
                const moduleLinks = document.querySelectorAll('.admin-module-link');
                console.log(`📋 找到 ${moduleLinks.length} 个管理模块链接`);
                results.push(`📋 找到 ${moduleLinks.length} 个管理模块链接`);

                // 检查管理卡片
                const adminCards = document.querySelectorAll('.admin-card');
                console.log(`📋 找到 ${adminCards.length} 个管理卡片`);
                results.push(`📋 找到 ${adminCards.length} 个管理卡片`);

                // 显示总结
                const summary = `页面元素检查完成！
✅ 存在的元素: ${existingElements}
❌ 缺失的元素: ${missingElements}
📋 管理模块链接: ${moduleLinks.length}
📋 管理卡片: ${adminCards.length}

详细结果请查看浏览器控制台。`;

                console.log('📊 检查总结:');
                console.log(`✅ 存在的元素: ${existingElements}`);
                console.log(`❌ 缺失的元素: ${missingElements}`);
                console.log(`📋 管理模块链接: ${moduleLinks.length}`);
                console.log(`📋 管理卡片: ${adminCards.length}`);

                if (missingElements === 0) {
                    console.log('🎉 所有关键元素都存在！');
                } else {
                    console.log('⚠️ 发现缺失的元素，请检查HTML结构');
                }

                alert(summary);
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 等待 LeanCloud 初始化
            function initApp() {
                if (typeof AV !== 'undefined') {
                    // 先初始化权限管理器
                    if (window.permissionManager) {
                        window.permissionManager.init().then(() => {
                            console.log('权限管理器初始化完成');
                        }).catch(error => {
                            console.error('权限管理器初始化失败:', error);
                        });
                    }

                    // 初始化管理应用
                    const adminApp = new AdminApp();
                    adminApp.init();
                } else {
                    setTimeout(initApp, 100);
                }
            }
            initApp();
        });
    </script>
</body>
</html>