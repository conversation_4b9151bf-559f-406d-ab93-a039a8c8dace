<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库初始化</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .init-step {
            margin-bottom: 1rem;
            padding: 1rem;
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
        }
        .step-success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .step-error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .step-pending {
            border-color: #ffc107;
            background-color: #fff3cd;
        }
        .step-running {
            border-color: #007bff;
            background-color: #d1ecf1;
        }
        .log-container {
            height: 400px;
            overflow-y: auto;
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 0.25rem;
            font-family: monospace;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">
            <i class="fas fa-database me-2"></i>数据库初始化
        </h1>
        
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            此页面用于初始化酒店管理系统的数据库，创建必要的数据表和示例数据。
        </div>

        <div class="row">
            <div class="col-md-8">
                <h3>初始化步骤</h3>
                
                <div id="step1" class="init-step step-pending">
                    <h5><i class="fas fa-check-circle me-2"></i>步骤1: 检查LeanCloud连接</h5>
                    <p class="mb-0">等待开始...</p>
                </div>
                
                <div id="step2" class="init-step step-pending">
                    <h5><i class="fas fa-users me-2"></i>步骤2: 创建用户和角色数据</h5>
                    <p class="mb-0">等待上一步完成...</p>
                </div>
                
                <div id="step3" class="init-step step-pending">
                    <h5><i class="fas fa-boxes me-2"></i>步骤3: 创建商品数据</h5>
                    <p class="mb-0">等待上一步完成...</p>
                </div>
                
                <div id="step4" class="init-step step-pending">
                    <h5><i class="fas fa-cogs me-2"></i>步骤4: 创建系统配置</h5>
                    <p class="mb-0">等待上一步完成...</p>
                </div>
                
                <div id="step5" class="init-step step-pending">
                    <h5><i class="fas fa-check-double me-2"></i>步骤5: 验证数据完整性</h5>
                    <p class="mb-0">等待上一步完成...</p>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-play me-2"></i>操作面板
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button id="startInitBtn" class="btn btn-primary">
                                <i class="fas fa-play me-2"></i>开始初始化
                            </button>
                            <button id="resetBtn" class="btn btn-warning" disabled>
                                <i class="fas fa-redo me-2"></i>重置状态
                            </button>
                            <button id="clearDataBtn" class="btn btn-danger" disabled>
                                <i class="fas fa-trash me-2"></i>清空数据
                            </button>
                        </div>
                        
                        <hr>
                        
                        <div class="text-center">
                            <div id="progressBar" class="progress mb-2" style="display: none;">
                                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <small id="progressText" class="text-muted">准备就绪</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h3>初始化日志</h3>
                <div id="logContainer" class="log-container">
                    <div class="text-muted">等待初始化开始...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入脚本 -->
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    
    <!-- LeanCloud SDK -->
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <script>
        window.addEventListener('load', function() {
            if (typeof AV === 'undefined') {
                console.log('主 CDN 加载失败，尝试备用源...');
                var script = document.createElement('script');
                script.src = 'https://cdn.bootcdn.net/ajax/libs/leancloud-storage/4.15.2/av-min.js';
                script.onerror = function() {
                    console.error('所有 CDN 源都加载失败，请检查网络连接');
                    alert('网络连接异常，请检查网络后刷新页面');
                };
                document.head.appendChild(script);
            }
        });
    </script>
    
    <!-- 配置文件 -->
    <script src="js/config.js"></script>

    <script>
        let initLogs = [];
        let currentStep = 0;
        let isInitializing = false;

        // 添加日志
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            initLogs.push(logEntry);
            
            const logContainer = document.getElementById('logContainer');
            const logElement = document.createElement('div');
            logElement.textContent = logEntry;
            
            if (type === 'error') {
                logElement.style.color = '#dc3545';
            } else if (type === 'success') {
                logElement.style.color = '#28a745';
            } else if (type === 'warning') {
                logElement.style.color = '#ffc107';
            }
            
            logContainer.appendChild(logElement);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 更新步骤状态
        function updateStep(stepNumber, status, message) {
            const stepElement = document.getElementById(`step${stepNumber}`);
            const paragraph = stepElement.querySelector('p');
            
            stepElement.className = `init-step step-${status}`;
            paragraph.textContent = message;
            
            const icon = stepElement.querySelector('i');
            if (status === 'success') {
                icon.className = 'fas fa-check-circle me-2';
            } else if (status === 'error') {
                icon.className = 'fas fa-times-circle me-2';
            } else if (status === 'running') {
                icon.className = 'fas fa-spinner fa-spin me-2';
            }
        }

        // 更新进度
        function updateProgress(percent, text) {
            const progressBar = document.querySelector('.progress-bar');
            const progressText = document.getElementById('progressText');
            
            progressBar.style.width = `${percent}%`;
            progressText.textContent = text;
            
            if (percent > 0) {
                document.getElementById('progressBar').style.display = 'block';
            }
        }

        // 开始初始化
        async function startInitialization() {
            if (isInitializing) return;
            
            isInitializing = true;
            document.getElementById('startInitBtn').disabled = true;
            document.getElementById('resetBtn').disabled = false;
            
            addLog('开始数据库初始化', 'info');
            updateProgress(0, '初始化开始...');
            
            try {
                // 步骤1: 检查LeanCloud连接
                currentStep = 1;
                updateStep(1, 'running', '正在检查LeanCloud连接...');
                updateProgress(10, '检查LeanCloud连接...');
                
                if (typeof AV === 'undefined') {
                    throw new Error('LeanCloud SDK未加载');
                }
                
                if (!AV.applicationId) {
                    throw new Error('LeanCloud未初始化');
                }
                
                addLog('LeanCloud连接正常', 'success');
                updateStep(1, 'success', 'LeanCloud连接正常');
                updateProgress(20, 'LeanCloud连接成功');
                
                // 步骤2: 创建用户和角色数据
                currentStep = 2;
                updateStep(2, 'running', '正在创建用户和角色数据...');
                updateProgress(30, '创建用户和角色数据...');
                await createUserAndRoleData();
                updateStep(2, 'success', '用户和角色数据创建完成');
                updateProgress(50, '用户和角色数据创建完成');
                
                // 步骤3: 创建商品数据
                currentStep = 3;
                updateStep(3, 'running', '正在创建商品数据...');
                updateProgress(60, '创建商品数据...');
                await createProductData();
                updateStep(3, 'success', '商品数据创建完成');
                updateProgress(80, '商品数据创建完成');
                
                // 步骤4: 创建系统配置
                currentStep = 4;
                updateStep(4, 'running', '正在创建系统配置...');
                updateProgress(90, '创建系统配置...');
                await createSystemConfig();
                updateStep(4, 'success', '系统配置创建完成');
                
                // 步骤5: 验证数据完整性
                currentStep = 5;
                updateStep(5, 'running', '正在验证数据完整性...');
                updateProgress(95, '验证数据完整性...');
                await verifyDataIntegrity();
                updateStep(5, 'success', '数据完整性验证通过');
                updateProgress(100, '初始化完成！');
                
                addLog('数据库初始化完成！', 'success');
                document.getElementById('clearDataBtn').disabled = false;
                
            } catch (error) {
                addLog(`初始化失败: ${error.message}`, 'error');
                updateStep(currentStep, 'error', `失败: ${error.message}`);
                updateProgress(0, '初始化失败');
            } finally {
                isInitializing = false;
                document.getElementById('startInitBtn').disabled = false;
            }
        }

        // 创建用户和角色数据
        async function createUserAndRoleData() {
            addLog('开始创建用户和角色数据');
            
            // 创建角色数据
            const roles = [
                { code: 'admin', name: '管理员', permissions: { system: { all: ['*'] }, inventory: { all: ['*'] }, work: { all: ['*'] }, repair: { all: ['*'] } } },
                { code: 'manager', name: '经理', permissions: { system: { basic: ['view'] }, inventory: { all: ['*'] }, work: { all: ['*'] }, repair: { manage: ['*'] } } },
                { code: 'user', name: '普通用户', permissions: { system: { basic: ['view'] }, inventory: { basic: ['view'] }, work: { basic: ['*'] }, repair: { basic: ['*'] } } }
            ];
            
            for (const roleData of roles) {
                try {
                    const Role = AV.Object.extend('Role');
                    const role = new Role();
                    Object.keys(roleData).forEach(key => {
                        role.set(key, roleData[key]);
                    });
                    await role.save();
                    addLog(`创建角色: ${roleData.name}`, 'success');
                } catch (error) {
                    if (error.code === 137) {
                        addLog(`角色已存在: ${roleData.name}`, 'warning');
                    } else {
                        throw error;
                    }
                }
            }
        }

        // 创建商品数据
        async function createProductData() {
            addLog('开始创建商品数据');
            
            const products = [
                { name: '洗发水', code: 'SHAMPOO-001', category: '洗护用品', currentStock: 50, minStock: 10, maxStock: 200, unit: '瓶', price: 25.00, status: 'active' },
                { name: '毛巾', code: 'TOWEL-001', category: '布草用品', currentStock: 100, minStock: 20, maxStock: 500, unit: '条', price: 15.00, status: 'active' },
                { name: '床单', code: 'SHEET-001', category: '布草用品', currentStock: 30, minStock: 15, maxStock: 100, unit: '套', price: 80.00, status: 'active' },
                { name: '清洁剂', code: 'CLEANER-001', category: '清洁用品', currentStock: 5, minStock: 10, maxStock: 50, unit: '瓶', price: 12.00, status: 'active' },
                { name: '垃圾袋', code: 'BAG-001', category: '清洁用品', currentStock: 0, minStock: 5, maxStock: 100, unit: '包', price: 8.00, status: 'active' }
            ];
            
            for (const productData of products) {
                const Product = AV.Object.extend('Product');
                const product = new Product();
                Object.keys(productData).forEach(key => {
                    product.set(key, productData[key]);
                });
                await product.save();
                addLog(`创建商品: ${productData.name}`, 'success');
            }
        }

        // 创建系统配置
        async function createSystemConfig() {
            addLog('开始创建系统配置');
            
            const configs = [
                { key: 'system.name', value: '酒店管理系统', description: '系统名称' },
                { key: 'inventory.auto_alert', value: 'true', description: '自动库存预警' },
                { key: 'inventory.alert_threshold', value: '0.8', description: '预警阈值' }
            ];
            
            for (const configData of configs) {
                const Config = AV.Object.extend('SystemConfig');
                const config = new Config();
                Object.keys(configData).forEach(key => {
                    config.set(key, configData[key]);
                });
                await config.save();
                addLog(`创建配置: ${configData.key}`, 'success');
            }
        }

        // 验证数据完整性
        async function verifyDataIntegrity() {
            addLog('开始验证数据完整性');
            
            // 验证角色数据
            const roleQuery = new AV.Query('Role');
            const roleCount = await roleQuery.count();
            addLog(`角色数据: ${roleCount} 条`, roleCount > 0 ? 'success' : 'error');
            
            // 验证商品数据
            const productQuery = new AV.Query('Product');
            const productCount = await productQuery.count();
            addLog(`商品数据: ${productCount} 条`, productCount > 0 ? 'success' : 'error');
            
            // 验证配置数据
            const configQuery = new AV.Query('SystemConfig');
            const configCount = await configQuery.count();
            addLog(`配置数据: ${configCount} 条`, configCount > 0 ? 'success' : 'error');
            
            if (roleCount === 0 || productCount === 0 || configCount === 0) {
                throw new Error('数据完整性验证失败');
            }
        }

        // 重置状态
        function resetStatus() {
            for (let i = 1; i <= 5; i++) {
                updateStep(i, 'pending', '等待开始...');
            }
            updateProgress(0, '准备就绪');
            document.getElementById('logContainer').innerHTML = '<div class="text-muted">等待初始化开始...</div>';
            initLogs = [];
            currentStep = 0;
            document.getElementById('resetBtn').disabled = true;
        }

        // 清空数据
        async function clearAllData() {
            if (!confirm('确定要清空所有数据吗？此操作不可恢复！')) {
                return;
            }
            
            addLog('开始清空数据...', 'warning');
            
            try {
                const tables = ['Role', 'Product', 'SystemConfig', 'InboundRecord', 'OutboundRecord'];
                
                for (const tableName of tables) {
                    try {
                        const query = new AV.Query(tableName);
                        query.limit(1000);
                        const objects = await query.find();
                        
                        if (objects.length > 0) {
                            await AV.Object.destroyAll(objects);
                            addLog(`清空表 ${tableName}: ${objects.length} 条记录`, 'warning');
                        }
                    } catch (error) {
                        addLog(`清空表 ${tableName} 失败: ${error.message}`, 'error');
                    }
                }
                
                addLog('数据清空完成', 'success');
                resetStatus();
                
            } catch (error) {
                addLog(`清空数据失败: ${error.message}`, 'error');
            }
        }

        // 事件监听
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('startInitBtn').addEventListener('click', startInitialization);
            document.getElementById('resetBtn').addEventListener('click', resetStatus);
            document.getElementById('clearDataBtn').addEventListener('click', clearAllData);
            
            addLog('数据库初始化工具加载完成', 'info');
        });
    </script>
</body>
</html>
