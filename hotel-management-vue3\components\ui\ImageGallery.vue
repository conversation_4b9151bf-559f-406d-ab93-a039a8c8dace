<template>
  <div class="image-gallery">
    <!-- 图片网格 -->
    <div 
      class="grid gap-2"
      :class="gridClass"
    >
      <div 
        v-for="(image, index) in images" 
        :key="index"
        class="relative group cursor-pointer overflow-hidden rounded-lg bg-gray-100"
        @click="openLightbox(index)"
      >
        <img 
          :src="image" 
          :alt="`图片 ${index + 1}`"
          class="w-full h-full object-cover transition-transform group-hover:scale-105"
          loading="lazy"
          @error="handleImageError"
        />
        
        <!-- 悬停遮罩 -->
        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center">
          <Icon 
            name="mdi:magnify-plus" 
            size="24" 
            class="text-white opacity-0 group-hover:opacity-100 transition-opacity"
          />
        </div>
        
        <!-- 图片数量标识（仅在多图时显示） -->
        <div 
          v-if="images.length > 1 && index === 0"
          class="absolute top-2 right-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded-full"
        >
          {{ images.length }}
        </div>
      </div>
    </div>

    <!-- 图片灯箱 -->
    <div 
      v-if="lightboxOpen"
      class="fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center"
      @click="closeLightbox"
    >
      <div class="relative max-w-4xl max-h-full p-4">
        <!-- 关闭按钮 -->
        <button 
          @click="closeLightbox"
          class="absolute top-4 right-4 text-white hover:text-gray-300 z-10"
        >
          <Icon name="mdi:close" size="32" />
        </button>
        
        <!-- 主图片 -->
        <img 
          :src="images[currentImageIndex]" 
          :alt="`图片 ${currentImageIndex + 1}`"
          class="max-w-full max-h-full object-contain"
          @click.stop
        />
        
        <!-- 导航按钮 -->
        <template v-if="images.length > 1">
          <button 
            v-if="currentImageIndex > 0"
            @click.stop="previousImage"
            class="absolute left-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300"
          >
            <Icon name="mdi:chevron-left" size="48" />
          </button>
          
          <button 
            v-if="currentImageIndex < images.length - 1"
            @click.stop="nextImage"
            class="absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300"
          >
            <Icon name="mdi:chevron-right" size="48" />
          </button>
        </template>
        
        <!-- 图片计数器 -->
        <div 
          v-if="images.length > 1"
          class="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white text-sm bg-black bg-opacity-50 px-3 py-1 rounded-full"
        >
          {{ currentImageIndex + 1 }} / {{ images.length }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  images: string[]
  maxHeight?: string
}

const props = withDefaults(defineProps<Props>(), {
  maxHeight: '200px'
})

const lightboxOpen = ref(false)
const currentImageIndex = ref(0)

// 计算网格布局类
const gridClass = computed(() => {
  const count = props.images.length
  
  if (count === 1) {
    return 'grid-cols-1'
  } else if (count === 2) {
    return 'grid-cols-2'
  } else if (count === 3) {
    return 'grid-cols-3'
  } else if (count === 4) {
    return 'grid-cols-2'
  } else {
    return 'grid-cols-3'
  }
})

// 方法
const openLightbox = (index: number) => {
  currentImageIndex.value = index
  lightboxOpen.value = true
  // 防止背景滚动
  document.body.style.overflow = 'hidden'
}

const closeLightbox = () => {
  lightboxOpen.value = false
  // 恢复背景滚动
  document.body.style.overflow = ''
}

const previousImage = () => {
  if (currentImageIndex.value > 0) {
    currentImageIndex.value--
  }
}

const nextImage = () => {
  if (currentImageIndex.value < props.images.length - 1) {
    currentImageIndex.value++
  }
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/images/image-placeholder.png' // 占位图片
}

// 键盘导航
const handleKeydown = (event: KeyboardEvent) => {
  if (!lightboxOpen.value) return
  
  switch (event.key) {
    case 'Escape':
      closeLightbox()
      break
    case 'ArrowLeft':
      previousImage()
      break
    case 'ArrowRight':
      nextImage()
      break
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  // 确保恢复背景滚动
  document.body.style.overflow = ''
})
</script>

<style scoped>
.image-gallery img {
  aspect-ratio: 1;
  min-height: 80px;
  max-height: v-bind(maxHeight);
}

/* 单张图片时使用不同的宽高比 */
.grid-cols-1 img {
  aspect-ratio: 16/9;
  max-height: 300px;
}

/* 两张图片时的布局 */
.grid-cols-2 img {
  aspect-ratio: 4/3;
}

/* 多张图片时的紧凑布局 */
.grid-cols-3 img {
  aspect-ratio: 1;
  max-height: 120px;
}
</style>
