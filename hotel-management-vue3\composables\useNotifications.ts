// composables/useNotifications.ts
// 通知系统组合式函数

import { ref, reactive } from 'vue'

export interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
  persistent?: boolean
  actions?: NotificationAction[]
  createdAt: Date
}

export interface NotificationAction {
  label: string
  action: () => void
  style?: 'primary' | 'secondary'
}

export interface ToastOptions {
  type?: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
  persistent?: boolean
  actions?: NotificationAction[]
}

// 全局通知状态
const notifications = ref<Notification[]>([])
const unreadCount = ref(0)

export function useNotifications() {
  
  // 添加通知
  const addNotification = (options: ToastOptions): string => {
    const id = `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    const notification: Notification = {
      id,
      type: options.type || 'info',
      title: options.title,
      message: options.message,
      duration: options.duration || (options.persistent ? 0 : 5000),
      persistent: options.persistent || false,
      actions: options.actions || [],
      createdAt: new Date()
    }
    
    notifications.value.unshift(notification)
    unreadCount.value++
    
    // 自动移除非持久化通知
    if (!notification.persistent && notification.duration > 0) {
      setTimeout(() => {
        removeNotification(id)
      }, notification.duration)
    }
    
    return id
  }

  // 移除通知
  const removeNotification = (id: string) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
      if (unreadCount.value > 0) {
        unreadCount.value--
      }
    }
  }

  // 清除所有通知
  const clearAllNotifications = () => {
    notifications.value = []
    unreadCount.value = 0
  }

  // 标记为已读
  const markAsRead = (id?: string) => {
    if (id) {
      // 标记单个通知为已读
      const notification = notifications.value.find(n => n.id === id)
      if (notification && unreadCount.value > 0) {
        unreadCount.value--
      }
    } else {
      // 标记所有通知为已读
      unreadCount.value = 0
    }
  }

  // 快捷方法
  const success = (title: string, message?: string, options?: Partial<ToastOptions>) => {
    return addNotification({
      type: 'success',
      title,
      message,
      ...options
    })
  }

  const error = (title: string, message?: string, options?: Partial<ToastOptions>) => {
    return addNotification({
      type: 'error',
      title,
      message,
      persistent: true, // 错误通知默认持久化
      ...options
    })
  }

  const warning = (title: string, message?: string, options?: Partial<ToastOptions>) => {
    return addNotification({
      type: 'warning',
      title,
      message,
      duration: 8000, // 警告通知显示时间更长
      ...options
    })
  }

  const info = (title: string, message?: string, options?: Partial<ToastOptions>) => {
    return addNotification({
      type: 'info',
      title,
      message,
      ...options
    })
  }

  // 系统通知
  const systemNotification = (title: string, message?: string, actions?: NotificationAction[]) => {
    return addNotification({
      type: 'info',
      title,
      message,
      persistent: true,
      actions
    })
  }

  // 工作流通知
  const workflowNotification = (type: 'repair' | 'inventory' | 'user', title: string, message?: string) => {
    const typeConfig = {
      repair: { type: 'warning' as const, duration: 10000 },
      inventory: { type: 'info' as const, duration: 8000 },
      user: { type: 'info' as const, duration: 6000 }
    }
    
    const config = typeConfig[type]
    
    return addNotification({
      type: config.type,
      title,
      message,
      duration: config.duration
    })
  }

  return {
    // 状态
    notifications: readonly(notifications),
    unreadCount: readonly(unreadCount),
    
    // 方法
    addNotification,
    removeNotification,
    clearAllNotifications,
    markAsRead,
    
    // 快捷方法
    success,
    error,
    warning,
    info,
    systemNotification,
    workflowNotification
  }
}

// 全局通知实例
let globalNotifications: ReturnType<typeof useNotifications> | null = null

export function useGlobalNotifications() {
  if (!globalNotifications) {
    globalNotifications = useNotifications()
  }
  return globalNotifications
}
