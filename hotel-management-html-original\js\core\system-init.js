/**
 * 系统初始化脚本
 * 负责系统启动时的初始化工作
 */

class SystemInitializer {
    constructor() {
        this.initSteps = [];
        this.initProgress = 0;
        this.isInitialized = false;
        this.initStartTime = null;
    }

    /**
     * 添加初始化步骤
     * @param {string} name - 步骤名称
     * @param {Function} handler - 处理函数
     * @param {Object} options - 选项
     */
    addInitStep(name, handler, options = {}) {
        this.initSteps.push({
            name,
            handler,
            required: options.required !== false,
            timeout: options.timeout || 10000,
            retries: options.retries || 0,
            completed: false,
            error: null
        });
    }

    /**
     * 开始系统初始化
     */
    async initialize() {
        if (this.isInitialized) {
            console.log('系统已经初始化完成');
            return true;
        }

        this.initStartTime = Date.now();
        console.log('🚀 开始系统初始化...');

        try {
            // 显示初始化进度
            this.showInitProgress();

            // 执行所有初始化步骤
            for (let i = 0; i < this.initSteps.length; i++) {
                const step = this.initSteps[i];
                await this.executeInitStep(step, i);
                this.updateProgress(i + 1);
            }

            this.isInitialized = true;
            const duration = Date.now() - this.initStartTime;
            console.log(`✅ 系统初始化完成，耗时 ${duration}ms`);
            
            this.hideInitProgress();
            this.onInitComplete();
            
            return true;

        } catch (error) {
            console.error('❌ 系统初始化失败:', error);
            this.hideInitProgress();
            this.onInitError(error);
            return false;
        }
    }

    /**
     * 执行单个初始化步骤
     * @param {Object} step - 初始化步骤
     * @param {number} index - 步骤索引
     */
    async executeInitStep(step, index) {
        console.log(`执行初始化步骤 ${index + 1}/${this.initSteps.length}: ${step.name}`);
        
        let attempts = 0;
        const maxAttempts = step.retries + 1;

        while (attempts < maxAttempts) {
            try {
                // 设置超时
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error(`初始化步骤超时: ${step.name}`)), step.timeout);
                });

                // 执行步骤
                await Promise.race([
                    step.handler(),
                    timeoutPromise
                ]);

                step.completed = true;
                console.log(`✅ ${step.name} 完成`);
                return;

            } catch (error) {
                attempts++;
                step.error = error;
                
                if (attempts < maxAttempts) {
                    console.warn(`⚠️ ${step.name} 失败，重试 ${attempts}/${step.retries}:`, error.message);
                    await new Promise(resolve => setTimeout(resolve, 1000 * attempts));
                } else {
                    console.error(`❌ ${step.name} 失败:`, error);
                    
                    if (step.required) {
                        throw new Error(`必需的初始化步骤失败: ${step.name} - ${error.message}`);
                    }
                }
            }
        }
    }

    /**
     * 显示初始化进度
     */
    showInitProgress() {
        const progressContainer = document.createElement('div');
        progressContainer.id = 'system-init-progress';
        progressContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.95);
            z-index: 99999;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        `;

        progressContainer.innerHTML = `
            <div style="text-align: center; max-width: 400px;">
                <div style="margin-bottom: 20px;">
                    <i class="fas fa-cog fa-spin" style="font-size: 48px; color: #007bff;"></i>
                </div>
                <h3 style="margin-bottom: 10px; color: #333;">系统初始化中...</h3>
                <p id="init-step-name" style="color: #666; margin-bottom: 20px;">准备开始</p>
                <div style="width: 300px; height: 6px; background: #e9ecef; border-radius: 3px; overflow: hidden;">
                    <div id="init-progress-bar" style="width: 0%; height: 100%; background: #007bff; transition: width 0.3s ease;"></div>
                </div>
                <p id="init-progress-text" style="margin-top: 10px; color: #666; font-size: 14px;">0%</p>
            </div>
        `;

        document.body.appendChild(progressContainer);
    }

    /**
     * 更新初始化进度
     * @param {number} completed - 已完成步骤数
     */
    updateProgress(completed) {
        const percentage = Math.round((completed / this.initSteps.length) * 100);
        
        const progressBar = document.getElementById('init-progress-bar');
        const progressText = document.getElementById('init-progress-text');
        const stepName = document.getElementById('init-step-name');
        
        if (progressBar) {
            progressBar.style.width = `${percentage}%`;
        }
        
        if (progressText) {
            progressText.textContent = `${percentage}%`;
        }
        
        if (stepName && completed < this.initSteps.length) {
            stepName.textContent = `正在执行: ${this.initSteps[completed].name}`;
        } else if (stepName) {
            stepName.textContent = '初始化完成';
        }
    }

    /**
     * 隐藏初始化进度
     */
    hideInitProgress() {
        const progressContainer = document.getElementById('system-init-progress');
        if (progressContainer) {
            progressContainer.style.opacity = '0';
            setTimeout(() => {
                progressContainer.remove();
            }, 300);
        }
    }

    /**
     * 初始化完成回调
     */
    onInitComplete() {
        // 触发系统初始化完成事件
        window.dispatchEvent(new CustomEvent('systemInitialized', {
            detail: {
                duration: Date.now() - this.initStartTime,
                steps: this.initSteps.length,
                success: true
            }
        }));
    }

    /**
     * 初始化错误回调
     * @param {Error} error - 错误对象
     */
    onInitError(error) {
        // 显示错误信息
        globalErrorHandler.showUserError('系统初始化失败，请刷新页面重试');
        
        // 触发系统初始化失败事件
        window.dispatchEvent(new CustomEvent('systemInitializationFailed', {
            detail: {
                error: error.message,
                duration: Date.now() - this.initStartTime,
                steps: this.initSteps.length,
                success: false
            }
        }));
    }

    /**
     * 获取初始化状态
     * @returns {Object} 初始化状态
     */
    getInitStatus() {
        return {
            isInitialized: this.isInitialized,
            totalSteps: this.initSteps.length,
            completedSteps: this.initSteps.filter(s => s.completed).length,
            failedSteps: this.initSteps.filter(s => s.error && !s.completed).length,
            steps: this.initSteps.map(s => ({
                name: s.name,
                completed: s.completed,
                error: s.error?.message
            }))
        };
    }
}

// 创建全局系统初始化器
const systemInitializer = new SystemInitializer();

// 添加默认初始化步骤
systemInitializer.addInitStep('LeanCloud SDK检查', async () => {
    if (typeof AV === 'undefined') {
        throw new Error('LeanCloud SDK未加载');
    }

    // 检查配置
    if (!WORKLOG_CONFIG?.LEANCLOUD?.APP_ID) {
        throw new Error('LeanCloud配置缺失');
    }

    // 检查是否已经初始化
    if (AV.applicationId) {
        console.log('LeanCloud已经初始化，跳过重复初始化');
        return;
    }

    // 初始化LeanCloud
    try {
        AV.init({
            appId: WORKLOG_CONFIG.LEANCLOUD.APP_ID,
            appKey: WORKLOG_CONFIG.LEANCLOUD.APP_KEY,
            serverURL: WORKLOG_CONFIG.LEANCLOUD.SERVER_URL
        });

        // 验证初始化是否成功
        if (!AV.applicationId) {
            throw new Error('LeanCloud初始化后applicationId为空');
        }

        console.log('LeanCloud SDK初始化完成，App ID:', AV.applicationId);
    } catch (error) {
        console.error('LeanCloud初始化失败:', error);
        throw error;
    }
}, { required: true });

systemInitializer.addInitStep('权限管理器初始化', async () => {
    if (typeof permissionManager !== 'undefined') {
        await permissionManager.init();
        console.log('权限管理器初始化完成');
    }
}, { required: false });

systemInitializer.addInitStep('性能管理器初始化', async () => {
    if (typeof performanceManager !== 'undefined') {
        // 启动图片懒加载
        performanceManager.lazyLoadImages();
        console.log('性能管理器初始化完成');
    }
}, { required: false });

systemInitializer.addInitStep('库存预警系统初始化', async () => {
    if (typeof inventoryAlertSystem !== 'undefined') {
        // 库存预警系统会自动初始化
        console.log('库存预警系统初始化完成');
    }
}, { required: false });

// 等待LeanCloud SDK加载完成后再初始化
function checkAndInitSystem() {
    if (typeof AV !== 'undefined') {
        console.log('LeanCloud SDK已加载，开始系统初始化');
        systemInitializer.initialize();
    } else {
        // 如果SDK还没加载，等待一段时间后重试
        setTimeout(checkAndInitSystem, 100);
    }
}

// 页面加载完成后等待LeanCloud并初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('页面DOM加载完成，等待LeanCloud SDK');
    setTimeout(checkAndInitSystem, 500);
});

// 导出到全局
if (typeof window !== 'undefined') {
    window.SystemInitializer = SystemInitializer;
    window.systemInitializer = systemInitializer;
}

// Node.js环境支持
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { SystemInitializer, systemInitializer };
}
