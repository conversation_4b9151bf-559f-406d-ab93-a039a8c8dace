<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">系统设置</h1>
          <p class="mt-1 text-sm text-gray-600">
            配置系统参数、功能开关和集成服务
          </p>
        </div>
        
        <div class="mt-4 sm:mt-0 flex space-x-3">
          <button 
            @click="handleSaveSettings"
            :disabled="saving"
            class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center disabled:opacity-50"
          >
            <Icon :name="saving ? 'mdi:loading' : 'mdi:content-save'" :size="16" :class="saving ? 'animate-spin' : ''" class="mr-1" />
            {{ saving ? '保存中...' : '保存设置' }}
          </button>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
      <!-- 左侧导航 -->
      <div class="lg:col-span-1">
        <nav class="space-y-1">
          <button 
            v-for="section in settingSections" 
            :key="section.id"
            @click="activeSection = section.id"
            :class="activeSection === section.id ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-transparent text-gray-900 hover:bg-gray-50'"
            class="w-full text-left group border-l-4 px-3 py-2 flex items-center text-sm font-medium transition-colors"
          >
            <Icon :name="section.icon" size="16" class="mr-3" />
            {{ section.name }}
          </button>
        </nav>
      </div>

      <!-- 右侧内容 -->
      <div class="lg:col-span-3">
        <!-- 基本设置 -->
        <div v-if="activeSection === 'basic'" class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">基本设置</h3>
            <p class="text-sm text-gray-600">配置系统基本信息和参数</p>
          </div>
          
          <div class="px-6 py-4 space-y-6">
            <!-- 系统名称 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                系统名称
              </label>
              <input 
                v-model="settings.basic.systemName"
                type="text"
                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="酒店管理系统"
              />
            </div>

            <!-- 系统描述 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                系统描述
              </label>
              <textarea 
                v-model="settings.basic.systemDescription"
                rows="3"
                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                placeholder="系统功能描述..."
              ></textarea>
            </div>

            <!-- 时区设置 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                时区设置
              </label>
              <select 
                v-model="settings.basic.timezone"
                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="Asia/Shanghai">中国标准时间 (UTC+8)</option>
                <option value="UTC">协调世界时 (UTC)</option>
                <option value="America/New_York">美国东部时间 (UTC-5)</option>
              </select>
            </div>

            <!-- 语言设置 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                默认语言
              </label>
              <select 
                v-model="settings.basic.language"
                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="zh-CN">简体中文</option>
                <option value="en-US">English</option>
              </select>
            </div>
          </div>
        </div>

        <!-- 企业微信设置 -->
        <div v-if="activeSection === 'wechat'" class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">企业微信设置</h3>
            <p class="text-sm text-gray-600">配置企业微信集成参数</p>
          </div>
          
          <div class="px-6 py-4 space-y-6">
            <!-- 启用开关 -->
            <div class="flex items-center justify-between">
              <div>
                <label class="text-sm font-medium text-gray-700">启用企业微信</label>
                <p class="text-sm text-gray-500">开启后可以发送通知消息</p>
              </div>
              <label class="relative inline-flex items-center cursor-pointer">
                <input 
                  v-model="settings.wechat.enabled"
                  type="checkbox" 
                  class="sr-only peer"
                />
                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <!-- 企业ID -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                企业ID (CorpId)
              </label>
              <input 
                v-model="settings.wechat.corpId"
                type="text"
                :disabled="!settings.wechat.enabled"
                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
                placeholder="企业微信的企业ID"
              />
            </div>

            <!-- 应用Secret -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                应用Secret
              </label>
              <input 
                v-model="settings.wechat.corpSecret"
                type="password"
                :disabled="!settings.wechat.enabled"
                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
                placeholder="应用的Secret密钥"
              />
            </div>

            <!-- 应用ID -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                应用ID (AgentId)
              </label>
              <input 
                v-model="settings.wechat.agentId"
                type="text"
                :disabled="!settings.wechat.enabled"
                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
                placeholder="应用的AgentId"
              />
            </div>

            <!-- 测试连接 -->
            <div v-if="settings.wechat.enabled">
              <button 
                @click="testWechatConnection"
                :disabled="testingWechat"
                class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors flex items-center disabled:opacity-50"
              >
                <Icon :name="testingWechat ? 'mdi:loading' : 'mdi:connection'" :size="16" :class="testingWechat ? 'animate-spin' : ''" class="mr-1" />
                {{ testingWechat ? '测试中...' : '测试连接' }}
              </button>
            </div>
          </div>
        </div>

        <!-- 通知设置 -->
        <div v-if="activeSection === 'notifications'" class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">通知设置</h3>
            <p class="text-sm text-gray-600">配置系统通知规则和方式</p>
          </div>
          
          <div class="px-6 py-4 space-y-6">
            <!-- 工作日志通知 -->
            <div class="border border-gray-200 rounded-lg p-4">
              <h4 class="text-md font-medium text-gray-900 mb-3">工作日志通知</h4>
              <div class="space-y-3">
                <label class="flex items-center">
                  <input 
                    v-model="settings.notifications.workLog.enabled"
                    type="checkbox"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span class="ml-2 text-sm text-gray-700">启用工作日志通知</span>
                </label>
                
                <label class="flex items-center">
                  <input 
                    v-model="settings.notifications.workLog.dailyReminder"
                    type="checkbox"
                    :disabled="!settings.notifications.workLog.enabled"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span class="ml-2 text-sm text-gray-700">每日提醒填写日志</span>
                </label>
              </div>
            </div>

            <!-- 报修通知 -->
            <div class="border border-gray-200 rounded-lg p-4">
              <h4 class="text-md font-medium text-gray-900 mb-3">报修通知</h4>
              <div class="space-y-3">
                <label class="flex items-center">
                  <input 
                    v-model="settings.notifications.repair.enabled"
                    type="checkbox"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span class="ml-2 text-sm text-gray-700">启用报修通知</span>
                </label>
                
                <label class="flex items-center">
                  <input 
                    v-model="settings.notifications.repair.urgentOnly"
                    type="checkbox"
                    :disabled="!settings.notifications.repair.enabled"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span class="ml-2 text-sm text-gray-700">仅紧急报修发送通知</span>
                </label>
              </div>
            </div>

            <!-- 库存通知 -->
            <div class="border border-gray-200 rounded-lg p-4">
              <h4 class="text-md font-medium text-gray-900 mb-3">库存通知</h4>
              <div class="space-y-3">
                <label class="flex items-center">
                  <input 
                    v-model="settings.notifications.inventory.enabled"
                    type="checkbox"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span class="ml-2 text-sm text-gray-700">启用库存预警通知</span>
                </label>
                
                <label class="flex items-center">
                  <input 
                    v-model="settings.notifications.inventory.lowStockAlert"
                    type="checkbox"
                    :disabled="!settings.notifications.inventory.enabled"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span class="ml-2 text-sm text-gray-700">低库存自动提醒</span>
                </label>
              </div>
            </div>
          </div>
        </div>

        <!-- 安全设置 -->
        <div v-if="activeSection === 'security'" class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">安全设置</h3>
            <p class="text-sm text-gray-600">配置系统安全策略和访问控制</p>
          </div>
          
          <div class="px-6 py-4 space-y-6">
            <!-- 密码策略 -->
            <div>
              <h4 class="text-md font-medium text-gray-900 mb-3">密码策略</h4>
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    最小密码长度
                  </label>
                  <input 
                    v-model.number="settings.security.passwordMinLength"
                    type="number"
                    min="6"
                    max="20"
                    class="w-32 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                
                <label class="flex items-center">
                  <input 
                    v-model="settings.security.requireSpecialChars"
                    type="checkbox"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span class="ml-2 text-sm text-gray-700">密码必须包含特殊字符</span>
                </label>
                
                <label class="flex items-center">
                  <input 
                    v-model="settings.security.requireNumbers"
                    type="checkbox"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span class="ml-2 text-sm text-gray-700">密码必须包含数字</span>
                </label>
              </div>
            </div>

            <!-- 会话设置 -->
            <div>
              <h4 class="text-md font-medium text-gray-900 mb-3">会话设置</h4>
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    会话超时时间 (小时)
                  </label>
                  <input 
                    v-model.number="settings.security.sessionTimeout"
                    type="number"
                    min="1"
                    max="24"
                    class="w-32 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                
                <label class="flex items-center">
                  <input 
                    v-model="settings.security.forceLogoutOnPasswordChange"
                    type="checkbox"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span class="ml-2 text-sm text-gray-700">密码修改后强制重新登录</span>
                </label>
              </div>
            </div>
          </div>
        </div>

        <!-- 存储设置 -->
        <div v-if="activeSection === 'storage'" class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">存储设置</h3>
            <p class="text-sm text-gray-600">配置文件存储和数据管理策略</p>
          </div>
          
          <div class="px-6 py-4 space-y-6">
            <!-- 文件上传限制 -->
            <div>
              <h4 class="text-md font-medium text-gray-900 mb-3">文件上传限制</h4>
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    单个文件最大大小 (MB)
                  </label>
                  <input 
                    v-model.number="settings.storage.maxFileSize"
                    type="number"
                    min="1"
                    max="100"
                    class="w-32 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    允许的文件类型
                  </label>
                  <input 
                    v-model="settings.storage.allowedFileTypes"
                    type="text"
                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="jpg,png,pdf,doc,docx"
                  />
                </div>
              </div>
            </div>

            <!-- 数据清理 -->
            <div>
              <h4 class="text-md font-medium text-gray-900 mb-3">数据清理</h4>
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    日志保留天数
                  </label>
                  <input 
                    v-model.number="settings.storage.logRetentionDays"
                    type="number"
                    min="30"
                    max="365"
                    class="w-32 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                
                <label class="flex items-center">
                  <input 
                    v-model="settings.storage.autoCleanup"
                    type="checkbox"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span class="ml-2 text-sm text-gray-700">启用自动清理过期数据</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面元数据
definePageMeta({
  title: '系统设置',
  middleware: 'auth',
  layout: 'default'
})

// 检查管理员权限
const authStore = useAuthStore()
if (!authStore.isAdmin) {
  throw createError({
    statusCode: 403,
    statusMessage: '权限不足，无法访问系统设置页面'
  })
}

// 响应式数据
const activeSection = ref('basic')
const saving = ref(false)
const testingWechat = ref(false)

const settingSections = [
  { id: 'basic', name: '基本设置', icon: 'mdi:cog' },
  { id: 'wechat', name: '企业微信', icon: 'mdi:wechat' },
  { id: 'notifications', name: '通知设置', icon: 'mdi:bell' },
  { id: 'security', name: '安全设置', icon: 'mdi:shield' },
  { id: 'storage', name: '存储设置', icon: 'mdi:database' }
]

const settings = reactive({
  basic: {
    systemName: '酒店管理系统',
    systemDescription: '现代化的酒店管理解决方案',
    timezone: 'Asia/Shanghai',
    language: 'zh-CN'
  },
  wechat: {
    enabled: true,
    corpId: '',
    corpSecret: '',
    agentId: ''
  },
  notifications: {
    workLog: {
      enabled: true,
      dailyReminder: true
    },
    repair: {
      enabled: true,
      urgentOnly: false
    },
    inventory: {
      enabled: true,
      lowStockAlert: true
    }
  },
  security: {
    passwordMinLength: 8,
    requireSpecialChars: true,
    requireNumbers: true,
    sessionTimeout: 8,
    forceLogoutOnPasswordChange: true
  },
  storage: {
    maxFileSize: 5,
    allowedFileTypes: 'jpg,png,pdf,doc,docx,xlsx',
    logRetentionDays: 90,
    autoCleanup: true
  }
})

// 方法
const handleSaveSettings = async () => {
  saving.value = true
  
  try {
    // 这里应该调用API保存设置
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 保存到localStorage作为演示
    localStorage.setItem('systemSettings', JSON.stringify(settings))
    
    alert('设置保存成功')
  } catch (error) {
    console.error('保存设置失败:', error)
    alert('保存设置失败，请稍后重试')
  } finally {
    saving.value = false
  }
}

const testWechatConnection = async () => {
  testingWechat.value = true
  
  try {
    // 这里应该调用API测试企业微信连接
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    alert('企业微信连接测试成功')
  } catch (error) {
    console.error('企业微信连接测试失败:', error)
    alert('企业微信连接测试失败，请检查配置')
  } finally {
    testingWechat.value = false
  }
}

// 生命周期
onMounted(() => {
  // 从localStorage加载设置
  const savedSettings = localStorage.getItem('systemSettings')
  if (savedSettings) {
    try {
      const parsed = JSON.parse(savedSettings)
      Object.assign(settings, parsed)
    } catch (error) {
      console.error('加载设置失败:', error)
    }
  }
})

// 页面标题
useHead({
  title: '系统设置 - 酒店管理系统'
})
</script>
