<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>任务列表</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- LeanCloud SDK -->
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <style>
        .status-pending { @apply bg-yellow-100 text-yellow-800; }
        .status-in-progress { @apply bg-blue-100 text-blue-800; }
        .status-completed { @apply bg-green-100 text-green-800; }
        .status-overdue { @apply bg-red-100 text-red-800; }
        .status-cancelled { @apply bg-gray-100 text-gray-800; }
        
        .priority-高 { @apply bg-red-100 text-red-800; }
        .priority-中 { @apply bg-yellow-100 text-yellow-800; }
        .priority-低 { @apply bg-green-100 text-green-800; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-3">
                    <a href="index.html" class="text-gray-600 hover:text-gray-800">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                    </a>
                    <h1 class="text-xl font-semibold text-gray-800">任务列表</h1>
                </div>
                <div class="flex items-center space-x-3">
                    <div id="userInfo" class="hidden items-center space-x-4">
                        <span id="realName" class="text-gray-800 font-medium"></span>
                        <button id="logoutBtn" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm">
                            退出登录
                        </button>
                    </div>
                    <button id="loginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm">
                        登录
                    </button>
                    <a href="task-create.html" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm">
                        发布任务
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="max-w-7xl mx-auto px-4 py-6">
        <!-- 未登录提示 -->
        <div id="accessDenied" class="bg-white rounded-lg shadow p-8 text-center">
            <h2 class="text-2xl font-bold text-gray-800 mb-4">需要登录</h2>
            <p class="text-gray-600 mb-6">请先登录后查看任务列表</p>
            <button id="promptLoginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg">
                立即登录
            </button>
        </div>

        <!-- 任务列表界面 -->
        <div id="listSection" class="hidden">
            <!-- 筛选和搜索 -->
            <div class="bg-white rounded-lg shadow p-6 mb-6">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                    <div class="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-4">
                        <select id="statusFilter" class="px-3 py-2 border border-gray-300 rounded-lg">
                            <option value="">全部状态</option>
                            <option value="pending">待开始</option>
                            <option value="in-progress">进行中</option>
                            <option value="completed">已完成</option>
                            <option value="overdue">已逾期</option>
                        </select>
                        <select id="priorityFilter" class="px-3 py-2 border border-gray-300 rounded-lg">
                            <option value="">全部优先级</option>
                            <option value="高">高优先级</option>
                            <option value="中">中优先级</option>
                            <option value="低">低优先级</option>
                        </select>
                        <select id="assigneeFilter" class="px-3 py-2 border border-gray-300 rounded-lg">
                            <option value="">全部执行人</option>
                            <option value="me">我的任务</option>
                            <option value="created">我创建的</option>
                        </select>
                        <input type="search" id="searchInput" placeholder="搜索任务标题或描述"
                            class="px-3 py-2 border border-gray-300 rounded-lg">
                    </div>
                    <div class="flex space-x-2">
                        <button id="refreshBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                            刷新
                        </button>
                    </div>
                </div>
            </div>

            <!-- 统计信息 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="text-2xl font-bold text-blue-600" id="totalCount">0</div>
                        <div class="ml-3">
                            <div class="text-sm font-medium text-gray-500">总任务</div>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="text-2xl font-bold text-yellow-600" id="pendingCount">0</div>
                        <div class="ml-3">
                            <div class="text-sm font-medium text-gray-500">待开始</div>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="text-2xl font-bold text-purple-600" id="inProgressCount">0</div>
                        <div class="ml-3">
                            <div class="text-sm font-medium text-gray-500">进行中</div>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="text-2xl font-bold text-green-600" id="completedCount">0</div>
                        <div class="ml-3">
                            <div class="text-sm font-medium text-gray-500">已完成</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 任务列表 -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-800">任务列表</h3>
                </div>
                <div id="tasksList" class="divide-y divide-gray-200">
                    <!-- 任务列表将通过JS动态填充 -->
                </div>
            </div>
        </div>
    </main>

    <!-- 进度更新弹窗 -->
    <div id="progressModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-800">更新任务进度</h2>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">任务进度</label>
                        <input type="range" id="progressSlider" min="0" max="100" value="0"
                            class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                        <div class="flex justify-between text-sm text-gray-500 mt-1">
                            <span>0%</span>
                            <span id="progressValue">0%</span>
                            <span>100%</span>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">进度说明</label>
                        <textarea id="progressNote" rows="3"
                            class="w-full p-3 border border-gray-300 rounded-lg"
                            placeholder="请描述当前进展情况..."></textarea>
                    </div>
                </div>
                <div class="flex gap-3 pt-6">
                    <button id="cancelProgress" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-3 rounded-lg">
                        取消
                    </button>
                    <button id="saveProgress" class="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg">
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 登录弹窗 -->
    <div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-800">用户登录</h2>
            </div>
            <div class="p-6">
                <form id="loginForm" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                        <input type="text" id="loginUsername" required
                            class="w-full p-3 border border-gray-300 rounded-lg">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                        <input type="password" id="loginPassword" required
                            class="w-full p-3 border border-gray-300 rounded-lg">
                    </div>
                    <div class="flex gap-3 pt-4">
                        <button type="button" id="cancelLogin" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-3 rounded-lg">
                            取消
                        </button>
                        <button type="submit" class="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg">
                            登录
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="../js/config.js?v=2.1"></script>
    <script src="../js/core/error-handler.js?v=2.1"></script>
    <script src="../js/utils.js?v=2.1"></script>
    <script src="../js/auth.js?v=2.1"></script>
    <script src="../js/base-app.js?v=2.1"></script>
    <script>
        class TaskListApp extends BaseWorkLogApp {
            constructor() {
                super({
                    pageType: 'task-list',
                    requiredElements: ['listSection']
                });
                
                this.tasks = [];
                this.filteredTasks = [];
                this.currentTask = null;
            }

            getPageElements() {
                return {
                    // 头部元素
                    userInfo: 'userInfo',
                    realName: 'realName',
                    loginBtn: 'loginBtn',
                    logoutBtn: 'logoutBtn',
                    // 登录弹窗元素
                    loginModal: 'loginModal',
                    loginForm: 'loginForm',
                    loginUsername: 'loginUsername',
                    loginPassword: 'loginPassword',
                    cancelLogin: 'cancelLogin',
                    // 页面内容元素
                    accessDenied: 'accessDenied',
                    listSection: 'listSection',
                    promptLoginBtn: 'promptLoginBtn',
                    // 筛选元素
                    statusFilter: 'statusFilter',
                    priorityFilter: 'priorityFilter',
                    assigneeFilter: 'assigneeFilter',
                    searchInput: 'searchInput',
                    refreshBtn: 'refreshBtn',
                    // 统计元素
                    totalCount: 'totalCount',
                    pendingCount: 'pendingCount',
                    inProgressCount: 'inProgressCount',
                    completedCount: 'completedCount',
                    // 列表元素
                    tasksList: 'tasksList',
                    // 进度弹窗元素
                    progressModal: 'progressModal',
                    progressSlider: 'progressSlider',
                    progressValue: 'progressValue',
                    progressNote: 'progressNote',
                    cancelProgress: 'cancelProgress',
                    saveProgress: 'saveProgress'
                };
            }

            bindPageEvents() {
                // 提示登录
                if (this.elements.promptLoginBtn) {
                    this.elements.promptLoginBtn.addEventListener('click', () => this.showLoginModal());
                }

                // 筛选事件
                ['statusFilter', 'priorityFilter', 'assigneeFilter'].forEach(filterId => {
                    if (this.elements[filterId]) {
                        this.elements[filterId].addEventListener('change', () => this.filterTasks());
                    }
                });

                // 搜索事件
                if (this.elements.searchInput) {
                    this.elements.searchInput.addEventListener('input', () => this.filterTasks());
                }

                // 刷新按钮
                if (this.elements.refreshBtn) {
                    this.elements.refreshBtn.addEventListener('click', () => this.loadTasks());
                }

                // 进度滑块
                if (this.elements.progressSlider) {
                    this.elements.progressSlider.addEventListener('input', (e) => {
                        this.elements.progressValue.textContent = e.target.value + '%';
                    });
                }

                // 进度弹窗按钮
                if (this.elements.cancelProgress) {
                    this.elements.cancelProgress.addEventListener('click', () => this.closeProgressModal());
                }
                if (this.elements.saveProgress) {
                    this.elements.saveProgress.addEventListener('click', () => this.saveTaskProgress());
                }
            }

            onUserLoggedIn() {
                console.log('=== 任务列表页面：用户登录成功 ===');
                console.log('当前用户:', this.currentUser?.get('username'));
                console.log('用户部门:', this.currentUser?.get('department'));
                
                this.showUserInterface();
                this.loadTasks();
            }

            onUserLoggedOut() {
                this.showAccessDenied();
            }

            showUserInterface() {
                // 调用父类方法更新头部显示
                super.showUserInterface();
                
                // 更新页面内容显示
                this.elements.accessDenied.style.display = 'none';
                this.elements.listSection.style.display = 'block';
            }

            showAccessDenied() {
                // 调用父类方法更新头部显示
                super.showLoginPrompt();
                
                // 更新页面内容显示
                this.elements.accessDenied.style.display = 'block';
                this.elements.listSection.style.display = 'none';
            }

            async loadTasks() {
                if (!this.currentUser) return;

                try {
                    console.log('=== 开始加载任务数据 ===');
                    
                    const Task = AV.Object.extend('Task');
                    const userDept = this.currentUser.get('department');
                    
                    // 构建查询条件：部门任务 + 分配给自己的任务 + 自己创建的任务
                    const deptQuery = new AV.Query(Task);
                    deptQuery.equalTo('creatorDept', userDept);
                    
                    const assignedQuery = new AV.Query(Task);
                    assignedQuery.equalTo('assignee', this.currentUser);
                    
                    const createdQuery = new AV.Query(Task);
                    createdQuery.equalTo('creator', this.currentUser);
                    
                    const mainQuery = AV.Query.or(deptQuery, assignedQuery, createdQuery);
                    mainQuery.descending('createdAt');
                    mainQuery.limit(100);
                    
                    this.tasks = await mainQuery.find();
                    console.log('查询成功！找到', this.tasks.length, '条任务');
                    
                    this.updateStatistics();
                    this.filterTasks();
                    
                } catch (error) {
                    console.error('=== 加载任务失败 ===');
                    console.error('错误详情:', error);
                    
                    if (error.message.includes('Class or object doesn\'t exists')) {
                        alert('任务数据表不存在，请联系管理员初始化数据库');
                    } else {
                        alert('加载任务失败: ' + error.message);
                    }
                }
            }

            updateStatistics() {
                const stats = {
                    total: this.tasks.length,
                    pending: 0,
                    'in-progress': 0,
                    completed: 0
                };

                this.tasks.forEach(task => {
                    const status = task.get('status');
                    if (stats.hasOwnProperty(status)) {
                        stats[status]++;
                    }
                });

                this.elements.totalCount.textContent = stats.total;
                this.elements.pendingCount.textContent = stats.pending;
                this.elements.inProgressCount.textContent = stats['in-progress'];
                this.elements.completedCount.textContent = stats.completed;
            }

            filterTasks() {
                let filtered = [...this.tasks];

                // 状态筛选
                const statusFilter = this.elements.statusFilter.value;
                if (statusFilter) {
                    filtered = filtered.filter(task => task.get('status') === statusFilter);
                }

                // 优先级筛选
                const priorityFilter = this.elements.priorityFilter.value;
                if (priorityFilter) {
                    filtered = filtered.filter(task => task.get('priority') === priorityFilter);
                }

                // 执行人筛选
                const assigneeFilter = this.elements.assigneeFilter.value;
                if (assigneeFilter === 'me') {
                    filtered = filtered.filter(task => {
                        const assignee = task.get('assignee');
                        return assignee && assignee.id === this.currentUser.id;
                    });
                } else if (assigneeFilter === 'created') {
                    filtered = filtered.filter(task => {
                        const creator = task.get('creator');
                        return creator && creator.id === this.currentUser.id;
                    });
                }

                // 搜索筛选
                const searchText = this.elements.searchInput.value.toLowerCase().trim();
                if (searchText) {
                    filtered = filtered.filter(task => {
                        const title = task.get('title').toLowerCase();
                        const description = task.get('description').toLowerCase();
                        return title.includes(searchText) || description.includes(searchText);
                    });
                }

                this.filteredTasks = filtered;
                this.renderTasks();
            }

            renderTasks() {
                if (this.filteredTasks.length === 0) {
                    this.elements.tasksList.innerHTML = `
                        <div class="p-8 text-center text-gray-500">
                            暂无符合条件的任务
                            <div class="mt-4">
                                <a href="task-create.html" class="text-blue-600 hover:text-blue-800">发布新任务</a>
                            </div>
                        </div>
                    `;
                    return;
                }

                const tasksHtml = this.filteredTasks.map(task => {
                    const status = task.get('status');
                    const priority = task.get('priority');
                    const dueDate = task.get('dueDate');
                    const progress = task.get('progress') || 0;
                    
                    // 检查是否逾期
                    const isOverdue = dueDate && new Date(dueDate) < new Date() && status !== 'completed';
                    const displayStatus = isOverdue ? 'overdue' : status;
                    
                    return `
                        <div class="p-6 hover:bg-gray-50">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3 mb-2">
                                        <h4 class="font-medium text-gray-900 cursor-pointer hover:text-blue-600" 
                                            onclick="window.location.href='task-detail.html?id=${task.id}'">${task.get('title')}</h4>
                                        <span class="px-2 py-1 rounded-full text-xs font-medium status-${displayStatus}">
                                            ${this.getStatusText(displayStatus)}
                                        </span>
                                        <span class="px-2 py-1 rounded-full text-xs font-medium priority-${priority}">
                                            ${priority}
                                        </span>
                                    </div>
                                    <div class="text-sm text-gray-600 space-y-1">
                                        <div>执行人: ${task.get('assigneeName')} | 创建人: ${task.get('creatorName')}</div>
                                        <div>截止时间: ${dueDate ? new Date(dueDate).toLocaleString() : '-'}</div>
                                        ${task.get('baseTaskId') ? `<div class="text-xs text-blue-600">任务组: ${task.get('baseTaskId')}</div>` : ''}
                                        <div class="flex items-center space-x-2">
                                            <span>进度: ${progress}%</span>
                                            <div class="w-24 bg-gray-200 rounded-full h-2">
                                                <div class="bg-blue-500 h-2 rounded-full" style="width: ${progress}%"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex space-x-2">
                                    ${this.getActionButtons(task)}
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');

                this.elements.tasksList.innerHTML = tasksHtml;
            }

            getActionButtons(task) {
                const status = task.get('status');
                const assignee = task.get('assignee');
                const creator = task.get('creator');
                const currentUserId = this.currentUser.id;
                
                let buttons = [];

                // 查看详情按钮
                buttons.push(`<button onclick="window.location.href='task-detail.html?id=${task.id}'" class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-xs">详情</button>`);

                // 如果是分配给自己的任务且未完成，显示更新进度按钮
                if (assignee && assignee.id === currentUserId && status !== 'completed') {
                    buttons.push(`<button onclick="taskListApp.showProgressModal('${task.id}')" class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-xs">更新进度</button>`);
                }

                // 如果是自己创建的任务且未完成，显示编辑按钮
                if (creator && creator.id === currentUserId && status !== 'completed') {
                    buttons.push(`<button onclick="window.location.href='task-edit.html?id=${task.id}'" class="bg-purple-500 hover:bg-purple-600 text-white px-3 py-1 rounded text-xs">编辑</button>`);
                }

                return buttons.join('');
            }

            getStatusText(status) {
                const statusMap = {
                    'pending': '待开始',
                    'in-progress': '进行中',
                    'completed': '已完成',
                    'overdue': '已逾期',
                    'cancelled': '已取消'
                };
                return statusMap[status] || status;
            }

            showProgressModal(taskId) {
                const task = this.tasks.find(t => t.id === taskId);
                if (!task) return;

                this.currentTask = task;
                const currentProgress = task.get('progress') || 0;
                
                this.elements.progressSlider.value = currentProgress;
                this.elements.progressValue.textContent = currentProgress + '%';
                this.elements.progressNote.value = '';
                
                this.elements.progressModal.classList.remove('hidden');
            }

            closeProgressModal() {
                this.elements.progressModal.classList.add('hidden');
                this.currentTask = null;
            }

            async saveTaskProgress() {
                if (!this.currentTask) return;

                try {
                    const progress = parseInt(this.elements.progressSlider.value);
                    const note = this.elements.progressNote.value.trim();

                    // 更新任务进度
                    this.currentTask.set('progress', progress);
                    
                    // 如果进度达到100%，自动标记为已完成
                    if (progress === 100) {
                        this.currentTask.set('status', 'completed');
                        this.currentTask.set('completeTime', new Date());
                    } else if (this.currentTask.get('status') === 'pending') {
                        // 如果是待开始状态，更新为进行中
                        this.currentTask.set('status', 'in-progress');
                        this.currentTask.set('startTime', new Date());
                    }

                    // 添加进度记录
                    const comments = this.currentTask.get('comments') || [];
                    if (note) {
                        comments.push({
                            type: 'progress',
                            author: this.currentUser.get('realName') || this.currentUser.get('username'),
                            content: `进度更新至${progress}%: ${note}`,
                            createTime: new Date()
                        });
                        this.currentTask.set('comments', comments);
                    }

                    await this.currentTask.save();
                    
                    alert('进度更新成功！');
                    this.closeProgressModal();
                    this.loadTasks();

                } catch (error) {
                    console.error('更新进度失败:', error);
                    alert('更新进度失败: ' + error.message);
                }
            }
        }

        // 全局变量
        let taskListApp;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            function initApp() {
                if (typeof AV !== 'undefined') {
                    taskListApp = new TaskListApp();
                    taskListApp.init();
                } else {
                    setTimeout(initApp, 100);
                }
            }
            initApp();
        });
    </script>
</body>
</html>
