<template>
  <div 
    v-if="show" 
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    @click="handleBackdropClick"
  >
    <div 
      class="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-hidden"
      @click.stop
    >
      <!-- 头部 -->
      <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-900">
          {{ isEditing ? '编辑角色' : '新增角色' }}
        </h3>
        <button 
          @click="handleClose"
          class="text-gray-400 hover:text-gray-600"
        >
          <Icon name="mdi:close" size="24" />
        </button>
      </div>

      <!-- 内容 -->
      <div class="px-6 py-4 max-h-[calc(90vh-140px)] overflow-y-auto">
        <form @submit.prevent="handleSubmit">
          <div class="space-y-6">
            <!-- 基本信息 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  角色代码 *
                </label>
                <input 
                  v-model="formData.code"
                  type="text"
                  required
                  :disabled="loading || isEditing"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
                  placeholder="如：manager"
                />
                <p class="text-xs text-gray-500 mt-1">角色代码创建后不可修改</p>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  角色名称 *
                </label>
                <input 
                  v-model="formData.name"
                  type="text"
                  required
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="如：部门经理"
                />
              </div>
            </div>

            <!-- 角色描述 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                角色描述
              </label>
              <textarea 
                v-model="formData.description"
                :disabled="loading"
                rows="3"
                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                placeholder="请输入角色描述"
              ></textarea>
            </div>

            <!-- 权限配置 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-3">
                权限配置
              </label>
              <div class="space-y-4">
                <div 
                  v-for="(module, moduleKey) in permissionModules" 
                  :key="moduleKey"
                  class="border border-gray-200 rounded-lg p-4"
                >
                  <h4 class="font-medium text-gray-900 mb-3">{{ module.name }}</h4>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <label 
                      v-for="permission in module.permissions" 
                      :key="permission.key"
                      class="flex items-center"
                    >
                      <input 
                        v-model="formData.permissions"
                        :value="permission.key"
                        type="checkbox"
                        :disabled="loading"
                        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span class="ml-2 text-sm text-gray-700">{{ permission.name }}</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <!-- 系统角色标识 -->
            <div v-if="isEditing && editRole?.isSystem" class="flex items-center p-3 bg-blue-50 rounded-md">
              <Icon name="mdi:information" size="16" class="text-blue-600 mr-2" />
              <span class="text-sm text-blue-600">这是系统预设角色，权限修改可能受限</span>
            </div>
          </div>
        </form>
      </div>

      <!-- 底部按钮 -->
      <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
        <button 
          type="button"
          @click="handleClose"
          class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
        >
          取消
        </button>
        <button 
          @click="handleSubmit"
          :disabled="loading || !isFormValid"
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {{ loading ? '保存中...' : (isEditing ? '更新' : '创建') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Role } from '~/types'

interface Props {
  show: boolean
  editRole?: Role | null
}

const props = withDefaults(defineProps<Props>(), {
  editRole: null
})

const emit = defineEmits<{
  'update:show': [value: boolean]
  created: [role: Role]
  updated: [role: Role]
}>()

// Store
const adminStore = useAdminStore()

// 响应式数据
const loading = ref(false)
const isEditing = computed(() => !!props.editRole)

// 表单数据
const formData = reactive({
  code: '',
  name: '',
  description: '',
  permissions: [] as string[]
})

// 权限模块定义
const permissionModules = {
  system: {
    name: '系统管理',
    permissions: [
      { key: 'user_management', name: '用户管理' },
      { key: 'role_management', name: '角色管理' },
      { key: 'department_management', name: '部门管理' },
      { key: 'system_settings', name: '系统设置' },
      { key: 'system_logs', name: '系统日志' },
      { key: 'data_backup', name: '数据备份' }
    ]
  },
  business: {
    name: '业务管理',
    permissions: [
      { key: 'worklog_management', name: '工作日志管理' },
      { key: 'repair_management', name: '报修管理' },
      { key: 'inventory_management', name: '库存管理' },
      { key: 'reports_view', name: '报表查看' },
      { key: 'data_export', name: '数据导出' }
    ]
  },
  operation: {
    name: '操作权限',
    permissions: [
      { key: 'worklog_create', name: '创建工作日志' },
      { key: 'repair_submit', name: '提交报修' },
      { key: 'repair_process', name: '处理报修' },
      { key: 'inventory_view', name: '查看库存' },
      { key: 'inventory_adjust', name: '调整库存' }
    ]
  }
}

// 计算属性
const isFormValid = computed(() => {
  return formData.code.trim().length > 0 && 
         formData.name.trim().length > 0 &&
         formData.permissions.length > 0
})

// 方法
const resetForm = () => {
  formData.code = ''
  formData.name = ''
  formData.description = ''
  formData.permissions = []
}

const loadFormData = () => {
  if (props.editRole) {
    formData.code = props.editRole.code
    formData.name = props.editRole.name
    formData.description = props.editRole.description || ''
    formData.permissions = [...props.editRole.permissions]
  } else {
    resetForm()
  }
}

const handleClose = () => {
  emit('update:show', false)
}

const handleBackdropClick = () => {
  handleClose()
}

const handleSubmit = async () => {
  if (!isFormValid.value) return
  
  loading.value = true
  
  try {
    if (isEditing.value && props.editRole) {
      // 更新角色
      const result = await adminStore.updateRole(props.editRole.id, {
        name: formData.name.trim(),
        description: formData.description.trim(),
        permissions: formData.permissions
      })
      
      if (result.success && result.data) {
        emit('updated', result.data)
        handleClose()
      } else {
        alert(result.error || '更新角色失败')
      }
    } else {
      // 创建角色
      const result = await adminStore.createRole({
        code: formData.code.trim(),
        name: formData.name.trim(),
        description: formData.description.trim(),
        isSystem: false,
        permissions: formData.permissions,
        userCount: 0
      })
      
      if (result.success && result.data) {
        emit('created', result.data)
        handleClose()
      } else {
        alert(result.error || '创建角色失败')
      }
    }
  } catch (error) {
    console.error('提交表单失败:', error)
    alert('操作失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 监听器
watch(() => props.show, (newShow) => {
  if (newShow) {
    loadFormData()
  }
})

// 键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.show) {
    handleClose()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>
