<template>
  <div 
    v-if="show" 
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    @click="handleBackdropClick"
  >
    <div 
      class="bg-white rounded-lg w-full max-w-md"
      @click.stop
    >
      <!-- 头部 -->
      <div class="px-6 py-4">
        <div class="flex items-center">
          <!-- 图标 -->
          <div 
            class="flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center mr-4"
            :class="iconBgClass"
          >
            <Icon 
              :name="iconName" 
              size="20" 
              :class="iconColorClass"
            />
          </div>
          
          <!-- 标题和描述 -->
          <div class="flex-1">
            <h3 class="text-lg font-medium text-gray-900">
              {{ title }}
            </h3>
            <p v-if="description" class="mt-1 text-sm text-gray-600">
              {{ description }}
            </p>
          </div>
        </div>
      </div>

      <!-- 内容 -->
      <div v-if="content" class="px-6 pb-4">
        <p class="text-sm text-gray-700">{{ content }}</p>
      </div>

      <!-- 底部按钮 -->
      <div class="px-6 py-4 bg-gray-50 rounded-b-lg flex justify-end space-x-3">
        <button 
          @click="handleCancel"
          :disabled="loading"
          class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50"
        >
          {{ cancelText }}
        </button>
        <button 
          @click="handleConfirm"
          :disabled="loading"
          class="px-4 py-2 rounded-md text-white transition-colors disabled:opacity-50 flex items-center"
          :class="confirmButtonClass"
        >
          <Icon v-if="loading" name="mdi:loading" size="16" class="mr-1 animate-spin" />
          {{ loading ? loadingText : confirmText }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  show: boolean
  title: string
  description?: string
  content?: string
  type?: 'info' | 'warning' | 'danger' | 'success'
  confirmText?: string
  cancelText?: string
  loadingText?: string
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'info',
  confirmText: '确认',
  cancelText: '取消',
  loadingText: '处理中...',
  loading: false
})

const emit = defineEmits<{
  'update:show': [value: boolean]
  confirm: []
  cancel: []
}>()

// 计算属性
const iconName = computed(() => {
  const iconMap = {
    info: 'mdi:information',
    warning: 'mdi:alert',
    danger: 'mdi:alert-circle',
    success: 'mdi:check-circle'
  }
  return iconMap[props.type]
})

const iconBgClass = computed(() => {
  const bgMap = {
    info: 'bg-blue-100',
    warning: 'bg-yellow-100',
    danger: 'bg-red-100',
    success: 'bg-green-100'
  }
  return bgMap[props.type]
})

const iconColorClass = computed(() => {
  const colorMap = {
    info: 'text-blue-600',
    warning: 'text-yellow-600',
    danger: 'text-red-600',
    success: 'text-green-600'
  }
  return colorMap[props.type]
})

const confirmButtonClass = computed(() => {
  const buttonMap = {
    info: 'bg-blue-600 hover:bg-blue-700',
    warning: 'bg-yellow-600 hover:bg-yellow-700',
    danger: 'bg-red-600 hover:bg-red-700',
    success: 'bg-green-600 hover:bg-green-700'
  }
  return buttonMap[props.type]
})

// 方法
const handleConfirm = () => {
  emit('confirm')
}

const handleCancel = () => {
  emit('cancel')
  emit('update:show', false)
}

const handleBackdropClick = () => {
  if (!props.loading) {
    handleCancel()
  }
}

// 键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (!props.show || props.loading) return
  
  if (event.key === 'Escape') {
    handleCancel()
  } else if (event.key === 'Enter') {
    handleConfirm()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>
