<template>
  <div 
    v-if="show" 
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    @click="handleBackdropClick"
  >
    <div 
      class="bg-white rounded-lg w-full max-w-md"
      @click.stop
    >
      <!-- 头部 -->
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900">库存调整</h3>
          <button 
            @click="handleClose"
            class="text-gray-400 hover:text-gray-600"
          >
            <Icon name="mdi:close" size="24" />
          </button>
        </div>
        
        <!-- 物品信息 -->
        <div v-if="item" class="mt-4 p-3 bg-gray-50 rounded-lg">
          <div class="flex items-center">
            <div class="flex-shrink-0 h-10 w-10">
              <img 
                v-if="item.images && item.images.length > 0"
                :src="item.images[0]" 
                :alt="item.name"
                class="h-10 w-10 rounded object-cover"
              />
              <div v-else class="h-10 w-10 rounded bg-gray-200 flex items-center justify-center">
                <Icon name="mdi:package-variant" size="20" class="text-gray-400" />
              </div>
            </div>
            <div class="ml-3">
              <div class="text-sm font-medium text-gray-900">{{ item.name }}</div>
              <div class="text-sm text-gray-500">
                当前库存: {{ item.quantity }} {{ item.unit }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 内容 -->
      <div class="px-6 py-4">
        <!-- 操作类型选择 -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-3">
            操作类型
          </label>
          <div class="grid grid-cols-3 gap-3">
            <button 
              @click="operationType = 'in'"
              :class="operationType === 'in' ? 'bg-green-100 border-green-500 text-green-700' : 'bg-white border-gray-300 text-gray-700'"
              class="border-2 rounded-lg p-3 text-center hover:bg-gray-50 transition-colors"
            >
              <Icon name="mdi:plus" size="20" class="mx-auto mb-1" />
              <div class="text-xs font-medium">入库</div>
            </button>
            
            <button 
              @click="operationType = 'out'"
              :class="operationType === 'out' ? 'bg-orange-100 border-orange-500 text-orange-700' : 'bg-white border-gray-300 text-gray-700'"
              class="border-2 rounded-lg p-3 text-center hover:bg-gray-50 transition-colors"
            >
              <Icon name="mdi:minus" size="20" class="mx-auto mb-1" />
              <div class="text-xs font-medium">出库</div>
            </button>
            
            <button 
              @click="operationType = 'adjust'"
              :class="operationType === 'adjust' ? 'bg-blue-100 border-blue-500 text-blue-700' : 'bg-white border-gray-300 text-gray-700'"
              class="border-2 rounded-lg p-3 text-center hover:bg-gray-50 transition-colors"
            >
              <Icon name="mdi:tune" size="20" class="mx-auto mb-1" />
              <div class="text-xs font-medium">调整</div>
            </button>
          </div>
        </div>

        <form @submit.prevent="handleSubmit">
          <!-- 数量输入 -->
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              {{ getQuantityLabel() }}
            </label>
            <div class="relative">
              <input 
                v-model.number="quantity"
                type="number"
                :min="operationType === 'adjust' ? 0 : 1"
                :max="operationType === 'out' ? item?.quantity : undefined"
                required
                :disabled="loading"
                class="w-full border border-gray-300 rounded-md px-3 py-2 pr-16 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                :placeholder="getQuantityPlaceholder()"
              />
              <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <span class="text-gray-500 text-sm">{{ item?.unit }}</span>
              </div>
            </div>
            
            <!-- 库存不足警告 -->
            <div v-if="operationType === 'out' && item && quantity > item.quantity" class="mt-1 text-sm text-red-600">
              库存不足，当前库存仅有 {{ item.quantity }} {{ item.unit }}
            </div>
            
            <!-- 调整后数量预览 -->
            <div v-if="operationType !== 'adjust' && item && quantity > 0" class="mt-1 text-sm text-gray-600">
              调整后库存: {{ getAdjustedQuantity() }} {{ item.unit }}
            </div>
          </div>

          <!-- 原因说明 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              原因说明 *
            </label>
            <textarea 
              v-model="reason"
              rows="3"
              required
              :disabled="loading"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
              :placeholder="getReasonPlaceholder()"
            ></textarea>
          </div>

          <!-- 参考单号 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              参考单号
            </label>
            <input 
              v-model="reference"
              type="text"
              :disabled="loading"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="如：采购单号、出库单号等"
            />
          </div>
        </form>
      </div>

      <!-- 底部按钮 -->
      <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
        <button 
          type="button"
          @click="handleClose"
          :disabled="loading"
          class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50"
        >
          取消
        </button>
        <button 
          @click="handleSubmit"
          :disabled="loading || !isFormValid"
          class="px-4 py-2 rounded-md text-white transition-colors disabled:opacity-50 flex items-center"
          :class="getSubmitButtonStyle()"
        >
          <Icon v-if="loading" name="mdi:loading" size="16" class="mr-1 animate-spin" />
          {{ loading ? '处理中...' : getSubmitButtonText() }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { InventoryItem } from '~/types'
import { useSimpleInventoryStore } from '~/stores/inventory-simple'

interface Props {
  show: boolean
  item?: InventoryItem | null
}

const props = withDefaults(defineProps<Props>(), {
  item: null
})

const emit = defineEmits<{
  'update:show': [value: boolean]
  adjusted: []
}>()

const inventoryStore = useSimpleInventoryStore()

// 响应式数据
const loading = ref(false)
const operationType = ref<'in' | 'out' | 'adjust'>('in')
const quantity = ref(0)
const reason = ref('')
const reference = ref('')

// 计算属性
const isFormValid = computed(() => {
  if (!props.item || !reason.value.trim()) return false
  
  if (operationType.value === 'adjust') {
    return quantity.value >= 0
  } else {
    return quantity.value > 0 && 
           (operationType.value !== 'out' || quantity.value <= props.item.quantity)
  }
})

// 方法
const resetForm = () => {
  operationType.value = 'in'
  quantity.value = 0
  reason.value = ''
  reference.value = ''
}

const handleClose = () => {
  if (!loading.value) {
    emit('update:show', false)
    resetForm()
  }
}

const handleBackdropClick = () => {
  handleClose()
}

const getQuantityLabel = () => {
  switch (operationType.value) {
    case 'in': return '入库数量'
    case 'out': return '出库数量'
    case 'adjust': return '调整后数量'
    default: return '数量'
  }
}

const getQuantityPlaceholder = () => {
  switch (operationType.value) {
    case 'in': return '请输入入库数量'
    case 'out': return '请输入出库数量'
    case 'adjust': return '请输入调整后的总数量'
    default: return '请输入数量'
  }
}

const getReasonPlaceholder = () => {
  switch (operationType.value) {
    case 'in': return '如：采购入库、退货入库等'
    case 'out': return '如：领用出库、损耗出库等'
    case 'adjust': return '如：盘点调整、系统纠错等'
    default: return '请输入原因说明'
  }
}

const getAdjustedQuantity = () => {
  if (!props.item) return 0
  
  switch (operationType.value) {
    case 'in': return props.item.quantity + quantity.value
    case 'out': return props.item.quantity - quantity.value
    default: return quantity.value
  }
}

const getSubmitButtonText = () => {
  switch (operationType.value) {
    case 'in': return '确认入库'
    case 'out': return '确认出库'
    case 'adjust': return '确认调整'
    default: return '确认'
  }
}

const getSubmitButtonStyle = () => {
  switch (operationType.value) {
    case 'in': return 'bg-green-600 hover:bg-green-700'
    case 'out': return 'bg-orange-600 hover:bg-orange-700'
    case 'adjust': return 'bg-blue-600 hover:bg-blue-700'
    default: return 'bg-blue-600 hover:bg-blue-700'
  }
}

const handleSubmit = async () => {
  if (!isFormValid.value || loading.value || !props.item) return
  
  loading.value = true
  
  try {
    let result
    
    switch (operationType.value) {
      case 'in':
        result = await inventoryStore.stockIn(
          props.item.id, 
          quantity.value, 
          reason.value.trim(),
          reference.value.trim() || undefined
        )
        break
        
      case 'out':
        result = await inventoryStore.stockOut(
          props.item.id, 
          quantity.value, 
          reason.value.trim(),
          reference.value.trim() || undefined
        )
        break
        
      case 'adjust':
        result = await inventoryStore.adjustStock(
          props.item.id, 
          quantity.value, 
          reason.value.trim()
        )
        break
        
      default:
        throw new Error('未知的操作类型')
    }
    
    if (result.success) {
      emit('adjusted')
      handleClose()
    } else {
      alert(result.error || '操作失败')
    }
  } catch (error) {
    console.error('库存调整失败:', error)
    alert('操作失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 监听props变化
watch(() => props.show, (newValue) => {
  if (newValue) {
    resetForm()
    // 根据物品库存状态设置默认操作类型
    if (props.item) {
      if (props.item.quantity <= props.item.minStock) {
        operationType.value = 'in'
      }
    }
  }
})

// 键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.show) {
    handleClose()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>
