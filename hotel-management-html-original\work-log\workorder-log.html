<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>工单记录</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- LeanCloud SDK -->
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-3">
                    <a href="index.html" class="text-gray-600 hover:text-gray-800">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                    </a>
                    <h1 class="text-xl font-semibold text-gray-800">工单记录</h1>
                </div>
                <div id="userInfo" class="hidden items-center space-x-4">
                    <span id="realName" class="text-gray-800 font-medium"></span>
                    <button id="logoutBtn" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm">
                        退出登录
                    </button>
                </div>
                <button id="loginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm">
                    登录
                </button>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="max-w-7xl mx-auto px-4 py-6">
        <!-- 未登录提示 -->
        <div id="accessDenied" class="bg-white rounded-lg shadow p-8 text-center">
            <h2 class="text-2xl font-bold text-gray-800 mb-4">需要登录</h2>
            <p class="text-gray-600 mb-6">请先登录后查看工单记录</p>
            <button id="promptLoginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg">
                立即登录
            </button>
        </div>

        <!-- 工单记录界面 -->
        <div id="workorderLogSection" class="hidden">
            <!-- 筛选和搜索 -->
            <div class="bg-white rounded-lg shadow p-6 mb-6">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                    <div class="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-4">
                        <select id="actionFilter" class="px-3 py-2 border border-gray-300 rounded-lg">
                            <option value="">全部操作</option>
                            <option value="create">创建工单</option>
                            <option value="complete">完成工单</option>
                        </select>
                        <input type="date" id="dateFilter" class="px-3 py-2 border border-gray-300 rounded-lg">
                        <input type="search" id="searchInput" placeholder="搜索工单号或描述"
                            class="px-3 py-2 border border-gray-300 rounded-lg">
                    </div>
                    <div class="flex space-x-2">
                        <button id="refreshBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                            刷新
                        </button>
                        <button id="exportBtn" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                            导出
                        </button>
                    </div>
                </div>
            </div>

            <!-- 统计信息 -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="text-2xl font-bold text-blue-600" id="totalCount">0</div>
                        <div class="ml-3">
                            <div class="text-sm font-medium text-gray-500">总记录数</div>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="text-2xl font-bold text-orange-600" id="createCount">0</div>
                        <div class="ml-3">
                            <div class="text-sm font-medium text-gray-500">创建工单</div>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="text-2xl font-bold text-green-600" id="completeCount">0</div>
                        <div class="ml-3">
                            <div class="text-sm font-medium text-gray-500">完成工单</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 记录列表 -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-800">工单记录列表</h3>
                </div>
                <div id="recordsList" class="divide-y divide-gray-200">
                    <!-- 记录列表将通过JS动态填充 -->
                </div>
            </div>
        </div>
    </main>

    <!-- 详情弹窗 -->
    <div id="detailModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-screen overflow-y-auto">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-xl font-semibold text-gray-800">工单记录详情</h2>
                    <button id="closeModal" class="text-gray-400 hover:text-gray-600 text-2xl">&times;</button>
                </div>
            </div>
            <div class="p-6">
                <div id="modalContent">
                    <!-- 详情内容将通过JS动态填充 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 登录弹窗 -->
    <div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-800">用户登录</h2>
            </div>
            <div class="p-6">
                <form id="loginForm" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                        <input type="text" id="loginUsername" required
                            class="w-full p-3 border border-gray-300 rounded-lg">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                        <input type="password" id="loginPassword" required
                            class="w-full p-3 border border-gray-300 rounded-lg">
                    </div>
                    <div class="flex gap-3 pt-4">
                        <button type="button" id="cancelLogin" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-3 rounded-lg">
                            取消
                        </button>
                        <button type="submit" class="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg">
                            登录
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="../js/config.js?v=2.1"></script>
    <script src="../js/core/error-handler.js?v=2.1"></script>
    <script src="../js/utils.js?v=2.1"></script>
    <script src="../js/auth.js?v=2.1"></script>
    <script src="../js/base-app.js?v=2.1"></script>
    <script>
        class WorkOrderLogApp extends BaseWorkLogApp {
            constructor() {
                super({
                    pageType: 'workorder-log',
                    requiredElements: ['workorderLogSection']
                });
                
                this.records = [];
                this.filteredRecords = [];
            }

            getPageElements() {
                return {
                    // 头部元素
                    userInfo: 'userInfo',
                    realName: 'realName',
                    loginBtn: 'loginBtn',
                    logoutBtn: 'logoutBtn',
                    // 登录弹窗元素
                    loginModal: 'loginModal',
                    loginForm: 'loginForm',
                    loginUsername: 'loginUsername',
                    loginPassword: 'loginPassword',
                    cancelLogin: 'cancelLogin',
                    // 页面内容元素
                    accessDenied: 'accessDenied',
                    workorderLogSection: 'workorderLogSection',
                    promptLoginBtn: 'promptLoginBtn',
                    // 筛选元素
                    actionFilter: 'actionFilter',
                    dateFilter: 'dateFilter',
                    searchInput: 'searchInput',
                    refreshBtn: 'refreshBtn',
                    exportBtn: 'exportBtn',
                    // 统计元素
                    totalCount: 'totalCount',
                    createCount: 'createCount',
                    completeCount: 'completeCount',
                    // 列表元素
                    recordsList: 'recordsList',
                    // 弹窗元素
                    detailModal: 'detailModal',
                    modalContent: 'modalContent',
                    closeModal: 'closeModal'
                };
            }

            bindPageEvents() {
                // 提示登录
                if (this.elements.promptLoginBtn) {
                    this.elements.promptLoginBtn.addEventListener('click', () => this.showLoginModal());
                }

                // 筛选事件
                ['actionFilter', 'dateFilter'].forEach(filterId => {
                    if (this.elements[filterId]) {
                        this.elements[filterId].addEventListener('change', () => this.filterRecords());
                    }
                });

                // 搜索事件
                if (this.elements.searchInput) {
                    this.elements.searchInput.addEventListener('input', () => this.filterRecords());
                }

                // 按钮事件
                if (this.elements.refreshBtn) {
                    this.elements.refreshBtn.addEventListener('click', () => this.loadRecords());
                }
                if (this.elements.exportBtn) {
                    this.elements.exportBtn.addEventListener('click', () => this.exportRecords());
                }

                // 弹窗关闭
                if (this.elements.closeModal) {
                    this.elements.closeModal.addEventListener('click', () => this.closeDetailModal());
                }
            }

            onUserLoggedIn() {
                console.log('=== 工单记录页面：用户登录成功 ===');
                console.log('当前用户:', this.currentUser?.get('username'));
                
                this.showUserInterface();
                this.loadRecords();
            }

            onUserLoggedOut() {
                this.showAccessDenied();
            }

            showUserInterface() {
                // 调用父类方法更新头部显示
                super.showUserInterface();
                
                // 更新页面内容显示
                this.elements.accessDenied.style.display = 'none';
                this.elements.workorderLogSection.style.display = 'block';
            }

            showAccessDenied() {
                // 调用父类方法更新头部显示
                super.showLoginPrompt();
                
                // 更新页面内容显示
                this.elements.accessDenied.style.display = 'block';
                this.elements.workorderLogSection.style.display = 'none';
            }

            async loadRecords() {
                if (!this.currentUser) return;

                try {
                    console.log('=== 开始加载工单记录 ===');
                    
                    const WorkLog = AV.Object.extend('WorkLog');
                    const query = new AV.Query(WorkLog);
                    
                    // 只查询工单相关的记录
                    query.equalTo('pageType', 'workorder');
                    query.equalTo('user', this.currentUser);
                    query.descending('createdAt');
                    query.limit(100);
                    
                    this.records = await query.find();
                    console.log('查询成功！找到', this.records.length, '条工单记录');
                    
                    this.updateStatistics();
                    this.filterRecords();
                    
                } catch (error) {
                    console.error('=== 加载工单记录失败 ===');
                    console.error('错误详情:', error);
                    alert('加载工单记录失败: ' + error.message);
                }
            }

            updateStatistics() {
                const stats = {
                    total: this.records.length,
                    create: 0,
                    complete: 0
                };

                this.records.forEach(record => {
                    const operationData = record.get('operationData');
                    if (operationData && operationData.action) {
                        if (operationData.action === 'create') {
                            stats.create++;
                        } else if (operationData.action === 'complete') {
                            stats.complete++;
                        }
                    }
                });

                this.elements.totalCount.textContent = stats.total;
                this.elements.createCount.textContent = stats.create;
                this.elements.completeCount.textContent = stats.complete;
            }

            filterRecords() {
                let filtered = [...this.records];

                // 操作类型筛选
                const actionFilter = this.elements.actionFilter.value;
                if (actionFilter) {
                    filtered = filtered.filter(record => {
                        const operationData = record.get('operationData');
                        return operationData && operationData.action === actionFilter;
                    });
                }

                // 日期筛选
                const dateFilter = this.elements.dateFilter.value;
                if (dateFilter) {
                    const filterDate = new Date(dateFilter);
                    const nextDay = new Date(filterDate);
                    nextDay.setDate(nextDay.getDate() + 1);
                    
                    filtered = filtered.filter(record => {
                        const recordDate = record.get('createdAt');
                        return recordDate >= filterDate && recordDate < nextDay;
                    });
                }

                // 搜索筛选
                const searchText = this.elements.searchInput.value.toLowerCase().trim();
                if (searchText) {
                    filtered = filtered.filter(record => {
                        const content = record.get('content').toLowerCase();
                        const operationData = record.get('operationData');
                        const orderNumber = operationData ? (operationData.orderNumber || '').toLowerCase() : '';
                        return content.includes(searchText) || orderNumber.includes(searchText);
                    });
                }

                this.filteredRecords = filtered;
                this.renderRecords();
            }

            renderRecords() {
                if (this.filteredRecords.length === 0) {
                    this.elements.recordsList.innerHTML = `
                        <div class="p-8 text-center text-gray-500">
                            暂无符合条件的工单记录
                        </div>
                    `;
                    return;
                }

                const recordsHtml = this.filteredRecords.map(record => {
                    const operationData = record.get('operationData');
                    const action = operationData ? operationData.action : 'unknown';
                    const orderNumber = operationData ? operationData.orderNumber : '未知';
                    const createTime = record.get('createdAt');
                    
                    const actionText = action === 'create' ? '创建工单' : action === 'complete' ? '完成工单' : '未知操作';
                    const actionColor = action === 'create' ? 'bg-blue-100 text-blue-800' : action === 'complete' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800';
                    
                    return `
                        <div class="p-6 hover:bg-gray-50 cursor-pointer" onclick="workOrderLogApp.showDetail('${record.id}')">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3 mb-2">
                                        <span class="px-2 py-1 rounded-full text-xs font-medium ${actionColor}">
                                            ${actionText}
                                        </span>
                                        <h4 class="font-medium text-gray-900">工单号: ${orderNumber}</h4>
                                    </div>
                                    <div class="text-sm text-gray-600">
                                        <div>时间: ${createTime.toLocaleString()}</div>
                                        <div class="mt-1 line-clamp-2">${record.get('content')}</div>
                                    </div>
                                </div>
                                <div>
                                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');

                this.elements.recordsList.innerHTML = recordsHtml;
            }

            showDetail(recordId) {
                const record = this.records.find(r => r.id === recordId);
                if (!record) return;

                const operationData = record.get('operationData');
                const action = operationData ? operationData.action : 'unknown';
                const actionText = action === 'create' ? '创建工单' : action === 'complete' ? '完成工单' : '未知操作';
                
                this.elements.modalContent.innerHTML = `
                    <div class="space-y-4">
                        <div>
                            <h3 class="font-semibold text-gray-800 mb-2">操作类型</h3>
                            <p class="text-gray-600">${actionText}</p>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-800 mb-2">操作时间</h3>
                            <p class="text-gray-600">${record.get('createdAt').toLocaleString()}</p>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-800 mb-2">详细内容</h3>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <pre class="whitespace-pre-wrap text-sm text-gray-700">${record.get('content')}</pre>
                            </div>
                        </div>
                        ${operationData ? `
                        <div>
                            <h3 class="font-semibold text-gray-800 mb-2">操作数据</h3>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <pre class="whitespace-pre-wrap text-sm text-gray-700">${JSON.stringify(operationData, null, 2)}</pre>
                            </div>
                        </div>
                        ` : ''}
                    </div>
                `;
                
                this.elements.detailModal.classList.remove('hidden');
            }

            closeDetailModal() {
                this.elements.detailModal.classList.add('hidden');
            }

            exportRecords() {
                if (this.filteredRecords.length === 0) {
                    alert('没有可导出的记录');
                    return;
                }

                let exportText = '工单记录导出\n';
                exportText += '导出时间: ' + new Date().toLocaleString() + '\n';
                exportText += '记录数量: ' + this.filteredRecords.length + '\n\n';
                exportText += '=' * 50 + '\n\n';

                this.filteredRecords.forEach((record, index) => {
                    const operationData = record.get('operationData');
                    const action = operationData ? operationData.action : 'unknown';
                    const actionText = action === 'create' ? '创建工单' : action === 'complete' ? '完成工单' : '未知操作';
                    
                    exportText += `${index + 1}. ${actionText}\n`;
                    exportText += `时间: ${record.get('createdAt').toLocaleString()}\n`;
                    exportText += `内容:\n${record.get('content')}\n`;
                    exportText += '-' * 30 + '\n\n';
                });

                // 复制到剪贴板
                navigator.clipboard.writeText(exportText).then(() => {
                    alert('工单记录已复制到剪贴板，可以粘贴到其他应用中');
                }).catch(() => {
                    // 如果复制失败，显示在新窗口
                    const newWindow = window.open('', '_blank');
                    newWindow.document.write('<pre>' + exportText + '</pre>');
                });
            }
        }

        // 全局变量
        let workOrderLogApp;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            function initApp() {
                if (typeof AV !== 'undefined') {
                    workOrderLogApp = new WorkOrderLogApp();
                    workOrderLogApp.init();
                } else {
                    setTimeout(initApp, 100);
                }
            }
            initApp();
        });
    </script>
</body>
</html>
