<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理系统数据初始化</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <style>
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .btn-fix {
            touch-action: manipulation;
        }
        .init-step {
            transition: all 0.3s ease;
        }
        .init-step.completed {
            background-color: #f0fdf4;
            border-color: #22c55e;
        }
        .init-step.error {
            background-color: #fef2f2;
            border-color: #ef4444;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="index.html" class="text-blue-600 hover:text-blue-800 mr-4">
                        ← 返回管理首页
                    </a>
                    <h1 class="text-xl font-semibold text-gray-900">管理系统数据初始化</h1>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 说明 -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
            <h2 class="text-lg font-medium text-blue-900 mb-2">数据初始化说明</h2>
            <p class="text-blue-800 mb-4">
                此页面用于初始化管理系统所需的基础数据表和示例数据。如果您是首次使用管理系统，建议按顺序执行以下初始化步骤。
            </p>
            <div class="text-sm text-blue-700">
                <p>• 系统设置：创建系统配置表和默认设置</p>
                <p>• 角色权限：创建角色表和默认角色权限</p>
                <p>• 操作日志：创建日志表和示例日志数据</p>
                <p>• 备份记录：创建备份表和示例备份记录</p>
            </div>
        </div>

        <!-- 初始化步骤 -->
        <div class="space-y-6">
            <!-- 系统设置初始化 -->
            <div id="systemSettingsStep" class="init-step bg-white border border-gray-200 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center mr-3">
                            <span class="text-white font-semibold">1</span>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">系统设置初始化</h3>
                            <p class="text-sm text-gray-500">创建系统配置表和默认设置</p>
                        </div>
                    </div>
                    <button id="initSystemSettings" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                        初始化
                    </button>
                </div>
                <div id="systemSettingsStatus" class="text-sm text-gray-600">
                    等待初始化...
                </div>
            </div>

            <!-- 角色权限初始化 -->
            <div id="rolesStep" class="init-step bg-white border border-gray-200 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3">
                            <span class="text-white font-semibold">2</span>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">角色权限初始化</h3>
                            <p class="text-sm text-gray-500">创建角色表和默认角色权限</p>
                        </div>
                    </div>
                    <button id="initRoles" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                        初始化
                    </button>
                </div>
                <div id="rolesStatus" class="text-sm text-gray-600">
                    等待初始化...
                </div>
            </div>

            <!-- 操作日志初始化 -->
            <div id="logsStep" class="init-step bg-white border border-gray-200 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center mr-3">
                            <span class="text-white font-semibold">3</span>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">操作日志初始化</h3>
                            <p class="text-sm text-gray-500">创建日志表和示例日志数据</p>
                        </div>
                    </div>
                    <button id="initLogs" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                        初始化
                    </button>
                </div>
                <div id="logsStatus" class="text-sm text-gray-600">
                    等待初始化...
                </div>
            </div>

            <!-- 备份记录初始化 -->
            <div id="backupStep" class="init-step bg-white border border-gray-200 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center mr-3">
                            <span class="text-white font-semibold">4</span>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">备份记录初始化</h3>
                            <p class="text-sm text-gray-500">创建备份表和示例备份记录</p>
                        </div>
                    </div>
                    <button id="initBackup" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                        初始化
                    </button>
                </div>
                <div id="backupStatus" class="text-sm text-gray-600">
                    等待初始化...
                </div>
            </div>
        </div>

        <!-- 批量操作 -->
        <div class="mt-8 bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">批量操作</h3>
            <div class="flex space-x-4">
                <button id="initAllBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg text-sm btn-fix">
                    一键初始化全部
                </button>
                <button id="checkStatusBtn" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg text-sm btn-fix">
                    检查状态
                </button>
                <button id="resetAllBtn" class="bg-red-500 hover:bg-red-600 text-white px-6 py-2 rounded-lg text-sm btn-fix">
                    重置全部数据
                </button>
            </div>
        </div>

        <!-- 初始化日志 -->
        <div class="mt-8 bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">初始化日志</h3>
            </div>
            <div class="p-6">
                <div id="initLog" class="bg-gray-50 rounded-lg p-4 h-64 overflow-y-auto text-sm font-mono">
                    <!-- 初始化日志将在这里显示 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 加载指示器 -->
    <div id="loadingIndicator" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-40" style="display: none;">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <span class="text-gray-700">初始化中...</span>
        </div>
    </div>

    <!-- 引入模块化JS文件 -->
    <script src="../js/config.js"></script>
    <script>
        // 管理系统数据初始化应用类
        class AdminDataInitApp {
            constructor() {
                this.logContainer = null;
            }

            init() {
                this.logContainer = document.getElementById('initLog');
                this.bindEvents();
                this.checkInitStatus();
            }

            bindEvents() {
                // 单独初始化按钮
                document.getElementById('initSystemSettings').addEventListener('click', () => {
                    this.initSystemSettings();
                });

                document.getElementById('initRoles').addEventListener('click', () => {
                    this.initRoles();
                });

                document.getElementById('initLogs').addEventListener('click', () => {
                    this.initLogs();
                });

                document.getElementById('initBackup').addEventListener('click', () => {
                    this.initBackup();
                });

                // 批量操作按钮
                document.getElementById('initAllBtn').addEventListener('click', () => {
                    this.initAll();
                });

                document.getElementById('checkStatusBtn').addEventListener('click', () => {
                    this.checkInitStatus();
                });

                document.getElementById('resetAllBtn').addEventListener('click', () => {
                    this.resetAll();
                });
            }

            log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const typeColor = {
                    'info': 'text-blue-600',
                    'success': 'text-green-600',
                    'error': 'text-red-600',
                    'warning': 'text-yellow-600'
                };
                
                const logEntry = document.createElement('div');
                logEntry.className = typeColor[type] || 'text-gray-600';
                logEntry.textContent = `[${timestamp}] ${message}`;
                
                this.logContainer.appendChild(logEntry);
                this.logContainer.scrollTop = this.logContainer.scrollHeight;
            }

            updateStepStatus(stepId, status, message) {
                const step = document.getElementById(stepId);
                const statusDiv = document.getElementById(stepId.replace('Step', 'Status'));
                
                step.classList.remove('completed', 'error');
                
                if (status === 'completed') {
                    step.classList.add('completed');
                    statusDiv.textContent = '✅ ' + message;
                } else if (status === 'error') {
                    step.classList.add('error');
                    statusDiv.textContent = '❌ ' + message;
                } else {
                    statusDiv.textContent = message;
                }
            }

            async checkInitStatus() {
                this.log('开始检查初始化状态...');

                // 检查系统设置
                try {
                    const settingsQuery = new AV.Query('SystemSettings');
                    const settingsCount = await settingsQuery.count();
                    if (settingsCount > 0) {
                        this.updateStepStatus('systemSettingsStep', 'completed', '已初始化');
                    } else {
                        this.updateStepStatus('systemSettingsStep', '', '未初始化');
                    }
                } catch (error) {
                    this.updateStepStatus('systemSettingsStep', '', '未初始化');
                }

                // 检查角色权限
                try {
                    const rolesQuery = new AV.Query('UserRole');
                    const rolesCount = await rolesQuery.count();
                    if (rolesCount > 0) {
                        this.updateStepStatus('rolesStep', 'completed', '已初始化');
                    } else {
                        this.updateStepStatus('rolesStep', '', '未初始化');
                    }
                } catch (error) {
                    this.updateStepStatus('rolesStep', '', '未初始化');
                }

                // 检查操作日志
                try {
                    const logsQuery = new AV.Query('SystemLog');
                    const logsCount = await logsQuery.count();
                    if (logsCount > 0) {
                        this.updateStepStatus('logsStep', 'completed', '已初始化');
                    } else {
                        this.updateStepStatus('logsStep', '', '未初始化');
                    }
                } catch (error) {
                    this.updateStepStatus('logsStep', '', '未初始化');
                }

                // 检查备份记录
                try {
                    const backupQuery = new AV.Query('BackupRecord');
                    const backupCount = await backupQuery.count();
                    if (backupCount > 0) {
                        this.updateStepStatus('backupStep', 'completed', '已初始化');
                    } else {
                        this.updateStepStatus('backupStep', '', '未初始化');
                    }
                } catch (error) {
                    this.updateStepStatus('backupStep', '', '未初始化');
                }

                this.log('状态检查完成', 'success');
            }

            async initSystemSettings() {
                try {
                    this.log('开始初始化系统设置...');
                    
                    const settings = new AV.Object('SystemSettings');
                    settings.set('systemName', '酒店管理系统');
                    settings.set('systemVersion', 'v1.0.0');
                    settings.set('companyName', '示例酒店');
                    settings.set('contactEmail', '<EMAIL>');
                    settings.set('systemDescription', '现代化酒店管理系统，提供全面的酒店运营管理功能');
                    settings.set('sessionTimeout', 30);
                    settings.set('passwordMinLength', 6);
                    settings.set('maxLoginAttempts', 5);
                    settings.set('lockoutDuration', 15);
                    settings.set('requireStrongPassword', true);
                    settings.set('enableTwoFactor', false);
                    settings.set('logSecurityEvents', true);
                    settings.set('defaultPageSize', 20);
                    settings.set('dateFormat', 'YYYY-MM-DD');
                    settings.set('timeFormat', '24');
                    settings.set('currency', '¥');
                    settings.set('enableNotifications', true);
                    settings.set('enableAutoBackup', true);
                    settings.set('enableDebugMode', false);
                    
                    await settings.save();
                    
                    this.updateStepStatus('systemSettingsStep', 'completed', '初始化成功');
                    this.log('系统设置初始化完成', 'success');
                } catch (error) {
                    this.updateStepStatus('systemSettingsStep', 'error', '初始化失败: ' + error.message);
                    this.log('系统设置初始化失败: ' + error.message, 'error');
                }
            }

            async initRoles() {
                try {
                    this.log('开始初始化角色权限...');
                    
                    const defaultRoles = [
                        {
                            name: '超级管理员',
                            code: 'super_admin',
                            description: '拥有系统所有权限的超级管理员',
                            status: 'active',
                            permissions: this.getAllPermissions()
                        },
                        {
                            name: '管理员',
                            code: 'admin',
                            description: '系统管理员，拥有大部分管理权限',
                            status: 'active',
                            permissions: this.getAdminPermissions()
                        },
                        {
                            name: '普通用户',
                            code: 'user',
                            description: '普通用户，拥有基本操作权限',
                            status: 'active',
                            permissions: this.getUserPermissions()
                        }
                    ];

                    for (const roleData of defaultRoles) {
                        const role = new AV.Object('UserRole');
                        Object.keys(roleData).forEach(key => {
                            role.set(key, roleData[key]);
                        });
                        await role.save();
                    }
                    
                    this.updateStepStatus('rolesStep', 'completed', '初始化成功');
                    this.log('角色权限初始化完成', 'success');
                } catch (error) {
                    this.updateStepStatus('rolesStep', 'error', '初始化失败: ' + error.message);
                    this.log('角色权限初始化失败: ' + error.message, 'error');
                }
            }

            async initLogs() {
                try {
                    this.log('开始初始化操作日志...');
                    
                    const sampleLogs = [
                        {
                            level: 'info',
                            module: 'user',
                            action: '用户登录',
                            message: '用户成功登录系统',
                            userId: 'admin',
                            userName: '管理员',
                            ipAddress: '*************',
                            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                            details: { loginTime: new Date().toISOString() }
                        },
                        {
                            level: 'success',
                            module: 'inventory',
                            action: '创建入库单',
                            message: '成功创建入库单 IN20241206001',
                            userId: 'user1',
                            userName: '张三',
                            ipAddress: '*************',
                            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                            details: { orderNo: 'IN20241206001', amount: 1500.00 }
                        },
                        {
                            level: 'warning',
                            module: 'inventory',
                            action: '库存预警',
                            message: '商品"办公用纸"库存不足，当前库存: 5',
                            userId: 'system',
                            userName: '系统',
                            ipAddress: '127.0.0.1',
                            userAgent: 'System',
                            details: { productName: '办公用纸', currentStock: 5, minStock: 10 }
                        },
                        {
                            level: 'error',
                            module: 'system',
                            action: '登录失败',
                            message: '用户登录失败，密码错误',
                            userId: 'unknown',
                            userName: '未知用户',
                            ipAddress: '*************',
                            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                            details: { attemptCount: 3, reason: 'invalid_password' }
                        }
                    ];

                    for (const logData of sampleLogs) {
                        const log = new AV.Object('SystemLog');
                        Object.keys(logData).forEach(key => {
                            log.set(key, logData[key]);
                        });
                        await log.save();
                    }
                    
                    this.updateStepStatus('logsStep', 'completed', '初始化成功');
                    this.log('操作日志初始化完成', 'success');
                } catch (error) {
                    this.updateStepStatus('logsStep', 'error', '初始化失败: ' + error.message);
                    this.log('操作日志初始化失败: ' + error.message, 'error');
                }
            }

            async initBackup() {
                try {
                    this.log('开始初始化备份记录...');
                    
                    const sampleBackups = [
                        {
                            name: 'backup_20241206_001',
                            type: 'full',
                            size: 15.6,
                            status: 'completed',
                            description: '完整系统备份',
                            compressed: true,
                            encrypted: false
                        },
                        {
                            name: 'backup_20241205_001',
                            type: 'incremental',
                            size: 3.2,
                            status: 'completed',
                            description: '增量备份',
                            compressed: true,
                            encrypted: false
                        }
                    ];

                    for (const backupData of sampleBackups) {
                        const backup = new AV.Object('BackupRecord');
                        Object.keys(backupData).forEach(key => {
                            backup.set(key, backupData[key]);
                        });
                        await backup.save();
                    }

                    // 创建备份设置
                    const backupSettings = new AV.Object('BackupSettings');
                    backupSettings.set('type', 'full');
                    backupSettings.set('frequency', 'daily');
                    backupSettings.set('retentionDays', 30);
                    backupSettings.set('autoBackup', true);
                    backupSettings.set('compression', true);
                    backupSettings.set('encryption', false);
                    await backupSettings.save();
                    
                    this.updateStepStatus('backupStep', 'completed', '初始化成功');
                    this.log('备份记录初始化完成', 'success');
                } catch (error) {
                    this.updateStepStatus('backupStep', 'error', '初始化失败: ' + error.message);
                    this.log('备份记录初始化失败: ' + error.message, 'error');
                }
            }

            async initAll() {
                this.log('开始一键初始化全部数据...', 'info');
                this.showLoading();

                try {
                    await this.initSystemSettings();
                    await this.initRoles();
                    await this.initLogs();
                    await this.initBackup();
                    
                    this.log('全部数据初始化完成！', 'success');
                    alert('全部数据初始化完成！');
                } catch (error) {
                    this.log('初始化过程中出现错误: ' + error.message, 'error');
                    alert('初始化过程中出现错误: ' + error.message);
                } finally {
                    this.hideLoading();
                }
            }

            async resetAll() {
                if (!confirm('确定要重置全部数据吗？此操作将删除所有管理系统数据，不可恢复！')) {
                    return;
                }

                this.log('开始重置全部数据...', 'warning');
                this.showLoading();

                try {
                    // 删除各个表的数据
                    const tables = ['SystemSettings', 'UserRole', 'SystemLog', 'BackupRecord', 'BackupSettings'];
                    
                    for (const tableName of tables) {
                        try {
                            const query = new AV.Query(tableName);
                            const objects = await query.find();
                            if (objects.length > 0) {
                                await AV.Object.destroyAll(objects);
                                this.log(`已清空 ${tableName} 表`, 'info');
                            }
                        } catch (error) {
                            this.log(`清空 ${tableName} 表失败: ${error.message}`, 'warning');
                        }
                    }
                    
                    // 重置步骤状态
                    ['systemSettingsStep', 'rolesStep', 'logsStep', 'backupStep'].forEach(stepId => {
                        this.updateStepStatus(stepId, '', '未初始化');
                    });
                    
                    this.log('全部数据重置完成', 'success');
                    alert('全部数据重置完成！');
                } catch (error) {
                    this.log('重置过程中出现错误: ' + error.message, 'error');
                    alert('重置过程中出现错误: ' + error.message);
                } finally {
                    this.hideLoading();
                }
            }

            getAllPermissions() {
                return {
                    'system': {
                        'user_management': ['view', 'create', 'edit', 'delete'],
                        'role_management': ['view', 'create', 'edit', 'delete'],
                        'system_settings': ['view', 'edit'],
                        'logs': ['view', 'export']
                    },
                    'inventory': {
                        'products': ['view', 'create', 'edit', 'delete'],
                        'warehouses': ['view', 'create', 'edit', 'delete'],
                        'inbound': ['view', 'create', 'edit', 'delete'],
                        'outbound': ['view', 'create', 'edit', 'delete'],
                        'transfer': ['view', 'create', 'edit', 'delete'],
                        'count': ['view', 'create', 'edit', 'delete']
                    },
                    'work': {
                        'logs': ['view', 'create', 'edit', 'delete'],
                        'reports': ['view', 'export']
                    },
                    'repair': {
                        'orders': ['view', 'create', 'edit', 'delete'],
                        'reports': ['view', 'export']
                    }
                };
            }

            getAdminPermissions() {
                const adminPermissions = this.getAllPermissions();
                // 管理员不能删除用户
                adminPermissions.system.user_management = ['view', 'create', 'edit'];
                return adminPermissions;
            }

            getUserPermissions() {
                return {
                    'work': {
                        'logs': ['view', 'create', 'edit']
                    },
                    'repair': {
                        'orders': ['view', 'create']
                    },
                    'inventory': {
                        'products': ['view'],
                        'inbound': ['view'],
                        'outbound': ['view']
                    }
                };
            }

            showLoading() {
                document.getElementById('loadingIndicator').style.display = 'flex';
            }

            hideLoading() {
                document.getElementById('loadingIndicator').style.display = 'none';
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            function initApp() {
                if (typeof AV !== 'undefined') {
                    const initApp = new AdminDataInitApp();
                    initApp.init();
                } else {
                    setTimeout(initApp, 100);
                }
            }
            initApp();
        });
    </script>
</body>
</html>
