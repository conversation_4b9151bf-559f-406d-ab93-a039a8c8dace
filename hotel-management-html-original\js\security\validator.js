/**
 * 统一安全验证模块
 * 提供输入验证、XSS防护、数据清理等安全功能
 */

class SecurityValidator {
    /**
     * HTML内容清理，防止XSS攻击
     * @param {string} input - 用户输入
     * @returns {string} 清理后的安全内容
     */
    static sanitizeHTML(input) {
        if (typeof input !== 'string') {
            return '';
        }
        
        // 创建临时DOM元素进行清理
        const div = document.createElement('div');
        div.textContent = input;
        return div.innerHTML;
    }

    /**
     * 清理并验证文本输入
     * @param {string} input - 输入文本
     * @param {Object} options - 验证选项
     * @returns {Object} {isValid: boolean, value: string, error?: string}
     */
    static validateText(input, options = {}) {
        const {
            required = false,
            minLength = 0,
            maxLength = 1000,
            pattern = null,
            allowHTML = false
        } = options;

        // 基础检查
        if (!input || typeof input !== 'string') {
            if (required) {
                return { isValid: false, value: '', error: '此字段为必填项' };
            }
            return { isValid: true, value: '' };
        }

        // 长度检查
        if (input.length < minLength) {
            return { 
                isValid: false, 
                value: input, 
                error: `最少需要${minLength}个字符` 
            };
        }

        if (input.length > maxLength) {
            return { 
                isValid: false, 
                value: input, 
                error: `最多允许${maxLength}个字符` 
            };
        }

        // 模式匹配
        if (pattern && !pattern.test(input)) {
            return { 
                isValid: false, 
                value: input, 
                error: '输入格式不正确' 
            };
        }

        // HTML清理
        const cleanValue = allowHTML ? input : this.sanitizeHTML(input);

        return { isValid: true, value: cleanValue };
    }

    /**
     * 验证数字输入
     * @param {any} input - 输入值
     * @param {Object} options - 验证选项
     * @returns {Object} {isValid: boolean, value: number, error?: string}
     */
    static validateNumber(input, options = {}) {
        const {
            required = false,
            min = -Infinity,
            max = Infinity,
            integer = false,
            positive = false
        } = options;

        // 空值检查
        if (input === null || input === undefined || input === '') {
            if (required) {
                return { isValid: false, value: 0, error: '此字段为必填项' };
            }
            return { isValid: true, value: 0 };
        }

        // 数字转换
        const num = Number(input);
        if (isNaN(num)) {
            return { isValid: false, value: 0, error: '请输入有效数字' };
        }

        // 整数检查
        if (integer && !Number.isInteger(num)) {
            return { isValid: false, value: num, error: '请输入整数' };
        }

        // 正数检查
        if (positive && num <= 0) {
            return { isValid: false, value: num, error: '请输入正数' };
        }

        // 范围检查
        if (num < min) {
            return { 
                isValid: false, 
                value: num, 
                error: `数值不能小于${min}` 
            };
        }

        if (num > max) {
            return { 
                isValid: false, 
                value: num, 
                error: `数值不能大于${max}` 
            };
        }

        return { isValid: true, value: num };
    }

    /**
     * 验证手机号
     * @param {string} phone - 手机号
     * @param {boolean} required - 是否必填
     * @returns {Object} 验证结果
     */
    static validatePhone(phone, required = false) {
        const phonePattern = /^1[3-9]\d{9}$/;
        
        if (!phone) {
            if (required) {
                return { isValid: false, value: '', error: '请输入手机号' };
            }
            return { isValid: true, value: '' };
        }

        if (!phonePattern.test(phone)) {
            return { 
                isValid: false, 
                value: phone, 
                error: '请输入正确的手机号格式' 
            };
        }

        return { isValid: true, value: phone };
    }

    /**
     * 验证邮箱
     * @param {string} email - 邮箱地址
     * @param {boolean} required - 是否必填
     * @returns {Object} 验证结果
     */
    static validateEmail(email, required = false) {
        const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (!email) {
            if (required) {
                return { isValid: false, value: '', error: '请输入邮箱地址' };
            }
            return { isValid: true, value: '' };
        }

        if (!emailPattern.test(email)) {
            return { 
                isValid: false, 
                value: email, 
                error: '请输入正确的邮箱格式' 
            };
        }

        return { isValid: true, value: email };
    }

    /**
     * 验证日期
     * @param {any} date - 日期值
     * @param {Object} options - 验证选项
     * @returns {Object} 验证结果
     */
    static validateDate(date, options = {}) {
        const {
            required = false,
            minDate = null,
            maxDate = null
        } = options;

        if (!date) {
            if (required) {
                return { isValid: false, value: null, error: '请选择日期' };
            }
            return { isValid: true, value: null };
        }

        const dateObj = new Date(date);
        if (isNaN(dateObj.getTime())) {
            return { 
                isValid: false, 
                value: null, 
                error: '请输入有效日期' 
            };
        }

        // 最小日期检查
        if (minDate && dateObj < new Date(minDate)) {
            return { 
                isValid: false, 
                value: dateObj, 
                error: `日期不能早于${new Date(minDate).toLocaleDateString()}` 
            };
        }

        // 最大日期检查
        if (maxDate && dateObj > new Date(maxDate)) {
            return { 
                isValid: false, 
                value: dateObj, 
                error: `日期不能晚于${new Date(maxDate).toLocaleDateString()}` 
            };
        }

        return { isValid: true, value: dateObj };
    }

    /**
     * 批量验证表单数据
     * @param {Object} formData - 表单数据
     * @param {Object} rules - 验证规则
     * @returns {Object} {isValid: boolean, errors: Object, values: Object}
     */
    static validateForm(formData, rules) {
        const errors = {};
        const values = {};
        let isValid = true;

        for (const [field, rule] of Object.entries(rules)) {
            const value = formData[field];
            let result;

            switch (rule.type) {
                case 'text':
                    result = this.validateText(value, rule);
                    break;
                case 'number':
                    result = this.validateNumber(value, rule);
                    break;
                case 'phone':
                    result = this.validatePhone(value, rule.required);
                    break;
                case 'email':
                    result = this.validateEmail(value, rule.required);
                    break;
                case 'date':
                    result = this.validateDate(value, rule);
                    break;
                default:
                    result = { isValid: true, value: value };
            }

            if (!result.isValid) {
                errors[field] = result.error;
                isValid = false;
            }

            values[field] = result.value;
        }

        return { isValid, errors, values };
    }

    /**
     * SQL注入防护（虽然使用LeanCloud，但保留作为最佳实践）
     * @param {string} input - 输入字符串
     * @returns {string} 清理后的字符串
     */
    static sanitizeSQL(input) {
        if (typeof input !== 'string') {
            return '';
        }
        
        // 移除常见的SQL注入字符
        return input.replace(/['";\\]/g, '');
    }

    /**
     * 文件上传安全检查
     * @param {File} file - 文件对象
     * @param {Object} options - 检查选项
     * @returns {Object} 检查结果
     */
    static validateFile(file, options = {}) {
        const {
            maxSize = 5 * 1024 * 1024, // 5MB
            allowedTypes = ['image/jpeg', 'image/png', 'image/gif'],
            allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif']
        } = options;

        if (!file) {
            return { isValid: false, error: '请选择文件' };
        }

        // 文件大小检查
        if (file.size > maxSize) {
            return { 
                isValid: false, 
                error: `文件大小不能超过${Math.round(maxSize / 1024 / 1024)}MB` 
            };
        }

        // 文件类型检查
        if (!allowedTypes.includes(file.type)) {
            return { 
                isValid: false, 
                error: `不支持的文件类型，仅支持：${allowedTypes.join(', ')}` 
            };
        }

        // 文件扩展名检查
        const fileName = file.name.toLowerCase();
        const hasValidExtension = allowedExtensions.some(ext => 
            fileName.endsWith(ext.toLowerCase())
        );

        if (!hasValidExtension) {
            return { 
                isValid: false, 
                error: `不支持的文件扩展名，仅支持：${allowedExtensions.join(', ')}` 
            };
        }

        return { isValid: true };
    }
}

// 导出到全局
if (typeof window !== 'undefined') {
    window.SecurityValidator = SecurityValidator;
}

// Node.js环境支持
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SecurityValidator;
}
