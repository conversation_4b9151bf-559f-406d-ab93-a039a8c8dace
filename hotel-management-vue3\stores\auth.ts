// stores/auth.ts
import { defineStore } from 'pinia'
import type { User } from '~/types'

interface AuthState {
  user: User | null
  isLoggedIn: boolean
  loading: boolean
}

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    user: null,
    isLoggedIn: false,
    loading: false
  }),

  getters: {
    isAdmin: (state) => {
      return state.user?.roles?.includes('admin') || false
    },
    
    userDisplayName: (state) => {
      return state.user?.realName || state.user?.username || '未知用户'
    },

    hasPermission: (state) => {
      return (permission: string) => {
        if (!state.user) return false
        if (state.user.roles.includes('admin')) return true
        return state.user.roles.includes(permission)
      }
    }
  },

  actions: {
    // 登录
    async login(username: string, password: string) {
      this.loading = true
      
      try {
        const { auth } = useLeanCloud()
        const result = await auth.login(username, password)
        
        if (result.success && result.data) {
          this.user = result.data
          this.isLoggedIn = true

          // 保存登录状态和用户信息到localStorage
          if (process.client) {
            localStorage.setItem('isLoggedIn', 'true')
            localStorage.setItem('currentUser', JSON.stringify(result.data))
          }

          return { success: true }
        } else {
          return { success: false, error: result.error }
        }
      } catch (error) {
        return { success: false, error: '登录失败' }
      } finally {
        this.loading = false
      }
    },

    // 注册
    async register(userData: {
      username: string
      password: string
      realName?: string
      phone?: string
      department?: string
    }) {
      this.loading = true
      
      try {
        const { auth } = useLeanCloud()
        const result = await auth.register(userData)
        
        if (result.success) {
          // 注册成功后自动登录
          return await this.login(userData.username, userData.password)
        } else {
          return { success: false, error: result.error }
        }
      } catch (error) {
        return { success: false, error: '注册失败' }
      } finally {
        this.loading = false
      }
    },

    // 登出
    async logout() {
      try {
        const { auth } = useLeanCloud()
        await auth.logout()
        
        this.user = null
        this.isLoggedIn = false

        // 清除本地存储
        if (process.client) {
          localStorage.removeItem('isLoggedIn')
          localStorage.removeItem('currentUser')
        }
        
        return { success: true }
      } catch (error) {
        return { success: false, error: '登出失败' }
      }
    },

    // 检查登录状态
    async checkAuth() {
      try {
        // 首先检查本地存储的登录状态
        if (process.client) {
          const savedLoginState = localStorage.getItem('isLoggedIn')
          const savedUser = localStorage.getItem('currentUser')

          if (savedLoginState === 'true' && savedUser) {
            try {
              const userData = JSON.parse(savedUser)
              this.user = userData
              this.isLoggedIn = true

              // 验证LeanCloud会话是否仍然有效
              const { auth } = useLeanCloud()
              const currentUser = auth.getCurrentUser()

              if (currentUser) {
                // 更新用户信息（可能有变化）
                this.user = currentUser
                // 更新本地存储
                localStorage.setItem('currentUser', JSON.stringify(currentUser))
                return true
              } else {
                // LeanCloud会话已失效，清除本地状态
                this.clearLocalAuth()
                return false
              }
            } catch (error) {
              console.error('解析本地用户数据失败:', error)
              this.clearLocalAuth()
            }
          }
        }

        // 如果本地没有保存状态，检查LeanCloud会话
        const { auth } = useLeanCloud()
        const currentUser = auth.getCurrentUser()

        if (currentUser) {
          this.user = currentUser
          this.isLoggedIn = true

          // 保存到本地存储
          if (process.client) {
            localStorage.setItem('isLoggedIn', 'true')
            localStorage.setItem('currentUser', JSON.stringify(currentUser))
          }

          return true
        } else {
          this.user = null
          this.isLoggedIn = false
          return false
        }
      } catch (error) {
        console.error('检查登录状态失败:', error)
        this.clearLocalAuth()
        return false
      }
    },

    // 清除本地认证信息
    clearLocalAuth() {
      this.user = null
      this.isLoggedIn = false

      if (process.client) {
        localStorage.removeItem('isLoggedIn')
        localStorage.removeItem('currentUser')
      }
    },

    // 更新用户信息
    async updateProfile(profileData: {
      realName?: string
      phone?: string
      department?: string
    }) {
      if (!this.user) return { success: false, error: '用户未登录' }
      
      try {
        const { auth } = useLeanCloud()
        const currentUser = auth.getCurrentUser()
        
        if (currentUser) {
          // 这里需要实现更新用户信息的逻辑
          // 更新本地状态
          this.user = {
            ...this.user,
            ...profileData
          }
          
          return { success: true }
        }
        
        return { success: false, error: '用户不存在' }
      } catch (error) {
        return { success: false, error: '更新失败' }
      }
    }
  }
})
