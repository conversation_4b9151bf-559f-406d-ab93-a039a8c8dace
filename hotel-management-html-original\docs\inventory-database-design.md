# 库存模块数据库设计

## 数据表结构设计

### 1. 供应商表 (Suppliers)

```javascript
// LeanCloud 类名: Supplier
{
  objectId: String,           // 系统自动生成
  name: String,              // 供应商名称 (必填)
  contact: String,           // 联系人
  phone: String,             // 联系电话
  email: String,             // 邮箱
  address: String,           // 地址
  status: String,            // 状态: active(活跃), inactive(停用)
  createdAt: Date,           // 创建时间
  updatedAt: Date,           // 更新时间
  createdBy: Pointer,        // 创建人 (指向 _User)
  updatedBy: Pointer         // 更新人 (指向 _User)
}
```

### 2. 商品分类表 (Categories)

```javascript
// LeanCloud 类名: Category
{
  objectId: String,           // 系统自动生成
  name: String,              // 分类名称 (必填)
  code: String,              // 分类编码
  parentId: Pointer,         // 上级分类 (指向 Category，可为空表示一级分类)
  level: Number,             // 分类层级 (1=一级, 2=二级, ...)
  description: String,       // 描述
  createdAt: Date,           // 创建时间
  updatedAt: Date,           // 更新时间
  createdBy: Pointer,        // 创建人 (指向 _User)
  updatedBy: Pointer         // 更新人 (指向 _User)
}
```

### 3. 仓库表 (Warehouses)

```javascript
// LeanCloud 类名: Warehouse
{
  objectId: String,           // 系统自动生成
  name: String,              // 仓库名称 (必填)
  code: String,              // 仓库编码
  type: String,              // 仓库类型: main(主仓库), sub(分仓库), temp(临时仓库)
  manager: String,           // 负责人
  phone: String,             // 联系电话
  address: String,           // 地址
  status: String,            // 状态: active(启用), inactive(停用)
  createdAt: Date,           // 创建时间
  updatedAt: Date,           // 更新时间
  createdBy: Pointer,        // 创建人 (指向 _User)
  updatedBy: Pointer         // 更新人 (指向 _User)
}
```

### 4. 商品表 (Products)

```javascript
// LeanCloud 类名: Product
{
  objectId: String,           // 系统自动生成
  name: String,              // 商品名称 (必填)
  code: String,              // 商品编码 (必填，唯一)
  categoryId: Pointer,       // 商品分类 (指向 Category)
  supplierId: Pointer,       // 主供应商 (指向 Supplier)
  unit: String,              // 计量单位 (个、箱、公斤等)
  specification: String,     // 规格型号
  description: String,       // 商品描述
  minStock: Number,          // 最低库存预警
  maxStock: Number,          // 最高库存预警
  status: String,            // 状态: active(启用), inactive(停用)
  createdAt: Date,           // 创建时间
  updatedAt: Date,           // 更新时间
  createdBy: Pointer,        // 创建人 (指向 _User)
  updatedBy: Pointer         // 更新人 (指向 _User)
}
```

### 5. 库存表 (Inventory)

```javascript
// LeanCloud 类名: Inventory
{
  objectId: String,           // 系统自动生成
  productId: Pointer,        // 商品 (指向 Product)
  warehouseId: Pointer,      // 仓库 (指向 Warehouse)
  quantity: Number,          // 当前库存数量
  reservedQuantity: Number,  // 预留数量 (已分配但未出库)
  availableQuantity: Number, // 可用数量 (quantity - reservedQuantity)
  lastInDate: Date,          // 最后入库时间
  lastOutDate: Date,         // 最后出库时间
  createdAt: Date,           // 创建时间
  updatedAt: Date,           // 更新时间
}
```

### 6. 入库单表 (InboundOrders)

```javascript
// LeanCloud 类名: InboundOrder
{
  objectId: String,           // 系统自动生成
  orderNo: String,           // 入库单号 (必填，唯一)
  supplierId: Pointer,       // 供应商 (指向 Supplier)
  warehouseId: Pointer,      // 入库仓库 (指向 Warehouse)
  type: String,              // 入库类型: purchase(采购入库), return(退货入库), transfer(调拨入库), other(其他入库)
  status: String,            // 状态: draft(草稿), confirmed(已确认), completed(已完成), cancelled(已取消)
  totalAmount: Number,       // 总金额
  remark: String,            // 备注
  createdAt: Date,           // 创建时间
  updatedAt: Date,           // 更新时间
  createdBy: Pointer,        // 创建人 (指向 _User)
  confirmedBy: Pointer,      // 确认人 (指向 _User)
  confirmedAt: Date          // 确认时间
}
```

### 7. 入库单明细表 (InboundOrderItems)

```javascript
// LeanCloud 类名: InboundOrderItem
{
  objectId: String,           // 系统自动生成
  orderId: Pointer,          // 入库单 (指向 InboundOrder)
  productId: Pointer,        // 商品 (指向 Product)
  quantity: Number,          // 入库数量
  unitPrice: Number,         // 单价
  amount: Number,            // 金额 (quantity * unitPrice)
  batchNo: String,           // 批次号
  expiryDate: Date,          // 有效期
  remark: String,            // 备注
  createdAt: Date,           // 创建时间
  updatedAt: Date            // 更新时间
}
```

### 8. 出库单表 (OutboundOrders)

```javascript
// LeanCloud 类名: OutboundOrder
{
  objectId: String,           // 系统自动生成
  orderNo: String,           // 出库单号 (必填，唯一)
  warehouseId: Pointer,      // 出库仓库 (指向 Warehouse)
  type: String,              // 出库类型: sale(销售出库), use(领用出库), transfer(调拨出库), other(其他出库)
  department: String,        // 领用部门 (领用出库时使用)
  recipient: String,         // 领用人/收货人
  status: String,            // 状态: draft(草稿), confirmed(已确认), completed(已完成), cancelled(已取消)
  totalAmount: Number,       // 总金额
  remark: String,            // 备注
  createdAt: Date,           // 创建时间
  updatedAt: Date,           // 更新时间
  createdBy: Pointer,        // 创建人 (指向 _User)
  confirmedBy: Pointer,      // 确认人 (指向 _User)
  confirmedAt: Date          // 确认时间
}
```

### 9. 出库单明细表 (OutboundOrderItems)

```javascript
// LeanCloud 类名: OutboundOrderItem
{
  objectId: String,           // 系统自动生成
  orderId: Pointer,          // 出库单 (指向 OutboundOrder)
  productId: Pointer,        // 商品 (指向 Product)
  quantity: Number,          // 出库数量
  unitPrice: Number,         // 单价
  amount: Number,            // 金额 (quantity * unitPrice)
  batchNo: String,           // 批次号
  remark: String,            // 备注
  createdAt: Date,           // 创建时间
  updatedAt: Date            // 更新时间
}
```

### 10. 调拨单表 (TransferOrders)

```javascript
// LeanCloud 类名: TransferOrder
{
  objectId: String,           // 系统自动生成
  orderNo: String,           // 调拨单号 (必填，唯一)
  fromWarehouseId: Pointer,  // 调出仓库 (指向 Warehouse)
  toWarehouseId: Pointer,    // 调入仓库 (指向 Warehouse)
  status: String,            // 状态: draft(草稿), confirmed(已确认), completed(已完成), cancelled(已取消)
  remark: String,            // 备注
  createdAt: Date,           // 创建时间
  updatedAt: Date,           // 更新时间
  createdBy: Pointer,        // 创建人 (指向 _User)
  confirmedBy: Pointer,      // 确认人 (指向 _User)
  confirmedAt: Date          // 确认时间
}
```

### 11. 调拨单明细表 (TransferOrderItems)

```javascript
// LeanCloud 类名: TransferOrderItem
{
  objectId: String,           // 系统自动生成
  orderId: Pointer,          // 调拨单 (指向 TransferOrder)
  productId: Pointer,        // 商品 (指向 Product)
  quantity: Number,          // 调拨数量
  remark: String,            // 备注
  createdAt: Date,           // 创建时间
  updatedAt: Date            // 更新时间
}
```

### 12. 盘点单表 (StockCheckOrders)

```javascript
// LeanCloud 类名: StockCheckOrder
{
  objectId: String,           // 系统自动生成
  orderNo: String,           // 盘点单号 (必填，唯一)
  warehouseId: Pointer,      // 盘点仓库 (指向 Warehouse)
  type: String,              // 盘点类型: full(全盘), partial(部分盘点)
  status: String,            // 状态: draft(草稿), checking(盘点中), completed(已完成), cancelled(已取消)
  checkDate: Date,           // 盘点日期
  remark: String,            // 备注
  createdAt: Date,           // 创建时间
  updatedAt: Date,           // 更新时间
  createdBy: Pointer,        // 创建人 (指向 _User)
  completedBy: Pointer,      // 完成人 (指向 _User)
  completedAt: Date          // 完成时间
}
```

### 13. 盘点单明细表 (StockCheckOrderItems)

```javascript
// LeanCloud 类名: StockCheckOrderItem
{
  objectId: String,           // 系统自动生成
  orderId: Pointer,          // 盘点单 (指向 StockCheckOrder)
  productId: Pointer,        // 商品 (指向 Product)
  systemQuantity: Number,    // 系统库存数量
  actualQuantity: Number,    // 实际盘点数量
  differenceQuantity: Number, // 差异数量 (actualQuantity - systemQuantity)
  remark: String,            // 备注
  createdAt: Date,           // 创建时间
  updatedAt: Date            // 更新时间
}
```

## 索引设计

### 重要索引
1. Product.code (唯一索引)
2. InboundOrder.orderNo (唯一索引)
3. OutboundOrder.orderNo (唯一索引)
4. TransferOrder.orderNo (唯一索引)
5. StockCheckOrder.orderNo (唯一索引)
6. Inventory.productId + warehouseId (复合索引)

## 权限设计

### ACL 权限控制
- 普通用户：只能查看和操作自己创建的单据
- 仓库管理员：可以管理指定仓库的所有单据
- 系统管理员：可以管理所有数据

### 角色权限
- inventory_user: 库存用户 (基本操作权限)
- inventory_manager: 库存管理员 (管理权限)
- inventory_admin: 库存系统管理员 (完全权限)
