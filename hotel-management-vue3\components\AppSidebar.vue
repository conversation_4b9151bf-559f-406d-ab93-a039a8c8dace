<template>
  <div class="flex flex-col h-full bg-white shadow-lg">
    <!-- Logo区域 -->
    <div class="flex items-center px-6 py-4 border-b border-gray-200">
      <Icon name="mdi:hotel" size="32" class="text-blue-600 mr-3" />
      <div>
        <h1 class="text-lg font-semibold text-gray-900">酒店管理</h1>
        <p class="text-xs text-gray-500">Management</p>
      </div>
      
      <!-- 移动端关闭按钮 -->
      <button 
        @click="$emit('close')"
        class="ml-auto lg:hidden text-gray-400 hover:text-gray-600"
      >
        <Icon name="mdi:close" size="24" />
      </button>
    </div>

    <!-- 导航菜单 -->
    <nav class="flex-1 px-4 py-6 space-y-2">
      <!-- 主要功能 -->
      <div class="mb-6">
        <h3 class="px-2 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
          主要功能
        </h3>
        
        <NuxtLink 
          to="/" 
          class="nav-item"
          :class="{ 'nav-item-active': $route.path === '/' }"
        >
          <Icon name="mdi:home" size="20" />
          <span>首页</span>
        </NuxtLink>
        
        <NuxtLink 
          to="/work-log" 
          class="nav-item"
          :class="{ 'nav-item-active': $route.path.startsWith('/work-log') }"
        >
          <Icon name="mdi:notebook" size="20" />
          <span>工作日志</span>
        </NuxtLink>
        
        <NuxtLink 
          to="/inventory" 
          class="nav-item"
          :class="{ 'nav-item-active': $route.path.startsWith('/inventory') }"
        >
          <Icon name="mdi:package-variant" size="20" />
          <span>库存管理</span>
        </NuxtLink>
        
        <NuxtLink
          to="/repair"
          class="nav-item"
          :class="{ 'nav-item-active': $route.path.startsWith('/repair') }"
        >
          <Icon name="mdi:wrench" size="20" />
          <span>报修系统</span>
        </NuxtLink>

        <NuxtLink
          to="/notifications"
          class="nav-item"
          :class="{ 'nav-item-active': $route.path === '/notifications' }"
        >
          <Icon name="mdi:bell" size="20" />
          <span>通知中心</span>
          <span
            v-if="unreadCount > 0"
            class="ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center"
          >
            {{ unreadCount > 99 ? '99+' : unreadCount }}
          </span>
        </NuxtLink>
      </div>

      <!-- 工作日志子菜单 -->
      <div class="mb-6">
        <h3 class="px-2 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
          日志类型
        </h3>
        
        <NuxtLink 
          to="/work-log?type=main" 
          class="nav-item nav-item-sub"
          :class="{ 'nav-item-active': $route.query.type === 'main' }"
        >
          <Icon name="mdi:notebook-outline" size="18" />
          <span>工作日志</span>
        </NuxtLink>
        
        <NuxtLink 
          to="/work-log?type=powerstation" 
          class="nav-item nav-item-sub"
          :class="{ 'nav-item-active': $route.query.type === 'powerstation' }"
        >
          <Icon name="mdi:flash" size="18" />
          <span>变电站</span>
        </NuxtLink>
        
        <NuxtLink 
          to="/work-log?type=waterfilter" 
          class="nav-item nav-item-sub"
          :class="{ 'nav-item-active': $route.query.type === 'waterfilter' }"
        >
          <Icon name="mdi:water" size="18" />
          <span>净水器</span>
        </NuxtLink>
        
        <NuxtLink 
          to="/work-log?type=aircondition" 
          class="nav-item nav-item-sub"
          :class="{ 'nav-item-active': $route.query.type === 'aircondition' }"
        >
          <Icon name="mdi:air-conditioner" size="18" />
          <span>空调系统</span>
        </NuxtLink>
        
        <NuxtLink 
          to="/work-log?type=construction" 
          class="nav-item nav-item-sub"
          :class="{ 'nav-item-active': $route.query.type === 'construction' }"
        >
          <Icon name="mdi:hammer-wrench" size="18" />
          <span>施工登记</span>
        </NuxtLink>
      </div>

      <!-- 管理功能 -->
      <div v-if="authStore.isAdmin" class="mb-6">
        <h3 class="px-2 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
          管理功能
        </h3>
        
        <NuxtLink 
          to="/admin" 
          class="nav-item"
          :class="{ 'nav-item-active': $route.path.startsWith('/admin') }"
        >
          <Icon name="mdi:cog" size="20" />
          <span>系统管理</span>
        </NuxtLink>
        
        <NuxtLink 
          to="/admin/users" 
          class="nav-item nav-item-sub"
          :class="{ 'nav-item-active': $route.path === '/admin/users' }"
        >
          <Icon name="mdi:account-group" size="18" />
          <span>用户管理</span>
        </NuxtLink>
        
        <NuxtLink 
          to="/admin/reports" 
          class="nav-item nav-item-sub"
          :class="{ 'nav-item-active': $route.path === '/admin/reports' }"
        >
          <Icon name="mdi:chart-line" size="18" />
          <span>统计报表</span>
        </NuxtLink>
      </div>
    </nav>

    <!-- 底部用户信息 -->
    <div v-if="authStore.isLoggedIn" class="border-t border-gray-200 p-4">
      <div class="flex items-center">
        <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-medium">
          {{ authStore.userDisplayName.charAt(0) }}
        </div>
        <div class="ml-3 flex-1 min-w-0">
          <p class="text-sm font-medium text-gray-900 truncate">
            {{ authStore.userDisplayName }}
          </p>
          <p class="text-xs text-gray-500 truncate">
            {{ authStore.user?.department || '未设置部门' }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useGlobalNotifications } from '~/composables/useNotifications'

defineEmits<{
  close: []
}>()

const authStore = useAuthStore()
const { unreadCount } = useGlobalNotifications()
</script>

<style scoped>
.nav-item {
  @apply flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-100 hover:text-gray-900 transition-colors;
}

.nav-item-active {
  @apply bg-blue-50 text-blue-700 border-r-2 border-blue-600;
}

.nav-item-sub {
  @apply ml-4 text-xs;
}

.nav-item svg {
  @apply mr-3 flex-shrink-0;
}
</style>
