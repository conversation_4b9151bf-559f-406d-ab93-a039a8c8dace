// stores/repair.ts
import { defineStore } from 'pinia'
import { useGlobalNotifications } from '~/composables/useNotifications'
import type { Repair, RepairComment, RepairCategory, User, PaginationOptions, FilterOptions } from '~/types'

interface RepairState {
  repairs: Repair[]
  comments: RepairComment[]
  categories: RepairCategory[]
  loading: boolean
  currentPage: number
  totalPages: number
  filters: FilterOptions & {
    status?: string
    priority?: string
    category?: string
    assignee?: string
    reporter?: string
  }
}

export const useRepairStore = defineStore('repair', {
  state: (): RepairState => ({
    repairs: [],
    comments: [],
    categories: [],
    loading: false,
    currentPage: 0,
    totalPages: 0,
    filters: {}
  }),

  getters: {
    // 按状态分组的报修单
    repairsByStatus: (state) => {
      const grouped: Record<string, Repair[]> = {}
      
      state.repairs.forEach(repair => {
        if (!grouped[repair.status]) {
          grouped[repair.status] = []
        }
        grouped[repair.status].push(repair)
      })
      
      return grouped
    },

    // 按优先级分组的报修单
    repairsByPriority: (state) => {
      const grouped: Record<string, Repair[]> = {}
      
      state.repairs.forEach(repair => {
        if (!grouped[repair.priority]) {
          grouped[repair.priority] = []
        }
        grouped[repair.priority].push(repair)
      })
      
      return grouped
    },

    // 报修统计
    repairStats: (state) => {
      const total = state.repairs.length
      const pending = state.repairs.filter(r => r.status === 'pending').length
      const inProgress = state.repairs.filter(r => r.status === 'in_progress').length
      const completed = state.repairs.filter(r => r.status === 'completed').length
      const urgent = state.repairs.filter(r => r.priority === 'urgent').length
      
      // 计算平均完成时间
      const completedRepairs = state.repairs.filter(r => r.status === 'completed' && r.actualTime)
      const avgCompletionTime = completedRepairs.length > 0 
        ? completedRepairs.reduce((sum, r) => sum + (r.actualTime || 0), 0) / completedRepairs.length
        : 0
      
      return {
        total,
        pending,
        inProgress,
        completed,
        urgent,
        avgCompletionTime: Math.round(avgCompletionTime * 10) / 10
      }
    },

    // 我的报修单（当前用户提交的）
    myRepairs: (state) => {
      const authStore = useAuthStore()
      if (!authStore.user) return []
      
      return state.repairs.filter(repair => repair.reporter?.id === authStore.user?.id)
    },

    // 分配给我的报修单
    assignedToMe: (state) => {
      const authStore = useAuthStore()
      if (!authStore.user) return []
      
      return state.repairs.filter(repair => repair.assignee?.id === authStore.user?.id)
    },

    // 筛选后的报修单
    filteredRepairs: (state) => {
      let filtered = [...state.repairs]
      
      // 按状态筛选
      if (state.filters.status) {
        filtered = filtered.filter(repair => repair.status === state.filters.status)
      }
      
      // 按优先级筛选
      if (state.filters.priority) {
        filtered = filtered.filter(repair => repair.priority === state.filters.priority)
      }
      
      // 按分类筛选
      if (state.filters.category) {
        filtered = filtered.filter(repair => repair.category === state.filters.category)
      }
      
      // 按处理人筛选
      if (state.filters.assignee) {
        filtered = filtered.filter(repair => repair.assignee?.id === state.filters.assignee)
      }
      
      // 按报修人筛选
      if (state.filters.reporter) {
        filtered = filtered.filter(repair => repair.reporter?.id === state.filters.reporter)
      }
      
      // 按日期范围筛选
      if (state.filters.startDate) {
        filtered = filtered.filter(repair => new Date(repair.createdAt) >= state.filters.startDate!)
      }
      
      if (state.filters.endDate) {
        filtered = filtered.filter(repair => new Date(repair.createdAt) <= state.filters.endDate!)
      }
      
      return filtered
    }
  },

  actions: {
    // 获取报修单列表
    async fetchRepairs(options: PaginationOptions & FilterOptions = {}) {
      this.loading = true
      
      try {
        // 这里应该调用LeanCloud API
        // 暂时使用模拟数据
        const mockRepairs: Repair[] = [
          {
            id: '1',
            title: '客房空调不制冷',
            description: '201房间空调开启后不制冷，客人投诉室内温度过高',
            location: '201房间',
            category: '空调设备',
            priority: 'high',
            status: 'pending',
            reporter: {
              id: 'user1',
              username: 'frontdesk',
              realName: '前台小李',
              department: '前厅部',
              roles: ['user'],
              createdAt: new Date()
            },
            images: [],
            createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2小时前
            updatedAt: new Date()
          },
          {
            id: '2',
            title: '电梯按钮失灵',
            description: '3号电梯的5楼按钮按下后没有反应，需要检修',
            location: '3号电梯',
            category: '电梯设备',
            priority: 'urgent',
            status: 'in_progress',
            reporter: {
              id: 'user2',
              username: 'security',
              realName: '保安老王',
              department: '保安部',
              roles: ['user'],
              createdAt: new Date()
            },
            assignee: {
              id: 'engineer1',
              username: 'engineer1',
              realName: '工程师张三',
              department: '工程部',
              roles: ['engineer'],
              createdAt: new Date()
            },
            estimatedTime: 2,
            assignedAt: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1小时前
            startedAt: new Date(Date.now() - 30 * 60 * 1000), // 30分钟前
            createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000), // 3小时前
            updatedAt: new Date()
          }
        ]
        
        if (options.page === 0) {
          this.repairs = mockRepairs
        } else {
          this.repairs.push(...mockRepairs)
        }
        
        this.currentPage = options.page || 0
        
        return { success: true, data: mockRepairs }
      } catch (error) {
        console.error('获取报修单失败:', error)
        return { success: false, error: '获取报修单失败' }
      } finally {
        this.loading = false
      }
    },

    // 创建报修单
    async createRepair(repairData: Omit<Repair, 'id' | 'createdAt' | 'updatedAt' | 'reporter'>) {
      try {
        const authStore = useAuthStore()
        
        const newRepair: Repair = {
          ...repairData,
          id: Date.now().toString(),
          reporter: authStore.user,
          status: 'pending',
          createdAt: new Date(),
          updatedAt: new Date()
        }
        
        this.repairs.unshift(newRepair)
        
        // 发送企业微信通知
        await this.sendRepairNotification(newRepair)

        // 显示成功通知
        const { success } = useGlobalNotifications()
        success(
          '报修工单创建成功',
          `工单 ${newRepair.id} 已提交，我们会尽快处理`
        )

        return { success: true, data: newRepair }
      } catch (error) {
        console.error('创建报修单失败:', error)
        return { success: false, error: '创建报修单失败' }
      }
    },

    // 更新报修单
    async updateRepair(id: string, repairData: Partial<Repair>) {
      try {
        const index = this.repairs.findIndex(repair => repair.id === id)
        if (index > -1) {
          const oldRepair = { ...this.repairs[index] }
          this.repairs[index] = {
            ...this.repairs[index],
            ...repairData,
            updatedAt: new Date()
          }
          
          // 如果状态发生变化，发送通知
          if (oldRepair.status !== this.repairs[index].status) {
            await this.sendStatusChangeNotification(this.repairs[index], oldRepair.status)
          }
        }
        
        return { success: true }
      } catch (error) {
        console.error('更新报修单失败:', error)
        return { success: false, error: '更新报修单失败' }
      }
    },

    // 分配报修单
    async assignRepair(id: string, assigneeId: string) {
      try {
        const repair = this.repairs.find(r => r.id === id)
        if (!repair) {
          return { success: false, error: '报修单不存在' }
        }
        
        // 这里应该从用户列表中获取用户信息
        const assignee: User = {
          id: assigneeId,
          username: 'engineer',
          realName: '工程师',
          department: '工程部',
          roles: ['engineer'],
          createdAt: new Date()
        }
        
        await this.updateRepair(id, {
          assignee,
          status: 'assigned',
          assignedAt: new Date()
        })
        
        return { success: true }
      } catch (error) {
        console.error('分配报修单失败:', error)
        return { success: false, error: '分配报修单失败' }
      }
    },

    // 开始处理报修单
    async startRepair(id: string) {
      try {
        await this.updateRepair(id, {
          status: 'in_progress',
          startedAt: new Date()
        })
        
        return { success: true }
      } catch (error) {
        console.error('开始处理失败:', error)
        return { success: false, error: '开始处理失败' }
      }
    },

    // 完成报修单
    async completeRepair(id: string, completionData: {
      solution: string
      actualTime?: number
      cost?: number
      materials?: string[]
    }) {
      try {
        const repair = this.repairs.find(r => r.id === id)
        if (!repair) {
          return { success: false, error: '报修单不存在' }
        }
        
        await this.updateRepair(id, {
          status: 'completed',
          solution: completionData.solution,
          actualTime: completionData.actualTime,
          cost: completionData.cost,
          materials: completionData.materials,
          completedAt: new Date()
        })
        
        // 发送完成通知
        const updatedRepair = this.repairs.find(r => r.id === id)
        if (updatedRepair) {
          await this.sendCompletionNotification(updatedRepair)
        }
        
        return { success: true }
      } catch (error) {
        console.error('完成报修单失败:', error)
        return { success: false, error: '完成报修单失败' }
      }
    },

    // 发送报修通知
    async sendRepairNotification(repair: Repair) {
      try {
        const { sendRepairNotification } = useWechat()
        
        await sendRepairNotification({
          title: repair.title,
          reporter: repair.reporter?.realName || repair.reporter?.username || '未知用户',
          location: repair.location,
          priority: repair.priority,
          description: repair.description,
          createdAt: repair.createdAt
        })
      } catch (error) {
        console.error('发送报修通知失败:', error)
      }
    },

    // 发送状态变更通知
    async sendStatusChangeNotification(repair: Repair, oldStatus: string) {
      try {
        const { sendTextMessage } = useWechat()
        
        const statusNames = {
          'pending': '待处理',
          'assigned': '已分配',
          'in_progress': '处理中',
          'completed': '已完成',
          'cancelled': '已取消',
          'rejected': '已拒绝'
        }
        
        const message = `报修工单状态更新：
        
工单标题：${repair.title}
位置：${repair.location}
状态：${statusNames[oldStatus as keyof typeof statusNames]} → ${statusNames[repair.status as keyof typeof statusNames]}
${repair.assignee ? `处理人：${repair.assignee.realName || repair.assignee.username}` : ''}

请及时关注工单进展。`
        
        await sendTextMessage(message, repair.reporter?.id)
      } catch (error) {
        console.error('发送状态变更通知失败:', error)
      }
    },

    // 发送完成通知
    async sendCompletionNotification(repair: Repair) {
      try {
        const { sendRepairCompletedNotification } = useWechat()
        
        await sendRepairCompletedNotification({
          title: repair.title,
          assignee: repair.assignee?.realName || repair.assignee?.username || '工程师',
          reporter: repair.reporter?.realName || repair.reporter?.username || '用户',
          location: repair.location,
          completedAt: repair.completedAt!,
          solution: repair.solution
        }, repair.reporter?.id)
      } catch (error) {
        console.error('发送完成通知失败:', error)
      }
    },

    // 设置筛选条件
    setFilters(filters: any) {
      this.filters = { ...this.filters, ...filters }
    },

    // 清除筛选条件
    clearFilters() {
      this.filters = {}
    },

    // 重置状态
    reset() {
      this.repairs = []
      this.comments = []
      this.categories = []
      this.loading = false
      this.currentPage = 0
      this.totalPages = 0
      this.filters = {}
    }
  }
})
