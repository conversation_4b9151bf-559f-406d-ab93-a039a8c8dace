# 库存管理系统

## 系统概述

库存管理系统是一个基于Web的现代化库存管理解决方案，提供完整的库存管理功能，包括商品管理、入库出库、库存转移、盘点统计等核心功能。

## 功能特性

### 🏠 **库存概览**
- 实时库存统计数据
- 今日入库/出库统计
- 库存预警提醒
- 快速操作入口

### 📦 **商品管理**
- 商品信息维护
- 分类管理
- 库存状态监控
- 批量操作支持

### ⬆️ **入库管理**
- 商品入库登记
- 供应商信息记录
- 入库历史查询
- 自动库存更新

### ⬇️ **出库管理**
- 商品出库登记
- 领用人信息记录
- 出库用途分类
- 出库历史查询

### 🔄 **库存转移**
- 仓库间转移
- 转移流程管理
- 转移状态跟踪
- 转移历史记录

### 📊 **库存盘点**
- 定期盘点管理
- 盘点差异分析
- 盘点会话控制
- 盘点报告生成

### 📈 **数据报表**
- 多维度统计分析
- 可视化图表展示
- 数据导出功能
- 自定义报表周期

### ⚙️ **数据初始化**
- 基础数据创建
- 示例数据导入
- 数据清理工具
- 系统初始化向导

## 技术架构

### 前端技术
- **HTML5** - 现代化页面结构
- **Tailwind CSS** - 响应式UI框架
- **JavaScript ES6+** - 现代化交互逻辑
- **Chart.js** - 数据可视化图表

### 后端服务
- **LeanCloud** - 云端数据存储
- **REST API** - 标准化数据接口
- **实时同步** - 多用户协作支持

### 权限系统
- **基于角色的访问控制** (RBAC)
- **细粒度权限管理**
- **安全认证机制**

## 数据模型

### 核心数据表

#### Product (商品)
```javascript
{
  name: "商品名称",
  code: "商品编码", 
  category: "商品分类",
  unit: "计量单位",
  currentStock: 100,
  minStock: 10,
  maxStock: 1000,
  price: 99.99,
  status: "active",
  description: "商品描述"
}
```

#### InboundRecord (入库记录)
```javascript
{
  productName: "商品名称",
  productCode: "商品编码",
  quantity: 50,
  unit: "个",
  warehouse: "主仓库",
  supplier: "供应商名称",
  operator: "操作员",
  inboundTime: "2024-01-15T10:00:00Z"
}
```

#### OutboundRecord (出库记录)
```javascript
{
  productName: "商品名称", 
  productCode: "商品编码",
  quantity: 20,
  unit: "个",
  warehouse: "主仓库",
  recipient: "领用人",
  purpose: "使用用途",
  department: "领用部门",
  operator: "操作员",
  outboundTime: "2024-01-15T14:00:00Z"
}
```

## 快速开始

### 1. 系统初始化
1. 访问 `inventory/index.html` 进入库存管理主页
2. 点击"数据初始化"进行系统初始化
3. 使用"一键初始化"创建基础数据

### 2. 基础配置
1. **商品分类**: 创建商品分类体系
2. **仓库设置**: 配置仓库和存储位置
3. **供应商管理**: 维护供应商信息
4. **商品录入**: 添加商品基础信息

### 3. 日常操作
1. **入库**: 记录商品入库信息
2. **出库**: 记录商品出库信息
3. **转移**: 处理仓库间转移
4. **盘点**: 定期进行库存盘点

### 4. 数据分析
1. **查看报表**: 分析库存数据
2. **导出数据**: 生成Excel报表
3. **监控预警**: 关注库存预警

## 权限配置

### 角色权限
- **super_admin**: 超级管理员，拥有所有权限
- **admin**: 管理员，拥有大部分管理权限
- **inventory_manager**: 库存管理员，拥有库存相关权限
- **user**: 普通用户，拥有基础查看权限

### 功能权限
- **inventory.products.view**: 查看商品
- **inventory.products.create**: 创建商品
- **inventory.inbound.create**: 入库操作
- **inventory.outbound.create**: 出库操作
- **inventory.transfer.create**: 转移操作
- **inventory.count.create**: 盘点操作
- **inventory.reports.view**: 查看报表

## 使用指南

### 商品管理
1. 进入"商品管理"页面
2. 点击"添加商品"创建新商品
3. 填写商品基本信息
4. 设置库存预警阈值
5. 保存商品信息

### 入库流程
1. 进入"入库管理"页面
2. 填写入库商品信息
3. 选择入库仓库
4. 记录供应商信息
5. 确认入库操作

### 出库流程
1. 进入"出库管理"页面
2. 填写出库商品信息
3. 选择出库仓库
4. 记录领用人信息
5. 选择使用用途
6. 确认出库操作

### 库存盘点
1. 进入"库存盘点"页面
2. 点击"开始盘点"
3. 逐一录入实际库存
4. 系统自动计算差异
5. 完成盘点操作

## 注意事项

### 数据安全
- 定期备份重要数据
- 设置合适的权限控制
- 监控异常操作记录

### 操作规范
- 及时录入库存变动
- 定期进行库存盘点
- 保持数据准确性

### 系统维护
- 定期清理过期数据
- 监控系统性能
- 及时更新系统版本

## 技术支持

如有问题或建议，请联系系统管理员。

---

**版本**: v1.0.0  
**更新时间**: 2024年1月  
**开发团队**: 库存管理系统开发组
