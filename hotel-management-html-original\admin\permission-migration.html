<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限系统迁移 - 系统管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <style>
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .btn-fix {
            touch-action: manipulation;
        }
        .migration-step {
            transition: all 0.3s ease;
        }
        .migration-step.completed {
            background-color: #f0fdf4;
            border-color: #22c55e;
        }
        .migration-step.error {
            background-color: #fef2f2;
            border-color: #ef4444;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="index.html" class="text-blue-600 hover:text-blue-800 mr-4">
                        ← 返回管理首页
                    </a>
                    <h1 class="text-xl font-semibold text-gray-900">权限系统迁移</h1>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 说明 -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
            <h2 class="text-lg font-medium text-yellow-900 mb-2">权限系统升级说明</h2>
            <p class="text-yellow-800 mb-4">
                系统已升级为统一的权限管理架构。此页面用于将旧的简单角色系统迁移到新的细粒度权限系统。
            </p>
            <div class="text-sm text-yellow-700">
                <p><strong>旧系统：</strong>用户角色存储为简单数组，如 ["admin", "user"]</p>
                <p><strong>新系统：</strong>基于 UserRole 表的细粒度权限控制，支持模块化权限管理</p>
                <p><strong>兼容性：</strong>新系统向后兼容旧的角色检查方式</p>
            </div>
        </div>

        <!-- 迁移状态检查 -->
        <div class="bg-white rounded-lg shadow mb-6 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">系统状态检查</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="text-center p-4 border rounded-lg">
                    <div class="text-2xl font-bold text-blue-600" id="oldSystemUsers">0</div>
                    <div class="text-sm text-gray-500">旧系统用户</div>
                </div>
                <div class="text-center p-4 border rounded-lg">
                    <div class="text-2xl font-bold text-green-600" id="newSystemRoles">0</div>
                    <div class="text-sm text-gray-500">新系统角色</div>
                </div>
                <div class="text-center p-4 border rounded-lg">
                    <div class="text-2xl font-bold text-purple-600" id="migrationStatus">未检查</div>
                    <div class="text-sm text-gray-500">迁移状态</div>
                </div>
            </div>
            <div class="mt-4">
                <button id="checkStatusBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                    检查状态
                </button>
            </div>
        </div>

        <!-- 迁移步骤 -->
        <div class="space-y-6">
            <!-- 步骤1：备份现有数据 -->
            <div id="backupStep" class="migration-step bg-white border border-gray-200 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                            <span class="text-white font-semibold">1</span>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">备份现有数据</h3>
                            <p class="text-sm text-gray-500">备份当前用户角色数据，确保迁移安全</p>
                        </div>
                    </div>
                    <button id="backupData" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                        备份数据
                    </button>
                </div>
                <div id="backupStatus" class="text-sm text-gray-600">
                    等待执行...
                </div>
            </div>

            <!-- 步骤2：创建新角色系统 -->
            <div id="createRolesStep" class="migration-step bg-white border border-gray-200 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3">
                            <span class="text-white font-semibold">2</span>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">创建新角色系统</h3>
                            <p class="text-sm text-gray-500">在 UserRole 表中创建对应的角色和权限</p>
                        </div>
                    </div>
                    <button id="createRoles" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                        创建角色
                    </button>
                </div>
                <div id="createRolesStatus" class="text-sm text-gray-600">
                    等待执行...
                </div>
            </div>

            <!-- 步骤3：迁移用户数据 -->
            <div id="migrateUsersStep" class="migration-step bg-white border border-gray-200 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center mr-3">
                            <span class="text-white font-semibold">3</span>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">迁移用户数据</h3>
                            <p class="text-sm text-gray-500">将用户的旧角色格式转换为新格式</p>
                        </div>
                    </div>
                    <button id="migrateUsers" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                        迁移用户
                    </button>
                </div>
                <div id="migrateUsersStatus" class="text-sm text-gray-600">
                    等待执行...
                </div>
            </div>

            <!-- 步骤4：验证迁移结果 -->
            <div id="verifyStep" class="migration-step bg-white border border-gray-200 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center mr-3">
                            <span class="text-white font-semibold">4</span>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">验证迁移结果</h3>
                            <p class="text-sm text-gray-500">检查迁移后的数据完整性和权限正确性</p>
                        </div>
                    </div>
                    <button id="verifyMigration" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                        验证结果
                    </button>
                </div>
                <div id="verifyStatus" class="text-sm text-gray-600">
                    等待执行...
                </div>
            </div>
        </div>

        <!-- 一键迁移 -->
        <div class="mt-8 bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">一键迁移</h3>
            <p class="text-gray-600 mb-4">
                如果您确认要进行完整迁移，可以使用一键迁移功能。此操作将按顺序执行所有迁移步骤。
            </p>
            <div class="flex space-x-4">
                <button id="startMigration" class="bg-indigo-500 hover:bg-indigo-600 text-white px-6 py-2 rounded-lg text-sm btn-fix">
                    开始一键迁移
                </button>
                <button id="rollbackMigration" class="bg-red-500 hover:bg-red-600 text-white px-6 py-2 rounded-lg text-sm btn-fix">
                    回滚迁移
                </button>
            </div>
        </div>

        <!-- 迁移日志 -->
        <div class="mt-8 bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">迁移日志</h3>
            </div>
            <div class="p-6">
                <div id="migrationLog" class="bg-gray-50 rounded-lg p-4 h-64 overflow-y-auto text-sm font-mono">
                    <!-- 迁移日志将在这里显示 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 加载指示器 -->
    <div id="loadingIndicator" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-40" style="display: none;">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <span class="text-gray-700">迁移中...</span>
        </div>
    </div>

    <!-- 引入模块化JS文件 -->
    <script src="../js/config.js"></script>
    <script src="../js/permission-manager.js"></script>
    <script>
        // 权限系统迁移应用类
        class PermissionMigrationApp {
            constructor() {
                this.logContainer = null;
                this.backupData = null;
            }

            init() {
                this.logContainer = document.getElementById('migrationLog');
                this.bindEvents();
                this.checkSystemStatus();
            }

            bindEvents() {
                document.getElementById('checkStatusBtn').addEventListener('click', () => {
                    this.checkSystemStatus();
                });

                document.getElementById('backupData').addEventListener('click', () => {
                    this.backupCurrentData();
                });

                document.getElementById('createRoles').addEventListener('click', () => {
                    this.createNewRoles();
                });

                document.getElementById('migrateUsers').addEventListener('click', () => {
                    this.migrateUserData();
                });

                document.getElementById('verifyMigration').addEventListener('click', () => {
                    this.verifyMigrationResult();
                });

                document.getElementById('startMigration').addEventListener('click', () => {
                    this.startFullMigration();
                });

                document.getElementById('rollbackMigration').addEventListener('click', () => {
                    this.rollbackMigration();
                });
            }

            log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const typeColor = {
                    'info': 'text-blue-600',
                    'success': 'text-green-600',
                    'error': 'text-red-600',
                    'warning': 'text-yellow-600'
                };
                
                const logEntry = document.createElement('div');
                logEntry.className = typeColor[type] || 'text-gray-600';
                logEntry.textContent = `[${timestamp}] ${message}`;
                
                this.logContainer.appendChild(logEntry);
                this.logContainer.scrollTop = this.logContainer.scrollHeight;
            }

            updateStepStatus(stepId, status, message) {
                const step = document.getElementById(stepId);
                const statusDiv = document.getElementById(stepId.replace('Step', 'Status'));
                
                step.classList.remove('completed', 'error');
                
                if (status === 'completed') {
                    step.classList.add('completed');
                    statusDiv.textContent = '✅ ' + message;
                } else if (status === 'error') {
                    step.classList.add('error');
                    statusDiv.textContent = '❌ ' + message;
                } else {
                    statusDiv.textContent = message;
                }
            }

            async checkSystemStatus() {
                this.log('开始检查系统状态...');

                try {
                    // 检查用户数量
                    const userQuery = new AV.Query('_User');
                    const totalUsers = await userQuery.count();
                    document.getElementById('oldSystemUsers').textContent = totalUsers;

                    // 检查新角色系统
                    let newRolesCount = 0;
                    try {
                        const roleQuery = new AV.Query('UserRole');
                        newRolesCount = await roleQuery.count();
                    } catch (error) {
                        // UserRole 表不存在
                    }
                    document.getElementById('newSystemRoles').textContent = newRolesCount;

                    // 判断迁移状态
                    let migrationStatus = '未迁移';
                    if (newRolesCount > 0) {
                        migrationStatus = '已迁移';
                    } else if (totalUsers > 0) {
                        migrationStatus = '需要迁移';
                    }
                    document.getElementById('migrationStatus').textContent = migrationStatus;

                    this.log('系统状态检查完成', 'success');
                } catch (error) {
                    this.log('检查系统状态失败: ' + error.message, 'error');
                }
            }

            async backupCurrentData() {
                try {
                    this.log('开始备份现有数据...');
                    
                    const userQuery = new AV.Query('_User');
                    const users = await userQuery.find();
                    
                    this.backupData = {
                        timestamp: new Date().toISOString(),
                        users: users.map(user => ({
                            id: user.id,
                            username: user.get('username'),
                            roles: user.get('roles'),
                            realName: user.get('realName'),
                            department: user.get('department')
                        }))
                    };

                    // 保存备份到本地存储
                    localStorage.setItem('permissionMigrationBackup', JSON.stringify(this.backupData));
                    
                    this.updateStepStatus('backupStep', 'completed', `已备份 ${this.backupData.users.length} 个用户`);
                    this.log(`数据备份完成，共备份 ${this.backupData.users.length} 个用户`, 'success');
                } catch (error) {
                    this.updateStepStatus('backupStep', 'error', '备份失败: ' + error.message);
                    this.log('数据备份失败: ' + error.message, 'error');
                }
            }

            async createNewRoles() {
                try {
                    this.log('开始创建新角色系统...');
                    
                    const defaultRoles = [
                        {
                            name: '超级管理员',
                            code: 'super_admin',
                            description: '拥有系统所有权限的超级管理员',
                            status: 'active',
                            permissions: this.getAllPermissions()
                        },
                        {
                            name: '管理员',
                            code: 'admin',
                            description: '系统管理员，拥有大部分管理权限',
                            status: 'active',
                            permissions: this.getAdminPermissions()
                        },
                        {
                            name: '普通用户',
                            code: 'user',
                            description: '普通用户，拥有基本操作权限',
                            status: 'active',
                            permissions: this.getUserPermissions()
                        }
                    ];

                    let createdCount = 0;
                    for (const roleData of defaultRoles) {
                        // 检查角色是否已存在
                        const existingQuery = new AV.Query('UserRole');
                        existingQuery.equalTo('code', roleData.code);
                        const existing = await existingQuery.first();
                        
                        if (!existing) {
                            const role = new AV.Object('UserRole');
                            Object.keys(roleData).forEach(key => {
                                role.set(key, roleData[key]);
                            });
                            await role.save();
                            createdCount++;
                            this.log(`创建角色: ${roleData.name}`, 'info');
                        } else {
                            this.log(`角色已存在: ${roleData.name}`, 'warning');
                        }
                    }
                    
                    this.updateStepStatus('createRolesStep', 'completed', `创建了 ${createdCount} 个角色`);
                    this.log('新角色系统创建完成', 'success');
                } catch (error) {
                    this.updateStepStatus('createRolesStep', 'error', '创建失败: ' + error.message);
                    this.log('创建新角色系统失败: ' + error.message, 'error');
                }
            }

            async migrateUserData() {
                try {
                    this.log('开始迁移用户数据...');
                    
                    const userQuery = new AV.Query('_User');
                    const users = await userQuery.find();
                    
                    let migratedCount = 0;
                    for (const user of users) {
                        const currentRoles = user.get('roles');
                        let normalizedRoles = [];
                        
                        // 标准化角色格式
                        if (typeof currentRoles === 'string') {
                            try {
                                normalizedRoles = JSON.parse(currentRoles);
                            } catch (e) {
                                normalizedRoles = [currentRoles];
                            }
                        } else if (Array.isArray(currentRoles)) {
                            normalizedRoles = currentRoles;
                        }
                        
                        // 确保角色格式正确
                        if (normalizedRoles.length > 0) {
                            user.set('roles', normalizedRoles);
                            await user.save();
                            migratedCount++;
                            this.log(`迁移用户: ${user.get('username')} -> ${normalizedRoles.join(', ')}`, 'info');
                        }
                    }
                    
                    this.updateStepStatus('migrateUsersStep', 'completed', `迁移了 ${migratedCount} 个用户`);
                    this.log('用户数据迁移完成', 'success');
                } catch (error) {
                    this.updateStepStatus('migrateUsersStep', 'error', '迁移失败: ' + error.message);
                    this.log('用户数据迁移失败: ' + error.message, 'error');
                }
            }

            async verifyMigrationResult() {
                try {
                    this.log('开始验证迁移结果...');
                    
                    // 验证角色表
                    const roleQuery = new AV.Query('UserRole');
                    const roles = await roleQuery.find();
                    this.log(`验证角色表: 找到 ${roles.length} 个角色`, 'info');
                    
                    // 验证用户数据
                    const userQuery = new AV.Query('_User');
                    const users = await userQuery.find();
                    
                    let validUsers = 0;
                    for (const user of users) {
                        const userRoles = user.get('roles');
                        if (Array.isArray(userRoles) && userRoles.length > 0) {
                            validUsers++;
                        }
                    }
                    
                    this.log(`验证用户数据: ${validUsers}/${users.length} 用户有效`, 'info');
                    
                    // 验证权限管理器
                    if (window.permissionManager) {
                        await window.permissionManager.init();
                        this.log('权限管理器初始化成功', 'success');
                    }
                    
                    this.updateStepStatus('verifyStep', 'completed', '验证通过');
                    this.log('迁移结果验证完成', 'success');
                } catch (error) {
                    this.updateStepStatus('verifyStep', 'error', '验证失败: ' + error.message);
                    this.log('迁移结果验证失败: ' + error.message, 'error');
                }
            }

            async startFullMigration() {
                if (!confirm('确定要开始完整迁移吗？此操作将修改现有数据结构。')) {
                    return;
                }

                this.log('开始完整权限系统迁移...', 'info');
                this.showLoading();

                try {
                    await this.backupCurrentData();
                    await this.createNewRoles();
                    await this.migrateUserData();
                    await this.verifyMigrationResult();
                    
                    this.log('完整迁移成功完成！', 'success');
                    alert('权限系统迁移完成！');
                } catch (error) {
                    this.log('迁移过程中出现错误: ' + error.message, 'error');
                    alert('迁移过程中出现错误: ' + error.message);
                } finally {
                    this.hideLoading();
                }
            }

            async rollbackMigration() {
                if (!confirm('确定要回滚迁移吗？这将恢复到迁移前的状态。')) {
                    return;
                }

                try {
                    this.log('开始回滚迁移...', 'warning');
                    
                    const backupData = localStorage.getItem('permissionMigrationBackup');
                    if (!backupData) {
                        throw new Error('没有找到备份数据');
                    }

                    const backup = JSON.parse(backupData);
                    this.log(`找到备份数据，时间: ${backup.timestamp}`, 'info');
                    
                    // 恢复用户数据
                    for (const userData of backup.users) {
                        const user = AV.Object.createWithoutData('_User', userData.id);
                        user.set('roles', userData.roles);
                        await user.save();
                    }
                    
                    this.log('迁移回滚完成', 'success');
                    alert('迁移回滚完成！');
                } catch (error) {
                    this.log('回滚失败: ' + error.message, 'error');
                    alert('回滚失败: ' + error.message);
                }
            }

            getAllPermissions() {
                return {
                    'system': {
                        'user_management': ['view', 'create', 'edit', 'delete'],
                        'role_management': ['view', 'create', 'edit', 'delete'],
                        'system_settings': ['view', 'edit'],
                        'logs': ['view', 'export'],
                        'reports': ['view', 'export'],
                        'backup': ['view', 'create', 'restore']
                    },
                    'inventory': {
                        'products': ['view', 'create', 'edit', 'delete'],
                        'warehouses': ['view', 'create', 'edit', 'delete'],
                        'inbound': ['view', 'create', 'edit', 'delete'],
                        'outbound': ['view', 'create', 'edit', 'delete'],
                        'transfer': ['view', 'create', 'edit', 'delete'],
                        'count': ['view', 'create', 'edit', 'delete']
                    },
                    'work': {
                        'logs': ['view', 'create', 'edit', 'delete'],
                        'reports': ['view', 'export']
                    },
                    'repair': {
                        'orders': ['view', 'create', 'edit', 'delete'],
                        'reports': ['view', 'export']
                    }
                };
            }

            getAdminPermissions() {
                const adminPermissions = this.getAllPermissions();
                // 管理员不能删除用户
                adminPermissions.system.user_management = ['view', 'create', 'edit'];
                return adminPermissions;
            }

            getUserPermissions() {
                return {
                    'work': {
                        'logs': ['view', 'create', 'edit']
                    },
                    'repair': {
                        'orders': ['view', 'create']
                    },
                    'inventory': {
                        'products': ['view'],
                        'inbound': ['view'],
                        'outbound': ['view']
                    }
                };
            }

            showLoading() {
                document.getElementById('loadingIndicator').style.display = 'flex';
            }

            hideLoading() {
                document.getElementById('loadingIndicator').style.display = 'none';
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            function initApp() {
                if (typeof AV !== 'undefined') {
                    const migrationApp = new PermissionMigrationApp();
                    migrationApp.init();
                } else {
                    setTimeout(initApp, 100);
                }
            }
            initApp();
        });
    </script>
</body>
</html>
