<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">系统管理</h1>
          <p class="mt-1 text-sm text-gray-600">
            管理系统设置、用户权限和数据统计
          </p>
        </div>
        
        <div class="mt-4 sm:mt-0 flex space-x-3">
          <button 
            @click="refreshData"
            :disabled="loading"
            class="border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 transition-colors flex items-center disabled:opacity-50"
          >
            <Icon :name="loading ? 'mdi:loading' : 'mdi:refresh'" :size="16" :class="loading ? 'animate-spin' : ''" class="mr-1" />
            刷新数据
          </button>
        </div>
      </div>
    </div>

    <!-- 系统概览统计 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:account-group" size="24" class="text-blue-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">总用户数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ systemStats.totalUsers }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:notebook" size="24" class="text-green-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">工作日志</p>
            <p class="text-2xl font-semibold text-gray-900">{{ systemStats.totalWorkLogs }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:package-variant" size="24" class="text-purple-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">库存物品</p>
            <p class="text-2xl font-semibold text-gray-900">{{ systemStats.totalInventoryItems }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:wrench" size="24" class="text-orange-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">报修工单</p>
            <p class="text-2xl font-semibold text-gray-900">{{ systemStats.totalRepairs }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- 用户管理 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div class="flex items-center mb-4">
          <Icon name="mdi:account-cog" size="32" class="text-blue-600 mr-3" />
          <div>
            <h3 class="text-lg font-semibold text-gray-900">用户管理</h3>
            <p class="text-sm text-gray-600">管理用户账户和权限</p>
          </div>
        </div>
        <div class="space-y-2 mb-4">
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">活跃用户:</span>
            <span class="font-medium text-gray-900">{{ systemStats.activeUsers }}</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">管理员:</span>
            <span class="font-medium text-gray-900">{{ systemStats.adminUsers }}</span>
          </div>
        </div>
        <NuxtLink 
          to="/admin/users"
          class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors text-center block"
        >
          管理用户
        </NuxtLink>
      </div>

      <!-- 部门管理 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div class="flex items-center mb-4">
          <Icon name="mdi:office-building" size="32" class="text-indigo-600 mr-3" />
          <div>
            <h3 class="text-lg font-semibold text-gray-900">部门管理</h3>
            <p class="text-sm text-gray-600">管理组织部门结构</p>
          </div>
        </div>
        <div class="space-y-2 mb-4">
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">总部门:</span>
            <span class="font-medium text-gray-900">8</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">系统部门:</span>
            <span class="font-medium text-gray-900">8</span>
          </div>
        </div>
        <NuxtLink
          to="/admin/departments"
          class="w-full bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors text-center block"
        >
          管理部门
        </NuxtLink>
      </div>

      <!-- 角色管理 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div class="flex items-center mb-4">
          <Icon name="mdi:shield-account" size="32" class="text-orange-600 mr-3" />
          <div>
            <h3 class="text-lg font-semibold text-gray-900">角色管理</h3>
            <p class="text-sm text-gray-600">管理用户角色权限</p>
          </div>
        </div>
        <div class="space-y-2 mb-4">
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">总角色:</span>
            <span class="font-medium text-gray-900">3</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">系统角色:</span>
            <span class="font-medium text-gray-900">3</span>
          </div>
        </div>
        <NuxtLink
          to="/admin/roles"
          class="w-full bg-orange-600 text-white px-4 py-2 rounded-md hover:bg-orange-700 transition-colors text-center block"
        >
          管理角色
        </NuxtLink>
      </div>

      <!-- 系统设置 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div class="flex items-center mb-4">
          <Icon name="mdi:cog" size="32" class="text-green-600 mr-3" />
          <div>
            <h3 class="text-lg font-semibold text-gray-900">系统设置</h3>
            <p class="text-sm text-gray-600">配置系统参数和功能</p>
          </div>
        </div>
        <div class="space-y-2 mb-4">
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">企业微信:</span>
            <span class="font-medium text-green-600">已配置</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">文件存储:</span>
            <span class="font-medium text-green-600">正常</span>
          </div>
        </div>
        <NuxtLink 
          to="/admin/settings"
          class="w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors text-center block"
        >
          系统设置
        </NuxtLink>
      </div>

      <!-- 数据统计 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div class="flex items-center mb-4">
          <Icon name="mdi:chart-line" size="32" class="text-purple-600 mr-3" />
          <div>
            <h3 class="text-lg font-semibold text-gray-900">数据统计</h3>
            <p class="text-sm text-gray-600">查看系统使用情况</p>
          </div>
        </div>
        <div class="space-y-2 mb-4">
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">今日活跃:</span>
            <span class="font-medium text-gray-900">{{ systemStats.todayActiveUsers }}</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">本周新增:</span>
            <span class="font-medium text-gray-900">{{ systemStats.weeklyNewUsers }}</span>
          </div>
        </div>
        <NuxtLink 
          to="/admin/reports"
          class="w-full bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors text-center block"
        >
          查看报表
        </NuxtLink>
      </div>
    </div>

    <!-- 第二行管理功能 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- 系统日志 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div class="flex items-center mb-4">
          <Icon name="mdi:file-document-multiple" size="32" class="text-red-600 mr-3" />
          <div>
            <h3 class="text-lg font-semibold text-gray-900">系统日志</h3>
            <p class="text-sm text-gray-600">查看系统操作记录</p>
          </div>
        </div>
        <div class="space-y-2 mb-4">
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">今日操作:</span>
            <span class="font-medium text-gray-900">156</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">异常记录:</span>
            <span class="font-medium text-red-600">2</span>
          </div>
        </div>
        <NuxtLink
          to="/admin/logs"
          class="w-full bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors text-center block"
        >
          查看日志
        </NuxtLink>
      </div>

      <!-- 数据备份 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div class="flex items-center mb-4">
          <Icon name="mdi:backup-restore" size="32" class="text-teal-600 mr-3" />
          <div>
            <h3 class="text-lg font-semibold text-gray-900">数据备份</h3>
            <p class="text-sm text-gray-600">备份和恢复数据</p>
          </div>
        </div>
        <div class="space-y-2 mb-4">
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">最近备份:</span>
            <span class="font-medium text-gray-900">今天</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">备份状态:</span>
            <span class="font-medium text-green-600">正常</span>
          </div>
        </div>
        <button
          @click="handleBackup"
          class="w-full bg-teal-600 text-white px-4 py-2 rounded-md hover:bg-teal-700 transition-colors text-center block"
        >
          立即备份
        </button>
      </div>

      <!-- 系统监控 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div class="flex items-center mb-4">
          <Icon name="mdi:monitor-dashboard" size="32" class="text-cyan-600 mr-3" />
          <div>
            <h3 class="text-lg font-semibold text-gray-900">系统监控</h3>
            <p class="text-sm text-gray-600">监控系统运行状态</p>
          </div>
        </div>
        <div class="space-y-2 mb-4">
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">CPU使用率:</span>
            <span class="font-medium text-gray-900">45%</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">内存使用:</span>
            <span class="font-medium text-gray-900">68%</span>
          </div>
        </div>
        <button
          @click="handleMonitor"
          class="w-full bg-cyan-600 text-white px-4 py-2 rounded-md hover:bg-cyan-700 transition-colors text-center block"
        >
          查看监控
        </button>
      </div>

      <!-- 系统维护 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div class="flex items-center mb-4">
          <Icon name="mdi:wrench-cog" size="32" class="text-amber-600 mr-3" />
          <div>
            <h3 class="text-lg font-semibold text-gray-900">系统维护</h3>
            <p class="text-sm text-gray-600">系统维护和优化</p>
          </div>
        </div>
        <div class="space-y-2 mb-4">
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">缓存清理:</span>
            <span class="font-medium text-gray-900">3天前</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">系统状态:</span>
            <span class="font-medium text-green-600">健康</span>
          </div>
        </div>
        <button
          @click="handleMaintenance"
          class="w-full bg-amber-600 text-white px-4 py-2 rounded-md hover:bg-amber-700 transition-colors text-center block"
        >
          系统维护
        </button>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 最近用户活动 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">最近用户活动</h3>
        </div>
        <div class="p-6">
          <div v-if="recentActivities.length === 0" class="text-center py-8">
            <Icon name="mdi:history" size="48" class="text-gray-400 mx-auto mb-2" />
            <p class="text-gray-500 text-sm">暂无活动记录</p>
          </div>
          <div v-else class="space-y-4">
            <div 
              v-for="activity in recentActivities.slice(0, 5)" 
              :key="activity.id"
              class="flex items-start space-x-3"
            >
              <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium flex-shrink-0">
                {{ activity.user.charAt(0) }}
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm text-gray-900">
                  <span class="font-medium">{{ activity.user }}</span>
                  {{ activity.action }}
                </p>
                <p class="text-xs text-gray-500">{{ formatTime(activity.timestamp) }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 系统状态 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">系统状态</h3>
        </div>
        <div class="p-6">
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <Icon name="mdi:database" size="16" class="text-gray-400 mr-2" />
                <span class="text-sm text-gray-700">数据库连接</span>
              </div>
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                <div class="w-1.5 h-1.5 bg-green-400 rounded-full mr-1"></div>
                正常
              </span>
            </div>
            
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <Icon name="mdi:cloud" size="16" class="text-gray-400 mr-2" />
                <span class="text-sm text-gray-700">文件存储</span>
              </div>
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                <div class="w-1.5 h-1.5 bg-green-400 rounded-full mr-1"></div>
                正常
              </span>
            </div>
            
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <Icon name="mdi:message" size="16" class="text-gray-400 mr-2" />
                <span class="text-sm text-gray-700">企业微信</span>
              </div>
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                <div class="w-1.5 h-1.5 bg-green-400 rounded-full mr-1"></div>
                已连接
              </span>
            </div>
            
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <Icon name="mdi:server" size="16" class="text-gray-400 mr-2" />
                <span class="text-sm text-gray-700">服务器状态</span>
              </div>
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                <div class="w-1.5 h-1.5 bg-green-400 rounded-full mr-1"></div>
                运行中
              </span>
            </div>
            
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <Icon name="mdi:memory" size="16" class="text-gray-400 mr-2" />
                <span class="text-sm text-gray-700">内存使用</span>
              </div>
              <span class="text-sm font-medium text-gray-900">65%</span>
            </div>
            
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <Icon name="mdi:harddisk" size="16" class="text-gray-400 mr-2" />
                <span class="text-sm text-gray-700">存储空间</span>
              </div>
              <span class="text-sm font-medium text-gray-900">42%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面元数据
definePageMeta({
  title: '系统管理',
  middleware: 'auth',
  layout: 'default'
})

// 检查管理员权限
const authStore = useAuthStore()
if (!authStore.isAdmin) {
  throw createError({
    statusCode: 403,
    statusMessage: '权限不足，无法访问管理页面'
  })
}

// 响应式数据
const loading = ref(false)

const systemStats = reactive({
  totalUsers: 0,
  activeUsers: 0,
  adminUsers: 0,
  todayActiveUsers: 0,
  weeklyNewUsers: 0,
  totalWorkLogs: 0,
  totalInventoryItems: 0,
  totalRepairs: 0
})

const recentActivities = ref([
  {
    id: '1',
    user: '张三',
    action: '创建了新的报修工单',
    timestamp: new Date(Date.now() - 5 * 60 * 1000)
  },
  {
    id: '2',
    user: '李四',
    action: '完成了库存盘点',
    timestamp: new Date(Date.now() - 15 * 60 * 1000)
  },
  {
    id: '3',
    user: '王五',
    action: '提交了工作日志',
    timestamp: new Date(Date.now() - 30 * 60 * 1000)
  },
  {
    id: '4',
    user: '赵六',
    action: '更新了用户信息',
    timestamp: new Date(Date.now() - 45 * 60 * 1000)
  }
])

// 方法
const loadSystemStats = async () => {
  try {
    // 这里应该调用API获取真实数据
    // 暂时使用模拟数据
    systemStats.totalUsers = 25
    systemStats.activeUsers = 18
    systemStats.adminUsers = 3
    systemStats.todayActiveUsers = 12
    systemStats.weeklyNewUsers = 5
    systemStats.totalWorkLogs = 156
    systemStats.totalInventoryItems = 89
    systemStats.totalRepairs = 34
  } catch (error) {
    console.error('加载系统统计失败:', error)
  }
}

const refreshData = async () => {
  loading.value = true
  try {
    await loadSystemStats()
  } finally {
    loading.value = false
  }
}

const handleBackup = () => {
  if (confirm('确定要立即备份系统数据吗？')) {
    alert('备份功能待实现')
  }
}

const handleMonitor = () => {
  alert('系统监控功能待实现')
}

const handleMaintenance = () => {
  if (confirm('确定要进行系统维护吗？这可能会影响系统性能。')) {
    alert('系统维护功能待实现')
  }
}

const formatTime = (date: Date) => {
  const now = new Date()
  const targetDate = new Date(date)
  const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000)
  
  if (diffInSeconds < 60) {
    return '刚刚'
  } else if (diffInSeconds < 3600) {
    return `${Math.floor(diffInSeconds / 60)}分钟前`
  } else if (diffInSeconds < 86400) {
    return `${Math.floor(diffInSeconds / 3600)}小时前`
  } else {
    return targetDate.toLocaleDateString('zh-CN')
  }
}

// 生命周期
onMounted(async () => {
  await loadSystemStats()
})

// 页面标题
useHead({
  title: '系统管理 - 酒店管理系统'
})
</script>
