<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">部门管理</h1>
          <p class="mt-1 text-sm text-gray-600">
            管理组织部门结构和人员分配
          </p>
        </div>
        
        <div class="mt-4 sm:mt-0 flex space-x-3">
          <button 
            @click="showCreateModal = true"
            class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center"
          >
            <Icon name="mdi:plus" size="16" class="mr-1" />
            新增部门
          </button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:office-building" size="24" class="text-blue-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">总部门数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ departmentStats.total }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:cog" size="24" class="text-green-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">系统部门</p>
            <p class="text-2xl font-semibold text-gray-900">{{ departmentStats.system }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:account-group" size="24" class="text-purple-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">总人员数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ totalUsers }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 部门列表 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">部门列表</h3>
      </div>
      
      <div v-if="loading" class="p-8 text-center">
        <Icon name="mdi:loading" size="32" class="text-gray-400 animate-spin mx-auto mb-4" />
        <p class="text-gray-600">加载中...</p>
      </div>
      
      <div v-else-if="departments.length === 0" class="p-8 text-center">
        <Icon name="mdi:office-building-outline" size="64" class="text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无部门</h3>
        <p class="text-gray-600 mb-6">创建你的第一个部门吧</p>
        <button 
          @click="showCreateModal = true"
          class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          新增部门
        </button>
      </div>
      
      <div v-else class="divide-y divide-gray-200">
        <div 
          v-for="department in departments" 
          :key="department.id"
          class="p-6 hover:bg-gray-50 transition-colors"
        >
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-3 mb-2">
                <h4 class="text-lg font-medium text-gray-900">{{ department.name }}</h4>
                <span 
                  v-if="department.isSystem"
                  class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full"
                >
                  系统部门
                </span>
                <span 
                  v-else
                  class="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full"
                >
                  自定义
                </span>
              </div>
              
              <p v-if="department.description" class="text-gray-600 mb-2">{{ department.description }}</p>
              
              <div class="flex items-center space-x-6 text-sm text-gray-500">
                <div class="flex items-center">
                  <Icon name="mdi:account-group" size="16" class="mr-1" />
                  {{ department.userCount }} 人
                </div>
                <div class="flex items-center">
                  <Icon name="mdi:calendar" size="16" class="mr-1" />
                  创建于 {{ formatDate(department.createdAt) }}
                </div>
              </div>
            </div>
            
            <div class="flex items-center space-x-2">
              <button 
                @click="editDepartment(department)"
                class="text-blue-600 hover:text-blue-700 p-2 rounded-md hover:bg-blue-50 transition-colors"
                title="编辑"
              >
                <Icon name="mdi:pencil" size="16" />
              </button>
              
              <button 
                v-if="!department.isSystem"
                @click="deleteDepartment(department)"
                class="text-red-600 hover:text-red-700 p-2 rounded-md hover:bg-red-50 transition-colors"
                title="删除"
              >
                <Icon name="mdi:delete" size="16" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建/编辑部门模态框 -->
    <CreateDepartmentModal
      v-model:show="showCreateModal"
      :edit-department="editingDepartment"
      @created="handleDepartmentCreated"
      @updated="handleDepartmentUpdated"
    />
  </div>
</template>

<script setup lang="ts">
import type { Department } from '~/types'
import CreateDepartmentModal from '~/components/admin/CreateDepartmentModal.vue'

// 页面元数据
definePageMeta({
  title: '部门管理',
  middleware: 'auth',
  requiresAdmin: true
})

// Store
const adminStore = useAdminStore()

// 响应式数据
const showCreateModal = ref(false)
const editingDepartment = ref<Department | null>(null)

// 计算属性
const departments = computed(() => adminStore.departments)
const departmentStats = computed(() => adminStore.departmentStats)
const loading = computed(() => adminStore.loading)

const totalUsers = computed(() => 
  departments.value.reduce((sum, dept) => sum + dept.userCount, 0)
)

// 方法
const formatDate = (date: Date) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

const editDepartment = (department: Department) => {
  editingDepartment.value = department
  showCreateModal.value = true
}

const deleteDepartment = async (department: Department) => {
  if (department.isSystem) {
    alert('系统部门不能删除')
    return
  }
  
  if (department.userCount > 0) {
    alert('该部门还有人员，请先转移人员后再删除')
    return
  }
  
  if (confirm(`确定要删除部门"${department.name}"吗？`)) {
    // 这里应该调用删除API
    alert('删除功能待实现')
  }
}

const handleDepartmentCreated = () => {
  showCreateModal.value = false
  editingDepartment.value = null
  adminStore.fetchDepartments()
}

const handleDepartmentUpdated = () => {
  showCreateModal.value = false
  editingDepartment.value = null
  adminStore.fetchDepartments()
}

// 生命周期
onMounted(async () => {
  await adminStore.fetchDepartments()
})

// 页面标题
useHead({
  title: '部门管理 - 酒店管理系统'
})
</script>
