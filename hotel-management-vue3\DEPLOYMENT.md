# 酒店管理系统部署指南

## 系统要求

### 最低配置
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 20GB 可用空间
- **操作系统**: Linux (Ubuntu 20.04+ 推荐)
- **Node.js**: 18.x 或更高版本
- **Docker**: 20.10+ (可选)

### 推荐配置
- **CPU**: 4核心
- **内存**: 8GB RAM
- **存储**: 50GB SSD
- **网络**: 100Mbps 带宽

## 部署方式

### 方式一：Docker 部署（推荐）

#### 1. 准备环境
```bash
# 安装 Docker 和 Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### 2. 克隆项目
```bash
git clone <your-repository-url>
cd hotel-management-vue3
```

#### 3. 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
nano .env
```

#### 4. 启动服务
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f app
```

#### 5. 访问应用
- 应用地址: http://your-domain.com
- 管理后台: http://your-domain.com/admin

### 方式二：传统部署

#### 1. 安装 Node.js
```bash
# 使用 NodeSource 仓库安装 Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

#### 2. 克隆和构建项目
```bash
git clone <your-repository-url>
cd hotel-management-vue3

# 安装依赖
npm install

# 构建项目
npm run build
```

#### 3. 配置环境变量
```bash
cp .env.example .env
nano .env
```

#### 4. 启动应用
```bash
# 生产模式启动
npm run start

# 或使用 PM2 管理进程
npm install -g pm2
pm2 start ecosystem.config.js
```

## 环境变量配置

### 必需配置
```env
# LeanCloud 配置
NUXT_PUBLIC_LEANCLOUD_APP_ID="your-app-id"
NUXT_PUBLIC_LEANCLOUD_APP_KEY="your-app-key"
NUXT_PUBLIC_LEANCLOUD_SERVER_URL="your-server-url"

# 企业微信配置
NUXT_WECHAT_CORP_ID="your-corp-id"
NUXT_WECHAT_CORP_SECRET="your-corp-secret"
NUXT_WECHAT_AGENT_ID="your-agent-id"

# 安全配置
NUXT_JWT_SECRET="your-jwt-secret"
NUXT_SESSION_SECRET="your-session-secret"
```

### 可选配置
```env
# 应用配置
NUXT_APP_NAME="酒店管理系统"
NUXT_PUBLIC_APP_ENV="production"

# 文件上传配置
NUXT_PUBLIC_MAX_FILE_SIZE="5242880"
NUXT_PUBLIC_ALLOWED_FILE_TYPES="jpg,jpeg,png,gif,pdf,doc,docx"
```

## Nginx 配置

### 创建 Nginx 配置文件
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 重定向到 HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL 证书配置
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 文件上传大小限制
    client_max_body_size 10M;
    
    # 代理到应用
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 数据库配置

### LeanCloud 设置
1. 登录 [LeanCloud 控制台](https://console.leancloud.cn/)
2. 创建新应用
3. 获取 App ID、App Key 和服务器地址
4. 配置数据表结构（参考 `docs/database-schema.md`）

### 数据表初始化
```javascript
// 在 LeanCloud 控制台执行以下代码初始化数据表
// 或使用提供的初始化脚本
npm run init:database
```

## 企业微信配置

### 1. 创建企业微信应用
1. 登录企业微信管理后台
2. 应用管理 → 自建 → 创建应用
3. 获取 AgentId 和 Secret

### 2. 配置回调地址
- 回调地址: `https://your-domain.com/api/wechat/callback`
- 可信域名: `your-domain.com`

### 3. 权限配置
- 发送消息权限
- 获取用户信息权限

## 监控和日志

### 应用监控
```bash
# 查看应用状态
pm2 status

# 查看日志
pm2 logs

# 监控资源使用
pm2 monit
```

### 系统监控
```bash
# 安装系统监控工具
sudo apt install htop iotop

# 查看系统资源
htop
```

### 日志管理
```bash
# 日志轮转配置
sudo nano /etc/logrotate.d/hotel-management

# 内容示例
/var/log/hotel-management/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
```

## 备份策略

### 数据备份
```bash
# 创建备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups"

# 备份上传文件
tar -czf $BACKUP_DIR/uploads_$DATE.tar.gz /app/uploads

# 备份配置文件
cp .env $BACKUP_DIR/env_$DATE.backup

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

### 自动备份
```bash
# 添加到 crontab
crontab -e

# 每天凌晨2点执行备份
0 2 * * * /path/to/backup.sh
```

## 安全配置

### 防火墙设置
```bash
# 启用 UFW 防火墙
sudo ufw enable

# 允许必要端口
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS

# 查看状态
sudo ufw status
```

### SSL 证书
```bash
# 使用 Let's Encrypt 免费证书
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

## 性能优化

### 应用优化
- 启用 Gzip 压缩
- 配置静态资源缓存
- 使用 CDN 加速
- 数据库查询优化

### 服务器优化
```bash
# 调整系统参数
echo 'net.core.somaxconn = 65535' >> /etc/sysctl.conf
echo 'fs.file-max = 100000' >> /etc/sysctl.conf
sysctl -p
```

## 故障排除

### 常见问题

#### 1. 应用无法启动
```bash
# 检查端口占用
netstat -tlnp | grep :3000

# 检查日志
docker-compose logs app
```

#### 2. 数据库连接失败
- 检查 LeanCloud 配置
- 验证网络连接
- 确认 API 密钥正确

#### 3. 企业微信通知失败
- 检查企业微信配置
- 验证回调地址
- 确认应用权限

### 日志分析
```bash
# 查看错误日志
tail -f /var/log/hotel-management/error.log

# 分析访问日志
tail -f /var/log/nginx/access.log
```

## 更新部署

### Docker 更新
```bash
# 拉取最新代码
git pull origin main

# 重新构建和部署
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

### 传统部署更新
```bash
# 拉取最新代码
git pull origin main

# 安装新依赖
npm install

# 重新构建
npm run build

# 重启应用
pm2 restart all
```

## 联系支持

如果在部署过程中遇到问题，请：
1. 查看本文档的故障排除部分
2. 检查应用日志
3. 联系技术支持团队

---

**注意**: 请确保在生产环境中使用强密码和安全的配置。定期更新系统和依赖包以保持安全性。
