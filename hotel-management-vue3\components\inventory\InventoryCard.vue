<template>
  <div class="bg-white rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
    <!-- 图片区域 -->
    <div class="relative h-48 bg-gray-100 rounded-t-lg overflow-hidden">
      <img 
        v-if="item.images && item.images.length > 0"
        :src="item.images[0]" 
        :alt="item.name"
        class="w-full h-full object-cover"
        @error="handleImageError"
      />
      <div v-else class="w-full h-full flex items-center justify-center">
        <Icon name="mdi:package-variant" size="48" class="text-gray-400" />
      </div>
      
      <!-- 状态标识 -->
      <div class="absolute top-2 left-2">
        <span 
          class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
          :class="getStatusStyle(item.status)"
        >
          {{ getStatusText(item.status) }}
        </span>
      </div>
      
      <!-- 库存警告 -->
      <div v-if="isLowStock" class="absolute top-2 right-2">
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
          <Icon name="mdi:alert" size="12" class="mr-1" />
          {{ item.quantity === 0 ? '缺货' : '低库存' }}
        </span>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="p-4">
      <!-- 基本信息 -->
      <div class="mb-3">
        <h3 class="text-lg font-semibold text-gray-900 mb-1">{{ item.name }}</h3>
        <p class="text-sm text-gray-600">{{ item.category }}</p>
      </div>

      <!-- 库存信息 -->
      <div class="mb-4">
        <div class="flex items-center justify-between mb-2">
          <span class="text-sm text-gray-600">当前库存</span>
          <span 
            class="text-lg font-semibold"
            :class="getQuantityColor(item.quantity, item.minStock)"
          >
            {{ item.quantity }} {{ item.unit }}
          </span>
        </div>
        
        <!-- 库存进度条 -->
        <div class="w-full bg-gray-200 rounded-full h-2">
          <div 
            class="h-2 rounded-full transition-all"
            :class="getProgressBarColor(item.quantity, item.minStock, item.maxStock)"
            :style="{ width: getProgressWidth(item.quantity, item.maxStock) }"
          ></div>
        </div>
        
        <div class="flex justify-between text-xs text-gray-500 mt-1">
          <span>最低: {{ item.minStock }}</span>
          <span v-if="item.maxStock">最高: {{ item.maxStock }}</span>
        </div>
      </div>

      <!-- 详细信息 -->
      <div class="space-y-2 mb-4">
        <div class="flex justify-between text-sm">
          <span class="text-gray-600">位置:</span>
          <span class="text-gray-900">{{ item.location }}</span>
        </div>
        
        <div v-if="item.supplier" class="flex justify-between text-sm">
          <span class="text-gray-600">供应商:</span>
          <span class="text-gray-900">{{ item.supplier }}</span>
        </div>
        
        <div v-if="item.price" class="flex justify-between text-sm">
          <span class="text-gray-600">单价:</span>
          <span class="text-gray-900">¥{{ item.price.toFixed(2) }}</span>
        </div>
        
        <div class="flex justify-between text-sm">
          <span class="text-gray-600">总价值:</span>
          <span class="text-gray-900 font-medium">¥{{ totalValue.toFixed(2) }}</span>
        </div>
      </div>

      <!-- 描述 -->
      <div v-if="item.description" class="mb-4">
        <p class="text-sm text-gray-600 line-clamp-2">{{ item.description }}</p>
      </div>

      <!-- 操作按钮 -->
      <div class="flex space-x-2">
        <div class="relative flex-1" ref="dropdownRef">
          <button 
            @click="dropdownOpen = !dropdownOpen"
            class="w-full bg-blue-600 text-white px-3 py-2 rounded-md hover:bg-blue-700 transition-colors text-sm flex items-center justify-center"
          >
            <Icon name="mdi:package-variant" size="16" class="mr-1" />
            库存操作
            <Icon name="mdi:chevron-down" size="16" class="ml-1" />
          </button>
          
          <!-- 下拉菜单 -->
          <div 
            v-if="dropdownOpen"
            class="absolute bottom-full mb-1 left-0 right-0 bg-white rounded-md shadow-lg py-1 z-10 border border-gray-200"
          >
            <button 
              @click="handleStockIn"
              class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            >
              <Icon name="mdi:plus" size="14" class="inline mr-2" />
              入库
            </button>
            <button 
              @click="handleStockOut"
              class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            >
              <Icon name="mdi:minus" size="14" class="inline mr-2" />
              出库
            </button>
            <button 
              @click="handleAdjust"
              class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            >
              <Icon name="mdi:tune" size="14" class="inline mr-2" />
              调整
            </button>
          </div>
        </div>
        
        <div class="relative" ref="moreDropdownRef">
          <button 
            @click="moreDropdownOpen = !moreDropdownOpen"
            class="border border-gray-300 text-gray-700 px-3 py-2 rounded-md hover:bg-gray-50 transition-colors"
          >
            <Icon name="mdi:dots-vertical" size="16" />
          </button>
          
          <!-- 更多操作下拉菜单 -->
          <div 
            v-if="moreDropdownOpen"
            class="absolute bottom-full mb-1 right-0 w-32 bg-white rounded-md shadow-lg py-1 z-10 border border-gray-200"
          >
            <button 
              @click="handleEdit"
              class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            >
              <Icon name="mdi:pencil" size="14" class="inline mr-2" />
              编辑
            </button>
            <button 
              @click="handleDelete"
              class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50"
            >
              <Icon name="mdi:delete" size="14" class="inline mr-2" />
              删除
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { InventoryItem } from '~/types'

interface Props {
  item: InventoryItem
}

const props = defineProps<Props>()
const emit = defineEmits<{
  edit: [item: InventoryItem]
  delete: [item: InventoryItem]
  adjust: [item: InventoryItem]
  'stock-in': [item: InventoryItem]
  'stock-out': [item: InventoryItem]
}>()

const dropdownOpen = ref(false)
const moreDropdownOpen = ref(false)
const dropdownRef = ref()
const moreDropdownRef = ref()

// 点击外部关闭下拉菜单
onClickOutside(dropdownRef, () => {
  dropdownOpen.value = false
})

onClickOutside(moreDropdownRef, () => {
  moreDropdownOpen.value = false
})

// 计算属性
const isLowStock = computed(() => {
  return props.item.quantity <= props.item.minStock
})

const totalValue = computed(() => {
  return props.item.quantity * (props.item.price || 0)
})

// 方法
const getStatusStyle = (status: string) => {
  const styles = {
    'active': 'bg-green-100 text-green-800',
    'inactive': 'bg-gray-100 text-gray-800',
    'discontinued': 'bg-red-100 text-red-800'
  }
  return styles[status as keyof typeof styles] || styles.active
}

const getStatusText = (status: string) => {
  const texts = {
    'active': '正常',
    'inactive': '停用',
    'discontinued': '停产'
  }
  return texts[status as keyof typeof texts] || '正常'
}

const getQuantityColor = (quantity: number, minStock: number) => {
  if (quantity === 0) return 'text-red-600'
  if (quantity <= minStock) return 'text-orange-600'
  return 'text-green-600'
}

const getProgressBarColor = (quantity: number, minStock: number, maxStock?: number) => {
  if (quantity === 0) return 'bg-red-500'
  if (quantity <= minStock) return 'bg-orange-500'
  return 'bg-green-500'
}

const getProgressWidth = (quantity: number, maxStock?: number) => {
  if (!maxStock) return '100%'
  const percentage = Math.min((quantity / maxStock) * 100, 100)
  return `${percentage}%`
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}

const handleEdit = () => {
  moreDropdownOpen.value = false
  emit('edit', props.item)
}

const handleDelete = () => {
  moreDropdownOpen.value = false
  emit('delete', props.item)
}

const handleAdjust = () => {
  dropdownOpen.value = false
  emit('adjust', props.item)
}

const handleStockIn = () => {
  dropdownOpen.value = false
  emit('stock-in', props.item)
}

const handleStockOut = () => {
  dropdownOpen.value = false
  emit('stock-out', props.item)
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
