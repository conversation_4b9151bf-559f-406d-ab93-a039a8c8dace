// stores/inventory-simple.ts
// 简化版库存Store，用于测试
import { defineStore } from 'pinia'
import AV from 'leancloud-storage'
import { useGlobalNotifications } from '~/composables/useNotifications'
import type { InventoryItem } from '~/types'

interface SimpleInventoryState {
  items: InventoryItem[]
  loading: boolean
  total: number
}

export const useSimpleInventoryStore = defineStore('simple-inventory', {
  state: (): SimpleInventoryState => ({
    items: [],
    loading: false,
    total: 0
  }),

  getters: {
    inventoryStats: (state) => ({
      totalItems: state.items.length,
      totalValue: state.items.reduce((sum, item) => sum + (item.price || 0) * item.quantity, 0),
      lowStockItems: state.items.filter(item => item.quantity <= item.minStock).length,
      outOfStockItems: state.items.filter(item => item.quantity === 0).length
    })
  },

  actions: {
    // 获取库存列表
    async fetchItems() {
      this.loading = true
      
      try {
        const query = new AV.Query('InventoryItem')
        query.limit(100) // 限制100条
        query.descending('updatedAt')
        
        const results = await query.find()
        this.items = results.map(item => ({
          id: item.id,
          name: item.get('name'),
          category: item.get('category'),
          quantity: item.get('quantity'),
          unit: item.get('unit'),
          minStock: item.get('minStock'),
          maxStock: item.get('maxStock') || 0,
          price: item.get('price') || 0,
          supplier: item.get('supplier') || '',
          location: item.get('location') || '',
          description: item.get('description') || '',
          status: item.get('status') || 'active',
          images: item.get('images') || [],
          createdAt: item.createdAt,
          updatedAt: item.updatedAt
        }))
        
        this.total = this.items.length
        
        return { success: true, data: this.items }
      } catch (error: any) {
        console.error('获取库存列表失败:', error)
        
        // 使用模拟数据作为后备
        this.items = [
          {
            id: '1',
            name: '洗发水',
            category: '客房用品',
            quantity: 50,
            unit: '瓶',
            minStock: 20,
            maxStock: 100,
            location: '仓库A',
            supplier: '日化供应商',
            price: 15.5,
            status: 'active',
            description: '高品质洗发水，适合酒店客房使用',
            images: [],
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            id: '2',
            name: '毛巾',
            category: '客房用品',
            quantity: 5,
            unit: '条',
            minStock: 30,
            maxStock: 200,
            location: '仓库B',
            supplier: '纺织品供应商',
            price: 25.0,
            status: 'active',
            description: '纯棉毛巾，柔软舒适',
            images: [],
            createdAt: new Date(),
            updatedAt: new Date()
          }
        ]
        
        this.total = this.items.length
        
        return { success: false, error: error.message || '获取库存失败' }
      } finally {
        this.loading = false
      }
    },

    // 创建库存项目
    async createItem(itemData: Omit<InventoryItem, 'id' | 'createdAt' | 'updatedAt'>) {
      try {
        const InventoryItem = AV.Object.extend('InventoryItem')
        const item = new InventoryItem()
        
        // 设置字段
        item.set('name', itemData.name)
        item.set('category', itemData.category)
        item.set('quantity', itemData.quantity)
        item.set('unit', itemData.unit)
        item.set('minStock', itemData.minStock)
        item.set('maxStock', itemData.maxStock || 0)
        item.set('price', itemData.price || 0)
        item.set('supplier', itemData.supplier || '')
        item.set('location', itemData.location || '')
        item.set('description', itemData.description || '')
        item.set('status', itemData.status || 'active')
        item.set('images', itemData.images || [])
        
        const savedItem = await item.save()
        
        const newItem: InventoryItem = {
          id: savedItem.id,
          name: savedItem.get('name'),
          category: savedItem.get('category'),
          quantity: savedItem.get('quantity'),
          unit: savedItem.get('unit'),
          minStock: savedItem.get('minStock'),
          maxStock: savedItem.get('maxStock'),
          price: savedItem.get('price'),
          supplier: savedItem.get('supplier'),
          location: savedItem.get('location'),
          description: savedItem.get('description'),
          status: savedItem.get('status'),
          images: savedItem.get('images'),
          createdAt: savedItem.createdAt,
          updatedAt: savedItem.updatedAt
        }
        
        this.items.unshift(newItem)
        this.total = this.items.length

        // 显示成功通知
        const { success } = useGlobalNotifications()
        success(
          '库存项目创建成功',
          `${newItem.name} 已添加到库存管理系统`
        )

        return { success: true, data: newItem }
      } catch (error: any) {
        console.error('创建库存项目失败:', error)
        return { success: false, error: error.message || '创建库存项目失败' }
      }
    },

    // 删除库存项目
    async deleteItem(id: string) {
      try {
        const item = AV.Object.createWithoutData('InventoryItem', id)
        await item.destroy()
        
        // 从本地数据中移除
        const index = this.items.findIndex(item => item.id === id)
        if (index > -1) {
          this.items.splice(index, 1)
          this.total = this.items.length
        }
        
        // 显示成功通知
        const { success } = useGlobalNotifications()
        success(
          '库存项目删除成功',
          '库存项目已从系统中移除'
        )

        return { success: true }
      } catch (error: any) {
        console.error('删除库存项目失败:', error)
        return { success: false, error: error.message || '删除库存项目失败' }
      }
    },

    // 库存调整
    async adjustStock(id: string, quantity: number, reason: string) {
      try {
        // 获取当前库存
        const itemQuery = new AV.Query('InventoryItem')
        const item = await itemQuery.get(id)
        const currentQuantity = item.get('quantity')

        // 更新库存数量
        item.set('quantity', quantity)
        await item.save()

        // 记录库存变动
        const Transaction = AV.Object.extend('InventoryTransaction')
        const transaction = new Transaction()

        transaction.set('itemId', id)
        transaction.set('type', 'adjust')
        transaction.set('quantity', quantity - currentQuantity)
        transaction.set('beforeQuantity', currentQuantity)
        transaction.set('afterQuantity', quantity)
        transaction.set('reason', reason)
        transaction.set('operator', 'current_user')

        await transaction.save()

        // 更新本地数据
        const localItem = this.items.find(item => item.id === id)
        if (localItem) {
          localItem.quantity = quantity
          localItem.updatedAt = new Date()
        }

        // 显示成功通知
        const { success } = useGlobalNotifications()
        success(
          '库存调整成功',
          `${localItem?.name || '库存项目'} 数量已调整为 ${quantity} ${localItem?.unit || '个'}`
        )

        return { success: true }
      } catch (error: any) {
        console.error('库存调整失败:', error)
        return { success: false, error: error.message || '库存调整失败' }
      }
    },

    // 重置状态
    reset() {
      this.items = []
      this.loading = false
      this.total = 0
    }
  }
})
