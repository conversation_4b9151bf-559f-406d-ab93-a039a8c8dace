<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>发布任务</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- LeanCloud SDK -->
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-4xl mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-3">
                    <a href="index.html" class="text-gray-600 hover:text-gray-800">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                    </a>
                    <h1 class="text-xl font-semibold text-gray-800">发布任务</h1>
                </div>
                <div id="userInfo" class="hidden items-center space-x-4">
                    <span id="realName" class="text-gray-800 font-medium"></span>
                    <button id="logoutBtn" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm">
                        退出登录
                    </button>
                </div>
                <button id="loginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm">
                    登录
                </button>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="max-w-4xl mx-auto px-4 py-6">
        <!-- 未登录提示 -->
        <div id="accessDenied" class="bg-white rounded-lg shadow p-8 text-center">
            <h2 class="text-2xl font-bold text-gray-800 mb-4">需要登录</h2>
            <p class="text-gray-600 mb-6">请先登录后使用任务发布功能</p>
            <button id="promptLoginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg">
                立即登录
            </button>
        </div>

        <!-- 任务发布表单 -->
        <div id="createSection" class="hidden">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <form id="taskForm" class="space-y-6">
                    <!-- 基本信息 -->
                    <div class="border-b border-gray-200 pb-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">基本信息</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700 mb-2">任务标题 *</label>
                                <input type="text" id="taskTitle" required
                                    class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="请输入任务标题">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">优先级 *</label>
                                <select id="taskPriority" required
                                    class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">请选择优先级</option>
                                    <option value="高">高优先级</option>
                                    <option value="中">中优先级</option>
                                    <option value="低">低优先级</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">截止时间 *</label>
                                <input type="datetime-local" id="taskDueDate" required
                                    class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700 mb-2">任务描述 *</label>
                                <textarea id="taskDescription" required rows="4"
                                    class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="请详细描述任务内容、要求和注意事项"></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- 任务分配 -->
                    <div class="border-b border-gray-200 pb-6">
                        <div class="grid grid-cols-1 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">执行人 *</label>
                                <div class="relative">
                                    <select id="taskAssignees" multiple required
                                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-h-[120px]">
                                        <!-- 部门用户列表将通过JS动态填充 -->
                                    </select>
                                    <p class="text-sm text-gray-500 mt-1">按住Ctrl键可选择多个执行人</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 附加信息 -->
                    <div class="pb-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">附加信息</h3>
                        
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">标签</label>
                                <input type="text" id="taskTags"
                                    class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="输入标签，用逗号分隔，如：紧急,重要,客房部">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">附件</label>
                                <input type="file" id="taskAttachments" multiple
                                    accept="image/*,.pdf,.doc,.docx,.xls,.xlsx,.txt"
                                    class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <p class="text-sm text-gray-500 mt-1">支持图片、PDF、Word、Excel等格式，最多5个文件</p>
                            </div>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                        <button type="button" id="cancelBtn" 
                            class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                            取消
                        </button>
                        <button type="submit" id="submitBtn"
                            class="px-6 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors font-medium">
                            发布任务
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </main>

    <!-- 成功提示弹窗 -->
    <div id="successModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
            <div class="p-6 text-center">
                <div class="text-green-500 text-4xl mb-4">✅</div>
                <h2 class="text-xl font-semibold text-gray-800 mb-2">任务发布成功！</h2>
                <p class="text-gray-600 mb-6">任务已成功分配给执行人</p>
                <div class="flex gap-3">
                    <button id="createAnotherBtn" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-3 rounded-lg">
                        继续发布
                    </button>
                    <button id="viewTaskBtn" class="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg">
                        查看任务
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 登录弹窗 -->
    <div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-800">用户登录</h2>
            </div>
            <div class="p-6">
                <form id="loginForm" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                        <input type="text" id="loginUsername" required
                            class="w-full p-3 border border-gray-300 rounded-lg">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                        <input type="password" id="loginPassword" required
                            class="w-full p-3 border border-gray-300 rounded-lg">
                    </div>
                    <div class="flex gap-3 pt-4">
                        <button type="button" id="cancelLogin" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-3 rounded-lg">
                            取消
                        </button>
                        <button type="submit" class="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg">
                            登录
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="../js/config.js?v=2.1"></script>
    <script src="../js/core/error-handler.js?v=2.1"></script>
    <script src="../js/utils.js?v=2.1"></script>
    <script src="../js/auth.js?v=2.1"></script>
    <script src="../js/base-app.js?v=2.1"></script>
    <script>
        class TaskCreateApp extends BaseWorkLogApp {
            constructor() {
                super({
                    pageType: 'task-create',
                    requiredElements: ['createSection']
                });
                
                this.departmentUsers = [];
                this.createdTaskId = null;
            }

            getPageElements() {
                return {
                    // 头部元素
                    userInfo: 'userInfo',
                    realName: 'realName',
                    loginBtn: 'loginBtn',
                    logoutBtn: 'logoutBtn',
                    // 登录弹窗元素
                    loginModal: 'loginModal',
                    loginForm: 'loginForm',
                    loginUsername: 'loginUsername',
                    loginPassword: 'loginPassword',
                    cancelLogin: 'cancelLogin',
                    // 页面内容元素
                    accessDenied: 'accessDenied',
                    createSection: 'createSection',
                    promptLoginBtn: 'promptLoginBtn',
                    // 表单元素
                    taskForm: 'taskForm',
                    taskTitle: 'taskTitle',
                    taskPriority: 'taskPriority',
                    taskDueDate: 'taskDueDate',
                    taskDescription: 'taskDescription',
                    taskAssignees: 'taskAssignees',
                    taskTags: 'taskTags',
                    taskAttachments: 'taskAttachments',
                    cancelBtn: 'cancelBtn',
                    submitBtn: 'submitBtn',
                    // 弹窗元素
                    successModal: 'successModal',
                    createAnotherBtn: 'createAnotherBtn',
                    viewTaskBtn: 'viewTaskBtn'
                };
            }

            bindPageEvents() {
                // 提示登录
                if (this.elements.promptLoginBtn) {
                    this.elements.promptLoginBtn.addEventListener('click', () => this.showLoginModal());
                }

                // 表单提交
                if (this.elements.taskForm) {
                    this.elements.taskForm.addEventListener('submit', (e) => this.handleSubmit(e));
                }

                // 取消按钮
                if (this.elements.cancelBtn) {
                    this.elements.cancelBtn.addEventListener('click', () => {
                        window.location.href = 'index.html';
                    });
                }



                // 成功弹窗按钮
                if (this.elements.createAnotherBtn) {
                    this.elements.createAnotherBtn.addEventListener('click', () => this.createAnother());
                }
                if (this.elements.viewTaskBtn) {
                    this.elements.viewTaskBtn.addEventListener('click', () => this.viewTask());
                }
            }

            onUserLoggedIn() {
                console.log('=== 任务发布页面：用户登录成功 ===');
                console.log('当前用户:', this.currentUser?.get('username'));
                console.log('用户部门:', this.currentUser?.get('department'));
                
                this.showUserInterface();
                this.loadDepartmentUsers();
                this.setDefaultDueDate();
            }

            onUserLoggedOut() {
                this.showAccessDenied();
            }

            showUserInterface() {
                // 调用父类方法更新头部显示
                super.showUserInterface();
                
                // 更新页面内容显示
                this.elements.accessDenied.style.display = 'none';
                this.elements.createSection.style.display = 'block';
            }

            showAccessDenied() {
                // 调用父类方法更新头部显示
                super.showLoginPrompt();
                
                // 更新页面内容显示
                this.elements.accessDenied.style.display = 'block';
                this.elements.createSection.style.display = 'none';
            }

            async loadDepartmentUsers() {
                try {
                    const userDept = this.currentUser.get('department');
                    if (!userDept) {
                        console.warn('当前用户没有设置部门信息');
                        return;
                    }

                    const User = AV.Object.extend('_User');
                    const query = new AV.Query(User);
                    query.equalTo('department', userDept);
                    query.ascending('realName');
                    
                    this.departmentUsers = await query.find();
                    this.renderAssigneeOptions();

                } catch (error) {
                    console.error('加载部门用户失败:', error);
                    this.elements.taskAssignee.innerHTML = '<option value="">加载用户失败</option>';
                }
            }

            renderAssigneeOptions() {
                const options = [];

                this.departmentUsers.forEach(user => {
                    const realName = user.get('realName') || user.get('username');
                    const department = user.get('department') || '';
                    options.push(`<option value="${user.id}" data-name="${realName}" data-dept="${department}">${realName}</option>`);
                });

                this.elements.taskAssignees.innerHTML = options.join('');
            }



            setDefaultDueDate() {
                // 设置默认截止时间为明天18:00
                const tomorrow = new Date();
                tomorrow.setDate(tomorrow.getDate() + 1);
                tomorrow.setHours(18, 0, 0, 0);
                
                const isoString = tomorrow.toISOString().slice(0, 16);
                this.elements.taskDueDate.value = isoString;
            }

            async handleSubmit(e) {
                e.preventDefault();
                
                try {
                    this.elements.submitBtn.disabled = true;
                    this.elements.submitBtn.textContent = '发布中...';

                    // 获取表单数据
                    const formData = this.getFormData();
                    
                    // 验证数据
                    if (!this.validateFormData(formData)) {
                        return;
                    }

                    // 创建任务
                    const task = await this.createTask(formData);
                    this.createdTaskId = task.id;
                    
                    // 显示成功提示
                    this.showSuccessModal();

                } catch (error) {
                    console.error('创建任务失败:', error);
                    alert('创建任务失败: ' + error.message);
                } finally {
                    this.elements.submitBtn.disabled = false;
                    this.elements.submitBtn.textContent = '发布任务';
                }
            }

            getFormData() {
                const selectedOptions = Array.from(this.elements.taskAssignees.selectedOptions);
                const assignees = selectedOptions.map(option => ({
                    id: option.value,
                    name: option.dataset.name,
                    dept: option.dataset.dept
                }));

                const tags = this.elements.taskTags.value
                    .split(',')
                    .map(tag => tag.trim())
                    .filter(tag => tag.length > 0);

                return {
                    title: this.elements.taskTitle.value.trim(),
                    description: this.elements.taskDescription.value.trim(),
                    priority: this.elements.taskPriority.value,
                    dueDate: new Date(this.elements.taskDueDate.value),
                    assignees: assignees,
                    tags: tags
                };
            }

            validateFormData(data) {
                if (!data.title) {
                    alert('请输入任务标题');
                    return false;
                }
                if (!data.description) {
                    alert('请输入任务描述');
                    return false;
                }
                if (!data.priority) {
                    alert('请选择优先级');
                    return false;
                }
                if (!data.assignees || data.assignees.length === 0) {
                    alert('请选择至少一个执行人');
                    return false;
                }
                if (data.dueDate <= new Date()) {
                    alert('截止时间必须晚于当前时间');
                    return false;
                }
                return true;
            }

            async createTask(data) {
                const Task = AV.Object.extend('Task');
                const baseTaskId = 'TASK' + Date.now();
                const tasks = [];

                // 为每个执行人创建一个任务记录
                for (let i = 0; i < data.assignees.length; i++) {
                    const assignee = data.assignees[i];
                    const task = new Task();

                    // 生成任务ID（如果有多个执行人，添加序号）
                    const taskId = data.assignees.length > 1 ? `${baseTaskId}-${i + 1}` : baseTaskId;

                    // 设置任务数据
                    task.set('taskId', taskId);
                    task.set('baseTaskId', baseTaskId); // 用于关联同一批任务
                    task.set('title', data.title);
                    task.set('description', data.description);
                    task.set('priority', data.priority);
                    task.set('status', 'pending');
                    task.set('progress', 0);

                    // 创建人信息
                    task.set('creator', this.currentUser);
                    task.set('creatorName', this.currentUser.get('realName') || this.currentUser.get('username'));
                    task.set('creatorDept', this.currentUser.get('department'));

                    // 执行人信息
                    const assigneeUser = this.departmentUsers.find(user => user.id === assignee.id);
                    if (assigneeUser) {
                        task.set('assignee', assigneeUser);
                    }
                    task.set('assigneeName', assignee.name);
                    task.set('assigneeDept', assignee.dept);

                    // 时间信息
                    task.set('createTime', new Date());
                    task.set('dueDate', data.dueDate);

                    // 其他信息
                    task.set('tags', data.tags);
                    task.set('attachments', []);
                    task.set('comments', []);

                    tasks.push(task);
                }

                // 批量保存所有任务
                await AV.Object.saveAll(tasks);

                // 返回第一个任务（用于跳转）
                return tasks[0];
            }

            showSuccessModal() {
                this.elements.successModal.classList.remove('hidden');
            }

            createAnother() {
                this.elements.successModal.classList.add('hidden');
                this.elements.taskForm.reset();
                this.setDefaultDueDate();
                this.createdTaskId = null;
            }

            viewTask() {
                if (this.createdTaskId) {
                    window.location.href = `task-detail.html?id=${this.createdTaskId}`;
                } else {
                    window.location.href = 'task-list.html';
                }
            }
        }

        // 全局变量
        let taskCreateApp;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            function initApp() {
                if (typeof AV !== 'undefined') {
                    taskCreateApp = new TaskCreateApp();
                    taskCreateApp.init();
                } else {
                    setTimeout(initApp, 100);
                }
            }
            initApp();
        });
    </script>
</body>
</html>
