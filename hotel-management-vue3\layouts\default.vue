<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 移动端顶部导航 -->
    <div class="lg:hidden">
      <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="px-4 py-3 flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <button @click="sidebarOpen = !sidebarOpen" class="text-gray-600">
              <Icon name="mdi:menu" size="24" />
            </button>
            <h1 class="text-lg font-semibold text-gray-800">酒店管理系统</h1>
          </div>
          
          <div class="flex items-center space-x-2">
            <button v-if="!authStore.isLoggedIn" @click="navigateTo('/auth/login')" class="text-sm bg-blue-600 text-white px-3 py-1 rounded">
              登录
            </button>
            <UserMenu v-else />
          </div>
        </div>
      </div>
    </div>

    <div class="flex">
      <!-- 侧边栏 -->
      <div class="hidden lg:flex lg:flex-shrink-0">
        <div class="flex flex-col w-64">
          <AppSidebar />
        </div>
      </div>

      <!-- 移动端侧边栏遮罩 -->
      <div 
        v-if="sidebarOpen" 
        class="fixed inset-0 z-40 lg:hidden"
        @click="sidebarOpen = false"
      >
        <div class="absolute inset-0 bg-gray-600 opacity-75"></div>
      </div>

      <!-- 移动端侧边栏 -->
      <div 
        :class="[
          'fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out lg:hidden',
          sidebarOpen ? 'translate-x-0' : '-translate-x-full'
        ]"
      >
        <AppSidebar @close="sidebarOpen = false" />
      </div>

      <!-- 主内容区域 -->
      <div class="flex-1 min-w-0">
        <!-- 桌面端顶部导航 -->
        <div class="hidden lg:block">
          <AppHeader />
        </div>

        <!-- 页面内容 -->
        <main class="flex-1">
          <slot />
        </main>
      </div>
    </div>

    <!-- 全局通知组件 -->
    <NotificationToast />
    <GlobalNotifications />
  </div>
</template>

<script setup lang="ts">
const authStore = useAuthStore()
const sidebarOpen = ref(false)

// 监听路由变化，关闭移动端侧边栏
const route = useRoute()
watch(() => route.path, () => {
  sidebarOpen.value = false
})

// 监听路由变化，关闭侧边栏
// 登录状态检查已在全局插件中处理
</script>
