# LeanCloud权限配置指南

## 问题说明

当您在管理后台尝试编辑用户信息时，可能会遇到403权限错误：
```
Error: The user cannot be altered by other users or with outdated session. [403 PUT]
```

这是LeanCloud的安全机制，默认情况下用户只能修改自己的信息。

## 🔧 解决方案

### 方案一：配置_User表权限（推荐）

#### 1. 登录LeanCloud控制台
- 访问：https://console.leancloud.cn/
- 选择您的应用

#### 2. 配置_User表权限
1. **进入数据存储**
   - 点击左侧菜单"数据存储" → "结构化数据"
   - 找到并点击"_User"表

2. **设置表权限**
   - 点击"权限"标签
   - 在"update"权限行中：
     ```
     允许所有用户：❌ 取消勾选
     允许登录用户：❌ 取消勾选
     允许指定用户：✅ 勾选，输入您的admin用户ID
     允许指定角色：✅ 勾选，输入"admin"
     ```

3. **保存设置**
   - 点击"保存"按钮

#### 3. 创建admin角色
1. **进入_Role表**
   - 如果没有_Role表，需要先创建
   - 点击"创建Class" → 输入"_Role" → 选择"内置Class"

2. **创建admin角色**
   - 点击"添加行"
   - 设置字段：
     ```
     name: admin
     users: [选择您的用户]
     roles: [留空]
     ```

3. **保存角色**
   - 点击"保存"

### 方案二：使用Master Key（开发环境）

⚠️ **注意**：此方法仅适用于开发环境，生产环境不建议使用。

#### 1. 获取Master Key
- 在LeanCloud控制台 → "设置" → "应用凭证"
- 复制"Master Key"

#### 2. 修改代码
在`js/config.js`中添加：
```javascript
// 仅开发环境使用
if (window.location.hostname === 'localhost') {
    AV.init({
        appId: 'your_app_id',
        appKey: 'your_app_key',
        masterKey: 'your_master_key'
    });
    AV.User.enableUnsafeCurrentUser();
}
```

### 方案三：使用云引擎（高级）

如果您需要更复杂的权限控制，可以使用LeanCloud云引擎：

#### 1. 创建云函数
```javascript
// 在云引擎中创建updateUser函数
AV.Cloud.define('updateUser', async (request) => {
    const { userId, userData } = request.params;
    const currentUser = request.user;
    
    // 检查当前用户是否为admin
    const adminRole = await new AV.Query(AV.Role)
        .equalTo('name', 'admin')
        .equalTo('users', currentUser)
        .first({ useMasterKey: true });
    
    if (!adminRole) {
        throw new AV.Cloud.Error('权限不足');
    }
    
    // 使用Master Key更新用户
    const user = AV.Object.createWithoutData('_User', userId);
    Object.keys(userData).forEach(key => {
        user.set(key, userData[key]);
    });
    
    return await user.save(null, { useMasterKey: true });
});
```

#### 2. 修改前端代码
```javascript
// 在admin.js中使用云函数
try {
    await AV.Cloud.run('updateUser', {
        userId: userId,
        userData: {
            realName: realName,
            phone: phone,
            department: department,
            roles: rolesString
        }
    });
    WorkLogUtils.showMessage('用户信息保存成功', 'success');
} catch (error) {
    WorkLogUtils.showMessage('保存失败: ' + error.message, 'error');
}
```

## 🔍 权限验证

### 检查当前权限设置

1. **查看_User表权限**
   - 进入LeanCloud控制台
   - 数据存储 → _User → 权限
   - 确认update权限配置正确

2. **验证角色设置**
   - 进入_Role表
   - 确认admin角色存在
   - 确认您的用户在admin角色中

3. **测试权限**
   - 在管理后台尝试编辑用户
   - 检查是否还有403错误

### 常见问题排查

#### 问题1：仍然出现403错误
**可能原因**：
- 权限配置未生效
- 用户不在admin角色中
- 角色名称不匹配

**解决方案**：
1. 刷新浏览器缓存
2. 重新登录
3. 检查角色配置

#### 问题2：无法创建_Role表
**可能原因**：
- 权限不足
- 应用配置问题

**解决方案**：
1. 使用应用管理员账号
2. 检查应用权限设置
3. 联系LeanCloud技术支持

#### 问题3：Master Key方式不工作
**可能原因**：
- Master Key错误
- 代码配置问题

**解决方案**：
1. 重新复制Master Key
2. 检查代码语法
3. 确认环境判断正确

## 📋 推荐配置

### 生产环境推荐配置

1. **_User表权限**：
   ```
   create: 允许所有用户 ✅
   find: 允许登录用户 ✅
   update: 允许指定角色(admin) ✅
   delete: 允许指定角色(admin) ✅
   ```

2. **_Role表权限**：
   ```
   create: 允许指定角色(admin) ✅
   find: 允许登录用户 ✅
   update: 允许指定角色(admin) ✅
   delete: 允许指定角色(admin) ✅
   ```

3. **角色设置**：
   - 创建admin角色
   - 将管理员用户添加到admin角色
   - 定期审查角色成员

### 开发环境配置

开发环境可以使用更宽松的权限设置，但要注意：
- 不要在生产环境使用Master Key
- 定期清理测试数据
- 使用不同的应用ID区分环境

## 🔒 安全建议

1. **最小权限原则**：只给用户必要的权限
2. **定期审查**：定期检查用户角色和权限
3. **日志监控**：监控权限相关的操作日志
4. **备份策略**：定期备份重要数据
5. **测试验证**：在生产环境部署前充分测试

---

**注意**：权限配置是安全的重要组成部分，请根据实际需求谨慎配置。
