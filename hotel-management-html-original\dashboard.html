<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>酒店管理系统 - 首页</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .hero-section {
            background: var(--primary-gradient);
            color: white;
            padding: 4rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 50px 50px;
        }

        .stats-card {
            border: none;
            border-radius: 20px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            transition: transform 0.3s, box-shadow 0.3s;
            background: white;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .stats-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .stats-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            margin: 0 auto 1rem;
        }

        .module-card {
            border: none;
            border-radius: 20px;
            transition: all 0.3s;
            background: white;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .module-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .module-icon {
            font-size: 4rem;
            margin-bottom: 1.5rem;
        }

        .quick-action {
            background: white;
            border: none;
            border-radius: 15px;
            padding: 1rem;
            margin: 0.5rem;
            transition: all 0.3s;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .quick-action:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }

        .welcome-text {
            font-size: 3rem;
            font-weight: 300;
            margin-bottom: 1rem;
        }

        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .activity-item {
            padding: 1rem;
            border-left: 4px solid #667eea;
            margin-bottom: 1rem;
            background: white;
            border-radius: 0 10px 10px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .time-badge {
            background: #667eea;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: var(--primary-gradient);">
        <div class="container">
            <a class="navbar-brand" href="dashboard.html">
                <i class="fas fa-hotel me-2"></i>酒店管理系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.html">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="work-log/index.html">工作日志</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="repair/index.html">报修管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="inventory-management/index.html">库存管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin/index.html">系统管理</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> 管理员
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user-cog me-2"></i>个人设置</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-bell me-2"></i>通知中心</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 欢迎区域 -->
    <div class="hero-section">
        <div class="container text-center">
            <h1 class="welcome-text">欢迎使用酒店管理系统</h1>
            <p class="subtitle">高效管理，智能运营，提升酒店服务品质</p>
            <div class="mt-4">
                <span class="time-badge me-3">
                    <i class="fas fa-calendar-day me-1"></i>
                    <span id="currentDate"></span>
                </span>
                <span class="time-badge">
                    <i class="fas fa-clock me-1"></i>
                    <span id="currentTime"></span>
                </span>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 数据统计 -->
        <div class="row mb-5">
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stats-icon" style="background: var(--primary-gradient);">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h3 class="mb-1">156</h3>
                    <p class="text-muted mb-0">今日工作日志</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stats-icon" style="background: var(--secondary-gradient);">
                        <i class="fas fa-tools"></i>
                    </div>
                    <h3 class="mb-1">23</h3>
                    <p class="text-muted mb-0">待处理报修</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stats-icon" style="background: var(--success-gradient);">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <h3 class="mb-1">1,234</h3>
                    <p class="text-muted mb-0">库存物品</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stats-icon" style="background: var(--warning-gradient);">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h3 class="mb-1">8</h3>
                    <p class="text-muted mb-0">库存预警</p>
                </div>
            </div>
        </div>

        <!-- 功能模块 -->
        <div class="row mb-5">
            <div class="col-md-4 mb-4">
                <div class="card module-card h-100">
                    <div class="card-body text-center p-4">
                        <i class="fas fa-clipboard-list module-icon text-primary"></i>
                        <h4 class="card-title mb-3">工作日志</h4>
                        <p class="card-text text-muted mb-4">记录日常工作内容，包括设备维护、客房服务等各类工作日志</p>
                        <a href="work-log/index.html" class="btn btn-primary btn-lg">
                            <i class="fas fa-arrow-right me-2"></i>进入模块
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card module-card h-100">
                    <div class="card-body text-center p-4">
                        <i class="fas fa-wrench module-icon text-danger"></i>
                        <h4 class="card-title mb-3">报修管理</h4>
                        <p class="card-text text-muted mb-4">设备故障报修、工单管理、维修进度跟踪和完成确认</p>
                        <a href="repair/index.html" class="btn btn-danger btn-lg">
                            <i class="fas fa-arrow-right me-2"></i>进入模块
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card module-card h-100">
                    <div class="card-body text-center p-4">
                        <i class="fas fa-warehouse module-icon text-success"></i>
                        <h4 class="card-title mb-3">库存管理</h4>
                        <p class="card-text text-muted mb-4">物品库存管理、出入库记录、盘点和库存预警功能</p>
                        <a href="inventory-management/index.html" class="btn btn-success btn-lg">
                            <i class="fas fa-arrow-right me-2"></i>进入模块
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快捷操作和最近活动 -->
        <div class="row">
            <div class="col-md-6">
                <div class="card module-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>快捷操作
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <button class="quick-action w-100" onclick="location.href='work-log/work-log.html'">
                                    <i class="fas fa-plus-circle text-primary mb-2 d-block"></i>
                                    <small>新建日志</small>
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="quick-action w-100" onclick="location.href='repair/repair.html'">
                                    <i class="fas fa-exclamation-triangle text-danger mb-2 d-block"></i>
                                    <small>紧急报修</small>
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="quick-action w-100" onclick="location.href='inventory/index.html'">
                                    <i class="fas fa-search text-info mb-2 d-block"></i>
                                    <small>库存查询</small>
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="quick-action w-100" onclick="location.href='admin/reports.html'">
                                    <i class="fas fa-chart-bar text-success mb-2 d-block"></i>
                                    <small>数据报表</small>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card module-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-clock me-2"></i>最近活动
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="activity-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <strong>张工程师</strong> 完成了客房301空调维修
                                    <br><small class="text-muted">工单 #R2024001</small>
                                </div>
                                <span class="time-badge">10分钟前</span>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <strong>李主管</strong> 提交了配电房巡检日志
                                    <br><small class="text-muted">设备运行正常</small>
                                </div>
                                <span class="time-badge">25分钟前</span>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <strong>王经理</strong> 审批了毛巾采购申请
                                    <br><small class="text-muted">数量：200条</small>
                                </div>
                                <span class="time-badge">1小时前</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="mt-5 py-4 text-center text-muted">
        <div class="container">
            <p>&copy; 2024 酒店管理系统. 版权所有.</p>
        </div>
    </footer>

    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>

    <!-- LeanCloud SDK - 使用更稳定的 CDN 源 -->
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <!-- 备用 CDN 源 -->
    <script>
        // 检测 LeanCloud SDK 是否加载成功，如果失败则使用备用源
        window.addEventListener('load', function() {
            if (typeof AV === 'undefined') {
                console.log('主 CDN 加载失败，尝试备用源...');
                var script = document.createElement('script');
                script.src = 'https://cdn.bootcdn.net/ajax/libs/leancloud-storage/4.15.2/av-min.js';
                script.onerror = function() {
                    console.error('所有 CDN 源都加载失败，请检查网络连接');
                    alert('网络连接异常，请检查网络后刷新页面');
                };
                document.head.appendChild(script);
            }
        });
    </script>

    <!-- 核心模块 -->
    <script src="js/core/error-handler.js"></script>
    <script src="js/core/performance.js"></script>

    <!-- 安全模块 -->
    <script src="js/security/validator.js"></script>
    <script src="js/security/permission-decorator.js"></script>

    <!-- 现有模块 -->
    <script src="js/config.js"></script>
    <script src="js/permission-manager.js"></script>
    <script src="js/modules/inventory-alert.js"></script>

    <!-- 系统初始化（必须最后加载） -->
    <script src="js/core/system-init.js"></script>
    <script>
        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const dateStr = now.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            });
            const timeStr = now.toLocaleTimeString('zh-CN');
            
            document.getElementById('currentDate').textContent = dateStr;
            document.getElementById('currentTime').textContent = timeStr;
        }

        // 初始化时间显示
        updateTime();
        setInterval(updateTime, 1000);
    </script>
</body>
</html>
