# 后台管理系统功能说明

## 功能概览

后台管理系统现在包含5个主要模块：

1. **用户管理** - 管理系统用户和权限
2. **部门管理** - 管理组织部门结构
3. **角色管理** - 管理用户角色和权限
4. **工单管理** - 管理报修工单（开发中）
5. **数据统计** - 系统使用统计（开发中）

## 详细功能

### 1. 用户管理 👥

#### 功能特性
- ✅ 查看所有用户列表
- ✅ 用户统计（总数、管理员、工程师、普通用户）
- ✅ 添加新用户（管理员直接创建）
- ✅ 编辑用户信息（姓名、电话、部门、角色）
- ✅ 用户搜索功能
- ✅ 用户注册功能（自助注册）

#### 用户信息字段
- 用户名（不可修改）
- 真实姓名
- 联系电话
- 所属部门（从部门管理中选择）
- 用户角色（支持多角色）

#### 添加用户功能
管理员可以直接在用户管理界面添加新用户：

**添加用户表单字段**：
- 用户名（必填，唯一）
- 真实姓名（必填）
- 联系电话（必填，格式验证）
- 所属部门（必填，从部门列表选择）
- 初始密码（必填，最少6位）
- 用户角色（可选，支持多选）

**与注册功能的区别**：
- **管理员添加**：在用户管理界面，管理员为他人创建账号
- **用户注册**：在登录界面，用户自助注册账号

#### 权限控制
- 只有admin角色可以访问用户管理
- 可以为用户分配admin、engineer角色
- 普通用户无特殊角色

### 2. 部门管理 🏢

#### 功能特性
- ✅ 查看所有部门列表
- ✅ 部门统计（总数、最大人数、平均人数）
- ✅ 添加新部门
- ✅ 编辑部门信息
- ✅ 删除自定义部门
- ✅ 初始化默认部门

#### 部门信息字段
- 部门名称
- 部门描述
- 创建时间
- 人员数量（自动统计）
- 类型（系统/自定义）

#### 默认部门
系统预设8个部门：
- 前厅部：负责客人接待和前台服务
- 客房部：负责客房清洁和维护
- 餐饮部：负责餐厅和宴会服务
- 工程部：负责设备维护和维修
- 保安部：负责酒店安全保卫
- 财务部：负责财务管理和会计
- 人事部：负责人力资源管理
- 销售部：负责市场营销和销售

#### 部门管理规则
- 系统部门不能删除，只能编辑
- 自定义部门可以删除
- 删除部门前会确认操作
- 部门人数实时统计

### 3. 角色管理 🔐

#### 功能特性
- ✅ 查看所有角色列表
- ✅ 角色统计（总数、系统角色、自定义角色）
- ✅ 角色权限展示
- ✅ 用户数量统计
- 🚧 自定义角色管理（开发中）

#### 系统默认角色

| 角色 | 显示名称 | 权限 | 说明 |
|------|----------|------|------|
| admin | 管理员 | 管理员 + 工程师 + 报修 | 系统管理员，拥有所有权限 |
| engineer | 工程师 | 工程师 + 报修 | 工程师，可以处理工单 |
| reporter | 普通用户 | 报修 | 普通用户，可以提交报修 |

#### 权限说明
- **管理员权限**：用户管理、部门管理、角色管理、系统配置
- **工程师权限**：工单管理、工单处理、工单查看
- **报修权限**：提交报修、查看自己的工单

### 4. 工单管理 🔧

#### 当前状态
🚧 **开发中** - 基础框架已完成，详细功能开发中

#### 计划功能
- 查看所有工单列表
- 工单状态管理
- 工单分配和处理
- 工单统计和报表

### 5. 数据统计 📊

#### 当前状态
🚧 **开发中** - 预留功能模块

#### 计划功能
- 用户活跃度统计
- 工单处理统计
- 部门工作量统计
- 系统使用报表

## 使用流程

### 管理员首次使用

1. **登录系统**
   ```
   访问 admin.html → 使用admin账号登录
   ```

2. **初始化部门**
   ```
   部门管理 → 点击"初始化默认部门" → 确认创建
   ```

3. **管理用户**
   ```
   用户管理 → 编辑用户 → 设置部门和角色 → 保存
   ```

4. **完善系统**
   ```
   根据需要添加自定义部门 → 分配用户角色 → 系统投入使用
   ```

### 日常管理操作

1. **用户管理**
   - 使用"添加用户"功能为新员工创建账号
   - 新用户注册后，分配部门和角色
   - 定期检查用户信息完整性
   - 根据需要调整用户权限

2. **部门管理**
   - 根据组织变化添加新部门
   - 更新部门描述和信息
   - 监控各部门人员分布

3. **角色管理**
   - 查看角色使用情况
   - 了解权限分配状态
   - 规划权限体系

## 数据集成

### 与报修系统的集成

1. **用户信息同步**
   - 报修时自动获取用户的部门信息
   - 联系方式从用户管理中统一维护

2. **权限控制**
   - 工程师角色可以访问工单管理
   - 管理员可以查看所有数据

3. **数据一致性**
   - 部门信息在整个系统中保持一致
   - 用户角色实时生效

### 数据流向

```
用户注册 → 管理员分配部门和角色 → 用户使用系统功能
    ↓
部门管理 ← 用户管理 → 角色管理
    ↓           ↓         ↓
报修系统 ← 工单管理 → 权限控制
```

## 技术特性

### 响应式设计
- 支持桌面和移动设备
- 自适应布局和交互

### 实时数据
- 用户统计实时更新
- 部门人数自动计算
- 角色分配即时生效

### 安全性
- 严格的权限控制
- 数据验证和错误处理
- 操作确认机制

### 可扩展性
- 模块化设计
- 易于添加新功能
- 支持自定义扩展

## 开发计划

### 近期计划
- [ ] 完善角色管理功能
- [ ] 实现工单管理模块
- [ ] 添加数据统计功能

### 长期计划
- [ ] 添加操作日志
- [ ] 实现数据导出
- [ ] 支持批量操作
- [ ] 添加系统配置

---

**注意**：本系统为演示项目，生产环境使用时请根据实际需求调整功能和安全配置。
