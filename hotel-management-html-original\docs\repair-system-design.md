# 报修工单系统设计方案

## 1. 系统概述

完整的报修工单管理系统，支持从报修提交到工单完成的全流程管理，并预留企业微信集成接口。

## 2. 数据模型设计

### 2.1 工单表 (RepairOrder)
```javascript
{
  objectId: String,           // 工单ID
  orderNumber: String,        // 工单号（自动生成）
  
  // 报修信息
  title: String,              // 报修标题
  description: String,        // 报修描述
  location: String,           // 报修位置
  urgency: String,           // 紧急程度 (低|中|高|紧急)
  category: String,          // 报修类别 (电气|水暖|空调|其他)
  
  // 报修人信息
  reporter: Pointer,         // 报修人用户指针
  reporterName: String,      // 报修人姓名
  reporterPhone: String,     // 报修人电话
  reporterDept: String,      // 报修人部门
  
  // 工单状态
  status: String,            // 状态 (pending|accepted|processing|completed|cancelled)
  priority: Number,          // 优先级 (1-5)
  
  // 处理人信息
  assignee: Pointer,         // 接单人用户指针
  assigneeName: String,      // 接单人姓名
  
  // 时间记录
  reportTime: Date,          // 报修时间
  acceptTime: Date,          // 接单时间
  startTime: Date,           // 开始处理时间
  completeTime: Date,        // 完成时间
  
  // 处理信息
  solution: String,          // 处理方案
  result: String,            // 处理结果
  materials: Array,          // 使用材料
  cost: Number,              // 费用
  
  // 评价信息
  rating: Number,            // 评分 (1-5)
  feedback: String,          // 反馈意见
  
  // 附件
  images: Array,             // 图片附件
  
  // 企业微信相关
  wechatMessageId: String,   // 企业微信消息ID
  notificationSent: Boolean, // 是否已发送通知
  
  createdAt: Date,
  updatedAt: Date
}
```

### 2.2 工单状态流转
```
pending (待接单) 
    ↓ [接单]
accepted (已接单)
    ↓ [开始处理]
processing (处理中)
    ↓ [完成工单]
completed (已完成)

任何状态都可以 → cancelled (已取消)
```

### 2.3 用户角色权限
```javascript
// 用户角色
roles: {
  "user": "普通用户",           // 只能提交报修
  "engineer": "工程人员",       // 可以接单处理
  "admin": "管理员"            // 全部权限
}

// 权限矩阵
permissions: {
  "user": ["create", "view_own"],
  "engineer": ["view_all", "accept", "process", "complete"],
  "admin": ["*"]
}
```

## 3. 页面设计

### 3.1 报修提交页面 (repair.html)
- 用户登录验证
- 报修表单优化
- 图片上传功能
- 提交确认

### 3.2 工单管理页面 (repair-manage.html)
- 待接单工单列表
- 工单详情查看
- 接单操作
- 状态更新

### 3.3 工单处理页面 (repair-process.html)
- 工单详情展示
- 处理进度更新
- 完成工单操作
- 时间统计

### 3.4 工单查询页面 (repair-query.html)
- 工单搜索
- 状态筛选
- 历史记录

## 4. 核心功能流程

### 4.1 报修流程
```
1. 用户登录 → 2. 填写报修信息 → 3. 提交工单 
   ↓
4. 生成工单号 → 5. 发送企业微信通知 → 6. 等待接单
```

### 4.2 接单流程
```
1. 工程人员收到通知 → 2. 查看工单详情 → 3. 点击接单
   ↓
4. 更新工单状态 → 5. 开始计时 → 6. 通知报修人
```

### 4.3 处理流程
```
1. 开始处理 → 2. 更新处理进度 → 3. 完成工单
   ↓
4. 填写处理结果 → 5. 结束计时 → 6. 通知报修人
```

## 5. 企业微信集成设计

### 5.1 消息推送场景
1. **新工单通知** → 工程部群
2. **接单通知** → 报修人
3. **完成通知** → 报修人
4. **超时提醒** → 工程部群

### 5.2 消息模板
```javascript
// 新工单通知
{
  msgtype: "textcard",
  textcard: {
    title: "新报修工单 #${orderNumber}",
    description: "报修人：${reporterName}\n位置：${location}\n问题：${description}",
    url: "${baseUrl}/repair-manage.html?id=${orderId}",
    btntxt: "立即处理"
  }
}

// 完成通知
{
  msgtype: "textcard", 
  textcard: {
    title: "工单已完成 #${orderNumber}",
    description: "处理人：${assigneeName}\n处理时间：${duration}\n结果：${result}",
    url: "${baseUrl}/repair-query.html?id=${orderId}",
    btntxt: "查看详情"
  }
}
```

### 5.3 API接口设计
```javascript
// 发送企业微信消息
POST /api/wechat/send-message
{
  type: "new_order|accepted|completed",
  orderId: "工单ID",
  recipients: ["用户ID列表"],
  data: { /* 消息数据 */ }
}

// 企业微信回调处理
POST /api/wechat/callback
{
  action: "click_button",
  orderId: "工单ID", 
  userId: "用户ID"
}
```

## 6. 实现计划

### 阶段一：核心功能 (当前)
- ✅ 数据模型设计
- 🔄 完善报修提交页面
- ⏳ 创建工单管理页面
- ⏳ 实现接单处理逻辑

### 阶段二：高级功能
- ⏳ 时间统计和SLA管理
- ⏳ 工单查询和报表
- ⏳ 用户评价系统

### 阶段三：企业微信集成
- ⏳ 企业微信API对接
- ⏳ 消息推送实现
- ⏳ 回调处理逻辑

## 7. 技术要点

### 7.1 状态管理
- 使用状态机模式
- 状态变更日志记录
- 并发控制

### 7.2 时间计算
- 工作时间统计
- SLA超时监控
- 响应时间分析

### 7.3 权限控制
- 基于角色的访问控制
- 数据行级权限
- 操作审计日志

### 7.4 性能优化
- 工单索引优化
- 分页查询
- 缓存策略

## 8. 扩展功能

1. **移动端适配**：响应式设计
2. **离线支持**：PWA技术
3. **数据分析**：工单统计报表
4. **自动派单**：智能分配算法
5. **知识库**：常见问题解决方案

## 9. 安全考虑

1. **数据加密**：敏感信息加密存储
2. **访问控制**：严格的权限验证
3. **审计日志**：操作记录追踪
4. **防护措施**：防止恶意提交

这个设计方案完全可行，我们可以先实现核心功能，然后逐步添加企业微信集成。

## 10. 实现进度

### 已完成 ✅
1. **系统架构设计** - 完整的数据模型和状态流转设计
2. **报修提交页面** - 用户友好的报修表单，支持图片上传
3. **工单管理页面** - 工程部人员的工单管理界面

### 进行中 🔄
4. **工单管理逻辑** - 接单、处理、完成工单的核心功能

### 待实现 ⏳
5. **时间计算和状态管理** - 工单时间跟踪
6. **企业微信集成** - 消息推送和通知
7. **工单查询页面** - 历史工单查询
8. **数据统计报表** - 工单处理效率分析

这个报修工单系统将大大提升酒店工程部的工作效率和响应速度！

## 11. 用户管理系统

### 用户数据模型扩展
```javascript
// 扩展用户字段
User: {
  username: String,      // 用户名
  realName: String,      // 真实姓名
  phone: String,         // 联系电话
  department: String,    // 所属部门
  roles: Array,          // 用户角色 ["admin", "engineer"]
  createdAt: Date,       // 注册时间
  updatedAt: Date        // 更新时间
}
```

### 后台管理功能
1. **用户管理**：
   - 查看所有用户列表
   - 编辑用户信息（姓名、电话、部门、角色）
   - 用户统计（总数、管理员、工程师、普通用户）
   - 用户搜索和筛选

2. **权限管理**：
   - 分配用户角色（管理员、工程师、普通用户）
   - 角色权限控制

3. **数据完整性**：
   - 确保所有用户都有完整的联系信息
   - 报修时自动获取用户信息，无需手动填写
