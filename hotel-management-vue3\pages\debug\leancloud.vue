<template>
  <div class="max-w-4xl mx-auto px-4 py-8">
    <h1 class="text-2xl font-bold mb-6">LeanCloud 调试信息</h1>
    
    <div class="space-y-6">
      <!-- 初始化状态 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-lg font-semibold mb-4">初始化状态</h2>
        <div class="space-y-2">
          <div class="flex items-center">
            <span class="w-32">AV对象:</span>
            <span :class="avStatus.exists ? 'text-green-600' : 'text-red-600'">
              {{ avStatus.exists ? '✓ 存在' : '✗ 不存在' }}
            </span>
          </div>
          <div class="flex items-center">
            <span class="w-32">配置状态:</span>
            <span :class="avStatus.configured ? 'text-green-600' : 'text-red-600'">
              {{ avStatus.configured ? '✓ 已配置' : '✗ 未配置' }}
            </span>
          </div>
          <div class="flex items-center">
            <span class="w-32">应用ID:</span>
            <span class="font-mono text-sm">{{ config.appId || '未设置' }}</span>
          </div>
          <div class="flex items-center">
            <span class="w-32">服务器URL:</span>
            <span class="font-mono text-sm">{{ config.serverUrl || '未设置' }}</span>
          </div>
        </div>
      </div>

      <!-- 环境变量 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-lg font-semibold mb-4">环境变量</h2>
        <div class="space-y-2">
          <div class="flex items-center">
            <span class="w-48">LEANCLOUD_APP_ID:</span>
            <span class="font-mono text-sm">{{ runtimeConfig.public.leancloudAppId || '未设置' }}</span>
          </div>
          <div class="flex items-center">
            <span class="w-48">LEANCLOUD_APP_KEY:</span>
            <span class="font-mono text-sm">{{ runtimeConfig.public.leancloudAppKey ? '已设置' : '未设置' }}</span>
          </div>
          <div class="flex items-center">
            <span class="w-48">LEANCLOUD_SERVER_URL:</span>
            <span class="font-mono text-sm">{{ runtimeConfig.public.leancloudServerUrl || '未设置' }}</span>
          </div>
        </div>
      </div>

      <!-- 测试功能 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-lg font-semibold mb-4">功能测试</h2>
        <div class="space-y-4">
          <button 
            @click="testCurrentUser"
            class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            测试获取当前用户
          </button>
          
          <button 
            @click="testQuery"
            class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 ml-4"
          >
            测试数据查询
          </button>
          
          <button 
            @click="reinitialize"
            class="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700 ml-4"
          >
            重新初始化
          </button>
        </div>
        
        <div v-if="testResults.length > 0" class="mt-4">
          <h3 class="font-medium mb-2">测试结果:</h3>
          <div class="bg-gray-100 p-4 rounded max-h-64 overflow-y-auto">
            <div v-for="(result, index) in testResults" :key="index" class="mb-2">
              <span class="text-sm text-gray-500">{{ result.time }}</span>
              <div :class="result.success ? 'text-green-600' : 'text-red-600'">
                {{ result.message }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import AV from 'leancloud-storage'

definePageMeta({
  title: 'LeanCloud调试'
})

const runtimeConfig = useRuntimeConfig()
const testResults = ref<Array<{time: string, message: string, success: boolean}>>([])

// AV状态检查
const avStatus = computed(() => {
  try {
    const exists = typeof AV !== 'undefined' && AV !== null
    const configured = exists && AV._config && AV._config.applicationId
    return { exists, configured }
  } catch (error) {
    return { exists: false, configured: false }
  }
})

// 配置信息
const config = computed(() => {
  try {
    return {
      appId: AV._config?.applicationId || '',
      serverUrl: AV._config?.serverURL || ''
    }
  } catch (error) {
    return { appId: '', serverUrl: '' }
  }
})

// 添加测试结果
const addTestResult = (message: string, success: boolean) => {
  testResults.value.unshift({
    time: new Date().toLocaleTimeString(),
    message,
    success
  })
}

// 测试获取当前用户
const testCurrentUser = async () => {
  try {
    const currentUser = AV.User.current()
    addTestResult(`获取当前用户成功: ${currentUser ? currentUser.getUsername() : '无用户'}`, true)
  } catch (error: any) {
    addTestResult(`获取当前用户失败: ${error.message}`, false)
  }
}

// 测试数据查询
const testQuery = async () => {
  try {
    const query = new AV.Query('WorkLog')
    query.limit(1)
    const results = await query.find()
    addTestResult(`数据查询成功: 找到 ${results.length} 条记录`, true)
  } catch (error: any) {
    addTestResult(`数据查询失败: ${error.message}`, false)
  }
}

// 重新初始化
const reinitialize = () => {
  try {
    AV.init({
      appId: runtimeConfig.public.leancloudAppId,
      appKey: runtimeConfig.public.leancloudAppKey,
      serverURL: runtimeConfig.public.leancloudServerUrl
    })
    addTestResult('重新初始化成功', true)
  } catch (error: any) {
    addTestResult(`重新初始化失败: ${error.message}`, false)
  }
}

// 页面标题
useHead({
  title: 'LeanCloud调试 - 酒店管理系统'
})
</script>
