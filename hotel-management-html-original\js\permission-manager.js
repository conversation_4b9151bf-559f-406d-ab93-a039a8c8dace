/**
 * 统一权限管理系统
 * 提供统一的权限检查、角色管理和访问控制功能
 */
class PermissionManager {
    constructor() {
        this.currentUser = null;
        this.userRoles = [];
        this.permissions = {};
        this.permissionCache = new Map();
        this.cacheExpiry = 5 * 60 * 1000; // 5分钟缓存
    }

    /**
     * 初始化权限管理器
     */
    async init() {
        try {
            // 检查LeanCloud是否可用且已初始化
            if (typeof AV === 'undefined') {
                throw new Error('LeanCloud SDK未加载');
            }

            if (!AV.applicationId) {
                throw new Error('LeanCloud未初始化');
            }

            this.currentUser = AV.User.current();
            if (this.currentUser) {
                await this.loadUserPermissions();
            }

            console.log('权限管理器初始化完成');
        } catch (error) {
            console.error('权限管理器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 加载当前用户的权限信息
     */
    async loadUserPermissions() {
        if (!this.currentUser) return;

        try {
            // 获取用户角色代码
            const userRoleCodes = this.currentUser.get('roles') || [];
            
            // 如果是字符串格式，尝试解析为数组
            let roleCodes = [];
            if (typeof userRoleCodes === 'string') {
                try {
                    roleCodes = JSON.parse(userRoleCodes);
                } catch (e) {
                    roleCodes = [userRoleCodes];
                }
            } else if (Array.isArray(userRoleCodes)) {
                roleCodes = userRoleCodes;
            }

            // 查询角色详细信息
            if (roleCodes.length > 0) {
                const roleQuery = new AV.Query('UserRole');
                roleQuery.containedIn('code', roleCodes);
                roleQuery.equalTo('status', 'active');
                this.userRoles = await roleQuery.find();

                // 合并所有角色的权限
                this.permissions = this.mergePermissions(this.userRoles);
            }

            console.log('用户权限加载完成:', this.permissions);
        } catch (error) {
            console.error('加载用户权限失败:', error);
            // 降级处理：使用简单角色检查
            this.permissions = this.getFallbackPermissions();
        }
    }

    /**
     * 合并多个角色的权限
     */
    mergePermissions(roles) {
        const mergedPermissions = {};

        roles.forEach(role => {
            const rolePermissions = role.get('permissions') || {};
            
            Object.keys(rolePermissions).forEach(module => {
                if (!mergedPermissions[module]) {
                    mergedPermissions[module] = {};
                }

                Object.keys(rolePermissions[module]).forEach(subModule => {
                    if (!mergedPermissions[module][subModule]) {
                        mergedPermissions[module][subModule] = new Set();
                    }

                    // 合并权限（取并集）
                    rolePermissions[module][subModule].forEach(permission => {
                        mergedPermissions[module][subModule].add(permission);
                    });
                });
            });
        });

        // 将 Set 转换为数组
        Object.keys(mergedPermissions).forEach(module => {
            Object.keys(mergedPermissions[module]).forEach(subModule => {
                mergedPermissions[module][subModule] = Array.from(mergedPermissions[module][subModule]);
            });
        });

        return mergedPermissions;
    }

    /**
     * 获取降级权限（兼容旧系统）
     */
    getFallbackPermissions() {
        if (!this.currentUser) return {};

        const userRoles = this.currentUser.get('roles') || [];
        let roleCodes = [];

        if (typeof userRoles === 'string') {
            try {
                roleCodes = JSON.parse(userRoles);
            } catch (e) {
                roleCodes = [userRoles];
            }
        } else if (Array.isArray(userRoles)) {
            roleCodes = userRoles;
        }

        // 基于角色代码提供基本权限
        if (roleCodes.includes('admin') || roleCodes.includes('super_admin')) {
            return this.getAdminPermissions();
        } else {
            return this.getUserPermissions();
        }
    }

    /**
     * 检查用户是否有特定权限
     * @param {string} module - 模块名称（如 'system', 'inventory'）
     * @param {string} subModule - 子模块名称（如 'user_management', 'products'）
     * @param {string} action - 操作类型（如 'view', 'create', 'edit', 'delete'）
     * @returns {boolean}
     */
    hasPermission(module, subModule, action) {
        if (!this.currentUser) return false;

        // 超级管理员拥有所有权限
        if (this.isSuperAdmin()) return true;

        // 检查具体权限
        const modulePermissions = this.permissions[module];
        if (!modulePermissions) return false;

        const subModulePermissions = modulePermissions[subModule];
        if (!subModulePermissions) return false;

        return subModulePermissions.includes(action);
    }

    /**
     * 检查用户是否有模块访问权限
     * @param {string} module - 模块名称
     * @returns {boolean}
     */
    hasModuleAccess(module) {
        if (!this.currentUser) return false;
        if (this.isSuperAdmin()) return true;

        return !!this.permissions[module];
    }

    /**
     * 检查用户是否已登录
     * @returns {boolean}
     */
    isLoggedIn() {
        return !!this.currentUser;
    }

    /**
     * 获取当前用户
     * @returns {Object|null}
     */
    getCurrentUser() {
        return this.currentUser;
    }

    /**
     * 检查用户是否是管理员
     * @returns {boolean}
     */
    isAdmin() {
        if (!this.currentUser) return false;

        const userRoles = this.getUserRoleCodes();
        return userRoles.includes('admin') || userRoles.includes('super_admin');
    }

    /**
     * 检查用户是否是超级管理员
     * @returns {boolean}
     */
    isSuperAdmin() {
        if (!this.currentUser) return false;

        const userRoles = this.getUserRoleCodes();
        return userRoles.includes('super_admin');
    }

    /**
     * 获取用户角色代码数组
     * @returns {Array}
     */
    getUserRoleCodes() {
        if (!this.currentUser) return [];

        const userRoles = this.currentUser.get('roles') || [];
        
        if (typeof userRoles === 'string') {
            try {
                return JSON.parse(userRoles);
            } catch (e) {
                return [userRoles];
            }
        } else if (Array.isArray(userRoles)) {
            return userRoles;
        }

        return [];
    }

    /**
     * 获取用户角色信息
     * @returns {Array}
     */
    getUserRoles() {
        return this.userRoles;
    }

    /**
     * 获取用户权限信息
     * @returns {Object}
     */
    getUserPermissions() {
        return {
            'work': {
                'logs': ['view', 'create', 'edit']
            },
            'repair': {
                'orders': ['view', 'create']
            },
            'inventory': {
                'products': ['view'],
                'inbound': ['view'],
                'outbound': ['view']
            }
        };
    }

    /**
     * 获取管理员权限
     * @returns {Object}
     */
    getAdminPermissions() {
        return {
            'system': {
                'user_management': ['view', 'create', 'edit'],
                'role_management': ['view', 'create', 'edit', 'delete'],
                'system_settings': ['view', 'edit'],
                'logs': ['view', 'export']
            },
            'inventory': {
                'products': ['view', 'create', 'edit', 'delete'],
                'warehouses': ['view', 'create', 'edit', 'delete'],
                'inbound': ['view', 'create', 'edit', 'delete'],
                'outbound': ['view', 'create', 'edit', 'delete'],
                'transfer': ['view', 'create', 'edit', 'delete'],
                'count': ['view', 'create', 'edit', 'delete']
            },
            'work': {
                'logs': ['view', 'create', 'edit', 'delete'],
                'reports': ['view', 'export']
            },
            'repair': {
                'orders': ['view', 'create', 'edit', 'delete'],
                'reports': ['view', 'export']
            }
        };
    }

    /**
     * 刷新用户权限
     */
    async refreshPermissions() {
        this.permissionCache.clear();
        await this.loadUserPermissions();
    }

    /**
     * 权限检查装饰器
     * @param {string} module 
     * @param {string} subModule 
     * @param {string} action 
     * @returns {Function}
     */
    requirePermission(module, subModule, action) {
        return (target, propertyName, descriptor) => {
            const method = descriptor.value;
            descriptor.value = function(...args) {
                if (!window.permissionManager?.hasPermission(module, subModule, action)) {
                    throw new Error(`权限不足：需要 ${module}.${subModule}.${action} 权限`);
                }
                return method.apply(this, args);
            };
        };
    }

    /**
     * 显示权限不足提示
     * @param {string} message 
     */
    showPermissionDenied(message = '您没有权限执行此操作') {
        if (typeof WorkLogUtils !== 'undefined' && WorkLogUtils.showMessage) {
            WorkLogUtils.showMessage(message, 'error');
        } else {
            alert(message);
        }
    }

    /**
     * 根据权限隐藏/显示页面元素
     * @param {string} selector - CSS选择器
     * @param {string} module 
     * @param {string} subModule 
     * @param {string} action 
     */
    toggleElementByPermission(selector, module, subModule, action) {
        const elements = document.querySelectorAll(selector);
        const hasPermission = this.hasPermission(module, subModule, action);

        elements.forEach(element => {
            if (hasPermission) {
                element.style.display = '';
                element.removeAttribute('disabled');
            } else {
                element.style.display = 'none';
                element.setAttribute('disabled', 'true');
            }
        });
    }

    /**
     * 权限检查中间件
     * @param {string} module 
     * @param {string} subModule 
     * @param {string} action 
     * @returns {boolean}
     */
    checkPermissionMiddleware(module, subModule, action) {
        if (!this.hasPermission(module, subModule, action)) {
            this.showPermissionDenied(`需要 ${module} > ${subModule} > ${action} 权限`);
            return false;
        }
        return true;
    }
}

// 创建全局权限管理器实例
window.permissionManager = new PermissionManager();

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async function() {
    if (typeof AV !== 'undefined') {
        await window.permissionManager.init();
        console.log('权限管理器初始化完成');
    }
});

// 导出权限管理器（如果支持模块化）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PermissionManager;
}
