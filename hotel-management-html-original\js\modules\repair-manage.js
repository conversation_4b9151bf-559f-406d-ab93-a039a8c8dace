/**
 * 工单管理模块 - 实现工单管理功能
 * 继承自BaseWorkLogApp，实现工单管理特定功能
 */

class RepairManageApp extends BaseWorkLogApp {
    constructor() {
        super({
            pageType: 'repair-manage',
            requiredElements: ['ordersList', 'statsPanel']
        });
        
        // 绑定方法上下文
        this.loadOrders = this.loadOrders.bind(this);
        this.refreshOrders = this.refreshOrders.bind(this);
        this.handleAcceptOrder = this.handleAcceptOrder.bind(this);
        this.showOrderDetail = this.showOrderDetail.bind(this);
        
        // 自动刷新相关
        this.autoRefreshInterval = null;
        this.isAutoRefresh = false;
        
        // 筛选条件
        this.filters = {
            status: '',
            urgency: '',
            category: ''
        };
    }

    /**
     * 获取页面特定的DOM元素
     */
    getPageElements() {
        return {
            // 页面区域
            welcomeSection: 'welcomeSection',
            statsPanel: 'statsPanel',
            controlPanel: 'controlPanel',
            ordersList: 'ordersList',
            loadingIndicator: 'loadingIndicator',
            emptyState: 'emptyState',
            
            // 统计数据
            pendingCount: 'pendingCount',
            processingCount: 'processingCount',
            todayCompletedCount: 'todayCompletedCount',
            totalCount: 'totalCount',
            
            // 筛选控件
            statusFilter: 'statusFilter',
            urgencyFilter: 'urgencyFilter',
            categoryFilter: 'categoryFilter',
            
            // 按钮
            refreshBtn: 'refreshBtn',
            autoRefreshBtn: 'autoRefreshBtn',
            welcomeLoginBtn: 'welcomeLoginBtn',
            
            // 弹窗
            orderModal: 'orderModal',
            modalTitle: 'modalTitle',
            modalContent: 'modalContent',
            closeModal: 'closeModal',
            
            // 用户信息
            realName: 'realName',
            userInfo: 'userInfo',
            loginBtn: 'loginBtn',
            logoutBtn: 'logoutBtn',
            refreshTime: 'refreshTime'
        };
    }

    /**
     * 绑定页面特定事件
     */
    bindPageEvents() {
        // 刷新按钮
        if (this.elements.refreshBtn) {
            this.elements.refreshBtn.addEventListener('click', this.refreshOrders);
        }
        
        // 自动刷新按钮
        if (this.elements.autoRefreshBtn) {
            this.elements.autoRefreshBtn.addEventListener('click', this.toggleAutoRefresh.bind(this));
        }
        
        // 筛选事件
        ['statusFilter', 'urgencyFilter', 'categoryFilter'].forEach(filterId => {
            if (this.elements[filterId]) {
                this.elements[filterId].addEventListener('change', () => {
                    this.updateFilters();
                    this.loadOrders();
                });
            }
        });
        
        // 欢迎页面登录按钮
        if (this.elements.welcomeLoginBtn) {
            this.elements.welcomeLoginBtn.addEventListener('click', () => {
                this.showLoginModal();
            });
        }
        
        // 关闭弹窗
        if (this.elements.closeModal) {
            this.elements.closeModal.addEventListener('click', () => {
                this.hideOrderModal();
            });
        }
        
        // 点击弹窗外部关闭
        if (this.elements.orderModal) {
            this.elements.orderModal.addEventListener('click', (e) => {
                if (e.target === this.elements.orderModal) {
                    this.hideOrderModal();
                }
            });
        }
    }

    /**
     * 用户登录后的回调
     */
    onUserLoggedIn() {
        this.showUserInterface();
        this.loadOrders();
        this.updateRefreshTime();
    }

    /**
     * 用户登出后的回调
     */
    onUserLoggedOut() {
        this.showLoginInterface();
        this.stopAutoRefresh();
    }

    /**
     * 显示用户界面
     */
    showUserInterface() {
        super.showUserInterface();
        
        if (this.elements.statsPanel) {
            this.elements.statsPanel.classList.remove('hidden');
        }
        
        if (this.elements.controlPanel) {
            this.elements.controlPanel.classList.remove('hidden');
        }
        
        if (this.elements.welcomeSection) {
            this.elements.welcomeSection.classList.add('hidden');
        }
    }

    /**
     * 显示登录界面
     */
    showLoginInterface() {
        super.showLoginPrompt();
        
        if (this.elements.statsPanel) {
            this.elements.statsPanel.classList.add('hidden');
        }
        
        if (this.elements.controlPanel) {
            this.elements.controlPanel.classList.add('hidden');
        }
        
        if (this.elements.ordersList) {
            this.elements.ordersList.classList.add('hidden');
        }
        
        if (this.elements.welcomeSection) {
            this.elements.welcomeSection.classList.remove('hidden');
        }
    }

    /**
     * 检查当前用户是否为工程师
     */
    isCurrentUserEngineer() {
        if (!this.currentUser) return false;
        const userRoles = this.currentUser.get('roles') || [];
        return userRoles.includes && (userRoles.includes('engineer') || userRoles.includes('admin'));
    }

    /**
     * 更新筛选条件
     */
    updateFilters() {
        this.filters.status = this.elements.statusFilter?.value || '';
        this.filters.urgency = this.elements.urgencyFilter?.value || '';
        this.filters.category = this.elements.categoryFilter?.value || '';
    }

    /**
     * 加载工单列表
     */
    async loadOrders() {
        if (!this.currentUser) {
            console.log('用户未登录，无法加载工单');
            return;
        }

        try {
            this.showLoading(true);

            // 查询工单
            const RepairOrder = AV.Object.extend('RepairOrder');
            const query = new AV.Query(RepairOrder);

            // 包含报修人信息
            query.include('reporter');
            query.include('assignee');

            // 应用筛选条件
            if (this.filters.status) {
                query.equalTo('status', this.filters.status);
            }
            if (this.filters.urgency) {
                query.equalTo('urgency', this.filters.urgency);
            }
            if (this.filters.category) {
                query.equalTo('category', this.filters.category);
            }

            // 按优先级和创建时间排序
            query.descending('priority');
            query.descending('createdAt');
            query.limit(100);

            const orders = await query.find();

            // 更新统计数据
            this.updateStats(orders);

            // 渲染工单列表
            this.renderOrders(orders);

            this.updateRefreshTime();

        } catch (error) {
            console.error('加载工单失败:', error);

            // 检查是否是表不存在的错误
            if (error.code === 101 || error.message.includes("doesn't exists")) {
                console.log('RepairOrder表不存在，显示初始化提示');
                this.showInitializationPrompt();
            } else {
                WorkLogUtils.showMessage('加载工单失败: ' + error.message, 'error');
            }
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 显示/隐藏加载状态
     */
    showLoading(show) {
        if (this.elements.loadingIndicator) {
            if (show) {
                this.elements.loadingIndicator.classList.remove('hidden');
            } else {
                this.elements.loadingIndicator.classList.add('hidden');
            }
        }
    }

    /**
     * 更新统计数据
     */
    updateStats(orders) {
        const stats = {
            pending: 0,
            processing: 0,
            todayCompleted: 0,
            total: orders.length
        };
        
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        orders.forEach(order => {
            const status = order.get('status');
            const completeTime = order.get('completeTime');
            
            switch (status) {
                case 'pending':
                    stats.pending++;
                    break;
                case 'accepted':
                case 'processing':
                    stats.processing++;
                    break;
                case 'completed':
                    if (completeTime && completeTime >= today) {
                        stats.todayCompleted++;
                    }
                    break;
            }
        });
        
        // 更新显示（同时更新移动端和桌面端）
        // 待接单
        if (this.elements.pendingCount) this.elements.pendingCount.textContent = stats.pending;
        document.querySelectorAll('.pending-count').forEach(el => el.textContent = stats.pending);

        // 处理中
        if (this.elements.processingCount) this.elements.processingCount.textContent = stats.processing;
        document.querySelectorAll('.processing-count').forEach(el => el.textContent = stats.processing);

        // 今日完成
        if (this.elements.todayCompletedCount) this.elements.todayCompletedCount.textContent = stats.todayCompleted;
        document.querySelectorAll('.completed-count').forEach(el => el.textContent = stats.todayCompleted);

        // 总工单
        if (this.elements.totalCount) this.elements.totalCount.textContent = stats.total;
        document.querySelectorAll('.total-count').forEach(el => el.textContent = stats.total);
    }

    /**
     * 显示初始化提示
     */
    showInitializationPrompt() {
        if (!this.elements.ordersList) return;

        this.elements.ordersList.classList.remove('hidden');
        if (this.elements.emptyState) {
            this.elements.emptyState.classList.add('hidden');
        }

        this.elements.ordersList.innerHTML = `
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-8 text-center">
                <div class="text-blue-500 text-4xl mb-4">🚀</div>
                <h3 class="text-lg font-semibold text-blue-800 mb-2">系统初始化</h3>
                <p class="text-blue-600 mb-6">检测到这是首次使用工单管理系统，需要初始化数据库表。</p>
                <div class="space-y-3">
                    <button onclick="repairManageApp.initializeDatabase()"
                            class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors font-medium btn-fix">
                        初始化数据库
                    </button>
                    <button onclick="repairManageApp.createSampleData()"
                            class="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg transition-colors font-medium btn-fix ml-3">
                        创建示例数据
                    </button>
                </div>
                <p class="text-sm text-blue-500 mt-4">
                    初始化数据库：创建空的工单表<br>
                    创建示例数据：创建一些测试工单用于演示
                </p>
            </div>
        `;

        // 更新统计数据为0
        this.updateStats([]);
    }

    /**
     * 渲染工单列表
     */
    renderOrders(orders) {
        if (!this.elements.ordersList) return;

        if (orders.length === 0) {
            this.elements.ordersList.classList.add('hidden');
            if (this.elements.emptyState) {
                this.elements.emptyState.classList.remove('hidden');
            }
            return;
        }

        this.elements.ordersList.classList.remove('hidden');
        if (this.elements.emptyState) {
            this.elements.emptyState.classList.add('hidden');
        }

        this.elements.ordersList.innerHTML = orders.map(order => this.renderOrderItem(order)).join('');
    }

    /**
     * 渲染单个工单项
     */
    renderOrderItem(order) {
        const orderNumber = order.get('orderNumber');
        const title = order.get('title');
        const status = order.get('status');
        const urgency = order.get('urgency');
        const category = order.get('category');
        const location = order.get('location');
        const reportTime = order.get('reportTime');
        const reporter = order.get('reporter');
        const reporterName = reporter ? (reporter.get('realName') || reporter.get('username')) : '未知';
        
        const statusText = this.getStatusText(status);
        const timeAgo = this.getTimeAgo(reportTime);
        
        return `
            <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow cursor-pointer"
                 onclick="repairManageApp.showOrderDetail('${order.id}')">
                <div class="flex justify-between items-start mb-4">
                    <div class="flex-1">
                        <div class="flex items-center space-x-3 mb-2">
                            <h3 class="text-lg font-semibold text-gray-800">${title}</h3>
                            <span class="status-${status} px-2 py-1 text-xs rounded-full font-medium">${statusText}</span>
                            <span class="urgency-${urgency} px-2 py-1 text-xs rounded-full font-medium">${urgency}</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-2">工单号：${orderNumber}</p>
                        <p class="text-sm text-gray-600">位置：${location} | 类别：${category}</p>
                    </div>
                    <div class="text-right text-sm text-gray-500">
                        <p>报修人：${reporterName}</p>
                        <p>${timeAgo}</p>
                    </div>
                </div>
                
                ${status === 'pending' ? `
                    <div class="flex justify-end">
                        <button onclick="event.stopPropagation(); repairManageApp.handleAcceptOrder('${order.id}')"
                                class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors text-sm font-medium btn-fix">
                            接单
                        </button>
                    </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * 获取状态文本
     */
    getStatusText(status) {
        const statusMap = {
            'pending': '待接单',
            'accepted': '已接单',
            'processing': '处理中',
            'completed': '已完成',
            'cancelled': '已取消'
        };
        return statusMap[status] || status;
    }

    /**
     * 获取时间差描述
     */
    getTimeAgo(date) {
        if (!date) return '';
        
        const now = new Date();
        const diff = now - date;
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);
        
        if (days > 0) return `${days}天前`;
        if (hours > 0) return `${hours}小时前`;
        if (minutes > 0) return `${minutes}分钟前`;
        return '刚刚';
    }

    /**
     * 处理接单操作
     */
    async handleAcceptOrder(orderId) {
        if (!confirm('确定要接受这个工单吗？')) {
            return;
        }
        
        try {
            const RepairOrder = AV.Object.extend('RepairOrder');
            const order = AV.Object.createWithoutData('RepairOrder', orderId);
            
            order.set('status', 'accepted');
            order.set('assignee', this.currentUser);
            order.set('assigneeName', this.currentUser.get('realName') || this.currentUser.get('username'));
            order.set('acceptTime', new Date());
            
            await order.save();
            
            WorkLogUtils.showMessage('接单成功！', 'success');

            // 重新加载工单列表
            this.loadOrders();
            
        } catch (error) {
            console.error('接单失败:', error);
            WorkLogUtils.showMessage('接单失败: ' + error.message, 'error');
        }
    }

    /**
     * 显示工单详情
     */
    async showOrderDetail(orderId) {
        try {
            const RepairOrder = AV.Object.extend('RepairOrder');
            const query = new AV.Query(RepairOrder);
            query.include('reporter');
            query.include('assignee');
            
            const order = await query.get(orderId);
            
            if (this.elements.modalTitle) {
                this.elements.modalTitle.textContent = `工单详情 - ${order.get('orderNumber')}`;
            }
            
            if (this.elements.modalContent) {
                this.elements.modalContent.innerHTML = this.generateOrderDetailHtml(order);
            }
            
            if (this.elements.orderModal) {
                this.elements.orderModal.classList.remove('hidden');
            }
            
        } catch (error) {
            console.error('加载工单详情失败:', error);
            WorkLogUtils.showMessage('加载工单详情失败: ' + error.message, 'error');
        }
    }

    /**
     * 生成工单详情HTML
     */
    generateOrderDetailHtml(order) {
        const reporter = order.get('reporter');
        const assignee = order.get('assignee');
        
        return `
            <div class="space-y-6">
                <!-- 基本信息 -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-3">基本信息</h3>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div><span class="text-gray-600">工单号：</span>${order.get('orderNumber')}</div>
                        <div><span class="text-gray-600">状态：</span><span class="status-${order.get('status')} px-2 py-1 rounded-full text-xs">${this.getStatusText(order.get('status'))}</span></div>
                        <div><span class="text-gray-600">标题：</span>${order.get('title')}</div>
                        <div><span class="text-gray-600">类别：</span>${order.get('category')}</div>
                        <div><span class="text-gray-600">紧急程度：</span><span class="urgency-${order.get('urgency')} px-2 py-1 rounded-full text-xs">${order.get('urgency')}</span></div>
                        <div><span class="text-gray-600">位置：</span>${order.get('location')}</div>
                    </div>
                </div>
                
                <!-- 故障描述 -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-3">故障描述</h3>
                    <p class="text-gray-700 bg-gray-50 p-3 rounded-lg">${order.get('description')}</p>
                </div>
                
                <!-- 报修人信息 -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-3">报修人信息</h3>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div><span class="text-gray-600">姓名：</span>${order.get('reporterName')}</div>
                        <div><span class="text-gray-600">电话：</span>${order.get('reporterPhone')}</div>
                        <div><span class="text-gray-600">部门：</span>${order.get('reporterDept')}</div>
                        <div><span class="text-gray-600">报修时间：</span>${order.get('reportTime')?.toLocaleString('zh-CN')}</div>
                    </div>
                </div>
                
                <!-- 处理信息 -->
                ${assignee ? `
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-3">处理信息</h3>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div><span class="text-gray-600">处理人：</span>${order.get('assigneeName')}</div>
                            <div><span class="text-gray-600">接单时间：</span>${order.get('acceptTime')?.toLocaleString('zh-CN') || '-'}</div>
                            <div><span class="text-gray-600">开始时间：</span>${order.get('startTime')?.toLocaleString('zh-CN') || '-'}</div>
                            <div><span class="text-gray-600">完成时间：</span>${order.get('completeTime')?.toLocaleString('zh-CN') || '-'}</div>
                        </div>
                    </div>
                ` : ''}
                
                <!-- 图片附件 -->
                ${order.get('images') && order.get('images').length > 0 ? `
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-3">图片附件</h3>
                        <div class="grid grid-cols-3 gap-3">
                            ${order.get('images').map(url => `
                                <img src="${url}" alt="故障图片" class="w-full h-24 object-cover rounded-lg cursor-pointer" 
                                     onclick="window.open('${url}', '_blank')">
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
                
                <!-- 操作按钮 -->
                <div class="flex justify-end space-x-3 pt-4 border-t">
                    ${order.get('status') === 'pending' ? `
                        <button onclick="repairManageApp.handleAcceptOrder('${order.id}'); repairManageApp.hideOrderModal();"
                                class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors text-sm font-medium btn-fix">
                            接单
                        </button>
                    ` : ''}
                    ${order.get('status') === 'accepted' ? `
                        <button onclick="repairManageApp.startProcessing('${order.id}');"
                                class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors text-sm font-medium btn-fix">
                            开始处理
                        </button>
                    ` : ''}
                    ${order.get('status') === 'processing' ? `
                        <button onclick="repairManageApp.completeOrder('${order.id}');"
                                class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors text-sm font-medium btn-fix">
                            完成工单
                        </button>
                    ` : ''}
                </div>
            </div>
        `;
    }

    /**
     * 隐藏工单详情弹窗
     */
    hideOrderModal() {
        if (this.elements.orderModal) {
            this.elements.orderModal.classList.add('hidden');
        }
    }

    /**
     * 刷新工单列表
     */
    async refreshOrders() {
        await this.loadOrders();
        WorkLogUtils.showMessage('工单列表已刷新', 'success');
    }

    /**
     * 切换自动刷新
     */
    toggleAutoRefresh() {
        if (this.isAutoRefresh) {
            this.stopAutoRefresh();
        } else {
            this.startAutoRefresh();
        }
    }

    /**
     * 开始自动刷新
     */
    startAutoRefresh() {
        this.isAutoRefresh = true;
        this.autoRefreshInterval = setInterval(() => {
            this.loadOrders();
        }, 30000); // 30秒刷新一次
        
        if (this.elements.autoRefreshBtn) {
            this.elements.autoRefreshBtn.textContent = '自动刷新: 开';
            this.elements.autoRefreshBtn.classList.remove('bg-gray-500', 'hover:bg-gray-600');
            this.elements.autoRefreshBtn.classList.add('bg-green-500', 'hover:bg-green-600');
        }
    }

    /**
     * 停止自动刷新
     */
    stopAutoRefresh() {
        this.isAutoRefresh = false;
        if (this.autoRefreshInterval) {
            clearInterval(this.autoRefreshInterval);
            this.autoRefreshInterval = null;
        }
        
        if (this.elements.autoRefreshBtn) {
            this.elements.autoRefreshBtn.textContent = '自动刷新: 关';
            this.elements.autoRefreshBtn.classList.remove('bg-green-500', 'hover:bg-green-600');
            this.elements.autoRefreshBtn.classList.add('bg-gray-500', 'hover:bg-gray-600');
        }
    }

    /**
     * 更新刷新时间
     */
    updateRefreshTime() {
        if (this.elements.refreshTime) {
            this.elements.refreshTime.textContent = `最后更新: ${new Date().toLocaleTimeString('zh-CN')}`;
        }
    }

    /**
     * 开始处理工单
     */
    async startProcessing(orderId) {
        try {
            const RepairOrder = AV.Object.extend('RepairOrder');
            const order = AV.Object.createWithoutData('RepairOrder', orderId);
            
            order.set('status', 'processing');
            order.set('startTime', new Date());
            
            await order.save();
            
            WorkLogUtils.showMessage('已开始处理工单', 'success');
            this.hideOrderModal();
            this.loadOrders();
            
        } catch (error) {
            console.error('开始处理失败:', error);
            WorkLogUtils.showMessage('开始处理失败: ' + error.message, 'error');
        }
    }

    /**
     * 完成工单
     */
    async completeOrder(orderId) {
        const result = prompt('请输入处理结果：');
        if (!result) return;

        try {
            const RepairOrder = AV.Object.extend('RepairOrder');
            const order = AV.Object.createWithoutData('RepairOrder', orderId);

            order.set('status', 'completed');
            order.set('completeTime', new Date());
            order.set('result', result);

            await order.save();

            WorkLogUtils.showMessage('工单已完成', 'success');
            this.hideOrderModal();
            this.loadOrders();

        } catch (error) {
            console.error('完成工单失败:', error);
            WorkLogUtils.showMessage('完成工单失败: ' + error.message, 'error');
        }
    }

    /**
     * 初始化数据库
     */
    async initializeDatabase() {
        try {
            WorkLogUtils.showMessage('正在初始化数据库...', 'info');

            // 创建一个空的RepairOrder对象来初始化表
            const RepairOrder = AV.Object.extend('RepairOrder');
            const testOrder = new RepairOrder();

            testOrder.set('orderNumber', 'INIT_TEST');
            testOrder.set('title', '初始化测试');
            testOrder.set('status', 'completed');
            testOrder.set('reporter', this.currentUser);

            await testOrder.save();

            // 立即删除测试数据
            await testOrder.destroy();

            WorkLogUtils.showMessage('数据库初始化成功！', 'success');

            // 重新加载工单列表
            this.loadOrders();

        } catch (error) {
            console.error('初始化数据库失败:', error);
            WorkLogUtils.showMessage('初始化数据库失败: ' + error.message, 'error');
        }
    }

    /**
     * 创建示例数据
     */
    async createSampleData() {
        if (!confirm('确定要创建示例数据吗？这将创建一些测试工单。')) {
            return;
        }

        try {
            WorkLogUtils.showMessage('正在创建示例数据...', 'info');

            const RepairOrder = AV.Object.extend('RepairOrder');
            const sampleOrders = [];

            // 创建示例工单数据
            const samples = [
                {
                    title: '客房空调不制冷',
                    category: '空调',
                    urgency: '高',
                    location: '3楼客房301',
                    description: '客房空调开启后不制冷，温度显示正常但出风温度较高，客人投诉。',
                    status: 'pending',
                    priority: 4
                },
                {
                    title: '大堂LED灯具故障',
                    category: '电气',
                    urgency: '中',
                    location: '一楼大堂',
                    description: '大堂吊灯部分LED灯珠不亮，影响照明效果。',
                    status: 'pending',
                    priority: 3
                },
                {
                    title: '电梯异响',
                    category: '电梯',
                    urgency: '紧急',
                    location: '1号电梯',
                    description: '电梯运行时有异常响声，可能存在安全隐患。',
                    status: 'accepted',
                    priority: 5
                },
                {
                    title: '洗手间水龙头漏水',
                    category: '水暖',
                    urgency: '低',
                    location: '2楼公共洗手间',
                    description: '洗手间水龙头关闭后仍有滴水现象。',
                    status: 'processing',
                    priority: 2
                }
            ];

            for (let i = 0; i < samples.length; i++) {
                const sample = samples[i];
                const order = new RepairOrder();

                // 生成工单号
                const now = new Date();
                const orderNumber = `WO${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}${String(i).padStart(2, '0')}`;

                order.set('orderNumber', orderNumber);
                order.set('title', sample.title);
                order.set('category', sample.category);
                order.set('urgency', sample.urgency);
                order.set('location', sample.location);
                order.set('description', sample.description);
                order.set('status', sample.status);
                order.set('priority', sample.priority);
                order.set('reportTime', new Date(now.getTime() - (i * 3600000))); // 每个工单间隔1小时
                order.set('reporter', this.currentUser);
                order.set('reporterName', this.currentUser.get('realName') || this.currentUser.get('username'));
                order.set('reporterPhone', '138****8888');
                order.set('reporterDept', '客房部');

                // 为已接单和处理中的工单设置处理人
                if (sample.status !== 'pending') {
                    order.set('assignee', this.currentUser);
                    order.set('assigneeName', this.currentUser.get('realName') || this.currentUser.get('username'));
                    order.set('acceptTime', new Date(now.getTime() - (i * 3600000) + 1800000)); // 接单时间为报修后30分钟
                }

                // 为处理中的工单设置开始时间
                if (sample.status === 'processing') {
                    order.set('startTime', new Date(now.getTime() - (i * 3600000) + 3600000)); // 开始时间为报修后1小时
                }

                sampleOrders.push(order);
            }

            // 批量保存
            await AV.Object.saveAll(sampleOrders);

            WorkLogUtils.showMessage(`成功创建${sampleOrders.length}个示例工单！`, 'success');

            // 重新加载工单列表
            this.loadOrders();

        } catch (error) {
            console.error('创建示例数据失败:', error);
            WorkLogUtils.showMessage('创建示例数据失败: ' + error.message, 'error');
        }
    }


}

// 导出模块
if (typeof window !== 'undefined') {
    window.RepairManageApp = RepairManageApp;
}
