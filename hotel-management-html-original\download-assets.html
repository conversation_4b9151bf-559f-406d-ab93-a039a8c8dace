<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下载本地资源</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #0056b3;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📦 下载本地资源文件</h1>
        <p>如果CDN加载有问题，可以下载资源文件到本地使用。</p>
        
        <div class="status info">
            <strong>说明：</strong>点击下面的按钮下载对应的资源文件，然后保存到项目的 <code>assets</code> 文件夹中。
        </div>

        <h3>Bootstrap CSS</h3>
        <a href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" 
           class="btn" download="bootstrap.min.css">下载 Bootstrap CSS</a>
        
        <h3>Bootstrap JavaScript</h3>
        <a href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js" 
           class="btn" download="bootstrap.bundle.min.js">下载 Bootstrap JS</a>
        
        <h3>Font Awesome</h3>
        <a href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css" 
           class="btn" download="font-awesome.min.css">下载 Font Awesome CSS</a>

        <h3>LeanCloud SDK</h3>
        <a href="https://cdn.jsdelivr.net/npm/leancloud-storage@4.15.2/dist/av-min.js" 
           class="btn" download="leancloud.min.js">下载 LeanCloud SDK</a>

        <hr>

        <h3>📁 本地文件结构</h3>
        <p>下载后，在项目根目录创建以下文件夹结构：</p>
        <pre>
hotel-management-html-original/
├── assets/
│   ├── css/
│   │   ├── bootstrap.min.css
│   │   └── font-awesome.min.css
│   └── js/
│       ├── bootstrap.bundle.min.js
│       └── leancloud.min.js
├── dashboard.html
└── ...
        </pre>

        <h3>🔧 修改HTML文件</h3>
        <p>下载完成后，需要修改HTML文件中的CDN链接为本地路径：</p>
        
        <h4>将CDN链接：</h4>
        <pre>&lt;link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet"&gt;
&lt;link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"&gt;
&lt;script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"&gt;&lt;/script&gt;</pre>

        <h4>修改为本地路径：</h4>
        <pre>&lt;link href="assets/css/bootstrap.min.css" rel="stylesheet"&gt;
&lt;link href="assets/css/font-awesome.min.css" rel="stylesheet"&gt;
&lt;script src="assets/js/bootstrap.bundle.min.js"&gt;&lt;/script&gt;</pre>

        <div class="status success">
            <strong>提示：</strong>使用本地资源可以避免网络问题，提高页面加载速度。
        </div>

        <h3>🚀 快速创建文件夹</h3>
        <p>你可以手动创建文件夹，或者使用以下命令：</p>
        <pre>mkdir assets
mkdir assets\css
mkdir assets\js</pre>

        <button class="btn" onclick="createFolders()">创建本地文件夹结构</button>
        <div id="folderStatus"></div>
    </div>

    <script>
        function createFolders() {
            const statusDiv = document.getElementById('folderStatus');
            statusDiv.innerHTML = `
                <div class="status info">
                    <strong>注意：</strong>浏览器无法直接创建文件夹。请手动创建以下文件夹：
                    <ul>
                        <li>assets/</li>
                        <li>assets/css/</li>
                        <li>assets/js/</li>
                    </ul>
                    然后将下载的文件放入对应文件夹中。
                </div>
            `;
        }

        // 检查资源加载状态
        function checkResources() {
            const resources = [
                'https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css',
                'https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css'
            ];

            resources.forEach(url => {
                fetch(url, { method: 'HEAD' })
                    .then(response => {
                        if (response.ok) {
                            console.log(`✅ ${url} 可以正常访问`);
                        } else {
                            console.log(`❌ ${url} 访问失败: ${response.status}`);
                        }
                    })
                    .catch(error => {
                        console.log(`❌ ${url} 网络错误:`, error);
                    });
            });
        }

        // 页面加载时检查资源
        window.addEventListener('load', checkResources);
    </script>
</body>
</html>
