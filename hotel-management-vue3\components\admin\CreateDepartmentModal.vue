<template>
  <div 
    v-if="show" 
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    @click="handleBackdropClick"
  >
    <div 
      class="bg-white rounded-lg w-full max-w-md"
      @click.stop
    >
      <!-- 头部 -->
      <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-900">
          {{ isEditing ? '编辑部门' : '新增部门' }}
        </h3>
        <button 
          @click="handleClose"
          class="text-gray-400 hover:text-gray-600"
        >
          <Icon name="mdi:close" size="24" />
        </button>
      </div>

      <!-- 内容 -->
      <div class="px-6 py-4">
        <form @submit.prevent="handleSubmit">
          <div class="space-y-4">
            <!-- 部门名称 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                部门名称 *
              </label>
              <input 
                v-model="formData.name"
                type="text"
                required
                :disabled="loading"
                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="请输入部门名称"
              />
            </div>

            <!-- 部门描述 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                部门描述
              </label>
              <textarea 
                v-model="formData.description"
                :disabled="loading"
                rows="3"
                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                placeholder="请输入部门描述"
              ></textarea>
            </div>

            <!-- 系统部门标识 -->
            <div v-if="isEditing && editDepartment?.isSystem" class="flex items-center">
              <Icon name="mdi:information" size="16" class="text-blue-600 mr-2" />
              <span class="text-sm text-blue-600">这是系统预设部门</span>
            </div>
          </div>
        </form>
      </div>

      <!-- 底部按钮 -->
      <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
        <button 
          type="button"
          @click="handleClose"
          class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
        >
          取消
        </button>
        <button 
          @click="handleSubmit"
          :disabled="loading || !isFormValid"
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {{ loading ? '保存中...' : (isEditing ? '更新' : '创建') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Department } from '~/types'

interface Props {
  show: boolean
  editDepartment?: Department | null
}

const props = withDefaults(defineProps<Props>(), {
  editDepartment: null
})

const emit = defineEmits<{
  'update:show': [value: boolean]
  created: [department: Department]
  updated: [department: Department]
}>()

// Store
const adminStore = useAdminStore()

// 响应式数据
const loading = ref(false)
const isEditing = computed(() => !!props.editDepartment)

// 表单数据
const formData = reactive({
  name: '',
  description: ''
})

// 计算属性
const isFormValid = computed(() => {
  return formData.name.trim().length > 0
})

// 方法
const resetForm = () => {
  formData.name = ''
  formData.description = ''
}

const loadFormData = () => {
  if (props.editDepartment) {
    formData.name = props.editDepartment.name
    formData.description = props.editDepartment.description || ''
  } else {
    resetForm()
  }
}

const handleClose = () => {
  emit('update:show', false)
}

const handleBackdropClick = () => {
  handleClose()
}

const handleSubmit = async () => {
  if (!isFormValid.value) return
  
  loading.value = true
  
  try {
    if (isEditing.value && props.editDepartment) {
      // 更新部门
      const result = await adminStore.updateDepartment(props.editDepartment.id, {
        name: formData.name.trim(),
        description: formData.description.trim()
      })
      
      if (result.success && result.data) {
        emit('updated', result.data)
        handleClose()
      } else {
        alert(result.error || '更新部门失败')
      }
    } else {
      // 创建部门
      const result = await adminStore.createDepartment({
        name: formData.name.trim(),
        description: formData.description.trim(),
        isSystem: false
      })
      
      if (result.success && result.data) {
        emit('created', result.data)
        handleClose()
      } else {
        alert(result.error || '创建部门失败')
      }
    }
  } catch (error) {
    console.error('提交表单失败:', error)
    alert('操作失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 监听器
watch(() => props.show, (newShow) => {
  if (newShow) {
    loadFormData()
  }
})

// 键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.show) {
    handleClose()
  }
  if (event.key === 'Enter' && event.ctrlKey && props.show) {
    handleSubmit()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>
