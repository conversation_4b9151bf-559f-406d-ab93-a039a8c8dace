<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据报表 - 系统管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .btn-fix {
            touch-action: manipulation;
        }
        .report-card {
            transition: all 0.3s ease;
        }
        .report-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="index.html" class="text-blue-600 hover:text-blue-800 mr-4">
                        ← 返回管理首页
                    </a>
                    <h1 class="text-xl font-semibold text-gray-900">数据报表</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="refreshDataBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                        刷新数据
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 时间范围选择 -->
        <div class="bg-white rounded-lg shadow mb-6 p-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">报表设置</h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">时间范围</label>
                    <select id="timeRange" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                        <option value="7">最近7天</option>
                        <option value="30" selected>最近30天</option>
                        <option value="90">最近90天</option>
                        <option value="365">最近一年</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">开始日期</label>
                    <input type="date" id="startDate"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">结束日期</label>
                    <input type="date" id="endDate"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                </div>
                <div class="flex items-end">
                    <button id="generateReportBtn" class="w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md text-sm btn-fix">
                        生成报表
                    </button>
                </div>
            </div>
        </div>

        <!-- 报表概览 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">总用户数</dt>
                            <dd class="text-lg font-medium text-gray-900" id="totalUsers">0</dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">库存商品</dt>
                            <dd class="text-lg font-medium text-gray-900" id="totalProducts">0</dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">工作日志</dt>
                            <dd class="text-lg font-medium text-gray-900" id="totalWorkLogs">0</dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">维修单</dt>
                            <dd class="text-lg font-medium text-gray-900" id="totalRepairs">0</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- 报表模块 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- 用户活跃度图表 -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">用户活跃度</h3>
                <canvas id="userActivityChart" width="400" height="200"></canvas>
            </div>

            <!-- 库存变化图表 -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">库存变化趋势</h3>
                <canvas id="inventoryTrendChart" width="400" height="200"></canvas>
            </div>

            <!-- 工作日志统计 -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">工作日志统计</h3>
                <canvas id="workLogChart" width="400" height="200"></canvas>
            </div>

            <!-- 维修统计 -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">维修统计</h3>
                <canvas id="repairChart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- 详细报表 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 部门工作统计 -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">部门工作统计</h3>
                </div>
                <div class="p-6">
                    <div id="departmentStats" class="space-y-4">
                        <!-- 部门统计将在这里动态加载 -->
                    </div>
                </div>
            </div>

            <!-- 库存预警 -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">库存预警</h3>
                </div>
                <div class="p-6">
                    <div id="inventoryAlerts" class="space-y-4">
                        <!-- 库存预警将在这里动态加载 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载指示器 -->
    <div id="loadingIndicator" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-40" style="display: none;">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <span class="text-gray-700">生成报表中...</span>
        </div>
    </div>

    <!-- 引入模块化JS文件 -->
    <script src="../js/config.js"></script>
    <script src="../js/permission-manager.js"></script>
    <script>
        // 数据报表应用类
        class ReportApp {
            constructor() {
                this.charts = {};
                this.reportData = {};
            }

            init() {
                this.bindEvents();
                this.initDateRange();
                this.loadReportData();
            }

            bindEvents() {
                // 生成报表按钮
                document.getElementById('generateReportBtn').addEventListener('click', () => {
                    this.loadReportData();
                });

                // 刷新数据按钮
                document.getElementById('refreshDataBtn').addEventListener('click', () => {
                    this.loadReportData();
                });

                // 时间范围选择
                document.getElementById('timeRange').addEventListener('change', (e) => {
                    this.updateDateRange(parseInt(e.target.value));
                });
            }

            initDateRange() {
                const endDate = new Date();
                const startDate = new Date();
                startDate.setDate(startDate.getDate() - 30);

                document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
                document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
            }

            updateDateRange(days) {
                const endDate = new Date();
                const startDate = new Date();
                startDate.setDate(startDate.getDate() - days);

                document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
                document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
            }

            async loadReportData() {
                try {
                    this.showLoading();

                    const startDate = new Date(document.getElementById('startDate').value);
                    const endDate = new Date(document.getElementById('endDate').value);
                    endDate.setHours(23, 59, 59, 999);

                    // 并行加载各种数据
                    const [
                        userStats,
                        productStats,
                        workLogStats,
                        repairStats,
                        departmentStats,
                        inventoryAlerts
                    ] = await Promise.all([
                        this.loadUserStats(startDate, endDate),
                        this.loadProductStats(),
                        this.loadWorkLogStats(startDate, endDate),
                        this.loadRepairStats(startDate, endDate),
                        this.loadDepartmentStats(startDate, endDate),
                        this.loadInventoryAlerts()
                    ]);

                    // 更新概览数据
                    this.updateOverview(userStats, productStats, workLogStats, repairStats);

                    // 更新图表
                    this.updateCharts(userStats, workLogStats, repairStats);

                    // 更新详细报表
                    this.updateDepartmentStats(departmentStats);
                    this.updateInventoryAlerts(inventoryAlerts);

                    this.hideLoading();
                } catch (error) {
                    console.error('加载报表数据失败:', error);
                    this.hideLoading();
                    alert('加载报表数据失败: ' + error.message);
                }
            }

            async loadUserStats(startDate, endDate) {
                try {
                    const totalQuery = new AV.Query('_User');
                    const totalUsers = await totalQuery.count();

                    const activeQuery = new AV.Query('SystemLog');
                    activeQuery.greaterThanOrEqualTo('createdAt', startDate);
                    activeQuery.lessThanOrEqualTo('createdAt', endDate);
                    activeQuery.notEqualTo('userId', 'system');
                    const activeLogs = await activeQuery.find();
                    
                    const activeUsers = new Set(activeLogs.map(log => log.get('userId'))).size;

                    return { total: totalUsers, active: activeUsers };
                } catch (error) {
                    console.error('加载用户统计失败:', error);
                    return { total: 0, active: 0 };
                }
            }

            async loadProductStats() {
                try {
                    const query = new AV.Query('Product');
                    const count = await query.count();
                    return { total: count };
                } catch (error) {
                    console.error('加载商品统计失败:', error);
                    return { total: 0 };
                }
            }

            async loadWorkLogStats(startDate, endDate) {
                try {
                    const query = new AV.Query('WorkLog');
                    query.greaterThanOrEqualTo('createdAt', startDate);
                    query.lessThanOrEqualTo('createdAt', endDate);
                    const count = await query.count();
                    return { total: count };
                } catch (error) {
                    console.error('加载工作日志统计失败:', error);
                    return { total: 0 };
                }
            }

            async loadRepairStats(startDate, endDate) {
                try {
                    const query = new AV.Query('RepairOrder');
                    query.greaterThanOrEqualTo('createdAt', startDate);
                    query.lessThanOrEqualTo('createdAt', endDate);
                    const count = await query.count();
                    return { total: count };
                } catch (error) {
                    console.error('加载维修统计失败:', error);
                    return { total: 0 };
                }
            }

            async loadDepartmentStats(startDate, endDate) {
                try {
                    const query = new AV.Query('WorkLog');
                    query.greaterThanOrEqualTo('createdAt', startDate);
                    query.lessThanOrEqualTo('createdAt', endDate);
                    query.include('userId');
                    const logs = await query.find();

                    const departmentStats = {};
                    logs.forEach(log => {
                        const user = log.get('userId');
                        if (user) {
                            const department = user.get('department') || '未分配';
                            if (!departmentStats[department]) {
                                departmentStats[department] = 0;
                            }
                            departmentStats[department]++;
                        }
                    });

                    return departmentStats;
                } catch (error) {
                    console.error('加载部门统计失败:', error);
                    return {};
                }
            }

            async loadInventoryAlerts() {
                try {
                    // 模拟库存预警数据
                    return [
                        { productName: '办公用纸', currentStock: 5, minStock: 10, status: 'low' },
                        { productName: '打印墨盒', currentStock: 2, minStock: 5, status: 'critical' },
                        { productName: '清洁用品', currentStock: 8, minStock: 15, status: 'low' }
                    ];
                } catch (error) {
                    console.error('加载库存预警失败:', error);
                    return [];
                }
            }

            updateOverview(userStats, productStats, workLogStats, repairStats) {
                document.getElementById('totalUsers').textContent = userStats.total;
                document.getElementById('totalProducts').textContent = productStats.total;
                document.getElementById('totalWorkLogs').textContent = workLogStats.total;
                document.getElementById('totalRepairs').textContent = repairStats.total;
            }

            updateCharts(userStats, workLogStats, repairStats) {
                // 用户活跃度图表
                this.createUserActivityChart(userStats);
                
                // 库存趋势图表
                this.createInventoryTrendChart();
                
                // 工作日志图表
                this.createWorkLogChart(workLogStats);
                
                // 维修统计图表
                this.createRepairChart(repairStats);
            }

            createUserActivityChart(userStats) {
                const ctx = document.getElementById('userActivityChart').getContext('2d');
                
                if (this.charts.userActivity) {
                    this.charts.userActivity.destroy();
                }

                this.charts.userActivity = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: ['活跃用户', '非活跃用户'],
                        datasets: [{
                            data: [userStats.active, userStats.total - userStats.active],
                            backgroundColor: ['#3B82F6', '#E5E7EB'],
                            borderWidth: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }

            createInventoryTrendChart() {
                const ctx = document.getElementById('inventoryTrendChart').getContext('2d');
                
                if (this.charts.inventoryTrend) {
                    this.charts.inventoryTrend.destroy();
                }

                // 模拟数据
                const labels = ['1月', '2月', '3月', '4月', '5月', '6月'];
                const data = [120, 135, 128, 142, 138, 145];

                this.charts.inventoryTrend = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: '库存总值',
                            data: data,
                            borderColor: '#10B981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }

            createWorkLogChart(workLogStats) {
                const ctx = document.getElementById('workLogChart').getContext('2d');
                
                if (this.charts.workLog) {
                    this.charts.workLog.destroy();
                }

                // 模拟每日工作日志数据
                const labels = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
                const data = [12, 15, 18, 14, 16, 8, 5];

                this.charts.workLog = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: '工作日志数量',
                            data: data,
                            backgroundColor: '#F59E0B',
                            borderRadius: 4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }

            createRepairChart(repairStats) {
                const ctx = document.getElementById('repairChart').getContext('2d');
                
                if (this.charts.repair) {
                    this.charts.repair.destroy();
                }

                // 模拟维修状态数据
                const data = {
                    labels: ['待处理', '处理中', '已完成', '已取消'],
                    datasets: [{
                        data: [5, 8, 25, 2],
                        backgroundColor: ['#EF4444', '#F59E0B', '#10B981', '#6B7280'],
                        borderWidth: 0
                    }]
                };

                this.charts.repair = new Chart(ctx, {
                    type: 'doughnut',
                    data: data,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }

            updateDepartmentStats(departmentStats) {
                const container = document.getElementById('departmentStats');
                
                if (Object.keys(departmentStats).length === 0) {
                    container.innerHTML = '<p class="text-gray-500 text-center">暂无部门统计数据</p>';
                    return;
                }

                const html = Object.entries(departmentStats).map(([department, count]) => `
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                        <span class="font-medium text-gray-900">${department}</span>
                        <span class="text-blue-600 font-semibold">${count} 条</span>
                    </div>
                `).join('');

                container.innerHTML = html;
            }

            updateInventoryAlerts(alerts) {
                const container = document.getElementById('inventoryAlerts');
                
                if (alerts.length === 0) {
                    container.innerHTML = '<p class="text-gray-500 text-center">暂无库存预警</p>';
                    return;
                }

                const html = alerts.map(alert => {
                    const statusClass = alert.status === 'critical' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800';
                    const statusText = alert.status === 'critical' ? '严重不足' : '库存偏低';
                    
                    return `
                        <div class="flex justify-between items-center p-3 border rounded">
                            <div>
                                <div class="font-medium text-gray-900">${alert.productName}</div>
                                <div class="text-sm text-gray-500">当前: ${alert.currentStock} / 最低: ${alert.minStock}</div>
                            </div>
                            <span class="px-2 py-1 text-xs font-medium rounded-full ${statusClass}">
                                ${statusText}
                            </span>
                        </div>
                    `;
                }).join('');

                container.innerHTML = html;
            }

            showLoading() {
                document.getElementById('loadingIndicator').style.display = 'flex';
            }

            hideLoading() {
                document.getElementById('loadingIndicator').style.display = 'none';
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            function initApp() {
                if (typeof AV !== 'undefined' && typeof Chart !== 'undefined') {
                    const reportApp = new ReportApp();
                    reportApp.init();
                } else {
                    setTimeout(initApp, 100);
                }
            }
            initApp();
        });
    </script>
</body>
</html>
