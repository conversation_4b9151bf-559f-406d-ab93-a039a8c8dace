<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>盘点管理 - 酒店管理系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <style>
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .btn-fix {
            touch-action: manipulation;
        }
        .status-draft { @apply bg-yellow-100 text-yellow-800; }
        .status-in-progress { @apply bg-blue-100 text-blue-800; }
        .status-completed { @apply bg-green-100 text-green-800; }
        .status-cancelled { @apply bg-red-100 text-red-800; }
        .diff-positive { @apply bg-green-50 text-green-800; }
        .diff-negative { @apply bg-red-50 text-red-800; }
        .diff-zero { @apply bg-gray-50 text-gray-800; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-900">盘点管理</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- 用户信息 -->
                    <div id="userInfo" class="flex items-center space-x-2" style="display: none;">
                        <span class="text-sm text-gray-700">欢迎，</span>
                        <span id="realName" class="text-sm font-medium text-gray-900"></span>
                        <button id="logoutBtn" class="text-sm text-red-600 hover:text-red-800 btn-fix">退出</button>
                    </div>
                    <button id="loginBtn" class="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                        登录
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 登录提示 -->
    <div id="loginPrompt" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8" style="display: none;">
        <div class="bg-white rounded-lg shadow p-8 text-center">
            <div class="mb-4">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">需要登录</h3>
            <p class="text-gray-500 mb-4">请先登录系统以使用盘点管理功能</p>
            <button id="loginPromptBtn" class="bg-indigo-500 hover:bg-indigo-600 text-white px-6 py-2 rounded-lg btn-fix">
                立即登录
            </button>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div id="countSection" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8" style="display: none;">
        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">本月盘点</dt>
                            <dd class="text-lg font-medium text-gray-900" id="monthCountCount">0</dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">进行中</dt>
                            <dd class="text-lg font-medium text-gray-900" id="inProgressCountCount">0</dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">已完成</dt>
                            <dd class="text-lg font-medium text-gray-900" id="completedCountCount">0</dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">差异项目</dt>
                            <dd class="text-lg font-medium text-gray-900" id="differenceItemsCount">0</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作栏 -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                    <div class="flex-1 min-w-0">
                        <h2 class="text-lg font-medium text-gray-900">盘点计划列表</h2>
                    </div>
                    <div class="mt-4 sm:mt-0 sm:ml-4 space-x-2">
                        <a href="count-analysis.html" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm btn-fix inline-block">
                            分析报告
                        </a>
                        <button id="createCountBtn" class="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                            新建盘点计划
                        </button>
                    </div>
                </div>
            </div>

            <!-- 搜索和筛选 -->
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <input type="text" id="searchInput" placeholder="搜索盘点计划..."
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm">
                    </div>
                    <div>
                        <select id="statusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm">
                            <option value="">全部状态</option>
                            <option value="draft">草稿</option>
                            <option value="in-progress">进行中</option>
                            <option value="completed">已完成</option>
                            <option value="cancelled">已取消</option>
                        </select>
                    </div>
                    <div>
                        <select id="warehouseFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm">
                            <option value="">全部仓库</option>
                            <!-- 仓库选项将动态加载 -->
                        </select>
                    </div>
                    <div>
                        <button id="searchBtn" class="w-full bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md text-sm btn-fix">
                            搜索
                        </button>
                    </div>
                </div>
            </div>

            <!-- 盘点计划列表 -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">盘点编号</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">盘点名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">仓库</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">盘点类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">计划日期</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">负责人</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody id="countTableBody" class="bg-white divide-y divide-gray-200">
                        <!-- 盘点计划列表将在这里动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span id="pageInfo">1-20</span> 条，共 <span id="totalCount">0</span> 条
                    </div>
                    <div class="flex space-x-2">
                        <button id="prevPageBtn" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 btn-fix">上一页</button>
                        <button id="nextPageBtn" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 btn-fix">下一页</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载指示器 -->
        <div id="loadingIndicator" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-40" style="display: none;">
            <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-500"></div>
                <span class="text-gray-700">加载中...</span>
            </div>
        </div>
    </div>

    <!-- 登录弹窗 -->
    <div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style="display: none;">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 fade-in">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 id="loginModalTitle" class="text-xl font-semibold text-gray-800">登录</h2>
                    <button id="closeLoginModal" class="text-gray-400 hover:text-gray-600 text-2xl btn-fix">&times;</button>
                </div>
            </div>
            <div class="p-6">
                <!-- 登录表单 -->
                <form id="loginForm" class="space-y-4">
                    <div>
                        <label for="loginUsername" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                        <input type="text" id="loginUsername" required
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm">
                    </div>
                    <div>
                        <label for="loginPassword" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                        <input type="password" id="loginPassword" required
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm">
                    </div>
                    <button type="submit" class="w-full bg-indigo-500 hover:bg-indigo-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                        登录
                    </button>
                </form>

                <!-- 注册表单 -->
                <form id="registerForm" class="space-y-4" style="display: none;">
                    <div>
                        <label for="registerUsername" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                        <input type="text" id="registerUsername" required
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm">
                    </div>
                    <div>
                        <label for="registerRealName" class="block text-sm font-medium text-gray-700 mb-1">真实姓名</label>
                        <input type="text" id="registerRealName" required
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm">
                    </div>
                    <div>
                        <label for="registerPhone" class="block text-sm font-medium text-gray-700 mb-1">电话</label>
                        <input type="tel" id="registerPhone" required
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm">
                    </div>
                    <div>
                        <label for="registerDepartment" class="block text-sm font-medium text-gray-700 mb-1">部门</label>
                        <select id="registerDepartment" required
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm">
                            <option value="">请选择部门</option>
                            <option value="工程部">工程部</option>
                            <option value="前厅部">前厅部</option>
                            <option value="客房部">客房部</option>
                            <option value="餐饮部">餐饮部</option>
                            <option value="保安部">保安部</option>
                            <option value="财务部">财务部</option>
                            <option value="人事部">人事部</option>
                            <option value="销售部">销售部</option>
                        </select>
                    </div>
                    <div>
                        <label for="registerPassword" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                        <input type="password" id="registerPassword" required minlength="6"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm">
                    </div>
                    <div>
                        <label for="registerPasswordConfirm" class="block text-sm font-medium text-gray-700 mb-1">确认密码</label>
                        <input type="password" id="registerPasswordConfirm" required minlength="6"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm">
                    </div>
                    <button type="submit" class="w-full bg-indigo-500 hover:bg-indigo-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                        注册
                    </button>
                </form>

                <div class="mt-4 text-center text-sm">
                    <span class="text-gray-600">还没有账号？</span>
                    <a href="#" id="showRegisterBtn" class="text-indigo-500 hover:text-indigo-600 font-medium">立即注册</a>
                </div>
                <div class="mt-2 text-center text-sm" id="backToLoginDiv" style="display: none;">
                    <span class="text-gray-600">已有账号？</span>
                    <a href="#" id="showLoginBtn" class="text-indigo-500 hover:text-indigo-600 font-medium">立即登录</a>
                </div>
            </div>
        </div>
    </div>

    <!-- 盘点计划编辑弹窗 -->
    <div id="countPlanModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto fade-in">
            <div class="p-4 border-b border-gray-200 sticky top-0 bg-white">
                <div class="flex justify-between items-center">
                    <h2 id="countPlanModalTitle" class="text-xl font-semibold text-gray-800">新建盘点计划</h2>
                    <button id="closeCountPlanModal" class="text-gray-400 hover:text-gray-600 text-2xl btn-fix">&times;</button>
                </div>
            </div>
            <div class="p-6">
                <form id="countPlanForm" class="space-y-6">
                    <input type="hidden" id="editCountPlanId">

                    <!-- 基本信息 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="countPlanNo" class="block text-sm font-medium text-gray-700 mb-1">盘点编号 <span class="text-red-500">*</span></label>
                            <input type="text" id="countPlanNo" required readonly
                                class="w-full p-3 border border-gray-300 rounded-lg bg-gray-50 text-sm">
                        </div>
                        <div>
                            <label for="countPlanName" class="block text-sm font-medium text-gray-700 mb-1">盘点名称 <span class="text-red-500">*</span></label>
                            <input type="text" id="countPlanName" required
                                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm">
                        </div>
                        <div>
                            <label for="countWarehouse" class="block text-sm font-medium text-gray-700 mb-1">盘点仓库 <span class="text-red-500">*</span></label>
                            <select id="countWarehouse" required
                                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm">
                                <option value="">请选择仓库</option>
                                <!-- 仓库选项将动态加载 -->
                            </select>
                        </div>
                        <div>
                            <label for="countType" class="block text-sm font-medium text-gray-700 mb-1">盘点类型 <span class="text-red-500">*</span></label>
                            <select id="countType" required
                                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm">
                                <option value="">请选择盘点类型</option>
                                <option value="full">全盘</option>
                                <option value="partial">抽盘</option>
                                <option value="cycle">循环盘点</option>
                                <option value="spot">临时盘点</option>
                            </select>
                        </div>
                        <div>
                            <label for="plannedDate" class="block text-sm font-medium text-gray-700 mb-1">计划日期 <span class="text-red-500">*</span></label>
                            <input type="date" id="plannedDate" required
                                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm">
                        </div>
                        <div>
                            <label for="countManager" class="block text-sm font-medium text-gray-700 mb-1">负责人 <span class="text-red-500">*</span></label>
                            <input type="text" id="countManager" required
                                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm">
                        </div>
                    </div>

                    <div>
                        <label for="countDescription" class="block text-sm font-medium text-gray-700 mb-1">盘点说明</label>
                        <textarea id="countDescription" rows="3"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm"></textarea>
                    </div>

                    <!-- 盘点范围 -->
                    <div>
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-800">盘点范围</h3>
                            <div class="space-x-2">
                                <button type="button" id="selectAllProducts" class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm btn-fix">
                                    全选
                                </button>
                                <button type="button" id="clearAllProducts" class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm btn-fix">
                                    清空
                                </button>
                            </div>
                        </div>

                        <div class="border border-gray-200 rounded-lg max-h-64 overflow-y-auto">
                            <table class="min-w-full">
                                <thead class="bg-gray-50 sticky top-0">
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                                            <input type="checkbox" id="selectAllCheckbox" class="rounded">
                                        </th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">商品信息</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">分类</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">当前库存</th>
                                    </tr>
                                </thead>
                                <tbody id="productSelectionTable">
                                    <!-- 商品选择列表将在这里动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="flex gap-3 pt-4">
                        <button type="button" id="cancelCountPlanEdit" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                            取消
                        </button>
                        <button type="button" id="saveCountPlanDraft" class="flex-1 bg-yellow-500 hover:bg-yellow-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                            保存草稿
                        </button>
                        <button type="submit" class="flex-1 bg-indigo-500 hover:bg-indigo-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                            开始盘点
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 盘点执行弹窗 -->
    <div id="countExecuteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-y-auto fade-in">
            <div class="p-4 border-b border-gray-200 sticky top-0 bg-white">
                <div class="flex justify-between items-center">
                    <h2 id="countExecuteModalTitle" class="text-xl font-semibold text-gray-800">盘点执行</h2>
                    <button id="closeCountExecuteModal" class="text-gray-400 hover:text-gray-600 text-2xl btn-fix">&times;</button>
                </div>
            </div>
            <div class="p-6">
                <div id="countExecuteContent">
                    <!-- 盘点执行内容将在这里动态加载 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 引入模块化JS文件 -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/base-app.js"></script>
    <script>
        // 初始化盘点管理应用
        let countApp;

        // 统一的初始化函数
        function initCountApp() {
            if (typeof AV !== 'undefined') {
                try {
                    countApp = new CountApp();
                    countApp.init();
                    console.log('盘点管理应用初始化成功');
                } catch (error) {
                    console.error('盘点管理应用初始化失败:', error);
                    alert('应用初始化失败: ' + error.message);
                }
            } else {
                setTimeout(initCountApp, 100);
            }
        }

        // 盘点管理应用类
        class CountApp {
            constructor() {
                this.currentPage = 1;
                this.pageSize = 20;
                this.totalCount = 0;
                this.elements = {};
            }

            /**
             * 初始化应用
             */
            init() {
                this.initElements();
                this.bindEvents();
                this.checkLoginStatus();
            }

            /**
             * 初始化DOM元素
             */
            initElements() {
                const pageElements = this.getPageElements();
                for (const [key, id] of Object.entries(pageElements)) {
                    this.elements[key] = document.getElementById(id);
                }
            }

            /**
             * 获取页面特定的DOM元素
             */
            getPageElements() {
                return {
                    // 主要区域
                    loginPrompt: 'loginPrompt',
                    countSection: 'countSection',

                    // 统计元素
                    monthCountCount: 'monthCountCount',
                    inProgressCountCount: 'inProgressCountCount',
                    completedCountCount: 'completedCountCount',
                    differenceItemsCount: 'differenceItemsCount',

                    // 搜索和筛选
                    searchInput: 'searchInput',
                    statusFilter: 'statusFilter',
                    warehouseFilter: 'warehouseFilter',
                    searchBtn: 'searchBtn',

                    // 列表和分页
                    countTableBody: 'countTableBody',
                    pageInfo: 'pageInfo',
                    totalCount: 'totalCount',
                    prevPageBtn: 'prevPageBtn',
                    nextPageBtn: 'nextPageBtn',

                    // 用户相关
                    realName: 'realName',
                    userInfo: 'userInfo',
                    loginBtn: 'loginBtn',
                    logoutBtn: 'logoutBtn',
                    loginPromptBtn: 'loginPromptBtn',

                    // 加载指示器
                    loadingIndicator: 'loadingIndicator'
                };
            }

            /**
             * 绑定事件
             */
            bindEvents() {
                // 搜索按钮
                if (this.elements.searchBtn) {
                    this.elements.searchBtn.addEventListener('click', () => {
                        this.currentPage = 1;
                        this.loadCountPlans();
                    });
                }

                // 搜索输入框回车
                if (this.elements.searchInput) {
                    this.elements.searchInput.addEventListener('keypress', (e) => {
                        if (e.key === 'Enter') {
                            this.currentPage = 1;
                            this.loadCountPlans();
                        }
                    });
                }

                // 筛选器变化
                if (this.elements.statusFilter) {
                    this.elements.statusFilter.addEventListener('change', () => {
                        this.currentPage = 1;
                        this.loadCountPlans();
                    });
                }

                if (this.elements.warehouseFilter) {
                    this.elements.warehouseFilter.addEventListener('change', () => {
                        this.currentPage = 1;
                        this.loadCountPlans();
                    });
                }

                // 分页按钮
                if (this.elements.prevPageBtn) {
                    this.elements.prevPageBtn.addEventListener('click', () => {
                        if (this.currentPage > 1) {
                            this.currentPage--;
                            this.loadCountPlans();
                        }
                    });
                }

                if (this.elements.nextPageBtn) {
                    this.elements.nextPageBtn.addEventListener('click', () => {
                        const maxPage = Math.ceil(this.totalCount / this.pageSize);
                        if (this.currentPage < maxPage) {
                            this.currentPage++;
                            this.loadCountPlans();
                        }
                    });
                }

                // 创建盘点计划按钮
                const createBtn = document.getElementById('createCountBtn');
                if (createBtn) {
                    createBtn.addEventListener('click', () => {
                        this.createCountPlan();
                    });
                }

                // 退出登录
                if (this.elements.logoutBtn) {
                    this.elements.logoutBtn.addEventListener('click', async () => {
                        try {
                            await AV.User.logOut();
                            this.checkLoginStatus();
                        } catch (error) {
                            console.error('退出登录失败:', error);
                        }
                    });
                }

                // 登录按钮
                if (this.elements.loginBtn) {
                    this.elements.loginBtn.addEventListener('click', () => {
                        this.showLoginModal();
                    });
                }

                if (this.elements.loginPromptBtn) {
                    this.elements.loginPromptBtn.addEventListener('click', () => {
                        this.showLoginModal();
                    });
                }
            }

            /**
             * 显示加载指示器
             */
            showLoading() {
                if (this.elements.loadingIndicator) {
                    this.elements.loadingIndicator.style.display = 'flex';
                }
            }

            /**
             * 隐藏加载指示器
             */
            hideLoading() {
                if (this.elements.loadingIndicator) {
                    this.elements.loadingIndicator.style.display = 'none';
                }
            }

            /**
             * 检查登录状态
             */
            checkLoginStatus() {
                const currentUser = AV.User.current();

                // 直接获取DOM元素，避免依赖this.elements
                const loginPrompt = document.getElementById('loginPrompt');
                const countSection = document.getElementById('countSection');
                const realName = document.getElementById('realName');
                const userInfo = document.getElementById('userInfo');
                const loginBtn = document.getElementById('loginBtn');

                if (currentUser) {
                    // 用户已登录
                    if (loginPrompt) loginPrompt.style.display = 'none';
                    if (countSection) countSection.style.display = 'block';

                    // 更新用户信息显示
                    if (realName) realName.textContent = currentUser.get('realName') || currentUser.get('username');
                    if (userInfo) userInfo.style.display = 'flex';
                    if (loginBtn) loginBtn.style.display = 'none';

                    // 加载数据
                    this.loadStatistics();
                    this.loadCountPlans();
                    this.loadWarehouseFilter();
                } else {
                    // 用户未登录
                    if (loginPrompt) loginPrompt.style.display = 'block';
                    if (countSection) countSection.style.display = 'none';
                    if (userInfo) userInfo.style.display = 'none';
                    if (loginBtn) loginBtn.style.display = 'block';
                }
            }

            /**
             * 创建新盘点计划
             */
            createCountPlan() {
                this.showCountPlanModal();
            }

            /**
             * 显示登录弹窗
             */
            showLoginModal() {
                document.getElementById('loginModal').style.display = 'flex';
            }

            /**
             * 加载统计数据
             */
            async loadStatistics() {
                try {
                    const today = new Date();
                    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

                    // 查询本月盘点数量
                    const monthQuery = new AV.Query('CountPlan');
                    monthQuery.greaterThanOrEqualTo('createdAt', startOfMonth);
                    const monthCount = await monthQuery.count();

                    // 查询进行中的盘点数量
                    const inProgressQuery = new AV.Query('CountPlan');
                    inProgressQuery.equalTo('status', 'in-progress');
                    const inProgressCount = await inProgressQuery.count();

                    // 查询已完成的盘点数量
                    const completedQuery = new AV.Query('CountPlan');
                    completedQuery.equalTo('status', 'completed');
                    const completedCount = await completedQuery.count();

                    // 查询有差异的项目数量
                    const differenceQuery = new AV.Query('CountRecord');
                    differenceQuery.notEqualTo('systemQuantity', 'actualQuantity');
                    const differenceCount = await differenceQuery.count();

                    // 更新显示
                    if (this.elements.monthCountCount) {
                        this.elements.monthCountCount.textContent = monthCount;
                    }
                    if (this.elements.inProgressCountCount) {
                        this.elements.inProgressCountCount.textContent = inProgressCount;
                    }
                    if (this.elements.completedCountCount) {
                        this.elements.completedCountCount.textContent = completedCount;
                    }
                    if (this.elements.differenceItemsCount) {
                        this.elements.differenceItemsCount.textContent = differenceCount;
                    }
                } catch (error) {
                    console.error('加载统计数据失败:', error);
                }
            }

            /**
             * 加载仓库筛选选项
             */
            async loadWarehouseFilter() {
                try {
                    const query = new AV.Query('Warehouse');
                    query.equalTo('status', 'active');
                    query.ascending('name');
                    const warehouses = await query.find();

                    const select = this.elements.warehouseFilter;
                    if (select) {
                        select.innerHTML = '<option value="">全部仓库</option>';
                        warehouses.forEach(warehouse => {
                            const option = document.createElement('option');
                            option.value = warehouse.id;
                            option.textContent = warehouse.get('name');
                            select.appendChild(option);
                        });
                    }
                } catch (error) {
                    console.error('加载仓库筛选选项失败:', error);
                }
            }

            /**
             * 加载盘点计划列表
             */
            async loadCountPlans() {
                try {
                    this.showLoading();

                    const query = new AV.Query('CountPlan');

                    // 应用搜索条件
                    const searchText = this.elements.searchInput?.value?.trim();
                    if (searchText) {
                        const planNoQuery = new AV.Query('CountPlan');
                        planNoQuery.contains('planNo', searchText);

                        const nameQuery = new AV.Query('CountPlan');
                        nameQuery.contains('name', searchText);

                        query._orQuery([planNoQuery, nameQuery]);
                    }

                    // 应用状态筛选
                    const statusFilter = this.elements.statusFilter?.value;
                    if (statusFilter) {
                        query.equalTo('status', statusFilter);
                    }

                    // 应用仓库筛选
                    const warehouseFilter = this.elements.warehouseFilter?.value;
                    if (warehouseFilter) {
                        query.equalTo('warehouseId', AV.Object.createWithoutData('Warehouse', warehouseFilter));
                    }

                    // 包含关联数据
                    query.include('warehouseId');
                    query.include('createdBy');

                    // 排序和分页
                    query.descending('createdAt');
                    query.limit(this.pageSize);
                    query.skip((this.currentPage - 1) * this.pageSize);

                    const results = await query.find();
                    const total = await query.count();

                    this.totalCount = total;
                    this.renderCountPlanList(results);
                    this.updatePagination();

                    this.hideLoading();
                } catch (error) {
                    console.error('加载盘点计划列表失败:', error);
                    this.hideLoading();
                    alert('加载盘点计划列表失败: ' + error.message);
                }
            }

            /**
             * 渲染盘点计划列表
             */
            renderCountPlanList(plans) {
                if (!this.elements.countTableBody) return;

                if (plans.length === 0) {
                    this.elements.countTableBody.innerHTML = `
                        <tr>
                            <td colspan="8" class="px-6 py-8 text-center text-gray-500">
                                暂无盘点计划数据
                            </td>
                        </tr>
                    `;
                    return;
                }

                const html = plans.map(plan => {
                    const warehouse = plan.get('warehouseId');
                    const status = plan.get('status');
                    const statusText = this.getStatusText(status);
                    const statusClass = this.getStatusClass(status);
                    const typeText = this.getTypeText(plan.get('type'));

                    return `
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                ${plan.get('planNo')}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${plan.get('name')}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${warehouse ? warehouse.get('name') : '-'}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${typeText}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium rounded-full ${statusClass}">
                                    ${statusText}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${plan.get('plannedDate') || '-'}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${plan.get('manager') || '-'}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="countApp.viewCountPlan('${plan.id}')"
                                            class="text-blue-600 hover:text-blue-900">查看</button>
                                    ${status === 'draft' ? `
                                        <button onclick="countApp.editCountPlan('${plan.id}')"
                                                class="text-green-600 hover:text-green-900">编辑</button>
                                        <button onclick="countApp.deleteCountPlan('${plan.id}')"
                                                class="text-red-600 hover:text-red-900">删除</button>
                                    ` : ''}
                                    ${status === 'in-progress' ? `
                                        <button onclick="countApp.executeCount('${plan.id}')"
                                                class="text-purple-600 hover:text-purple-900">执行盘点</button>
                                    ` : ''}
                                    ${status === 'completed' ? `
                                        <button onclick="countApp.viewCountResult('${plan.id}')"
                                                class="text-indigo-600 hover:text-indigo-900">查看结果</button>
                                    ` : ''}
                                </div>
                            </td>
                        </tr>
                    `;
                }).join('');

                this.elements.countTableBody.innerHTML = html;
            }

            /**
             * 获取状态文本
             */
            getStatusText(status) {
                const statusMap = {
                    'draft': '草稿',
                    'in-progress': '进行中',
                    'completed': '已完成',
                    'cancelled': '已取消'
                };
                return statusMap[status] || status;
            }

            /**
             * 获取状态样式类
             */
            getStatusClass(status) {
                const classMap = {
                    'draft': 'status-draft',
                    'in-progress': 'status-in-progress',
                    'completed': 'status-completed',
                    'cancelled': 'status-cancelled'
                };
                return classMap[status] || 'status-draft';
            }

            /**
             * 获取类型文本
             */
            getTypeText(type) {
                const typeMap = {
                    'full': '全盘',
                    'partial': '抽盘',
                    'cycle': '循环盘点',
                    'spot': '临时盘点'
                };
                return typeMap[type] || type;
            }

            /**
             * 更新分页信息
             */
            updatePagination() {
                const start = (this.currentPage - 1) * this.pageSize + 1;
                const end = Math.min(this.currentPage * this.pageSize, this.totalCount);

                if (this.elements.pageInfo) {
                    this.elements.pageInfo.textContent = `${start}-${end}`;
                }
                if (this.elements.totalCount) {
                    this.elements.totalCount.textContent = this.totalCount;
                }

                // 更新分页按钮状态
                if (this.elements.prevPageBtn) {
                    this.elements.prevPageBtn.disabled = this.currentPage <= 1;
                }
                if (this.elements.nextPageBtn) {
                    const maxPage = Math.ceil(this.totalCount / this.pageSize);
                    this.elements.nextPageBtn.disabled = this.currentPage >= maxPage;
                }
            }

            /**
             * 显示盘点计划弹窗
             */
            async showCountPlanModal(planId = null) {
                const modal = document.getElementById('countPlanModal');
                const title = document.getElementById('countPlanModalTitle');
                const form = document.getElementById('countPlanForm');

                // 重置表单状态
                form.reset();
                const inputs = form.querySelectorAll('input, select, textarea, button');
                inputs.forEach(input => {
                    input.disabled = false;
                });
                document.getElementById('saveCountPlanDraft').style.display = 'inline-block';
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn) submitBtn.style.display = 'inline-block';

                if (planId) {
                    title.textContent = '查看盘点计划';
                    await this.loadCountPlanForEdit(planId);
                } else {
                    title.textContent = '新建盘点计划';
                    document.getElementById('editCountPlanId').value = '';
                    this.generateCountPlanNo();
                    // 设置默认日期为今天
                    document.getElementById('plannedDate').value = new Date().toISOString().split('T')[0];
                    // 设置默认负责人为当前用户
                    const currentUser = AV.User.current();
                    if (currentUser) {
                        document.getElementById('countManager').value = currentUser.get('realName') || currentUser.get('username');
                    }
                }

                await this.loadWarehouses();
                await this.loadProductSelection();
                modal.style.display = 'flex';
            }

            /**
             * 生成盘点计划编号
             */
            generateCountPlanNo() {
                const now = new Date();
                const dateStr = now.getFullYear().toString() +
                              (now.getMonth() + 1).toString().padStart(2, '0') +
                              now.getDate().toString().padStart(2, '0');
                const timeStr = now.getHours().toString().padStart(2, '0') +
                               now.getMinutes().toString().padStart(2, '0') +
                               now.getSeconds().toString().padStart(2, '0');
                const planNo = `COUNT${dateStr}${timeStr}`;
                document.getElementById('countPlanNo').value = planNo;
            }

            /**
             * 加载仓库列表
             */
            async loadWarehouses() {
                try {
                    const query = new AV.Query('Warehouse');
                    query.equalTo('status', 'active');
                    query.ascending('name');
                    const warehouses = await query.find();

                    const select = document.getElementById('countWarehouse');
                    select.innerHTML = '<option value="">请选择仓库</option>';

                    warehouses.forEach(warehouse => {
                        const option = document.createElement('option');
                        option.value = warehouse.id;
                        option.textContent = warehouse.get('name');
                        select.appendChild(option);
                    });
                } catch (error) {
                    console.error('加载仓库失败:', error);
                }
            }

            /**
             * 查看盘点计划
             */
            async viewCountPlan(planId) {
                await this.showCountPlanModal(planId);
                // 设置为只读模式
                setTimeout(() => {
                    const form = document.getElementById('countPlanForm');
                    const inputs = form.querySelectorAll('input, select, textarea, button');
                    inputs.forEach(input => {
                        if (input.type !== 'button' && input.id !== 'closeCountPlanModal' && input.id !== 'cancelCountPlanEdit') {
                            input.disabled = true;
                        }
                    });

                    // 隐藏操作按钮
                    document.getElementById('saveCountPlanDraft').style.display = 'none';
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) submitBtn.style.display = 'none';
                }, 100);
            }

            /**
             * 编辑盘点计划
             */
            editCountPlan(planId) {
                this.showCountPlanModal(planId);
            }

            /**
             * 删除盘点计划
             */
            async deleteCountPlan(planId) {
                if (!confirm('确定要删除这个盘点计划吗？')) {
                    return;
                }

                try {
                    const plan = AV.Object.createWithoutData('CountPlan', planId);
                    await plan.destroy();
                    alert('删除成功');
                    this.loadCountPlans();
                    this.loadStatistics();
                } catch (error) {
                    console.error('删除失败:', error);
                    alert('删除失败: ' + error.message);
                }
            }

            /**
             * 执行盘点
             */
            executeCount(planId) {
                this.showCountExecuteModal(planId);
            }

            /**
             * 加载商品选择列表
             */
            async loadProductSelection() {
                try {
                    const warehouseSelect = document.getElementById('countWarehouse');
                    const warehouseId = warehouseSelect.value;

                    if (!warehouseId) {
                        document.getElementById('productSelectionTable').innerHTML = '';
                        return;
                    }

                    const query = new AV.Query('Product');
                    query.equalTo('status', 'active');
                    query.include('categoryId');
                    query.ascending('name');
                    const products = await query.find();

                    const tbody = document.getElementById('productSelectionTable');
                    tbody.innerHTML = '';

                    for (const product of products) {
                        // 查询该商品在选定仓库的库存
                        const inventoryQuery = new AV.Query('Inventory');
                        inventoryQuery.equalTo('productId', product);
                        inventoryQuery.equalTo('warehouseId', AV.Object.createWithoutData('Warehouse', warehouseId));
                        const inventory = await inventoryQuery.first();

                        const currentStock = inventory ? inventory.get('availableQuantity') || 0 : 0;
                        const category = product.get('categoryId');

                        const row = document.createElement('tr');
                        row.className = 'hover:bg-gray-50';
                        row.innerHTML = `
                            <td class="px-4 py-2">
                                <input type="checkbox" class="product-checkbox rounded" value="${product.id}">
                            </td>
                            <td class="px-4 py-2">
                                <div class="text-sm font-medium text-gray-900">${product.get('name')}</div>
                                <div class="text-sm text-gray-500">${product.get('code')}</div>
                            </td>
                            <td class="px-4 py-2 text-sm text-gray-500">
                                ${category ? category.get('name') : '-'}
                            </td>
                            <td class="px-4 py-2 text-sm text-gray-900 font-medium">
                                ${currentStock}
                            </td>
                        `;
                        tbody.appendChild(row);
                    }

                    // 绑定商品复选框事件
                    const productCheckboxes = tbody.querySelectorAll('.product-checkbox');
                    productCheckboxes.forEach(checkbox => {
                        checkbox.addEventListener('change', this.updateSelectAllCheckbox);
                    });

                } catch (error) {
                    console.error('加载商品选择列表失败:', error);
                }
            }

            /**
             * 更新全选复选框状态
             */
            updateSelectAllCheckbox() {
                const productCheckboxes = document.querySelectorAll('.product-checkbox');
                const checkedBoxes = document.querySelectorAll('.product-checkbox:checked');
                const selectAllCheckbox = document.getElementById('selectAllCheckbox');

                if (checkedBoxes.length === 0) {
                    selectAllCheckbox.indeterminate = false;
                    selectAllCheckbox.checked = false;
                } else if (checkedBoxes.length === productCheckboxes.length) {
                    selectAllCheckbox.indeterminate = false;
                    selectAllCheckbox.checked = true;
                } else {
                    selectAllCheckbox.indeterminate = true;
                    selectAllCheckbox.checked = false;
                }
            }

            /**
             * 保存盘点计划
             */
            async saveCountPlan(status = 'draft') {
                try {
                    // 获取表单数据
                    const planNo = document.getElementById('countPlanNo').value;
                    const name = document.getElementById('countPlanName').value;
                    const warehouseId = document.getElementById('countWarehouse').value;
                    const type = document.getElementById('countType').value;
                    const plannedDate = document.getElementById('plannedDate').value;
                    const manager = document.getElementById('countManager').value;
                    const description = document.getElementById('countDescription').value;
                    const editId = document.getElementById('editCountPlanId').value;

                    // 验证必填字段
                    if (!planNo || !name || !warehouseId || !type || !plannedDate || !manager) {
                        alert('请填写必填字段');
                        return;
                    }

                    // 获取选中的商品
                    const selectedProducts = this.getSelectedProducts();
                    if (selectedProducts.length === 0) {
                        alert('请至少选择一个商品进行盘点');
                        return;
                    }

                    // 保存盘点计划
                    let plan;
                    if (editId) {
                        plan = AV.Object.createWithoutData('CountPlan', editId);
                    } else {
                        plan = new AV.Object('CountPlan');
                        plan.set('planNo', planNo);
                        plan.set('createdBy', AV.User.current());
                    }

                    plan.set('name', name);
                    plan.set('warehouseId', AV.Object.createWithoutData('Warehouse', warehouseId));
                    plan.set('type', type);
                    plan.set('status', status);
                    plan.set('plannedDate', plannedDate);
                    plan.set('manager', manager);
                    plan.set('description', description);

                    await plan.save();

                    // 保存盘点范围
                    if (editId) {
                        // 删除原有范围
                        const scopeQuery = new AV.Query('CountScope');
                        scopeQuery.equalTo('planId', plan);
                        const oldScopes = await scopeQuery.find();
                        await AV.Object.destroyAll(oldScopes);
                    }

                    // 创建新范围
                    for (const productId of selectedProducts) {
                        const scope = new AV.Object('CountScope');
                        scope.set('planId', plan);
                        scope.set('productId', AV.Object.createWithoutData('Product', productId));
                        scope.set('warehouseId', AV.Object.createWithoutData('Warehouse', warehouseId));
                        await scope.save();
                    }

                    alert(status === 'draft' ? '草稿保存成功' : '盘点计划创建成功，可以开始盘点了');
                    document.getElementById('countPlanModal').style.display = 'none';
                    this.loadCountPlans();
                    this.loadStatistics();

                } catch (error) {
                    console.error('保存盘点计划失败:', error);
                    alert('保存失败: ' + error.message);
                }
            }

            /**
             * 获取选中的商品ID列表
             */
            getSelectedProducts() {
                const checkedBoxes = document.querySelectorAll('.product-checkbox:checked');
                return Array.from(checkedBoxes).map(checkbox => checkbox.value);
            }

            /**
             * 加载盘点计划数据用于编辑
             */
            async loadCountPlanForEdit(planId) {
                try {
                    const query = new AV.Query('CountPlan');
                    query.include('warehouseId');
                    const plan = await query.get(planId);

                    // 填充基本信息
                    document.getElementById('editCountPlanId').value = planId;
                    document.getElementById('countPlanNo').value = plan.get('planNo');
                    document.getElementById('countPlanName').value = plan.get('name');
                    document.getElementById('countType').value = plan.get('type') || '';
                    document.getElementById('plannedDate').value = plan.get('plannedDate') || '';
                    document.getElementById('countManager').value = plan.get('manager') || '';
                    document.getElementById('countDescription').value = plan.get('description') || '';

                    // 设置仓库
                    const warehouse = plan.get('warehouseId');
                    if (warehouse) {
                        document.getElementById('countWarehouse').value = warehouse.id;
                    }

                    // 加载并选中商品
                    await this.loadProductSelection();
                    await this.loadSelectedProducts(planId);

                } catch (error) {
                    console.error('加载盘点计划数据失败:', error);
                    alert('加载盘点计划数据失败: ' + error.message);
                }
            }

            /**
             * 加载已选中的商品
             */
            async loadSelectedProducts(planId) {
                try {
                    const query = new AV.Query('CountScope');
                    query.equalTo('planId', AV.Object.createWithoutData('CountPlan', planId));
                    query.include('productId');
                    const scopes = await query.find();

                    const selectedProductIds = scopes.map(scope => scope.get('productId').id);

                    // 选中对应的复选框
                    const checkboxes = document.querySelectorAll('.product-checkbox');
                    checkboxes.forEach(checkbox => {
                        checkbox.checked = selectedProductIds.includes(checkbox.value);
                    });

                    this.updateSelectAllCheckbox();
                } catch (error) {
                    console.error('加载已选中商品失败:', error);
                }
            }

            /**
             * 显示盘点执行弹窗
             */
            async showCountExecuteModal(planId) {
                const modal = document.getElementById('countExecuteModal');
                const title = document.getElementById('countExecuteModalTitle');
                const content = document.getElementById('countExecuteContent');

                title.textContent = '盘点执行';
                content.innerHTML = '<div class="text-center py-8">加载中...</div>';
                modal.style.display = 'flex';

                try {
                    // 加载盘点计划信息
                    const planQuery = new AV.Query('CountPlan');
                    planQuery.include('warehouseId');
                    const plan = await planQuery.get(planId);

                    // 加载盘点范围
                    const scopeQuery = new AV.Query('CountScope');
                    scopeQuery.equalTo('planId', plan);
                    scopeQuery.include('productId');
                    scopeQuery.include('warehouseId');
                    const scopes = await scopeQuery.find();

                    // 生成盘点执行界面
                    content.innerHTML = this.generateCountExecuteContent(plan, scopes);

                    // 绑定事件
                    this.bindCountExecuteEvents(planId);

                } catch (error) {
                    console.error('加载盘点执行数据失败:', error);
                    content.innerHTML = '<div class="text-center py-8 text-red-500">加载失败: ' + error.message + '</div>';
                }
            }

            /**
             * 生成盘点执行内容
             */
            generateCountExecuteContent(plan, scopes) {
                const warehouse = plan.get('warehouseId');

                return `
                    <div class="space-y-6">
                        <!-- 盘点信息 -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h3 class="text-lg font-medium text-gray-900 mb-2">盘点信息</h3>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div><span class="font-medium">盘点编号:</span> ${plan.get('planNo')}</div>
                                <div><span class="font-medium">盘点名称:</span> ${plan.get('name')}</div>
                                <div><span class="font-medium">盘点仓库:</span> ${warehouse ? warehouse.get('name') : '-'}</div>
                                <div><span class="font-medium">盘点类型:</span> ${this.getTypeText(plan.get('type'))}</div>
                                <div><span class="font-medium">负责人:</span> ${plan.get('manager')}</div>
                                <div><span class="font-medium">计划日期:</span> ${plan.get('plannedDate')}</div>
                            </div>
                        </div>

                        <!-- 盘点商品列表 -->
                        <div>
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-medium text-gray-900">盘点商品</h3>
                                <button id="saveCountRecords" class="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-lg text-sm">
                                    保存盘点结果
                                </button>
                            </div>

                            <div class="overflow-x-auto">
                                <table class="min-w-full border border-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">商品信息</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">系统库存</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">实际库存</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">差异</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">备注</th>
                                        </tr>
                                    </thead>
                                    <tbody id="countRecordsTable">
                                        ${scopes.map(scope => this.generateCountRecordRow(scope)).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                `;
            }

            /**
             * 查看盘点结果
             */
            viewCountResult(planId) {
                this.showCountResultModal(planId);
            }

            /**
             * 生成盘点记录行
             */
            generateCountRecordRow(scope) {
                const product = scope.get('productId');
                const productId = product.id;

                return `
                    <tr class="hover:bg-gray-50" data-product-id="${productId}">
                        <td class="px-4 py-2">
                            <div class="text-sm font-medium text-gray-900">${product.get('name')}</div>
                            <div class="text-sm text-gray-500">${product.get('code')}</div>
                        </td>
                        <td class="px-4 py-2">
                            <span class="system-quantity text-sm font-medium text-gray-900">-</span>
                        </td>
                        <td class="px-4 py-2">
                            <input type="number" class="actual-quantity w-20 p-2 border border-gray-300 rounded text-sm"
                                   min="0" step="0.01" placeholder="实际数量">
                        </td>
                        <td class="px-4 py-2">
                            <span class="difference text-sm font-medium">-</span>
                        </td>
                        <td class="px-4 py-2">
                            <input type="text" class="remark w-32 p-2 border border-gray-300 rounded text-sm"
                                   placeholder="备注">
                        </td>
                    </tr>
                `;
            }

            /**
             * 绑定盘点执行事件
             */
            async bindCountExecuteEvents(planId) {
                // 加载系统库存数量
                await this.loadSystemQuantities(planId);

                // 绑定实际数量输入事件
                const actualQuantityInputs = document.querySelectorAll('.actual-quantity');
                actualQuantityInputs.forEach(input => {
                    input.addEventListener('input', this.calculateDifference);
                });

                // 绑定保存按钮事件
                const saveBtn = document.getElementById('saveCountRecords');
                if (saveBtn) {
                    saveBtn.addEventListener('click', () => {
                        this.saveCountRecords(planId);
                    });
                }
            }

            /**
             * 加载系统库存数量
             */
            async loadSystemQuantities(planId) {
                try {
                    const planQuery = new AV.Query('CountPlan');
                    planQuery.include('warehouseId');
                    const plan = await planQuery.get(planId);
                    const warehouse = plan.get('warehouseId');

                    const rows = document.querySelectorAll('#countRecordsTable tr');

                    for (const row of rows) {
                        const productId = row.dataset.productId;
                        if (!productId) continue;

                        // 查询库存
                        const inventoryQuery = new AV.Query('Inventory');
                        inventoryQuery.equalTo('productId', AV.Object.createWithoutData('Product', productId));
                        inventoryQuery.equalTo('warehouseId', warehouse);
                        const inventory = await inventoryQuery.first();

                        const systemQty = inventory ? inventory.get('availableQuantity') || 0 : 0;
                        const systemQuantitySpan = row.querySelector('.system-quantity');
                        if (systemQuantitySpan) {
                            systemQuantitySpan.textContent = systemQty.toString();
                        }
                    }
                } catch (error) {
                    console.error('加载系统库存数量失败:', error);
                }
            }

            /**
             * 计算差异
             */
            calculateDifference(event) {
                const row = event.target.closest('tr');
                const systemQuantitySpan = row.querySelector('.system-quantity');
                const actualQuantityInput = row.querySelector('.actual-quantity');
                const differenceSpan = row.querySelector('.difference');

                const systemQty = parseFloat(systemQuantitySpan.textContent) || 0;
                const actualQty = parseFloat(actualQuantityInput.value) || 0;
                const difference = actualQty - systemQty;

                differenceSpan.textContent = difference.toFixed(2);

                // 设置差异样式
                differenceSpan.className = 'difference text-sm font-medium';
                if (difference > 0) {
                    differenceSpan.classList.add('diff-positive');
                } else if (difference < 0) {
                    differenceSpan.classList.add('diff-negative');
                } else {
                    differenceSpan.classList.add('diff-zero');
                }
            }

            /**
             * 保存盘点记录
             */
            async saveCountRecords(planId) {
                try {
                    const rows = document.querySelectorAll('#countRecordsTable tr');
                    const records = [];

                    // 验证数据
                    for (const row of rows) {
                        const productId = row.dataset.productId;
                        const systemQuantitySpan = row.querySelector('.system-quantity');
                        const actualQuantityInput = row.querySelector('.actual-quantity');
                        const remarkInput = row.querySelector('.remark');

                        if (!productId || !actualQuantityInput.value.trim()) {
                            alert('请填写所有商品的实际库存数量');
                            return;
                        }

                        const systemQty = parseFloat(systemQuantitySpan.textContent) || 0;
                        const actualQty = parseFloat(actualQuantityInput.value) || 0;
                        const difference = actualQty - systemQty;
                        const remark = remarkInput.value.trim();

                        records.push({
                            productId,
                            systemQuantity: systemQty,
                            actualQuantity: actualQty,
                            difference,
                            remark
                        });
                    }

                    // 获取盘点计划和仓库信息
                    const planQuery = new AV.Query('CountPlan');
                    planQuery.include('warehouseId');
                    const plan = await planQuery.get(planId);
                    const warehouse = plan.get('warehouseId');

                    // 删除原有记录（如果存在）
                    const existingQuery = new AV.Query('CountRecord');
                    existingQuery.equalTo('planId', plan);
                    const existingRecords = await existingQuery.find();
                    if (existingRecords.length > 0) {
                        await AV.Object.destroyAll(existingRecords);
                    }

                    // 保存新记录
                    for (const recordData of records) {
                        const record = new AV.Object('CountRecord');
                        record.set('planId', plan);
                        record.set('productId', AV.Object.createWithoutData('Product', recordData.productId));
                        record.set('warehouseId', warehouse);
                        record.set('systemQuantity', recordData.systemQuantity);
                        record.set('actualQuantity', recordData.actualQuantity);
                        record.set('difference', recordData.difference);
                        record.set('countDate', new Date());
                        record.set('countBy', AV.User.current());
                        record.set('remark', recordData.remark);

                        await record.save();
                    }

                    // 更新盘点计划状态为已完成
                    plan.set('status', 'completed');
                    plan.set('completedDate', new Date());
                    await plan.save();

                    alert('盘点记录保存成功！');
                    document.getElementById('countExecuteModal').style.display = 'none';
                    this.loadCountPlans();
                    this.loadStatistics();

                } catch (error) {
                    console.error('保存盘点记录失败:', error);
                    alert('保存失败: ' + error.message);
                }
            }

            /**
             * 显示盘点结果弹窗
             */
            async showCountResultModal(planId) {
                const modal = document.getElementById('countExecuteModal');
                const title = document.getElementById('countExecuteModalTitle');
                const content = document.getElementById('countExecuteContent');

                title.textContent = '盘点结果';
                content.innerHTML = '<div class="text-center py-8">加载中...</div>';
                modal.style.display = 'flex';

                try {
                    // 加载盘点计划信息
                    const planQuery = new AV.Query('CountPlan');
                    planQuery.include('warehouseId');
                    const plan = await planQuery.get(planId);

                    // 加载盘点记录
                    const recordQuery = new AV.Query('CountRecord');
                    recordQuery.equalTo('planId', plan);
                    recordQuery.include('productId');
                    recordQuery.include('countBy');
                    const records = await recordQuery.find();

                    // 生成盘点结果界面
                    content.innerHTML = this.generateCountResultContent(plan, records);

                } catch (error) {
                    console.error('加载盘点结果数据失败:', error);
                    content.innerHTML = '<div class="text-center py-8 text-red-500">加载失败: ' + error.message + '</div>';
                }
            }

            /**
             * 生成盘点结果内容
             */
            generateCountResultContent(plan, records) {
                const warehouse = plan.get('warehouseId');
                const totalItems = records.length;
                const differenceItems = records.filter(r => r.get('difference') !== 0).length;
                const positiveItems = records.filter(r => r.get('difference') > 0).length;
                const negativeItems = records.filter(r => r.get('difference') < 0).length;

                return `
                    <div class="space-y-6">
                        <!-- 盘点信息 -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h3 class="text-lg font-medium text-gray-900 mb-2">盘点信息</h3>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div><span class="font-medium">盘点编号:</span> ${plan.get('planNo')}</div>
                                <div><span class="font-medium">盘点名称:</span> ${plan.get('name')}</div>
                                <div><span class="font-medium">盘点仓库:</span> ${warehouse ? warehouse.get('name') : '-'}</div>
                                <div><span class="font-medium">盘点类型:</span> ${this.getTypeText(plan.get('type'))}</div>
                                <div><span class="font-medium">负责人:</span> ${plan.get('manager')}</div>
                                <div><span class="font-medium">完成日期:</span> ${plan.get('completedDate') ? plan.get('completedDate').toLocaleDateString() : '-'}</div>
                            </div>
                        </div>

                        <!-- 盘点统计 -->
                        <div class="grid grid-cols-4 gap-4">
                            <div class="bg-blue-50 p-4 rounded-lg text-center">
                                <div class="text-2xl font-bold text-blue-600">${totalItems}</div>
                                <div class="text-sm text-blue-600">盘点商品</div>
                            </div>
                            <div class="bg-red-50 p-4 rounded-lg text-center">
                                <div class="text-2xl font-bold text-red-600">${differenceItems}</div>
                                <div class="text-sm text-red-600">有差异</div>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg text-center">
                                <div class="text-2xl font-bold text-green-600">${positiveItems}</div>
                                <div class="text-sm text-green-600">盘盈</div>
                            </div>
                            <div class="bg-orange-50 p-4 rounded-lg text-center">
                                <div class="text-2xl font-bold text-orange-600">${negativeItems}</div>
                                <div class="text-sm text-orange-600">盘亏</div>
                            </div>
                        </div>

                        <!-- 盘点明细 -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">盘点明细</h3>
                            <div class="overflow-x-auto">
                                <table class="min-w-full border border-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">商品信息</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">系统库存</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">实际库存</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">差异</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">盘点人</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">备注</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${records.map(record => this.generateCountResultRow(record)).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                `;
            }

            /**
             * 生成盘点结果行
             */
            generateCountResultRow(record) {
                const product = record.get('productId');
                const countBy = record.get('countBy');
                const difference = record.get('difference');

                let differenceClass = 'text-gray-900';
                let differenceText = difference.toFixed(2);

                if (difference > 0) {
                    differenceClass = 'text-green-600 font-medium';
                    differenceText = '+' + differenceText;
                } else if (difference < 0) {
                    differenceClass = 'text-red-600 font-medium';
                }

                return `
                    <tr class="hover:bg-gray-50">
                        <td class="px-4 py-2">
                            <div class="text-sm font-medium text-gray-900">${product.get('name')}</div>
                            <div class="text-sm text-gray-500">${product.get('code')}</div>
                        </td>
                        <td class="px-4 py-2 text-sm text-gray-900">${record.get('systemQuantity')}</td>
                        <td class="px-4 py-2 text-sm text-gray-900">${record.get('actualQuantity')}</td>
                        <td class="px-4 py-2 text-sm ${differenceClass}">${differenceText}</td>
                        <td class="px-4 py-2 text-sm text-gray-500">${countBy ? countBy.get('realName') || countBy.get('username') : '-'}</td>
                        <td class="px-4 py-2 text-sm text-gray-500">${record.get('remark') || '-'}</td>
                    </tr>
                `;
            }
        }

        // 页面加载完成后的统一初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化应用
            initCountApp();

            // 绑定盘点计划弹窗事件
            document.getElementById('closeCountPlanModal').addEventListener('click', function() {
                document.getElementById('countPlanModal').style.display = 'none';
            });

            document.getElementById('cancelCountPlanEdit').addEventListener('click', function() {
                document.getElementById('countPlanModal').style.display = 'none';
            });

            // 绑定盘点执行弹窗事件
            document.getElementById('closeCountExecuteModal').addEventListener('click', function() {
                document.getElementById('countExecuteModal').style.display = 'none';
            });

            // 绑定仓库选择变化事件
            document.getElementById('countWarehouse').addEventListener('change', async function() {
                if (countApp) {
                    await countApp.loadProductSelection();
                }
            });

            // 绑定盘点计划表单提交
            document.getElementById('countPlanForm').addEventListener('submit', async function(e) {
                e.preventDefault();
                if (countApp) {
                    await countApp.saveCountPlan('in-progress');
                }
            });

            document.getElementById('saveCountPlanDraft').addEventListener('click', async function() {
                if (countApp) {
                    await countApp.saveCountPlan('draft');
                }
            });

            // 绑定商品选择相关事件
            document.getElementById('selectAllProducts').addEventListener('click', function() {
                const checkboxes = document.querySelectorAll('#productSelectionTable input[type="checkbox"]');
                checkboxes.forEach(checkbox => checkbox.checked = true);
                document.getElementById('selectAllCheckbox').checked = true;
            });

            document.getElementById('clearAllProducts').addEventListener('click', function() {
                const checkboxes = document.querySelectorAll('#productSelectionTable input[type="checkbox"]');
                checkboxes.forEach(checkbox => checkbox.checked = false);
                document.getElementById('selectAllCheckbox').checked = false;
            });

            document.getElementById('selectAllCheckbox').addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('#productSelectionTable input[type="checkbox"]');
                checkboxes.forEach(checkbox => checkbox.checked = this.checked);
            });

            // 绑定登录相关事件
            const loginBtn = document.getElementById('loginBtn');
            if (loginBtn) {
                loginBtn.addEventListener('click', function() {
                    document.getElementById('loginModal').style.display = 'flex';
                });
            }

            const loginPromptBtn = document.getElementById('loginPromptBtn');
            if (loginPromptBtn) {
                loginPromptBtn.addEventListener('click', function() {
                    document.getElementById('loginModal').style.display = 'flex';
                });
            }

            const closeLoginModal = document.getElementById('closeLoginModal');
            if (closeLoginModal) {
                closeLoginModal.addEventListener('click', function() {
                    document.getElementById('loginModal').style.display = 'none';
                });
            }

            // 登录表单提交
            const loginForm = document.getElementById('loginForm');
            if (loginForm) {
                loginForm.addEventListener('submit', async function(e) {
                    e.preventDefault();

                    const username = document.getElementById('loginUsername').value;
                    const password = document.getElementById('loginPassword').value;

                    try {
                        await AV.User.logIn(username, password);
                        document.getElementById('loginModal').style.display = 'none';
                        if (countApp) {
                            countApp.checkLoginStatus();
                        }
                        alert('登录成功');
                    } catch (error) {
                        console.error('登录失败:', error);
                        alert('登录失败: ' + error.message);
                    }
                });
            }

            // 注册表单提交
            const registerForm = document.getElementById('registerForm');
            if (registerForm) {
                registerForm.addEventListener('submit', async function(e) {
                    e.preventDefault();

                    const username = document.getElementById('registerUsername').value;
                    const realName = document.getElementById('registerRealName').value;
                    const phone = document.getElementById('registerPhone').value;
                    const department = document.getElementById('registerDepartment').value;
                    const password = document.getElementById('registerPassword').value;
                    const passwordConfirm = document.getElementById('registerPasswordConfirm').value;

                    if (password !== passwordConfirm) {
                        alert('两次输入的密码不一致');
                        return;
                    }

                    try {
                        const user = new AV.User();
                        user.setUsername(username);
                        user.setPassword(password);
                        user.set('realName', realName);
                        user.set('phone', phone);
                        user.set('department', department);
                        user.set('roles', []);

                        await user.signUp();
                        document.getElementById('loginModal').style.display = 'none';
                        if (countApp) {
                            countApp.checkLoginStatus();
                        }
                        alert('注册成功');
                    } catch (error) {
                        console.error('注册失败:', error);
                        alert('注册失败: ' + error.message);
                    }
                });
            }

            // 切换登录/注册表单
            const showRegisterBtn = document.getElementById('showRegisterBtn');
            const showLoginBtn = document.getElementById('showLoginBtn');
            const loginFormDiv = document.getElementById('loginForm');
            const registerFormDiv = document.getElementById('registerForm');
            const backToLoginDiv = document.getElementById('backToLoginDiv');
            const loginModalTitle = document.getElementById('loginModalTitle');

            if (showRegisterBtn) {
                showRegisterBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    loginFormDiv.style.display = 'none';
                    registerFormDiv.style.display = 'block';
                    showRegisterBtn.parentElement.style.display = 'none';
                    backToLoginDiv.style.display = 'block';
                    loginModalTitle.textContent = '注册';
                });
            }

            if (showLoginBtn) {
                showLoginBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    registerFormDiv.style.display = 'none';
                    loginFormDiv.style.display = 'block';
                    backToLoginDiv.style.display = 'none';
                    showRegisterBtn.parentElement.style.display = 'block';
                    loginModalTitle.textContent = '登录';
                });
            }
        });
    </script>
