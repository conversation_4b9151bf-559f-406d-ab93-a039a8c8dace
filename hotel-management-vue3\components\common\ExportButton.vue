<template>
  <div class="relative inline-block text-left">
    <button
      @click="toggleDropdown"
      class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      :disabled="loading || !data.length"
    >
      <Icon name="mdi:download" size="16" class="mr-2" />
      {{ loading ? '导出中...' : '导出数据' }}
      <Icon name="mdi:chevron-down" size="16" class="ml-2" />
    </button>

    <!-- 下拉菜单 -->
    <div
      v-show="showDropdown"
      class="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50"
      @click.stop
    >
      <div class="py-1">
        <button
          @click="handleExport('excel')"
          class="group flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 w-full text-left"
          :disabled="loading"
        >
          <Icon name="mdi:file-excel" size="16" class="mr-3 text-green-600" />
          导出为 Excel
          <span class="ml-auto text-xs text-gray-500">.xlsx</span>
        </button>
        
        <button
          @click="handleExport('pdf')"
          class="group flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 w-full text-left"
          :disabled="loading"
        >
          <Icon name="mdi:file-pdf-box" size="16" class="mr-3 text-red-600" />
          导出为 PDF
          <span class="ml-auto text-xs text-gray-500">.pdf</span>
        </button>
        
        <button
          @click="handleExport('csv')"
          class="group flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 w-full text-left"
          :disabled="loading"
        >
          <Icon name="mdi:file-delimited" size="16" class="mr-3 text-blue-600" />
          导出为 CSV
          <span class="ml-auto text-xs text-gray-500">.csv</span>
        </button>
        
        <div class="border-t border-gray-100"></div>
        
        <button
          @click="handleCopyText"
          class="group flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 w-full text-left"
          :disabled="loading"
        >
          <Icon name="mdi:content-copy" size="16" class="mr-3 text-gray-600" />
          复制为文本
          <span class="ml-auto text-xs text-gray-500">剪贴板</span>
        </button>
      </div>
    </div>

    <!-- 遮罩层 -->
    <div
      v-show="showDropdown"
      class="fixed inset-0 z-40"
      @click="showDropdown = false"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { useExport, type ExportColumn } from '~/composables/useExport'
import { useGlobalNotifications } from '~/composables/useNotifications'

interface Props {
  data: any[]
  columns: ExportColumn[]
  filename?: string
  title?: string
}

const props = withDefaults(defineProps<Props>(), {
  filename: '',
  title: ''
})

const emit = defineEmits<{
  exported: [format: string, success: boolean]
}>()

const { exportData, exportToText } = useExport()
const { success, error } = useGlobalNotifications()

// 状态
const showDropdown = ref(false)
const loading = ref(false)

// 切换下拉菜单
const toggleDropdown = () => {
  if (!props.data.length) return
  showDropdown.value = !showDropdown.value
}

// 处理导出
const handleExport = async (format: 'excel' | 'pdf' | 'csv') => {
  if (!props.data.length) {
    alert('没有数据可导出')
    return
  }

  loading.value = true
  showDropdown.value = false

  try {
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
    const filename = props.filename || `export_${timestamp}`
    
    const result = await exportData({
      data: props.data,
      columns: props.columns,
      filename: `${filename}.${format === 'excel' ? 'xlsx' : format}`,
      title: props.title,
      format
    })

    if (result.success) {
      // 显示成功消息
      const formatNames = {
        excel: 'Excel',
        pdf: 'PDF',
        csv: 'CSV'
      }

      success(
        '导出成功',
        `${formatNames[format]} 文件已成功导出到下载文件夹`
      )
      emit('exported', format, true)
    } else {
      error(
        '导出失败',
        result.error || '导出过程中发生未知错误'
      )
      emit('exported', format, false)
    }
  } catch (err: any) {
    console.error('导出失败:', err)
    error(
      '导出失败',
      err.message || '导出过程中发生未知错误'
    )
    emit('exported', format, false)
  } finally {
    loading.value = false
  }
}

// 处理复制文本
const handleCopyText = async () => {
  if (!props.data.length) {
    alert('没有数据可复制')
    return
  }

  loading.value = true
  showDropdown.value = false

  try {
    const result = await exportToText({
      data: props.data,
      columns: props.columns,
      title: props.title,
      format: 'csv' // 这里format参数不会被使用
    })

    if (result.success) {
      success(
        '复制成功',
        '数据已复制到剪贴板，可以粘贴到其他应用中'
      )
      emit('exported', 'text', true)
    } else {
      error(
        '复制失败',
        result.error || '复制过程中发生未知错误'
      )
      emit('exported', 'text', false)
    }
  } catch (err: any) {
    console.error('复制失败:', err)
    error(
      '复制失败',
      err.message || '复制过程中发生未知错误'
    )
    emit('exported', 'text', false)
  } finally {
    loading.value = false
  }
}

// 点击外部关闭下拉菜单
onMounted(() => {
  document.addEventListener('click', (e) => {
    if (!e.target?.closest('.relative')) {
      showDropdown.value = false
    }
  })
})
</script>
