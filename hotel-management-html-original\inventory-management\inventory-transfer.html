<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调拨管理 - 酒店管理系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <style>
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .btn-fix {
            touch-action: manipulation;
        }
        .status-draft { @apply bg-yellow-100 text-yellow-800; }
        .status-confirmed { @apply bg-blue-100 text-blue-800; }
        .status-completed { @apply bg-green-100 text-green-800; }
        .status-cancelled { @apply bg-red-100 text-red-800; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-900">调拨管理</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- 用户信息 -->
                    <div id="userInfo" class="flex items-center space-x-2" style="display: none;">
                        <span class="text-sm text-gray-700">欢迎，</span>
                        <span id="realName" class="text-sm font-medium text-gray-900"></span>
                        <button id="logoutBtn" class="text-sm text-red-600 hover:text-red-800 btn-fix">退出</button>
                    </div>
                    <button id="loginBtn" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                        登录
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 登录提示 -->
    <div id="loginPrompt" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8" style="display: none;">
        <div class="bg-white rounded-lg shadow p-8 text-center">
            <div class="mb-4">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">需要登录</h3>
            <p class="text-gray-500 mb-4">请先登录系统以使用调拨管理功能</p>
            <button id="loginPromptBtn" class="bg-purple-500 hover:bg-purple-600 text-white px-6 py-2 rounded-lg btn-fix">
                立即登录
            </button>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div id="transferSection" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8" style="display: none;">
        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">今日调拨</dt>
                            <dd class="text-lg font-medium text-gray-900" id="todayTransferCount">0</dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">待确认</dt>
                            <dd class="text-lg font-medium text-gray-900" id="pendingTransferCount">0</dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">本月调拨</dt>
                            <dd class="text-lg font-medium text-gray-900" id="monthTransferCount">0</dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">调拨金额</dt>
                            <dd class="text-lg font-medium text-gray-900" id="totalTransferAmount">¥0.00</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作栏 -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                    <div class="flex-1 min-w-0">
                        <h2 class="text-lg font-medium text-gray-900">调拨单列表</h2>
                    </div>
                    <div class="mt-4 sm:mt-0 sm:ml-4">
                        <button id="createTransferBtn" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                            新建调拨单
                        </button>
                    </div>
                </div>
            </div>

            <!-- 搜索和筛选 -->
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <input type="text" id="searchInput" placeholder="搜索调拨单号..."
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm">
                    </div>
                    <div>
                        <select id="statusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm">
                            <option value="">全部状态</option>
                            <option value="draft">草稿</option>
                            <option value="confirmed">已确认</option>
                            <option value="completed">已完成</option>
                            <option value="cancelled">已取消</option>
                        </select>
                    </div>
                    <div>
                        <input type="date" id="dateFilter"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm">
                    </div>
                    <div>
                        <button id="searchBtn" class="w-full bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md text-sm btn-fix">
                            搜索
                        </button>
                    </div>
                </div>
            </div>

            <!-- 调拨单列表 -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">调拨单号</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">源仓库</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">目标仓库</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">调拨金额</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody id="transferTableBody" class="bg-white divide-y divide-gray-200">
                        <!-- 调拨单列表将在这里动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span id="pageInfo">1-20</span> 条，共 <span id="totalCount">0</span> 条
                    </div>
                    <div class="flex space-x-2">
                        <button id="prevPageBtn" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 btn-fix">上一页</button>
                        <button id="nextPageBtn" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 btn-fix">下一页</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载指示器 -->
        <div id="loadingIndicator" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-40" style="display: none;">
            <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-500"></div>
                <span class="text-gray-700">加载中...</span>
            </div>
        </div>
    </div>

    <!-- 登录弹窗 -->
    <div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style="display: none;">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 fade-in">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 id="loginModalTitle" class="text-xl font-semibold text-gray-800">登录</h2>
                    <button id="closeLoginModal" class="text-gray-400 hover:text-gray-600 text-2xl btn-fix">&times;</button>
                </div>
            </div>
            <div class="p-6">
                <!-- 登录表单 -->
                <form id="loginForm" class="space-y-4">
                    <div>
                        <label for="loginUsername" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                        <input type="text" id="loginUsername" required
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm">
                    </div>
                    <div>
                        <label for="loginPassword" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                        <input type="password" id="loginPassword" required
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm">
                    </div>
                    <button type="submit" class="w-full bg-purple-500 hover:bg-purple-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                        登录
                    </button>
                </form>

                <!-- 注册表单 -->
                <form id="registerForm" class="space-y-4" style="display: none;">
                    <div>
                        <label for="registerUsername" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                        <input type="text" id="registerUsername" required
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm">
                    </div>
                    <div>
                        <label for="registerRealName" class="block text-sm font-medium text-gray-700 mb-1">真实姓名</label>
                        <input type="text" id="registerRealName" required
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm">
                    </div>
                    <div>
                        <label for="registerPhone" class="block text-sm font-medium text-gray-700 mb-1">电话</label>
                        <input type="tel" id="registerPhone" required
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm">
                    </div>
                    <div>
                        <label for="registerDepartment" class="block text-sm font-medium text-gray-700 mb-1">部门</label>
                        <select id="registerDepartment" required
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm">
                            <option value="">请选择部门</option>
                            <option value="工程部">工程部</option>
                            <option value="前厅部">前厅部</option>
                            <option value="客房部">客房部</option>
                            <option value="餐饮部">餐饮部</option>
                            <option value="保安部">保安部</option>
                            <option value="财务部">财务部</option>
                            <option value="人事部">人事部</option>
                            <option value="销售部">销售部</option>
                        </select>
                    </div>
                    <div>
                        <label for="registerPassword" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                        <input type="password" id="registerPassword" required minlength="6"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm">
                    </div>
                    <div>
                        <label for="registerPasswordConfirm" class="block text-sm font-medium text-gray-700 mb-1">确认密码</label>
                        <input type="password" id="registerPasswordConfirm" required minlength="6"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm">
                    </div>
                    <button type="submit" class="w-full bg-purple-500 hover:bg-purple-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                        注册
                    </button>
                </form>

                <div class="mt-4 text-center text-sm">
                    <span class="text-gray-600">还没有账号？</span>
                    <a href="#" id="showRegisterBtn" class="text-purple-500 hover:text-purple-600 font-medium">立即注册</a>
                </div>
                <div class="mt-2 text-center text-sm" id="backToLoginDiv" style="display: none;">
                    <span class="text-gray-600">已有账号？</span>
                    <a href="#" id="showLoginBtn" class="text-purple-500 hover:text-purple-600 font-medium">立即登录</a>
                </div>
            </div>
        </div>
    </div>

    <!-- 调拨单编辑弹窗 -->
    <div id="transferOrderModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-5xl max-h-[90vh] overflow-y-auto fade-in">
            <div class="p-4 border-b border-gray-200 sticky top-0 bg-white">
                <div class="flex justify-between items-center">
                    <h2 id="transferOrderModalTitle" class="text-xl font-semibold text-gray-800">新建调拨单</h2>
                    <button id="closeTransferOrderModal" class="text-gray-400 hover:text-gray-600 text-2xl btn-fix">&times;</button>
                </div>
            </div>
            <div class="p-6">
                <form id="transferOrderForm" class="space-y-6">
                    <input type="hidden" id="editTransferOrderId">

                    <!-- 基本信息 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="transferOrderNo" class="block text-sm font-medium text-gray-700 mb-1">调拨单号 <span class="text-red-500">*</span></label>
                            <input type="text" id="transferOrderNo" required readonly
                                class="w-full p-3 border border-gray-300 rounded-lg bg-gray-50 text-sm">
                        </div>
                        <div>
                            <label for="transferDate" class="block text-sm font-medium text-gray-700 mb-1">调拨日期 <span class="text-red-500">*</span></label>
                            <input type="date" id="transferDate" required
                                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm">
                        </div>
                        <div>
                            <label for="sourceWarehouse" class="block text-sm font-medium text-gray-700 mb-1">源仓库 <span class="text-red-500">*</span></label>
                            <select id="sourceWarehouse" required
                                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm">
                                <option value="">请选择源仓库</option>
                                <!-- 仓库选项将动态加载 -->
                            </select>
                        </div>
                        <div>
                            <label for="targetWarehouse" class="block text-sm font-medium text-gray-700 mb-1">目标仓库 <span class="text-red-500">*</span></label>
                            <select id="targetWarehouse" required
                                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm">
                                <option value="">请选择目标仓库</option>
                                <!-- 仓库选项将动态加载 -->
                            </select>
                        </div>
                        <div class="md:col-span-2">
                            <label for="transferReason" class="block text-sm font-medium text-gray-700 mb-1">调拨原因</label>
                            <textarea id="transferReason" rows="3"
                                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm"></textarea>
                        </div>
                    </div>

                    <!-- 调拨明细 -->
                    <div>
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-800">调拨明细</h3>
                            <button type="button" id="addTransferItem" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                                添加商品
                            </button>
                        </div>

                        <div class="overflow-x-auto">
                            <table class="min-w-full border border-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">商品</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">源库存</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">调拨数量</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">单价</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">金额</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">批次号</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="transferItemsTable">
                                    <!-- 调拨明细将在这里动态添加 -->
                                </tbody>
                            </table>
                        </div>

                        <div class="mt-4 text-right">
                            <span class="text-lg font-semibold">总金额: ¥<span id="totalTransferAmount">0.00</span></span>
                        </div>
                    </div>

                    <div class="flex gap-3 pt-4">
                        <button type="button" id="cancelTransferOrderEdit" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                            取消
                        </button>
                        <button type="button" id="saveTransferOrderDraft" class="flex-1 bg-yellow-500 hover:bg-yellow-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                            保存草稿
                        </button>
                        <button type="submit" class="flex-1 bg-purple-500 hover:bg-purple-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                            确认调拨
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 引入模块化JS文件 -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/base-app.js"></script>
    <script>
        // 初始化调拨管理应用
        let transferApp;

        // 统一的初始化函数
        function initTransferApp() {
            if (typeof AV !== 'undefined') {
                try {
                    transferApp = new TransferApp();
                    transferApp.init();
                    console.log('调拨管理应用初始化成功');
                } catch (error) {
                    console.error('调拨管理应用初始化失败:', error);
                    alert('应用初始化失败: ' + error.message);
                }
            } else {
                setTimeout(initTransferApp, 100);
            }
        }

        // 调拨管理应用类
        class TransferApp {
            constructor() {
                this.currentPage = 1;
                this.pageSize = 20;
                this.totalCount = 0;
                this.elements = {};
            }

            /**
             * 初始化应用
             */
            init() {
                this.initElements();
                this.bindEvents();
                this.checkLoginStatus();
            }

            /**
             * 初始化DOM元素
             */
            initElements() {
                const pageElements = this.getPageElements();
                for (const [key, id] of Object.entries(pageElements)) {
                    this.elements[key] = document.getElementById(id);
                }
            }

            /**
             * 获取页面特定的DOM元素
             */
            getPageElements() {
                return {
                    // 主要区域
                    loginPrompt: 'loginPrompt',
                    transferSection: 'transferSection',

                    // 统计元素
                    todayTransferCount: 'todayTransferCount',
                    pendingTransferCount: 'pendingTransferCount',
                    monthTransferCount: 'monthTransferCount',
                    totalTransferAmount: 'totalTransferAmount',

                    // 搜索和筛选
                    searchInput: 'searchInput',
                    statusFilter: 'statusFilter',
                    dateFilter: 'dateFilter',
                    searchBtn: 'searchBtn',

                    // 列表和分页
                    transferTableBody: 'transferTableBody',
                    pageInfo: 'pageInfo',
                    totalCount: 'totalCount',
                    prevPageBtn: 'prevPageBtn',
                    nextPageBtn: 'nextPageBtn',

                    // 用户相关
                    realName: 'realName',
                    userInfo: 'userInfo',
                    loginBtn: 'loginBtn',
                    logoutBtn: 'logoutBtn',
                    loginPromptBtn: 'loginPromptBtn',

                    // 加载指示器
                    loadingIndicator: 'loadingIndicator'
                };
            }

            /**
             * 绑定事件
             */
            bindEvents() {
                // 搜索按钮
                if (this.elements.searchBtn) {
                    this.elements.searchBtn.addEventListener('click', () => {
                        this.currentPage = 1;
                        this.loadTransferOrders();
                    });
                }

                // 搜索输入框回车
                if (this.elements.searchInput) {
                    this.elements.searchInput.addEventListener('keypress', (e) => {
                        if (e.key === 'Enter') {
                            this.currentPage = 1;
                            this.loadTransferOrders();
                        }
                    });
                }

                // 筛选器变化
                if (this.elements.statusFilter) {
                    this.elements.statusFilter.addEventListener('change', () => {
                        this.currentPage = 1;
                        this.loadTransferOrders();
                    });
                }

                if (this.elements.dateFilter) {
                    this.elements.dateFilter.addEventListener('change', () => {
                        this.currentPage = 1;
                        this.loadTransferOrders();
                    });
                }

                // 分页按钮
                if (this.elements.prevPageBtn) {
                    this.elements.prevPageBtn.addEventListener('click', () => {
                        if (this.currentPage > 1) {
                            this.currentPage--;
                            this.loadTransferOrders();
                        }
                    });
                }

                if (this.elements.nextPageBtn) {
                    this.elements.nextPageBtn.addEventListener('click', () => {
                        const maxPage = Math.ceil(this.totalCount / this.pageSize);
                        if (this.currentPage < maxPage) {
                            this.currentPage++;
                            this.loadTransferOrders();
                        }
                    });
                }

                // 创建调拨单按钮
                const createBtn = document.getElementById('createTransferBtn');
                if (createBtn) {
                    createBtn.addEventListener('click', () => {
                        this.createTransferOrder();
                    });
                }

                // 退出登录
                if (this.elements.logoutBtn) {
                    this.elements.logoutBtn.addEventListener('click', async () => {
                        try {
                            await AV.User.logOut();
                            this.checkLoginStatus();
                        } catch (error) {
                            console.error('退出登录失败:', error);
                        }
                    });
                }

                // 登录按钮
                if (this.elements.loginBtn) {
                    this.elements.loginBtn.addEventListener('click', () => {
                        this.showLoginModal();
                    });
                }

                if (this.elements.loginPromptBtn) {
                    this.elements.loginPromptBtn.addEventListener('click', () => {
                        this.showLoginModal();
                    });
                }
            }

            /**
             * 显示加载指示器
             */
            showLoading() {
                if (this.elements.loadingIndicator) {
                    this.elements.loadingIndicator.style.display = 'flex';
                }
            }

            /**
             * 隐藏加载指示器
             */
            hideLoading() {
                if (this.elements.loadingIndicator) {
                    this.elements.loadingIndicator.style.display = 'none';
                }
            }

            /**
             * 检查登录状态
             */
            checkLoginStatus() {
                const currentUser = AV.User.current();

                // 直接获取DOM元素，避免依赖this.elements
                const loginPrompt = document.getElementById('loginPrompt');
                const transferSection = document.getElementById('transferSection');
                const realName = document.getElementById('realName');
                const userInfo = document.getElementById('userInfo');
                const loginBtn = document.getElementById('loginBtn');

                if (currentUser) {
                    // 用户已登录
                    if (loginPrompt) loginPrompt.style.display = 'none';
                    if (transferSection) transferSection.style.display = 'block';

                    // 更新用户信息显示
                    if (realName) realName.textContent = currentUser.get('realName') || currentUser.get('username');
                    if (userInfo) userInfo.style.display = 'flex';
                    if (loginBtn) loginBtn.style.display = 'none';

                    // 加载数据
                    this.loadStatistics();
                    this.loadTransferOrders();
                } else {
                    // 用户未登录
                    if (loginPrompt) loginPrompt.style.display = 'block';
                    if (transferSection) transferSection.style.display = 'none';
                    if (userInfo) userInfo.style.display = 'none';
                    if (loginBtn) loginBtn.style.display = 'block';
                }
            }

            /**
             * 加载统计数据
             */
            async loadStatistics() {
                try {
                    const today = new Date();
                    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
                    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

                    // 查询今日调拨单数量
                    const todayQuery = new AV.Query('TransferOrder');
                    todayQuery.greaterThanOrEqualTo('createdAt', startOfDay);
                    const todayCount = await todayQuery.count();

                    // 查询待确认调拨单数量
                    const pendingQuery = new AV.Query('TransferOrder');
                    pendingQuery.equalTo('status', 'draft');
                    const pendingCount = await pendingQuery.count();

                    // 查询本月调拨单数量
                    const monthQuery = new AV.Query('TransferOrder');
                    monthQuery.greaterThanOrEqualTo('createdAt', startOfMonth);
                    const monthCount = await monthQuery.count();

                    // 查询总调拨金额（本月）
                    const amountQuery = new AV.Query('TransferOrder');
                    amountQuery.greaterThanOrEqualTo('createdAt', startOfMonth);
                    amountQuery.equalTo('status', 'completed');
                    const orders = await amountQuery.find();
                    const totalAmount = orders.reduce((sum, order) => sum + (order.get('totalAmount') || 0), 0);

                    // 更新显示
                    if (this.elements.todayTransferCount) {
                        this.elements.todayTransferCount.textContent = todayCount;
                    }
                    if (this.elements.pendingTransferCount) {
                        this.elements.pendingTransferCount.textContent = pendingCount;
                    }
                    if (this.elements.monthTransferCount) {
                        this.elements.monthTransferCount.textContent = monthCount;
                    }
                    if (this.elements.totalTransferAmount) {
                        this.elements.totalTransferAmount.textContent = '¥' + totalAmount.toFixed(2);
                    }
                } catch (error) {
                    console.error('加载统计数据失败:', error);
                }
            }

            /**
             * 创建新调拨单
             */
            createTransferOrder() {
                this.showTransferOrderModal();
            }

            /**
             * 显示登录弹窗
             */
            showLoginModal() {
                document.getElementById('loginModal').style.display = 'flex';
            }

            /**
             * 显示调拨单弹窗
             */
            async showTransferOrderModal(orderId = null) {
                const modal = document.getElementById('transferOrderModal');
                const title = document.getElementById('transferOrderModalTitle');
                const form = document.getElementById('transferOrderForm');

                // 重置表单状态
                form.reset();
                const inputs = form.querySelectorAll('input, select, textarea, button');
                inputs.forEach(input => {
                    input.disabled = false;
                });
                document.getElementById('saveTransferOrderDraft').style.display = 'inline-block';
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn) submitBtn.style.display = 'inline-block';

                if (orderId) {
                    title.textContent = '查看调拨单';
                    await this.loadTransferOrderForEdit(orderId);
                } else {
                    title.textContent = '新建调拨单';
                    document.getElementById('editTransferOrderId').value = '';
                    this.generateTransferOrderNo();
                    this.clearTransferItems();
                    // 设置默认日期为今天
                    document.getElementById('transferDate').value = new Date().toISOString().split('T')[0];
                }

                await this.loadWarehouses();
                modal.style.display = 'flex';
            }

            /**
             * 生成调拨单号
             */
            generateTransferOrderNo() {
                const now = new Date();
                const dateStr = now.getFullYear().toString() +
                              (now.getMonth() + 1).toString().padStart(2, '0') +
                              now.getDate().toString().padStart(2, '0');
                const timeStr = now.getHours().toString().padStart(2, '0') +
                               now.getMinutes().toString().padStart(2, '0') +
                               now.getSeconds().toString().padStart(2, '0');
                const orderNo = `TF${dateStr}${timeStr}`;
                document.getElementById('transferOrderNo').value = orderNo;
            }

            /**
             * 加载仓库列表
             */
            async loadWarehouses() {
                try {
                    const query = new AV.Query('Warehouse');
                    query.equalTo('status', 'active');
                    query.ascending('name');
                    const warehouses = await query.find();

                    const sourceSelect = document.getElementById('sourceWarehouse');
                    const targetSelect = document.getElementById('targetWarehouse');

                    sourceSelect.innerHTML = '<option value="">请选择源仓库</option>';
                    targetSelect.innerHTML = '<option value="">请选择目标仓库</option>';

                    warehouses.forEach(warehouse => {
                        const sourceOption = document.createElement('option');
                        sourceOption.value = warehouse.id;
                        sourceOption.textContent = warehouse.get('name');
                        sourceSelect.appendChild(sourceOption);

                        const targetOption = document.createElement('option');
                        targetOption.value = warehouse.id;
                        targetOption.textContent = warehouse.get('name');
                        targetSelect.appendChild(targetOption);
                    });
                } catch (error) {
                    console.error('加载仓库失败:', error);
                }
            }

            /**
             * 清空调拨明细
             */
            clearTransferItems() {
                const tbody = document.getElementById('transferItemsTable');
                tbody.innerHTML = '';
                this.updateTotalTransferAmount();
            }

            /**
             * 更新总金额
             */
            updateTotalTransferAmount() {
                const rows = document.querySelectorAll('#transferItemsTable tr');
                let total = 0;

                rows.forEach(row => {
                    const amountCell = row.querySelector('.item-amount');
                    if (amountCell) {
                        total += parseFloat(amountCell.textContent) || 0;
                    }
                });

                document.getElementById('totalTransferAmount').textContent = total.toFixed(2);
            }

            /**
             * 加载调拨单列表
             */
            async loadTransferOrders() {
                try {
                    this.showLoading();

                    const query = new AV.Query('TransferOrder');

                    // 应用搜索条件
                    const searchText = this.elements.searchInput?.value?.trim();
                    if (searchText) {
                        query.contains('orderNo', searchText);
                    }

                    // 应用状态筛选
                    const statusFilter = this.elements.statusFilter?.value;
                    if (statusFilter) {
                        query.equalTo('status', statusFilter);
                    }

                    // 应用日期筛选
                    const dateFilter = this.elements.dateFilter?.value;
                    if (dateFilter) {
                        const filterDate = new Date(dateFilter);
                        const nextDay = new Date(filterDate);
                        nextDay.setDate(nextDay.getDate() + 1);
                        query.greaterThanOrEqualTo('createdAt', filterDate);
                        query.lessThan('createdAt', nextDay);
                    }

                    // 包含关联数据
                    query.include('sourceWarehouseId');
                    query.include('targetWarehouseId');
                    query.include('createdBy');

                    // 排序和分页
                    query.descending('createdAt');
                    query.limit(this.pageSize);
                    query.skip((this.currentPage - 1) * this.pageSize);

                    const results = await query.find();
                    const total = await query.count();

                    this.totalCount = total;
                    this.renderTransferOrderList(results);
                    this.updatePagination();

                    this.hideLoading();
                } catch (error) {
                    console.error('加载调拨单列表失败:', error);
                    this.hideLoading();
                    alert('加载调拨单列表失败: ' + error.message);
                }
            }

            /**
             * 渲染调拨单列表
             */
            renderTransferOrderList(orders) {
                if (!this.elements.transferTableBody) return;

                if (orders.length === 0) {
                    this.elements.transferTableBody.innerHTML = `
                        <tr>
                            <td colspan="7" class="px-6 py-8 text-center text-gray-500">
                                暂无调拨单数据
                            </td>
                        </tr>
                    `;
                    return;
                }

                const html = orders.map(order => {
                    const sourceWarehouse = order.get('sourceWarehouseId');
                    const targetWarehouse = order.get('targetWarehouseId');
                    const status = order.get('status');
                    const statusText = this.getStatusText(status);
                    const statusClass = this.getStatusClass(status);

                    return `
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                ${order.get('orderNo')}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${sourceWarehouse ? sourceWarehouse.get('name') : '-'}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${targetWarehouse ? targetWarehouse.get('name') : '-'}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ¥${(order.get('totalAmount') || 0).toFixed(2)}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium rounded-full ${statusClass}">
                                    ${statusText}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${order.createdAt.toLocaleDateString()}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="transferApp.viewTransferOrder('${order.id}')"
                                            class="text-blue-600 hover:text-blue-900">查看</button>
                                    ${status === 'draft' ? `
                                        <button onclick="transferApp.editTransferOrder('${order.id}')"
                                                class="text-green-600 hover:text-green-900">编辑</button>
                                        <button onclick="transferApp.deleteTransferOrder('${order.id}')"
                                                class="text-red-600 hover:text-red-900">删除</button>
                                    ` : ''}
                                </div>
                            </td>
                        </tr>
                    `;
                }).join('');

                this.elements.transferTableBody.innerHTML = html;
            }

            /**
             * 获取状态文本
             */
            getStatusText(status) {
                const statusMap = {
                    'draft': '草稿',
                    'confirmed': '已确认',
                    'completed': '已完成',
                    'cancelled': '已取消'
                };
                return statusMap[status] || status;
            }

            /**
             * 获取状态样式类
             */
            getStatusClass(status) {
                const classMap = {
                    'draft': 'status-draft',
                    'confirmed': 'status-confirmed',
                    'completed': 'status-completed',
                    'cancelled': 'status-cancelled'
                };
                return classMap[status] || 'status-draft';
            }

            /**
             * 更新分页信息
             */
            updatePagination() {
                const start = (this.currentPage - 1) * this.pageSize + 1;
                const end = Math.min(this.currentPage * this.pageSize, this.totalCount);

                if (this.elements.pageInfo) {
                    this.elements.pageInfo.textContent = `${start}-${end}`;
                }
                if (this.elements.totalCount) {
                    this.elements.totalCount.textContent = this.totalCount;
                }

                // 更新分页按钮状态
                if (this.elements.prevPageBtn) {
                    this.elements.prevPageBtn.disabled = this.currentPage <= 1;
                }
                if (this.elements.nextPageBtn) {
                    const maxPage = Math.ceil(this.totalCount / this.pageSize);
                    this.elements.nextPageBtn.disabled = this.currentPage >= maxPage;
                }
            }

            /**
             * 查看调拨单
             */
            async viewTransferOrder(orderId) {
                await this.showTransferOrderModal(orderId);
                // 设置为只读模式
                setTimeout(() => {
                    const form = document.getElementById('transferOrderForm');
                    const inputs = form.querySelectorAll('input, select, textarea, button');
                    inputs.forEach(input => {
                        if (input.type !== 'button' && input.id !== 'closeTransferOrderModal' && input.id !== 'cancelTransferOrderEdit') {
                            input.disabled = true;
                        }
                    });

                    // 隐藏操作按钮
                    document.getElementById('saveTransferOrderDraft').style.display = 'none';
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) submitBtn.style.display = 'none';
                }, 100);
            }

            /**
             * 编辑调拨单
             */
            editTransferOrder(orderId) {
                this.showTransferOrderModal(orderId);
            }

            /**
             * 删除调拨单
             */
            async deleteTransferOrder(orderId) {
                if (!confirm('确定要删除这个调拨单吗？')) {
                    return;
                }

                try {
                    const order = AV.Object.createWithoutData('TransferOrder', orderId);
                    await order.destroy();
                    alert('删除成功');
                    this.loadTransferOrders();
                    this.loadStatistics();
                } catch (error) {
                    console.error('删除失败:', error);
                    alert('删除失败: ' + error.message);
                }
            }

            /**
             * 加载调拨单数据用于编辑
             */
            async loadTransferOrderForEdit(orderId) {
                try {
                    const query = new AV.Query('TransferOrder');
                    query.include('sourceWarehouseId');
                    query.include('targetWarehouseId');
                    const order = await query.get(orderId);

                    // 填充基本信息
                    document.getElementById('editTransferOrderId').value = orderId;
                    document.getElementById('transferOrderNo').value = order.get('orderNo');
                    document.getElementById('transferDate').value = order.get('transferDate') || new Date().toISOString().split('T')[0];
                    document.getElementById('transferReason').value = order.get('reason') || '';

                    // 设置仓库
                    const sourceWarehouse = order.get('sourceWarehouseId');
                    if (sourceWarehouse) {
                        document.getElementById('sourceWarehouse').value = sourceWarehouse.id;
                    }

                    const targetWarehouse = order.get('targetWarehouseId');
                    if (targetWarehouse) {
                        document.getElementById('targetWarehouse').value = targetWarehouse.id;
                    }

                    // 加载调拨明细
                    await this.loadTransferOrderItems(orderId);

                } catch (error) {
                    console.error('加载调拨单数据失败:', error);
                    alert('加载调拨单数据失败: ' + error.message);
                }
            }

            /**
             * 加载调拨单明细
             */
            async loadTransferOrderItems(orderId) {
                try {
                    const query = new AV.Query('TransferOrderItem');
                    query.equalTo('orderId', AV.Object.createWithoutData('TransferOrder', orderId));
                    query.include('productId');
                    const items = await query.find();

                    const tbody = document.getElementById('transferItemsTable');
                    tbody.innerHTML = '';

                    items.forEach(item => {
                        this.addTransferItemRow(item);
                    });

                    this.updateTotalTransferAmount();
                } catch (error) {
                    console.error('加载调拨明细失败:', error);
                    // 如果是表不存在的错误，清空明细表格
                    if (error.message.includes('Class or object doesn\'t exists')) {
                        console.log('TransferOrderItem 表不存在，将在保存时自动创建');
                        const tbody = document.getElementById('transferItemsTable');
                        tbody.innerHTML = '';
                        this.updateTotalTransferAmount();
                    }
                }
            }
        }

        // 页面加载完成后的统一初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化应用
            initTransferApp();

            // 绑定调拨单弹窗事件
            document.getElementById('closeTransferOrderModal').addEventListener('click', function() {
                document.getElementById('transferOrderModal').style.display = 'none';
            });

            document.getElementById('cancelTransferOrderEdit').addEventListener('click', function() {
                document.getElementById('transferOrderModal').style.display = 'none';
            });

            document.getElementById('addTransferItem').addEventListener('click', function() {
                if (transferApp) {
                    transferApp.addTransferItemRow();
                }
            });

            // 绑定调拨单表单提交
            document.getElementById('transferOrderForm').addEventListener('submit', async function(e) {
                e.preventDefault();
                if (transferApp) {
                    await transferApp.saveTransferOrder('confirmed');
                }
            });

            document.getElementById('saveTransferOrderDraft').addEventListener('click', async function() {
                if (transferApp) {
                    await transferApp.saveTransferOrder('draft');
                }
            });

            // 绑定登录相关事件
            const loginBtn = document.getElementById('loginBtn');
            if (loginBtn) {
                loginBtn.addEventListener('click', function() {
                    document.getElementById('loginModal').style.display = 'flex';
                });
            }

            const loginPromptBtn = document.getElementById('loginPromptBtn');
            if (loginPromptBtn) {
                loginPromptBtn.addEventListener('click', function() {
                    document.getElementById('loginModal').style.display = 'flex';
                });
            }

            const closeLoginModal = document.getElementById('closeLoginModal');
            if (closeLoginModal) {
                closeLoginModal.addEventListener('click', function() {
                    document.getElementById('loginModal').style.display = 'none';
                });
            }

            // 登录表单提交
            const loginForm = document.getElementById('loginForm');
            if (loginForm) {
                loginForm.addEventListener('submit', async function(e) {
                    e.preventDefault();

                    const username = document.getElementById('loginUsername').value;
                    const password = document.getElementById('loginPassword').value;

                    try {
                        await AV.User.logIn(username, password);
                        document.getElementById('loginModal').style.display = 'none';
                        if (transferApp) {
                            transferApp.checkLoginStatus();
                        }
                        alert('登录成功');
                    } catch (error) {
                        console.error('登录失败:', error);
                        alert('登录失败: ' + error.message);
                    }
                });
            }

            // 注册表单提交
            const registerForm = document.getElementById('registerForm');
            if (registerForm) {
                registerForm.addEventListener('submit', async function(e) {
                    e.preventDefault();

                    const username = document.getElementById('registerUsername').value;
                    const realName = document.getElementById('registerRealName').value;
                    const phone = document.getElementById('registerPhone').value;
                    const department = document.getElementById('registerDepartment').value;
                    const password = document.getElementById('registerPassword').value;
                    const passwordConfirm = document.getElementById('registerPasswordConfirm').value;

                    if (password !== passwordConfirm) {
                        alert('两次输入的密码不一致');
                        return;
                    }

                    try {
                        const user = new AV.User();
                        user.setUsername(username);
                        user.setPassword(password);
                        user.set('realName', realName);
                        user.set('phone', phone);
                        user.set('department', department);
                        user.set('roles', []);

                        await user.signUp();
                        document.getElementById('loginModal').style.display = 'none';
                        if (transferApp) {
                            transferApp.checkLoginStatus();
                        }
                        alert('注册成功');
                    } catch (error) {
                        console.error('注册失败:', error);
                        alert('注册失败: ' + error.message);
                    }
                });
            }

            // 切换登录/注册表单
            const showRegisterBtn = document.getElementById('showRegisterBtn');
            const showLoginBtn = document.getElementById('showLoginBtn');
            const loginFormDiv = document.getElementById('loginForm');
            const registerFormDiv = document.getElementById('registerForm');
            const backToLoginDiv = document.getElementById('backToLoginDiv');
            const loginModalTitle = document.getElementById('loginModalTitle');

            if (showRegisterBtn) {
                showRegisterBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    loginFormDiv.style.display = 'none';
                    registerFormDiv.style.display = 'block';
                    showRegisterBtn.parentElement.style.display = 'none';
                    backToLoginDiv.style.display = 'block';
                    loginModalTitle.textContent = '注册';
                });
            }

            if (showLoginBtn) {
                showLoginBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    registerFormDiv.style.display = 'none';
                    loginFormDiv.style.display = 'block';
                    backToLoginDiv.style.display = 'none';
                    showRegisterBtn.parentElement.style.display = 'block';
                    loginModalTitle.textContent = '登录';
                });
            }
        });
    </script>
