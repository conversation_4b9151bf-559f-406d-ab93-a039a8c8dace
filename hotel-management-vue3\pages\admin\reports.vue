<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">数据统计</h1>
          <p class="mt-1 text-sm text-gray-600">
            查看系统使用情况和业务数据分析
          </p>
        </div>
        
        <div class="mt-4 sm:mt-0 flex space-x-3">
          <!-- 时间范围选择 -->
          <select 
            v-model="selectedPeriod"
            @change="loadReportData"
            class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="7">最近7天</option>
            <option value="30">最近30天</option>
            <option value="90">最近90天</option>
            <option value="365">最近一年</option>
          </select>
          
          <button 
            @click="exportReport"
            class="border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 transition-colors flex items-center"
          >
            <Icon name="mdi:download" size="16" class="mr-1" />
            导出报表
          </button>
        </div>
      </div>
    </div>

    <!-- 概览统计 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600">总用户数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ reportData.overview.totalUsers }}</p>
            <p class="text-xs text-green-600 mt-1">
              <Icon name="mdi:trending-up" size="12" class="inline mr-1" />
              +{{ reportData.overview.userGrowth }}% 较上期
            </p>
          </div>
          <Icon name="mdi:account-group" size="32" class="text-blue-600" />
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600">工作日志</p>
            <p class="text-2xl font-semibold text-gray-900">{{ reportData.overview.totalWorkLogs }}</p>
            <p class="text-xs text-green-600 mt-1">
              <Icon name="mdi:trending-up" size="12" class="inline mr-1" />
              +{{ reportData.overview.workLogGrowth }}% 较上期
            </p>
          </div>
          <Icon name="mdi:notebook" size="32" class="text-green-600" />
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600">报修工单</p>
            <p class="text-2xl font-semibold text-gray-900">{{ reportData.overview.totalRepairs }}</p>
            <p class="text-xs text-red-600 mt-1">
              <Icon name="mdi:trending-down" size="12" class="inline mr-1" />
              -{{ reportData.overview.repairReduction }}% 较上期
            </p>
          </div>
          <Icon name="mdi:wrench" size="32" class="text-orange-600" />
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600">库存价值</p>
            <p class="text-2xl font-semibold text-gray-900">¥{{ reportData.overview.inventoryValue.toLocaleString() }}</p>
            <p class="text-xs text-blue-600 mt-1">
              <Icon name="mdi:trending-up" size="12" class="inline mr-1" />
              +{{ reportData.overview.inventoryGrowth }}% 较上期
            </p>
          </div>
          <Icon name="mdi:package-variant" size="32" class="text-purple-600" />
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- 用户活跃度趋势 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">用户活跃度趋势</h3>
        </div>
        <div class="p-6">
          <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
            <div class="text-center">
              <Icon name="mdi:chart-line" size="48" class="text-gray-400 mx-auto mb-2" />
              <p class="text-gray-500 text-sm">图表组件待集成</p>
              <p class="text-xs text-gray-400">可使用 Chart.js 或 ECharts</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 工作日志统计 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">工作日志统计</h3>
        </div>
        <div class="p-6">
          <div class="space-y-4">
            <div v-for="logType in reportData.workLogStats" :key="logType.type" class="flex items-center justify-between">
              <div class="flex items-center">
                <div 
                  class="w-3 h-3 rounded-full mr-3"
                  :style="{ backgroundColor: logType.color }"
                ></div>
                <span class="text-sm text-gray-700">{{ logType.name }}</span>
              </div>
              <div class="text-right">
                <div class="text-sm font-medium text-gray-900">{{ logType.count }}</div>
                <div class="text-xs text-gray-500">{{ logType.percentage }}%</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- 报修工单状态分布 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">报修工单状态分布</h3>
        </div>
        <div class="p-6">
          <div class="space-y-4">
            <div v-for="status in reportData.repairStats" :key="status.status" class="flex items-center justify-between">
              <div class="flex items-center">
                <div 
                  class="w-3 h-3 rounded-full mr-3"
                  :style="{ backgroundColor: status.color }"
                ></div>
                <span class="text-sm text-gray-700">{{ status.name }}</span>
              </div>
              <div class="text-right">
                <div class="text-sm font-medium text-gray-900">{{ status.count }}</div>
                <div class="text-xs text-gray-500">{{ status.percentage }}%</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 部门活跃度排名 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">部门活跃度排名</h3>
        </div>
        <div class="p-6">
          <div class="space-y-4">
            <div v-for="(dept, index) in reportData.departmentStats" :key="dept.name" class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium mr-3">
                  {{ index + 1 }}
                </div>
                <span class="text-sm text-gray-700">{{ dept.name }}</span>
              </div>
              <div class="text-right">
                <div class="text-sm font-medium text-gray-900">{{ dept.activeUsers }}</div>
                <div class="text-xs text-gray-500">活跃用户</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详细数据表格 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900">详细数据</h3>
          
          <!-- 表格切换 -->
          <div class="flex space-x-1 bg-gray-100 rounded-lg p-1">
            <button 
              v-for="tab in dataTabs" 
              :key="tab.id"
              @click="activeDataTab = tab.id"
              :class="activeDataTab === tab.id ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-500 hover:text-gray-700'"
              class="px-3 py-1 text-sm font-medium rounded-md transition-colors"
            >
              {{ tab.name }}
            </button>
          </div>
        </div>
      </div>

      <!-- 用户数据表格 -->
      <div v-if="activeDataTab === 'users'" class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">部门</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">工作日志</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">报修工单</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后活跃</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="user in reportData.userDetails" :key="user.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium mr-3">
                    {{ user.name.charAt(0) }}
                  </div>
                  <div class="text-sm font-medium text-gray-900">{{ user.name }}</div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ user.department }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ user.workLogCount }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ user.repairCount }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatTime(user.lastActive) }}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 其他数据表格可以类似实现 -->
      <div v-else class="p-12 text-center">
        <Icon name="mdi:table" size="48" class="text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">{{ dataTabs.find(t => t.id === activeDataTab)?.name }} 数据</h3>
        <p class="text-gray-600">详细数据表格待实现</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面元数据
definePageMeta({
  title: '数据统计',
  middleware: 'auth',
  requiresAdmin: true
})

// 检查管理员权限
const authStore = useAuthStore()
if (!authStore.isAdmin) {
  throw createError({
    statusCode: 403,
    statusMessage: '权限不足，无法访问数据统计页面'
  })
}

// 响应式数据
const selectedPeriod = ref('30')
const activeDataTab = ref('users')

const dataTabs = [
  { id: 'users', name: '用户数据' },
  { id: 'worklogs', name: '工作日志' },
  { id: 'repairs', name: '报修工单' },
  { id: 'inventory', name: '库存数据' }
]

const reportData = reactive({
  overview: {
    totalUsers: 25,
    userGrowth: 12,
    totalWorkLogs: 156,
    workLogGrowth: 8,
    totalRepairs: 34,
    repairReduction: 5,
    inventoryValue: 125000,
    inventoryGrowth: 3
  },
  workLogStats: [
    { type: 'daily', name: '日常工作', count: 89, percentage: 57, color: '#3B82F6' },
    { type: 'maintenance', name: '设备维护', count: 34, percentage: 22, color: '#10B981' },
    { type: 'cleaning', name: '清洁工作', count: 23, percentage: 15, color: '#F59E0B' },
    { type: 'other', name: '其他', count: 10, percentage: 6, color: '#6B7280' }
  ],
  repairStats: [
    { status: 'completed', name: '已完成', count: 18, percentage: 53, color: '#10B981' },
    { status: 'in_progress', name: '处理中', count: 8, percentage: 24, color: '#3B82F6' },
    { status: 'pending', name: '待处理', count: 6, percentage: 18, color: '#F59E0B' },
    { status: 'cancelled', name: '已取消', count: 2, percentage: 5, color: '#6B7280' }
  ],
  departmentStats: [
    { name: '工程部', activeUsers: 8 },
    { name: '前厅部', activeUsers: 6 },
    { name: '客房部', activeUsers: 5 },
    { name: '餐饮部', activeUsers: 4 },
    { name: '保安部', activeUsers: 2 }
  ],
  userDetails: [
    {
      id: '1',
      name: '张工程师',
      department: '工程部',
      workLogCount: 23,
      repairCount: 12,
      lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000)
    },
    {
      id: '2',
      name: '前台小李',
      department: '前厅部',
      workLogCount: 18,
      repairCount: 3,
      lastActive: new Date(Date.now() - 1 * 60 * 60 * 1000)
    },
    {
      id: '3',
      name: '客房小王',
      department: '客房部',
      workLogCount: 15,
      repairCount: 5,
      lastActive: new Date(Date.now() - 4 * 60 * 60 * 1000)
    }
  ]
})

// 方法
const loadReportData = async () => {
  try {
    // 这里应该根据selectedPeriod调用API加载数据
    console.log('加载', selectedPeriod.value, '天的数据')
  } catch (error) {
    console.error('加载报表数据失败:', error)
  }
}

const exportReport = () => {
  // 这里应该实现导出功能
  alert('导出功能待实现')
}

const formatTime = (date: Date) => {
  const now = new Date()
  const targetDate = new Date(date)
  const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000)
  
  if (diffInSeconds < 60) {
    return '刚刚'
  } else if (diffInSeconds < 3600) {
    return `${Math.floor(diffInSeconds / 60)}分钟前`
  } else if (diffInSeconds < 86400) {
    return `${Math.floor(diffInSeconds / 3600)}小时前`
  } else {
    return targetDate.toLocaleDateString('zh-CN')
  }
}

// 生命周期
onMounted(() => {
  loadReportData()
})

// 页面标题
useHead({
  title: '数据统计 - 酒店管理系统'
})
</script>
