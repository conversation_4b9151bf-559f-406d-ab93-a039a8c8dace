/**
 * 主页模块 - 工作日志列表和发布功能
 * 继承自BaseWorkLogApp，实现主页特定功能
 */

class MainPageApp extends BaseWorkLogApp {
    constructor() {
        super({
            pageType: 'main',
            requiredElements: ['postForm', 'logsList', 'loadingIndicator']
        });

        // 分页相关
        this.currentPage = 0;
        this.hasMoreData = true;
        this.isLoading = false;

        // 筛选相关
        this.filterOptions = {
            startDate: null,
            endDate: null,
            userId: null
        };
        this.isLoadingUsers = false;

        // 上传的图片列表
        this.uploadedImages = [];

        // 绑定方法上下文
        this.handlePost = this.handlePost.bind(this);
        this.handleDeleteLog = this.handleDeleteLog.bind(this);
        this.loadLogs = this.loadLogs.bind(this);
        this.handleScroll = this.handleScroll.bind(this);
        this.handleApplyFilter = this.handleApplyFilter.bind(this);
        this.handleClearFilter = this.handleClearFilter.bind(this);
        this.handleImageUpload = this.handleImageUpload.bind(this);
    }

    /**
     * 获取页面特定的DOM元素
     */
    getPageElements() {
        return {
            postForm: 'postForm',
            postContent: 'postContent',
            imageInput: 'imageInput',
            uploadArea: 'uploadArea',
            imagePreview: 'imagePreview',
            logsList: 'logsList',
            loadingIndicator: 'loadingIndicator',
            noMoreContent: 'noMoreContent',
            editRealNameBtn: 'editRealNameBtn',
            editRealNameModal: 'editRealNameModal',
            editRealNameForm: 'editRealNameForm',
            newRealName: 'newRealName',
            cancelEditRealName: 'cancelEditRealName',
            realName: 'realName',
            username: 'username',
            postSection: 'postSection',
            specialFunctionsSection: 'specialFunctionsSection',
            welcomeSection: 'welcomeSection',
            filterSection: 'filterSection',
            startDate: 'startDate',
            endDate: 'endDate',
            userFilter: 'userFilter',
            applyFilter: 'applyFilter',
            clearFilter: 'clearFilter'
        };
    }

    /**
     * 绑定页面特定事件
     */
    bindPageEvents() {
        // 发布表单提交
        if (this.elements.postForm) {
            this.elements.postForm.addEventListener('submit', this.handlePost);
        }

        // 图片上传相关事件
        this.bindImageUploadEvents();

        // 编辑真实姓名相关事件
        if (this.elements.editRealNameBtn) {
            this.elements.editRealNameBtn.addEventListener('click', () => {
                this.showEditRealNameModal();
            });
        }

        if (this.elements.cancelEditRealName) {
            this.elements.cancelEditRealName.addEventListener('click', () => {
                this.hideEditRealNameModal();
            });
        }

        if (this.elements.editRealNameForm) {
            this.elements.editRealNameForm.addEventListener('submit', (e) => {
                this.handleEditRealName(e);
            });
        }

        // 筛选功能事件绑定
        if (this.elements.applyFilter) {
            this.elements.applyFilter.addEventListener('click', this.handleApplyFilter);
        }

        if (this.elements.clearFilter) {
            this.elements.clearFilter.addEventListener('click', this.handleClearFilter);
        }

        // 设置无限滚动
        this.setupInfiniteScroll();
    }

    /**
     * 用户登录后的回调
     */
    onUserLoggedIn() {
        this.showUserInterface();
        this.showLoggedInContent();
        this.loadLogs();
    }

    /**
     * 用户登出后的回调
     */
    onUserLoggedOut() {
        this.showLoginInterface();
        this.hideLoggedInContent();
        this.resetLogsList();
    }

    /**
     * 显示用户界面
     */
    showUserInterface() {
        super.showUserInterface();

        if (this.elements.postSection) {
            this.elements.postSection.classList.remove('hidden');
        }

        if (this.elements.specialFunctionsSection) {
            this.elements.specialFunctionsSection.classList.remove('hidden');
        }

        if (this.elements.welcomeSection) {
            this.elements.welcomeSection.classList.add('hidden');
        }

        // 检查用户权限，决定是否显示筛选功能
        this.updateFilterVisibility().catch(error => {
            console.error('更新筛选功能可见性失败:', error);
        });
    }

    /**
     * 显示登录界面
     */
    showLoginInterface() {
        super.showLoginPrompt();

        if (this.elements.postSection) {
            this.elements.postSection.classList.add('hidden');
        }

        if (this.elements.specialFunctionsSection) {
            this.elements.specialFunctionsSection.classList.add('hidden');
        }

        if (this.elements.filterSection) {
            this.elements.filterSection.classList.add('hidden');
        }

        if (this.elements.welcomeSection) {
            this.elements.welcomeSection.classList.remove('hidden');
        }
    }

    /**
     * 显示登录后的内容
     */
    showLoggedInContent() {
        // 显示统计模块
        const statisticsSection = document.getElementById('statisticsSection');
        if (statisticsSection) {
            statisticsSection.classList.remove('hidden');
        }

        // 显示日志列表
        const logsList = document.getElementById('logsList');
        if (logsList) {
            logsList.classList.remove('hidden');
        }
    }

    /**
     * 隐藏登录后的内容
     */
    hideLoggedInContent() {
        // 隐藏统计模块
        const statisticsSection = document.getElementById('statisticsSection');
        if (statisticsSection) {
            statisticsSection.classList.add('hidden');
        }

        // 隐藏日志列表
        const logsList = document.getElementById('logsList');
        if (logsList) {
            logsList.classList.add('hidden');
        }

        // 隐藏加载指示器
        const loadingIndicator = document.getElementById('loadingIndicator');
        if (loadingIndicator) {
            loadingIndicator.classList.add('hidden');
        }

        // 隐藏无更多内容提示
        const noMoreContent = document.getElementById('noMoreContent');
        if (noMoreContent) {
            noMoreContent.classList.add('hidden');
        }
    }

    /**
     * 发送日志内容到企业微信群机器人（通过后端PHP中转）
     */
    async sendToWeComGroup(content) {
        try {
            await fetch('https://www.junwei.bid:89/web/20/wecom-webhook.php', {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({ content })
            });
        } catch (err) {
            console.error("Webhook推送异常", err);
        }
    }
    
    /**
     * 离线日志队列本地存储键
     */
    static OFFLINE_QUEUE_KEY = 'worklog_offline_queue';

    /**
     * 保存离线日志到本地
     */
    saveOfflineLog(logData) {
        let queue = [];
        try {
            queue = JSON.parse(localStorage.getItem(MainPageApp.OFFLINE_QUEUE_KEY)) || [];
        } catch (e) {}
        queue.push(logData);
        localStorage.setItem(MainPageApp.OFFLINE_QUEUE_KEY, JSON.stringify(queue));
    }

    /**
     * 检查并同步离线日志
     */
    async syncOfflineLogs() {
        let queue = [];
        try {
            queue = JSON.parse(localStorage.getItem(MainPageApp.OFFLINE_QUEUE_KEY)) || [];
        } catch (e) {}
        if (!queue.length) return;
        const failed = [];
        for (const log of queue) {
            try {
                await this.submitWorkLog({ content: log.content }, log.images || [], log.content);
                // 推送到企业微信群
                let userName = this.currentUser?.get('realName') || this.currentUser?.get('username') || '';
                let msg = `【新工作日志-离线补发】\n用户：${userName}\n内容：${log.content}`;
                this.sendToWeComGroup(msg);
            } catch (e) {
                failed.push(log); // 失败的保留
            }
        }
        if (failed.length) {
            localStorage.setItem(MainPageApp.OFFLINE_QUEUE_KEY, JSON.stringify(failed));
        } else {
            localStorage.removeItem(MainPageApp.OFFLINE_QUEUE_KEY);
        }
        if (queue.length > failed.length) {
            WorkLogUtils.showMessage('离线日志已自动同步', 'success');
            this.resetLogsList();
            await this.loadLogs();
        }
    }

    /**
     * 处理发布日志
     */
    async handlePost(e) {
        e.preventDefault();
        const content = this.elements.postContent?.value?.trim();
        if (!content) {
            WorkLogUtils.showMessage('请输入日志内容', 'warning');
            return;
        }
        const submitBtn = this.elements.postForm.querySelector('button[type="submit"]');
        const originalText = submitBtn?.textContent;
        try {
            if (submitBtn) {
                submitBtn.textContent = '发布中...';
                submitBtn.disabled = true;
            }
            // 上传图片
            const images = await this.uploadImages();
            // 提交日志
            const formData = { 
                content,
                operationRecord: "常规工作记录" // 添加默认操作记录
            };
            try {
                await this.submitWorkLog(formData, images, content);
                WorkLogUtils.showMessage('日志发布成功！', 'success');
                // 推送到企业微信群
                let userName = this.currentUser?.get('realName') || this.currentUser?.get('username') || '';
                let msg = `【新工作日志】\n用户：${userName}\n内容：${content}`;
                this.sendToWeComGroup(msg);
            } catch (err) {
                // 离线或写入失败，保存到本地
                this.saveOfflineLog({ content, images });
                WorkLogUtils.showMessage('网络异常，日志已离线保存，联网后将自动同步', 'warning');
            }
            // 重置表单
            this.resetForm('postForm');
            // 重置上传的图片
            this.uploadedImages = [];
            this.updateImagePreview();
            // 重新加载日志列表
            this.resetLogsList();
            await this.loadLogs();
        } catch (error) {
            console.error('发布失败:', error);
            WorkLogUtils.showMessage('发布失败: ' + error.message, 'error');
        } finally {
            if (submitBtn) {
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        }
    }

    /**
     * 绑定图片上传事件
     */
    bindImageUploadEvents() {
        // 文件选择事件
        if (this.elements.imageInput) {
            this.elements.imageInput.addEventListener('change', this.handleImageUpload);
        }
    }

    /**
     * 处理图片上传
     */
    handleImageUpload(event) {
        const files = event.target.files;
        if (files.length > 0) {
            this.handleImageFiles(files);
        }
    }

    /**
     * 处理图片文件
     */
    async handleImageFiles(files) {
        const maxFiles = 5;
        const currentCount = this.uploadedImages.length;

        if (currentCount + files.length > maxFiles) {
            WorkLogUtils.showMessage(`最多只能上传${maxFiles}张图片`, 'warning');
            return;
        }

        for (let file of files) {
            if (!file.type.startsWith('image/')) {
                WorkLogUtils.showMessage('只能上传图片文件', 'warning');
                continue;
            }

            if (file.size > 5 * 1024 * 1024) {
                WorkLogUtils.showMessage('图片大小不能超过5MB', 'warning');
                continue;
            }

            try {
                const imageUrl = await WorkLogUtils.uploadImages([file], 'main');
                if (imageUrl && imageUrl.length > 0) {
                    this.uploadedImages.push(imageUrl[0]);
                    this.updateImagePreview();
                }
            } catch (error) {
                console.error('图片上传失败:', error);
                WorkLogUtils.showMessage('图片上传失败: ' + error.message, 'error');
            }
        }
    }

    /**
     * 更新图片预览
     */
    updateImagePreview() {
        if (!this.elements.imagePreview) return;

        if (this.uploadedImages.length === 0) {
            this.elements.imagePreview.classList.add('hidden');
            return;
        }

        this.elements.imagePreview.classList.remove('hidden');
        this.elements.imagePreview.innerHTML = this.uploadedImages.map((url, index) => `
            <div class="relative">
                <img src="${url}" alt="预览图片" class="w-full h-16 object-cover rounded-lg">
                <button type="button" onclick="mainPageApp.removeImage(${index})"
                        class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs hover:bg-red-600">
                    ×
                </button>
            </div>
        `).join('');
    }

    /**
     * 移除图片
     */
    removeImage(index) {
        this.uploadedImages.splice(index, 1);
        this.updateImagePreview();
    }

    /**
     * 上传图片（保持向后兼容）
     */
    async uploadImages() {
        // 返回已上传的图片列表
        return this.uploadedImages;
    }

    /**
     * 检查当前用户是否为管理员
     */
    isCurrentUserAdmin() {
        if (!this.currentUser) return false;
        const userRoles = this.currentUser.get('roles') || [];
        return userRoles.includes && userRoles.includes('admin');
    }

    /**
     * 更新筛选功能的可见性
     */
    async updateFilterVisibility() {
        if (this.elements.filterSection) {
            if (this.isCurrentUserAdmin()) {
                console.log('显示筛选功能，当前用户是管理员');
                this.elements.filterSection.classList.remove('hidden');
                await this.loadUserOptions();
            } else {
                console.log('隐藏筛选功能，当前用户不是管理员');
                this.elements.filterSection.classList.add('hidden');
            }
        }
    }

    /**
     * 加载用户选项到筛选下拉框
     * 由于LeanCloud权限限制，从现有日志中提取用户信息
     */
    async loadUserOptions() {
        console.log('开始加载用户选项...');

        if (!this.elements.userFilter) {
            console.error('userFilter元素未找到');
            return;
        }

        // 防止重复调用
        if (this.isLoadingUsers) {
            console.log('用户选项正在加载中，跳过重复调用');
            return;
        }
        this.isLoadingUsers = true;

        try {
            // 清空现有选项（保留"全部用户"选项）
            this.elements.userFilter.innerHTML = '<option value="">全部用户</option>';

            console.log('从日志中提取用户信息...');

            // 查询所有日志来获取用户信息
            const WorkLog = AV.Object.extend('WorkLog');
            const query = new AV.Query(WorkLog);
            query.include('user');
            query.limit(1000); // 获取足够多的日志来提取用户
            query.descending('createdAt');

            const logs = await query.find();
            console.log(`从 ${logs.length} 条日志中提取用户信息`);

            // 用Map来去重用户
            const userMap = new Map();

            logs.forEach(log => {
                const user = log.get('user');
                if (user && user.id) {
                    const userId = user.id;
                    const realName = user.get('realName') || user.get('username') || '未知用户';
                    const username = user.get('username') || '未知';

                    // 确保不重复添加
                    if (!userMap.has(userId)) {
                        userMap.set(userId, {
                            id: userId,
                            realName: realName,
                            username: username
                        });
                    }
                }
            });

            console.log(`去重后找到 ${userMap.size} 个不同的用户`);

            // 按用户名排序并添加到下拉框
            const sortedUsers = Array.from(userMap.values()).sort((a, b) =>
                (a.username || '').localeCompare(b.username || '')
            );

            // 再次确保下拉框是干净的，移除所有非空value的选项
            const existingOptions = this.elements.userFilter.querySelectorAll('option:not([value=""])');
            existingOptions.forEach(option => option.remove());

            sortedUsers.forEach(user => {
                const option = document.createElement('option');
                option.value = user.id;
                option.textContent = `${user.realName} (${user.username})`;
                this.elements.userFilter.appendChild(option);
                console.log(`添加用户选项: ${user.realName} (${user.username})`);
            });

            console.log(`用户选项加载完成，共 ${sortedUsers.length} 个用户`);

        } catch (error) {
            console.error('加载用户列表失败:', error);
            // 如果查询失败，至少显示当前用户
            if (this.currentUser) {
                const option = document.createElement('option');
                option.value = this.currentUser.id;
                const realName = this.currentUser.get('realName') || this.currentUser.get('username');
                const username = this.currentUser.get('username');
                option.textContent = `${realName} (${username})`;
                this.elements.userFilter.appendChild(option);
                console.log('添加当前用户作为备选项');
            }
        } finally {
            this.isLoadingUsers = false;
        }
    }

    /**
     * 重置日志列表
     */
    resetLogsList() {
        this.currentPage = 0;
        this.hasMoreData = true;
        this.isLoading = false;

        if (this.elements.logsList) {
            this.elements.logsList.innerHTML = '';
        }

        if (this.elements.noMoreContent) {
            this.elements.noMoreContent.classList.add('hidden');
        }
    }

    /**
     * 加载日志列表
     */
    async loadLogs() {
        // 检查用户是否已登录
        if (!WorkLogAuth.getCurrentUser()) {
            return;
        }

        if (this.isLoading || !this.hasMoreData) {
            return;
        }

        this.isLoading = true;

        if (this.elements.loadingIndicator) {
            this.elements.loadingIndicator.classList.remove('hidden');
        }

        try {
            const WorkLog = AV.Object.extend('WorkLog');
            const query = new AV.Query(WorkLog);

            query.include('user');

            // 权限控制：普通用户只能看到自己的日志
            if (!this.isCurrentUserAdmin() && this.currentUser) {
                query.equalTo('user', this.currentUser);
            }

            // 应用筛选条件（仅管理员可用）
            if (this.isCurrentUserAdmin()) {
                this.applyFilterToQuery(query);
            }

            // 按创建时间倒序
            query.descending('createdAt');
            query.limit(WORKLOG_CONFIG.APP.PAGE_SIZE);
            query.skip(this.currentPage * WORKLOG_CONFIG.APP.PAGE_SIZE);

            const logs = await query.find();

            if (logs.length === 0) {
                this.hasMoreData = false;
                if (this.elements.noMoreContent) {
                    this.elements.noMoreContent.classList.remove('hidden');
                }
            } else {
                logs.forEach(log => this.renderLogItem(log));
                this.currentPage++;
            }

        } catch (error) {
            console.error('加载日志失败:', error);
            WorkLogUtils.showMessage('加载日志失败: ' + error.message, 'error');
        } finally {
            this.isLoading = false;
            if (this.elements.loadingIndicator) {
                this.elements.loadingIndicator.classList.add('hidden');
            }
        }
    }

    /**
     * 渲染日志项
     */
    renderLogItem(log, returnElement = false) {
        const logElement = document.createElement('div');
        logElement.className = 'bg-white rounded-lg shadow-md p-6 mb-4 log-item';
        logElement.dataset.logId = log.id;

        const user = log.get('user');
        // 处理用户名显示逻辑：
        // 1. 优先使用用户对象的信息
        // 2. 如果没有用户对象，使用存储的username字段
        // 3. 如果都没有，使用authorName字段（历史数据兼容）
        let username, realName;

        if (user) {
            username = user.get('username');
            realName = user.get('realName') || username;
        } else {
            // 处理历史数据
            const storedUsername = log.get('username');
            const authorName = log.get('authorName');

            if (storedUsername) {
                username = storedUsername;
                realName = authorName || storedUsername;
            } else if (authorName) {
                // 历史数据只有authorName的情况
                username = authorName;
                realName = authorName;
            } else {
                username = '未知用户';
                realName = '未知用户';
            }
        }
        const content = log.get('content') || '';
        const images = log.get('images') || [];
        const createdAt = log.get('createdAt');
        const pageType = log.get('pageType') || 'main';

        // 页面类型标签
        const pageTypeLabels = {
            'main': '工作日志',
            'powerstation': '高压配电记录',
            'waterfilter': '水处理记录',
            'aircondition': '空调记录',
        };

        const pageTypeLabel = pageTypeLabels[pageType] || '工作日志';

        let imagesHtml = '';
        if (images.length > 0) {
            imagesHtml = `
                <div class="mt-4">
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-2">
                        ${images.map(imageUrl => `
                            <img src="${imageUrl}"
                                 alt="日志图片"
                                 class="w-full h-32 object-cover rounded cursor-pointer hover:opacity-80 transition-opacity"
                                 onclick="openImageModal('${imageUrl}')">
                        `).join('')}
                    </div>
                </div>
            `;
        }

        // 操作按钮（删除、详情、分享）
        let actionButtonsHtml = '';
        if (this.currentUser && user) {
            const isPinned = log.get('isPinned') === true;
            // 检查用户权限 - 使用roles字段判断管理员权限
            const userRoles = this.currentUser.get('roles') || [];
            const isAdmin = userRoles.includes('admin');

            // 删除按钮（仅对当前用户的日志显示）
            if (user.id === this.currentUser.id) {
                actionButtonsHtml += `
                    <button onclick="mainPageApp.handleDeleteLog('${log.id}', this.closest('.log-item'))" 
                            class="text-red-500 hover:text-red-700 text-sm">
                        删除
                    </button>
                `;
            }
        }

        // 详情页按钮（所有日志都显示）
        actionButtonsHtml += `
            <a href="log-detail.html?objectId=${log.id}" target="_blank" class="text-blue-500 hover:text-blue-700 text-sm ml-2">详情/分享</a>
        `;

        logElement.innerHTML = `
            <div class="flex justify-between items-start mb-3">
                <div class="flex items-center space-x-2">
                    <span class="font-semibold text-gray-800">${realName}</span>
                    <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">${pageTypeLabel}</span>
                </div>
                <div class="flex items-center space-x-2">
                    <span class="text-gray-500 text-sm">${WorkLogUtils.getTimeAgo(createdAt)}</span>
                    ${actionButtonsHtml}
                </div>
            </div>
            <div class="text-gray-700 whitespace-pre-wrap break-words overflow-wrap-anywhere log-content">${content}</div>
            ${imagesHtml}
        `;

        if (this.elements.logsList) {
            this.elements.logsList.appendChild(logElement);
        }
        // 返回元素用于测试或特殊用法
        if (returnElement) return logElement;
    }

    /**
     * 应用筛选条件到查询
     */
    applyFilterToQuery(query) {
        // 日期筛选
        if (this.filterOptions.startDate) {
            const startDate = new Date(this.filterOptions.startDate);
            startDate.setHours(0, 0, 0, 0);
            query.greaterThanOrEqualTo('createdAt', startDate);
        }

        if (this.filterOptions.endDate) {
            const endDate = new Date(this.filterOptions.endDate);
            endDate.setHours(23, 59, 59, 999);
            query.lessThanOrEqualTo('createdAt', endDate);
        }

        // 用户筛选
        if (this.filterOptions.userId) {
            const user = AV.Object.createWithoutData('_User', this.filterOptions.userId);
            query.equalTo('user', user);
        }
    }

    /**
     * 处理应用筛选
     */
    async handleApplyFilter() {
        if (!this.isCurrentUserAdmin()) {
            WorkLogUtils.showMessage('只有管理员可以使用筛选功能', 'warning');
            return;
        }

        // 获取筛选条件
        this.filterOptions.startDate = this.elements.startDate?.value || null;
        this.filterOptions.endDate = this.elements.endDate?.value || null;
        this.filterOptions.userId = this.elements.userFilter?.value || null;

        // 重置并重新加载日志
        this.resetLogsList();
        await this.loadLogs();

        WorkLogUtils.showMessage('筛选已应用', 'success');
    }

    /**
     * 处理清除筛选
     */
    async handleClearFilter() {
        if (!this.isCurrentUserAdmin()) {
            return;
        }

        // 清空筛选条件
        this.filterOptions = {
            startDate: null,
            endDate: null,
            userId: null
        };

        // 清空表单
        if (this.elements.startDate) this.elements.startDate.value = '';
        if (this.elements.endDate) this.elements.endDate.value = '';
        if (this.elements.userFilter) this.elements.userFilter.value = '';

        // 重置并重新加载日志
        this.resetLogsList();
        await this.loadLogs();

        WorkLogUtils.showMessage('筛选已清除', 'success');
    }

    /**
     * 处理删除日志
     */
    async handleDeleteLog(logId, logElement) {
        if (!confirm('确定要删除这条日志吗？')) {
            return;
        }

        try {
            const WorkLog = AV.Object.extend('WorkLog');
            const log = AV.Object.createWithoutData('WorkLog', logId);
            await log.destroy();

            // 从DOM中移除
            if (logElement && logElement.parentNode) {
                logElement.parentNode.removeChild(logElement);
            }

            WorkLogUtils.showMessage('日志删除成功', 'success');

        } catch (error) {
            console.error('删除日志失败:', error);
            WorkLogUtils.showMessage('删除失败: ' + error.message, 'error');
        }
    }



    /**
     * 设置无限滚动
     */
    setupInfiniteScroll() {
        window.addEventListener('scroll', this.handleScroll);
    }

    /**
     * 处理滚动事件
     */
    handleScroll() {
        // 检查用户是否已登录
        if (!WorkLogAuth.getCurrentUser()) {
            return;
        }

        if (this.isLoading || !this.hasMoreData) {
            return;
        }

        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const windowHeight = window.innerHeight;
        const documentHeight = document.documentElement.scrollHeight;

        if (scrollTop + windowHeight >= documentHeight - 100) {
            this.loadLogs();
        }
    }

    /**
     * 显示编辑真实姓名模态框
     */
    showEditRealNameModal() {
        if (this.elements.editRealNameModal) {
            this.elements.editRealNameModal.classList.remove('hidden');
            
            // 设置当前真实姓名
            if (this.elements.newRealName && this.currentUser) {
                this.elements.newRealName.value = this.currentUser.get('realName') || '';
            }
        }
    }

    /**
     * 隐藏编辑真实姓名模态框
     */
    hideEditRealNameModal() {
        if (this.elements.editRealNameModal) {
            this.elements.editRealNameModal.classList.add('hidden');
        }
    }

    /**
     * 处理编辑真实姓名
     */
    async handleEditRealName(e) {
        e.preventDefault();
        
        const newRealName = this.elements.newRealName?.value?.trim();
        if (!newRealName) {
            WorkLogUtils.showMessage('请输入真实姓名', 'warning');
            return;
        }
        
        try {
            this.currentUser.set('realName', newRealName);
            await this.currentUser.save();
            
            WorkLogUtils.showMessage('真实姓名更新成功', 'success');
            this.hideEditRealNameModal();
            super.updateUserDisplay();
            
        } catch (error) {
            console.error('更新真实姓名失败:', error);
            WorkLogUtils.showMessage('更新失败: ' + error.message, 'error');
        }
    }



    /**
     * 初始化时自动同步离线日志
     */
    async init() {
        await super.init?.();
        this.syncOfflineLogs();
    }

    /**
     * 销毁应用
     */
    destroy() {
        super.destroy();
        window.removeEventListener('scroll', this.handleScroll);
    }
}

// 统计当前用户发表日志次数（总、今日、本周、本月、本年）
async function updateMyPostCount() {
    if (!window.WorkLogAuth || !WorkLogAuth.getCurrentUser) return;
    const user = WorkLogAuth.getCurrentUser();
    if (!user) return;
    try {
        const query = new AV.Query('WorkLog');
        query.equalTo('user', user);
        // 总数
        const total = await query.count();
        document.querySelectorAll('.total-count').forEach(el => el.textContent = total);

        // 今日
        const today = new Date();
        today.setHours(0,0,0,0);
        const tomorrow = new Date(today);
        tomorrow.setDate(today.getDate() + 1);
        const todayQuery = new AV.Query('WorkLog');
        todayQuery.equalTo('user', user);
        todayQuery.greaterThanOrEqualTo('createdAt', today);
        todayQuery.lessThan('createdAt', tomorrow);
        const todayCount = await todayQuery.count();
        document.querySelectorAll('.today-count').forEach(el => el.textContent = todayCount);

        // 本周（周一为第一天）
        const weekStart = new Date();
        weekStart.setHours(0,0,0,0);
        weekStart.setDate(weekStart.getDate() - ((weekStart.getDay() + 6) % 7));
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 7);
        const weekQuery = new AV.Query('WorkLog');
        weekQuery.equalTo('user', user);
        weekQuery.greaterThanOrEqualTo('createdAt', weekStart);
        weekQuery.lessThan('createdAt', weekEnd);
        const weekCount = await weekQuery.count();
        document.querySelectorAll('.week-count').forEach(el => el.textContent = weekCount);

        // 本月
        const monthStart = new Date();
        monthStart.setHours(0,0,0,0);
        monthStart.setDate(1);
        const monthEnd = new Date(monthStart);
        monthEnd.setMonth(monthStart.getMonth() + 1);
        const monthQuery = new AV.Query('WorkLog');
        monthQuery.equalTo('user', user);
        monthQuery.greaterThanOrEqualTo('createdAt', monthStart);
        monthQuery.lessThan('createdAt', monthEnd);
        const monthCount = await monthQuery.count();
        document.querySelectorAll('.month-count').forEach(el => el.textContent = monthCount);

        // 本年
        const yearStart = new Date();
        yearStart.setHours(0,0,0,0);
        yearStart.setMonth(0, 1);
        const yearEnd = new Date(yearStart);
        yearEnd.setFullYear(yearStart.getFullYear() + 1);
        const yearQuery = new AV.Query('WorkLog');
        yearQuery.equalTo('user', user);
        yearQuery.greaterThanOrEqualTo('createdAt', yearStart);
        yearQuery.lessThan('createdAt', yearEnd);
        const yearCount = await yearQuery.count();
        document.querySelectorAll('.year-count').forEach(el => el.textContent = yearCount);
    } catch (e) {
        document.querySelectorAll('.total-count').forEach(el => el.textContent = '--');
        document.querySelectorAll('.today-count').forEach(el => el.textContent = '--');
        document.querySelectorAll('.week-count').forEach(el => el.textContent = '--');
        document.querySelectorAll('.month-count').forEach(el => el.textContent = '--');
        document.querySelectorAll('.year-count').forEach(el => el.textContent = '--');
    }
}

// 登录后统计
window.addEventListener('userLoggedIn', updateMyPostCount);

// 全局图片模态框函数（保持向后兼容）
function openImageModal(imageUrl) {
    // 检查是否已存在模态框，避免重复创建
    const existingModal = document.querySelector('.image-modal');
    if (existingModal) {
        existingModal.remove();
    }

    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 image-modal';
    modal.innerHTML = `
        <div class="relative max-w-4xl max-h-full p-4">
            <img src="${imageUrl}" alt="放大图片" class="max-w-full max-h-full object-contain">
            <button class="absolute top-2 right-2 text-white text-2xl hover:text-gray-300" onclick="document.querySelector('.image-modal').remove()">
                ×
            </button>
        </div>
    `;
    
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
    
    document.body.appendChild(modal);
}

// 导出模块
if (typeof window !== 'undefined') {
    window.MainPageApp = MainPageApp;
    window.openImageModal = openImageModal;
}