<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">库存管理测试</h1>
          <p class="mt-1 text-sm text-gray-600">
            测试LeanCloud集成的库存管理功能
          </p>
        </div>
        
        <div class="mt-4 sm:mt-0 flex space-x-3">
          <button 
            @click="loadData"
            class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            加载数据
          </button>
          
          <button 
            @click="createTestItem"
            class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
          >
            创建测试项目
          </button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <Icon name="mdi:package-variant" size="24" class="text-blue-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">总物品数</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.totalItems }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <Icon name="mdi:currency-cny" size="24" class="text-green-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">总价值</p>
            <p class="text-2xl font-bold text-gray-900">¥{{ stats.totalValue.toFixed(2) }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <Icon name="mdi:alert" size="24" class="text-yellow-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">低库存</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.lowStockItems }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <Icon name="mdi:alert-circle" size="24" class="text-red-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">缺货</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.outOfStockItems }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="text-center py-12">
      <Icon name="mdi:loading" size="32" class="text-blue-600 animate-spin mx-auto mb-4" />
      <p class="text-gray-600">加载中...</p>
    </div>

    <!-- 库存列表 -->
    <div v-else-if="items.length > 0" class="bg-white shadow rounded-lg overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">库存列表</h3>
      </div>
      
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                物品信息
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                分类
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                库存数量
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                单价
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="item in items" :key="item.id">
              <td class="px-6 py-4 whitespace-nowrap">
                <div>
                  <div class="text-sm font-medium text-gray-900">{{ item.name }}</div>
                  <div class="text-sm text-gray-500">{{ item.description }}</div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {{ item.category }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ item.quantity }} {{ item.unit }}</div>
                <div class="text-sm text-gray-500">最低: {{ item.minStock }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                ¥{{ item.price?.toFixed(2) || '0.00' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <button 
                  @click="deleteItem(item)"
                  class="text-red-600 hover:text-red-900"
                >
                  删除
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="text-center py-12">
      <Icon name="mdi:package-variant-closed" size="64" class="text-gray-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 mb-2">暂无库存物品</h3>
      <p class="text-gray-600 mb-6">点击"加载数据"获取库存信息</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { InventoryItem } from '~/types'
import { useSimpleInventoryStore } from '~/stores/inventory-simple'

// 页面元数据
definePageMeta({
  title: '库存管理测试',
  middleware: 'auth'
})

// 状态管理
const inventoryStore = useSimpleInventoryStore()

// 计算属性
const items = computed(() => inventoryStore.items)
const stats = computed(() => inventoryStore.inventoryStats)
const loading = computed(() => inventoryStore.loading)

// 方法
const loadData = async () => {
  await inventoryStore.fetchItems()
}

const createTestItem = async () => {
  const testItem = {
    name: `测试物品-${Date.now()}`,
    category: '测试分类',
    quantity: Math.floor(Math.random() * 100) + 1,
    unit: '个',
    minStock: 10,
    maxStock: 100,
    price: Math.floor(Math.random() * 100) + 10,
    supplier: '测试供应商',
    location: '测试位置',
    description: '这是一个测试物品',
    status: 'active' as const,
    images: []
  }
  
  await inventoryStore.createItem(testItem)
}

const deleteItem = async (item: InventoryItem) => {
  if (confirm(`确定要删除 ${item.name} 吗？`)) {
    await inventoryStore.deleteItem(item.id)
  }
}

// 页面加载时获取数据
onMounted(() => {
  loadData()
})
</script>
