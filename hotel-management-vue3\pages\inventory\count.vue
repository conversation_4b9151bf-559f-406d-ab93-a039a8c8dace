<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">库存盘点</h1>
          <p class="mt-1 text-sm text-gray-600">
            管理库存盘点计划和盘点记录
          </p>
        </div>
        
        <div class="mt-4 sm:mt-0 flex space-x-3">
          <button 
            @click="showCreateModal = true"
            class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center"
          >
            <Icon name="mdi:plus" size="16" class="mr-1" />
            创建盘点计划
          </button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:clipboard-list" size="24" class="text-blue-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">总计划数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ countPlans.length }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:play-circle" size="24" class="text-green-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">进行中</p>
            <p class="text-2xl font-semibold text-gray-900">{{ activePlans.length }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:check-circle" size="24" class="text-purple-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">已完成</p>
            <p class="text-2xl font-semibold text-gray-900">{{ completedPlans.length }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:file-document" size="24" class="text-orange-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">盘点记录</p>
            <p class="text-2xl font-semibold text-gray-900">{{ countRecords.length }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 盘点计划列表 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">盘点计划</h3>
      </div>
      
      <div v-if="loading" class="p-8 text-center">
        <Icon name="mdi:loading" size="32" class="text-gray-400 animate-spin mx-auto mb-4" />
        <p class="text-gray-600">加载中...</p>
      </div>
      
      <div v-else-if="countPlans.length === 0" class="p-8 text-center">
        <Icon name="mdi:clipboard-list-outline" size="64" class="text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无盘点计划</h3>
        <p class="text-gray-600 mb-6">创建你的第一个盘点计划吧</p>
        <button 
          @click="showCreateModal = true"
          class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          创建盘点计划
        </button>
      </div>
      
      <div v-else class="divide-y divide-gray-200">
        <div 
          v-for="plan in countPlans" 
          :key="plan.id"
          class="p-6 hover:bg-gray-50 transition-colors"
        >
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-3 mb-2">
                <h4 class="text-lg font-medium text-gray-900">{{ plan.name }}</h4>
                <span 
                  :class="getStatusClass(plan.status)"
                  class="px-2 py-1 text-xs font-medium rounded-full"
                >
                  {{ getStatusText(plan.status) }}
                </span>
              </div>
              
              <p v-if="plan.description" class="text-gray-600 mb-2">{{ plan.description }}</p>
              
              <div class="flex items-center space-x-6 text-sm text-gray-500">
                <div class="flex items-center">
                  <Icon name="mdi:calendar" size="16" class="mr-1" />
                  开始: {{ formatDate(plan.startDate) }}
                </div>
                <div v-if="plan.endDate" class="flex items-center">
                  <Icon name="mdi:calendar-check" size="16" class="mr-1" />
                  结束: {{ formatDate(plan.endDate) }}
                </div>
                <div class="flex items-center">
                  <Icon name="mdi:package-variant" size="16" class="mr-1" />
                  {{ plan.items.length }} 个物品
                </div>
              </div>
            </div>
            
            <div class="flex items-center space-x-2">
              <button 
                v-if="plan.status === 'draft'"
                @click="startCount(plan)"
                class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 transition-colors"
              >
                开始盘点
              </button>
              
              <button 
                v-if="plan.status === 'active'"
                @click="executeCount(plan)"
                class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors"
              >
                执行盘点
              </button>
              
              <button 
                @click="viewRecords(plan)"
                class="border border-gray-300 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-50 transition-colors"
              >
                查看记录
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 模态框 -->
    <StockCountModal
      v-model:show="showCreateModal"
      @created="handlePlanCreated"
    />
    
    <StockCountModal
      v-model:show="showExecuteModal"
      :plan="selectedPlan"
      @completed="handleCountCompleted"
    />
  </div>
</template>

<script setup lang="ts">
import type { StockCountPlan } from '~/types'
import StockCountModal from '~/components/inventory/StockCountModal.vue'

// 页面元数据
definePageMeta({
  title: '库存盘点',
  middleware: 'auth'
})

// Store
const inventoryStore = useInventoryStore()

// 响应式数据
const showCreateModal = ref(false)
const showExecuteModal = ref(false)
const selectedPlan = ref<StockCountPlan | null>(null)
const loading = ref(false)

// 计算属性
const countPlans = computed(() => inventoryStore.countPlans)
const countRecords = computed(() => inventoryStore.countRecords)

const activePlans = computed(() => 
  countPlans.value.filter(plan => plan.status === 'active')
)

const completedPlans = computed(() => 
  countPlans.value.filter(plan => plan.status === 'completed')
)

// 方法
const getStatusClass = (status: string) => {
  switch (status) {
    case 'draft':
      return 'bg-gray-100 text-gray-800'
    case 'active':
      return 'bg-green-100 text-green-800'
    case 'completed':
      return 'bg-blue-100 text-blue-800'
    case 'cancelled':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'draft':
      return '草稿'
    case 'active':
      return '进行中'
    case 'completed':
      return '已完成'
    case 'cancelled':
      return '已取消'
    default:
      return '未知'
  }
}

const formatDate = (date: Date) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

const startCount = async (plan: StockCountPlan) => {
  const result = await inventoryStore.startCount(plan.id)
  if (result.success) {
    alert('盘点已开始！')
  } else {
    alert(result.error || '开始盘点失败')
  }
}

const executeCount = (plan: StockCountPlan) => {
  selectedPlan.value = plan
  showExecuteModal.value = true
}

const viewRecords = async (plan: StockCountPlan) => {
  const result = await inventoryStore.getCountRecords(plan.id)
  if (result.success) {
    // 这里可以显示记录详情模态框
    console.log('盘点记录:', result.data)
    alert(`该计划共有 ${result.data?.length || 0} 条盘点记录`)
  }
}

const handlePlanCreated = () => {
  showCreateModal.value = false
  alert('盘点计划创建成功！')
}

const handleCountCompleted = () => {
  showExecuteModal.value = false
  selectedPlan.value = null
  alert('盘点完成！')
}

// 生命周期
onMounted(async () => {
  loading.value = true
  try {
    await inventoryStore.fetchItems({ page: 0 })
  } finally {
    loading.value = false
  }
})

// 页面标题
useHead({
  title: '库存盘点 - 酒店管理系统'
})
</script>
