// scripts/init-user-data.js
// 初始化用户管理模块的LeanCloud数据

import AV from 'leancloud-storage'

// 初始化LeanCloud
AV.init({
  appId: 'epbCQbfBnJNaZv0O5CCLacgJ-gzGzoHsz',
  appKey: '9atvXPb61ih8GXsOVHD8dRCh',
  serverURL: 'https://epbcqbfb.lc-cn-n1-shared.com'
})

async function initUserData() {
  console.log('👥 初始化用户管理模块数据...')
  
  try {
    // 创建部门数据
    console.log('🏢 创建部门数据...')
    
    const departments = [
      {
        name: '管理层',
        description: '酒店高级管理人员'
      },
      {
        name: '前台部',
        description: '负责客人接待、入住退房等服务'
      },
      {
        name: '客房部',
        description: '负责客房清洁、维护等工作'
      },
      {
        name: '餐饮部',
        description: '负责餐厅、酒吧等餐饮服务'
      },
      {
        name: '工程部',
        description: '负责设备维修、保养等技术工作'
      },
      {
        name: '保安部',
        description: '负责酒店安全保卫工作'
      },
      {
        name: '财务部',
        description: '负责财务管理、会计核算等工作'
      },
      {
        name: '人事部',
        description: '负责人力资源管理工作'
      }
    ]
    
    for (const deptData of departments) {
      const Department = AV.Object.extend('Department')
      const dept = new Department()
      
      Object.keys(deptData).forEach(key => {
        dept.set(key, deptData[key])
      })
      
      await dept.save()
      console.log(`✅ 创建部门: ${deptData.name}`)
    }
    
    // 跳过角色创建，因为LeanCloud中已有UserRole表且字段类型不匹配
    console.log('⚠️  跳过角色创建（LeanCloud中已存在UserRole表）')
    
    // 创建示例用户数据
    console.log('👤 创建示例用户数据...')
    
    const users = [
      {
        username: 'manager1',
        password: '123456',
        realName: '张经理',
        email: '<EMAIL>',
        phone: '13800138001',
        department: '管理层',
        roles: 'manager'
      },
      {
        username: 'frontdesk1',
        password: '123456',
        realName: '李小姐',
        email: '<EMAIL>',
        phone: '13800138002',
        department: '前台部',
        roles: 'employee'
      },
      {
        username: 'housekeeper1',
        password: '123456',
        realName: '王阿姨',
        email: '<EMAIL>',
        phone: '13800138003',
        department: '客房部',
        roles: 'housekeeper'
      },
      {
        username: 'engineer1',
        password: '123456',
        realName: '张师傅',
        email: '<EMAIL>',
        phone: '13800138004',
        department: '工程部',
        roles: 'engineer'
      },
      {
        username: 'security1',
        password: '123456',
        realName: '刘保安',
        email: '<EMAIL>',
        phone: '13800138005',
        department: '保安部',
        roles: 'employee'
      }
    ]
    
    for (const userData of users) {
      try {
        const user = new AV.User()
        
        Object.keys(userData).forEach(key => {
          user.set(key, userData[key])
        })
        
        user.set('status', 'active')
        
        await user.signUp()
        console.log(`✅ 创建用户: ${userData.realName} (${userData.username})`)
      } catch (error) {
        if (error.code === 202) {
          console.log(`⚠️  用户已存在: ${userData.realName} (${userData.username})`)
        } else {
          console.error(`❌ 创建用户失败: ${userData.realName}`, error.message)
        }
      }
    }
    
    // 创建权限配置
    console.log('🔐 创建权限配置...')
    
    const permissions = [
      {
        name: 'user.view',
        displayName: '查看用户',
        description: '查看用户列表和基本信息'
      },
      {
        name: 'user.manage',
        displayName: '管理用户',
        description: '创建、编辑、删除用户'
      },
      {
        name: 'worklog.view',
        displayName: '查看工作日志',
        description: '查看工作日志记录'
      },
      {
        name: 'worklog.create',
        displayName: '创建工作日志',
        description: '创建和编辑工作日志'
      },
      {
        name: 'repair.view',
        displayName: '查看报修',
        description: '查看报修工单'
      },
      {
        name: 'repair.create',
        displayName: '创建报修',
        description: '提交报修工单'
      },
      {
        name: 'repair.manage',
        displayName: '管理报修',
        description: '分配、处理报修工单'
      },
      {
        name: 'inventory.view',
        displayName: '查看库存',
        description: '查看库存信息'
      },
      {
        name: 'inventory.adjust',
        displayName: '调整库存',
        description: '调整库存数量'
      },
      {
        name: 'report.view',
        displayName: '查看报表',
        description: '查看各类统计报表'
      }
    ]
    
    for (const permData of permissions) {
      const Permission = AV.Object.extend('Permission')
      const perm = new Permission()
      
      Object.keys(permData).forEach(key => {
        perm.set(key, permData[key])
      })
      
      await perm.save()
      console.log(`✅ 创建权限: ${permData.displayName}`)
    }
    
    console.log('🎉 用户管理模块数据初始化完成！')
    
  } catch (error) {
    console.error('❌ 初始化失败:', error)
  }
}

async function main() {
  console.log('🚀 开始初始化用户管理模块数据...')
  
  try {
    await initUserData()
    console.log('✅ 用户管理模块数据初始化完成！')
  } catch (error) {
    console.error('❌ 初始化过程中出现错误:', error)
  }
  
  process.exit(0)
}

// 直接运行主函数
main()
