<template>
  <div 
    v-if="show" 
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    @click="handleBackdropClick"
  >
    <div 
      class="bg-white rounded-lg w-full max-w-md"
      @click.stop
    >
      <!-- 头部 -->
      <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-900">筛选用户</h3>
        <button 
          @click="handleClose"
          class="text-gray-400 hover:text-gray-600"
        >
          <Icon name="mdi:close" size="24" />
        </button>
      </div>

      <!-- 内容 -->
      <div class="px-6 py-4">
        <form @submit.prevent="handleApply">
          <!-- 部门筛选 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              部门
            </label>
            <select 
              v-model="filters.department"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">全部部门</option>
              <option value="前厅部">前厅部</option>
              <option value="客房部">客房部</option>
              <option value="餐饮部">餐饮部</option>
              <option value="工程部">工程部</option>
              <option value="保安部">保安部</option>
              <option value="财务部">财务部</option>
              <option value="人事部">人事部</option>
              <option value="信息部">信息部</option>
            </select>
          </div>

          <!-- 角色筛选 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              角色
            </label>
            <select 
              v-model="filters.role"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">全部角色</option>
              <option value="admin">管理员</option>
              <option value="engineer">工程师</option>
              <option value="user">普通用户</option>
            </select>
          </div>

          <!-- 状态筛选 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              账户状态
            </label>
            <select 
              v-model="filters.status"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">全部状态</option>
              <option value="active">活跃</option>
              <option value="inactive">禁用</option>
            </select>
          </div>

          <!-- 注册时间筛选 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              注册时间
            </label>
            <div class="grid grid-cols-2 gap-3">
              <div>
                <input 
                  v-model="filters.startDate"
                  type="date"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <div class="text-xs text-gray-500 mt-1">开始日期</div>
              </div>
              <div>
                <input 
                  v-model="filters.endDate"
                  type="date"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <div class="text-xs text-gray-500 mt-1">结束日期</div>
              </div>
            </div>
          </div>

          <!-- 快速筛选 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-3">
              快速筛选
            </label>
            <div class="space-y-2">
              <label class="flex items-center">
                <input 
                  v-model="filters.hasPhone"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span class="ml-2 text-sm text-gray-700">已填写手机号</span>
              </label>
              
              <label class="flex items-center">
                <input 
                  v-model="filters.recentLogin"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span class="ml-2 text-sm text-gray-700">最近7天有登录</span>
              </label>
            </div>
          </div>
        </form>
      </div>

      <!-- 底部按钮 -->
      <div class="px-6 py-4 border-t border-gray-200 flex justify-between">
        <button 
          type="button"
          @click="handleReset"
          class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
        >
          重置筛选
        </button>
        
        <div class="flex space-x-3">
          <button 
            type="button"
            @click="handleClose"
            class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
          >
            取消
          </button>
          <button 
            @click="handleApply"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            应用筛选
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  show: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:show': [value: boolean]
  apply: [filters: any]
}>()

// 响应式数据
const filters = reactive({
  department: '',
  role: '',
  status: '',
  startDate: '',
  endDate: '',
  hasPhone: false,
  recentLogin: false
})

// 方法
const resetFilters = () => {
  filters.department = ''
  filters.role = ''
  filters.status = ''
  filters.startDate = ''
  filters.endDate = ''
  filters.hasPhone = false
  filters.recentLogin = false
}

const handleClose = () => {
  emit('update:show', false)
}

const handleBackdropClick = () => {
  handleClose()
}

const handleReset = () => {
  resetFilters()
}

const handleApply = () => {
  // 构建筛选对象，过滤掉空值
  const appliedFilters: any = {}
  
  if (filters.department) appliedFilters.department = filters.department
  if (filters.role) appliedFilters.role = filters.role
  if (filters.status) appliedFilters.status = filters.status
  if (filters.startDate) appliedFilters.startDate = new Date(filters.startDate)
  if (filters.endDate) appliedFilters.endDate = new Date(filters.endDate)
  if (filters.hasPhone) appliedFilters.hasPhone = filters.hasPhone
  if (filters.recentLogin) appliedFilters.recentLogin = filters.recentLogin
  
  emit('apply', appliedFilters)
  handleClose()
}

// 键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.show) {
    handleClose()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>
