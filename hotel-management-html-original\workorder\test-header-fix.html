<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>头部登录功能测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-4xl mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-3">
                    <h1 class="text-xl font-semibold text-gray-800">头部登录功能测试</h1>
                </div>
                <div id="userInfo" class="hidden items-center space-x-4">
                    <span id="realName" class="text-gray-800 font-medium"></span>
                    <button id="logoutBtn" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm">
                        退出登录
                    </button>
                </div>
                <button id="loginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm">
                    登录
                </button>
            </div>
        </div>
    </nav>

    <!-- 主内容 -->
    <main class="max-w-4xl mx-auto px-4 py-6">
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold mb-4">头部登录状态测试</h2>
            <div id="status" class="space-y-2 mb-4">
                <div>页面加载中...</div>
            </div>
            <div class="space-x-4">
                <button id="testBtn" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                    测试头部更新
                </button>
                <button id="simulateLoginBtn" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg">
                    模拟登录
                </button>
                <button id="simulateLogoutBtn" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg">
                    模拟退出
                </button>
            </div>
        </div>
    </main>

    <!-- 登录弹窗 -->
    <div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-800">用户登录</h2>
            </div>
            <div class="p-6">
                <form id="loginForm" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                        <input type="text" id="loginUsername" required
                            class="w-full p-3 border border-gray-300 rounded-lg">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                        <input type="password" id="loginPassword" required
                            class="w-full p-3 border border-gray-300 rounded-lg">
                    </div>
                    <div class="flex gap-3 pt-4">
                        <button type="button" id="cancelLogin" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-3 rounded-lg">
                            取消
                        </button>
                        <button type="submit" class="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg">
                            登录
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="../js/config.js?v=2.1"></script>
    <script src="../js/core/error-handler.js?v=2.1"></script>
    <script src="../js/utils.js?v=2.1"></script>
    <script src="../js/auth.js?v=2.1"></script>
    <script src="../js/base-app.js?v=2.1"></script>
    <script>
        class HeaderTestApp extends BaseWorkLogApp {
            constructor() {
                super({
                    pageType: 'header-test',
                    requiredElements: []
                });
            }

            getPageElements() {
                return {
                    // 头部元素
                    userInfo: 'userInfo',
                    realName: 'realName',
                    loginBtn: 'loginBtn',
                    logoutBtn: 'logoutBtn',
                    // 登录弹窗元素
                    loginModal: 'loginModal',
                    loginForm: 'loginForm',
                    loginUsername: 'loginUsername',
                    loginPassword: 'loginPassword',
                    cancelLogin: 'cancelLogin',
                    // 测试元素
                    status: 'status',
                    testBtn: 'testBtn',
                    simulateLoginBtn: 'simulateLoginBtn',
                    simulateLogoutBtn: 'simulateLogoutBtn'
                };
            }

            bindPageEvents() {
                if (this.elements.testBtn) {
                    this.elements.testBtn.addEventListener('click', () => this.testHeaderUpdate());
                }
                if (this.elements.simulateLoginBtn) {
                    this.elements.simulateLoginBtn.addEventListener('click', () => this.simulateLogin());
                }
                if (this.elements.simulateLogoutBtn) {
                    this.elements.simulateLogoutBtn.addEventListener('click', () => this.simulateLogout());
                }
            }

            onUserLoggedIn() {
                console.log('=== 测试页面：用户登录成功 ===');
                console.log('当前用户:', this.currentUser?.get('username'));
                console.log('用户真实姓名:', this.currentUser?.get('realName'));
                
                this.updateStatus();
                
                // 调用父类方法更新头部显示
                super.showUserInterface();
            }

            onUserLoggedOut() {
                console.log('=== 测试页面：用户已退出 ===');
                this.updateStatus();
                
                // 调用父类方法更新头部显示
                super.showLoginPrompt();
            }

            updateStatus() {
                if (!this.elements.status) return;

                const user = this.currentUser;
                if (user) {
                    this.elements.status.innerHTML = `
                        <div class="text-green-600">✅ 用户已登录</div>
                        <div>用户名: ${user.get('username')}</div>
                        <div>真实姓名: ${user.get('realName') || '未设置'}</div>
                        <div>userInfo元素: ${this.elements.userInfo ? '存在' : '不存在'}</div>
                        <div>realName元素: ${this.elements.realName ? '存在' : '不存在'}</div>
                        <div>loginBtn元素: ${this.elements.loginBtn ? '存在' : '不存在'}</div>
                        <div>logoutBtn元素: ${this.elements.logoutBtn ? '存在' : '不存在'}</div>
                        <div>userInfo显示状态: ${this.elements.userInfo?.classList.contains('hidden') ? '隐藏' : '显示'}</div>
                        <div>loginBtn显示状态: ${this.elements.loginBtn?.classList.contains('hidden') ? '隐藏' : '显示'}</div>
                    `;
                } else {
                    this.elements.status.innerHTML = `
                        <div class="text-red-600">❌ 用户未登录</div>
                        <div>userInfo显示状态: ${this.elements.userInfo?.classList.contains('hidden') ? '隐藏' : '显示'}</div>
                        <div>loginBtn显示状态: ${this.elements.loginBtn?.classList.contains('hidden') ? '隐藏' : '显示'}</div>
                    `;
                }
            }

            testHeaderUpdate() {
                console.log('=== 测试头部更新 ===');
                this.updateStatus();
                
                if (this.currentUser) {
                    console.log('强制调用super.showUserInterface()');
                    super.showUserInterface();
                } else {
                    console.log('强制调用super.showLoginPrompt()');
                    super.showLoginPrompt();
                }
                
                setTimeout(() => {
                    this.updateStatus();
                }, 100);
            }

            simulateLogin() {
                // 模拟登录状态
                console.log('模拟登录...');
                super.showUserInterface();
                if (this.elements.realName) {
                    this.elements.realName.textContent = '测试用户';
                }
                this.updateStatus();
            }

            simulateLogout() {
                // 模拟退出状态
                console.log('模拟退出...');
                super.showLoginPrompt();
                this.updateStatus();
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            function initApp() {
                if (typeof AV !== 'undefined') {
                    const headerTestApp = new HeaderTestApp();
                    headerTestApp.init();
                } else {
                    setTimeout(initApp, 100);
                }
            }
            initApp();
        });
    </script>
</body>
</html>
