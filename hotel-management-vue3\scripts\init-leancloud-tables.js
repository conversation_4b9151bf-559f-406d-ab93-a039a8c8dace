// scripts/init-leancloud-tables.js
// 初始化LeanCloud数据表和示例数据

import AV from 'leancloud-storage'

// 初始化LeanCloud
AV.init({
  appId: 'epbCQbfBnJNaZv0O5CCLacgJ-gzGzoHsz',
  appKey: '9atvXPb61ih8GXsOVHD8dRCh',
  serverURL: 'https://epbcqbfb.lc-cn-n1-shared.com'
})

async function initInventoryTables() {
  console.log('🔧 初始化库存管理相关数据表...')
  
  try {
    // 1. 创建库存项目示例数据
    console.log('📦 创建库存项目示例数据...')
    
    const inventoryItems = [
      {
        name: '洗发水',
        category: '客房用品',
        quantity: 50,
        unit: '瓶',
        minStock: 20,
        maxStock: 100,
        price: 15.5,
        supplier: '日化供应商',
        location: '仓库A-01',
        description: '高品质洗发水，适合酒店客房使用',
        images: []
      },
      {
        name: '毛巾',
        category: '客房用品',
        quantity: 200,
        unit: '条',
        minStock: 50,
        maxStock: 500,
        price: 25.0,
        supplier: '纺织品供应商',
        location: '仓库B-02',
        description: '纯棉毛巾，柔软舒适',
        images: []
      },
      {
        name: '清洁剂',
        category: '清洁用品',
        quantity: 8,
        unit: '瓶',
        minStock: 10,
        maxStock: 50,
        price: 35.0,
        supplier: '清洁用品公司',
        location: '仓库C-03',
        description: '多功能清洁剂，去污力强',
        images: []
      },
      {
        name: '床单',
        category: '布草用品',
        quantity: 150,
        unit: '套',
        minStock: 30,
        maxStock: 300,
        price: 45.0,
        supplier: '酒店布草供应商',
        location: '仓库D-04',
        description: '高档酒店床单，舒适透气',
        images: []
      },
      {
        name: '垃圾袋',
        category: '清洁用品',
        quantity: 500,
        unit: '个',
        minStock: 100,
        maxStock: 1000,
        price: 0.5,
        supplier: '日用品供应商',
        location: '仓库E-05',
        description: '环保垃圾袋，结实耐用',
        images: []
      }
    ]
    
    for (const itemData of inventoryItems) {
      const InventoryItem = AV.Object.extend('InventoryItem')
      const item = new InventoryItem()
      
      Object.keys(itemData).forEach(key => {
        item.set(key, itemData[key])
      })
      
      await item.save()
      console.log(`✅ 创建库存项目: ${itemData.name}`)
    }
    
    // 2. 创建盘点计划示例数据
    console.log('📋 创建盘点计划示例数据...')
    
    const countPlans = [
      {
        name: '2024年第一季度盘点',
        description: '对所有客房用品进行全面盘点',
        startDate: new Date('2024-03-01'),
        endDate: new Date('2024-03-07'),
        items: [], // 实际应用中这里会是物品ID数组
        status: 'completed',
        createdBy: 'admin'
      },
      {
        name: '清洁用品专项盘点',
        description: '对清洁用品类别进行专项盘点',
        startDate: new Date('2024-03-15'),
        endDate: new Date('2024-03-20'),
        items: [],
        status: 'in_progress',
        createdBy: 'admin'
      }
    ]
    
    for (const planData of countPlans) {
      const CountPlan = AV.Object.extend('StockCountPlan')
      const plan = new CountPlan()
      
      Object.keys(planData).forEach(key => {
        plan.set(key, planData[key])
      })
      
      await plan.save()
      console.log(`✅ 创建盘点计划: ${planData.name}`)
    }
    
    // 3. 创建库存变动记录示例数据
    console.log('📊 创建库存变动记录示例数据...')
    
    const transactions = [
      {
        itemId: 'item1', // 实际应用中这里会是真实的物品ID
        type: 'in',
        quantity: 20,
        beforeQuantity: 30,
        afterQuantity: 50,
        reason: '采购入库',
        operator: 'admin'
      },
      {
        itemId: 'item2',
        type: 'out',
        quantity: 10,
        beforeQuantity: 210,
        afterQuantity: 200,
        reason: '客房使用',
        operator: 'housekeeper'
      }
    ]
    
    for (const transactionData of transactions) {
      const Transaction = AV.Object.extend('InventoryTransaction')
      const transaction = new Transaction()
      
      Object.keys(transactionData).forEach(key => {
        transaction.set(key, transactionData[key])
      })
      
      await transaction.save()
      console.log(`✅ 创建库存变动记录: ${transactionData.reason}`)
    }
    
    console.log('🎉 库存管理数据表初始化完成！')
    
  } catch (error) {
    console.error('❌ 初始化失败:', error)
  }
}

async function main() {
  console.log('🚀 开始初始化LeanCloud数据表...')
  
  try {
    await initInventoryTables()
    console.log('✅ 所有数据表初始化完成！')
  } catch (error) {
    console.error('❌ 初始化过程中出现错误:', error)
  }
  
  process.exit(0)
}

// 直接运行主函数
main()
