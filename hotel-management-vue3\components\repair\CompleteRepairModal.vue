<template>
  <div 
    v-if="show && repair" 
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    @click="handleBackdropClick"
  >
    <div 
      class="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-hidden"
      @click.stop
    >
      <!-- 头部 -->
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900">完成工单</h3>
          <button 
            @click="handleClose"
            class="text-gray-400 hover:text-gray-600"
          >
            <Icon name="mdi:close" size="24" />
          </button>
        </div>
        
        <!-- 工单信息 -->
        <div class="mt-4 p-3 bg-gray-50 rounded-lg">
          <div class="flex items-center justify-between">
            <div>
              <h4 class="text-sm font-medium text-gray-900">{{ repair.title }}</h4>
              <p class="text-xs text-gray-500">{{ repair.location }} · {{ repair.category }}</p>
            </div>
            <span 
              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
              :class="getPriorityStyle(repair.priority)"
            >
              {{ getPriorityText(repair.priority) }}
            </span>
          </div>
        </div>
      </div>

      <!-- 内容 -->
      <div class="px-6 py-4 max-h-[calc(90vh-140px)] overflow-y-auto">
        <form @submit.prevent="handleSubmit">
          <!-- 解决方案 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              解决方案 *
            </label>
            <textarea 
              v-model="formData.solution"
              rows="4"
              required
              :disabled="loading"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
              placeholder="请详细描述问题的解决过程和方案..."
            ></textarea>
            <div class="mt-1 text-xs text-gray-500">
              {{ formData.solution.length }} / 500 字符
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 实际用时 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                实际用时 (小时) *
              </label>
              <input 
                v-model.number="formData.actualTime"
                type="number"
                min="0.1"
                max="72"
                step="0.1"
                required
                :disabled="loading"
                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="实际花费时间"
              />
              <div v-if="repair.estimatedTime" class="mt-1 text-xs text-gray-500">
                预计时间: {{ repair.estimatedTime }} 小时
              </div>
            </div>

            <!-- 维修成本 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                维修成本 (元)
              </label>
              <input 
                v-model.number="formData.cost"
                type="number"
                min="0"
                step="0.01"
                :disabled="loading"
                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="材料和人工成本"
              />
            </div>
          </div>

          <!-- 使用材料 -->
          <div class="mt-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              使用材料
            </label>
            
            <!-- 已添加的材料 -->
            <div v-if="formData.materials.length > 0" class="mb-3">
              <div class="flex flex-wrap gap-2">
                <span 
                  v-for="(material, index) in formData.materials" 
                  :key="index"
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {{ material }}
                  <button 
                    type="button"
                    @click="removeMaterial(index)"
                    class="ml-1 hover:text-blue-600"
                  >
                    <Icon name="mdi:close" size="12" />
                  </button>
                </span>
              </div>
            </div>
            
            <!-- 添加材料 -->
            <div class="flex space-x-2">
              <input 
                v-model="newMaterial"
                type="text"
                :disabled="loading"
                class="flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="输入材料名称"
                @keyup.enter="addMaterial"
              />
              <button 
                type="button"
                @click="addMaterial"
                :disabled="!newMaterial.trim() || loading"
                class="px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors disabled:opacity-50"
              >
                添加
              </button>
            </div>
          </div>

          <!-- 完成照片 -->
          <div class="mt-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              完成照片
            </label>
            
            <!-- 已上传的图片 -->
            <div v-if="formData.images.length > 0" class="mb-4">
              <div class="grid grid-cols-4 gap-2">
                <div 
                  v-for="(image, index) in formData.images" 
                  :key="index"
                  class="relative group"
                >
                  <img 
                    :src="image" 
                    :alt="`完成照片 ${index + 1}`"
                    class="w-full h-20 object-cover rounded border border-gray-200"
                  />
                  <button 
                    type="button"
                    @click="removeImage(index)"
                    class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600 opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    ×
                  </button>
                </div>
              </div>
            </div>
            
            <!-- 上传按钮 -->
            <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors">
              <input 
                ref="fileInput"
                type="file" 
                multiple 
                accept="image/*"
                class="hidden"
                @change="handleFileSelect"
              />
              
              <button 
                type="button"
                @click="$refs.fileInput?.click()"
                :disabled="loading || uploading"
                class="text-blue-600 hover:text-blue-700 font-medium disabled:opacity-50"
              >
                <Icon name="mdi:camera" size="24" class="mx-auto mb-2" />
                {{ uploading ? '上传中...' : '添加完成照片' }}
              </button>
              
              <p class="text-xs text-gray-500 mt-1">
                展示维修完成后的效果
              </p>
            </div>
          </div>

          <!-- 质量检查 -->
          <div class="mt-6">
            <label class="block text-sm font-medium text-gray-700 mb-3">
              质量检查
            </label>
            <div class="space-y-2">
              <label class="flex items-center">
                <input 
                  v-model="formData.qualityChecks.functionalTest"
                  type="checkbox"
                  :disabled="loading"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span class="ml-2 text-sm text-gray-700">功能测试通过</span>
              </label>
              
              <label class="flex items-center">
                <input 
                  v-model="formData.qualityChecks.safetyCheck"
                  type="checkbox"
                  :disabled="loading"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span class="ml-2 text-sm text-gray-700">安全检查通过</span>
              </label>
              
              <label class="flex items-center">
                <input 
                  v-model="formData.qualityChecks.cleanupDone"
                  type="checkbox"
                  :disabled="loading"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span class="ml-2 text-sm text-gray-700">现场清理完成</span>
              </label>
            </div>
          </div>
        </form>
      </div>

      <!-- 底部按钮 -->
      <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
        <button 
          type="button"
          @click="handleClose"
          :disabled="loading"
          class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50"
        >
          取消
        </button>
        <button 
          @click="handleSubmit"
          :disabled="loading || !isFormValid"
          class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center"
        >
          <Icon v-if="loading" name="mdi:loading" size="16" class="mr-1 animate-spin" />
          {{ loading ? '完成中...' : '确认完成' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Repair } from '~/types'

interface Props {
  show: boolean
  repair?: Repair | null
}

const props = withDefaults(defineProps<Props>(), {
  repair: null
})

const emit = defineEmits<{
  'update:show': [value: boolean]
  completed: []
}>()

const repairStore = useRepairStore()

// 响应式数据
const loading = ref(false)
const uploading = ref(false)
const fileInput = ref<HTMLInputElement>()
const newMaterial = ref('')

const formData = reactive({
  solution: '',
  actualTime: 0,
  cost: 0,
  materials: [] as string[],
  images: [] as string[],
  qualityChecks: {
    functionalTest: false,
    safetyCheck: false,
    cleanupDone: false
  }
})

// 计算属性
const isFormValid = computed(() => {
  return formData.solution.trim().length > 0 && 
         formData.actualTime > 0 &&
         formData.solution.length <= 500
})

// 方法
const resetForm = () => {
  formData.solution = ''
  formData.actualTime = 0
  formData.cost = 0
  formData.materials = []
  formData.images = []
  formData.qualityChecks.functionalTest = false
  formData.qualityChecks.safetyCheck = false
  formData.qualityChecks.cleanupDone = false
  newMaterial.value = ''
}

const handleClose = () => {
  if (!loading.value) {
    emit('update:show', false)
    resetForm()
  }
}

const handleBackdropClick = () => {
  handleClose()
}

const getPriorityStyle = (priority: string) => {
  const styles = {
    'low': 'bg-gray-100 text-gray-800',
    'medium': 'bg-yellow-100 text-yellow-800',
    'high': 'bg-orange-100 text-orange-800',
    'urgent': 'bg-red-100 text-red-800'
  }
  return styles[priority as keyof typeof styles] || styles.medium
}

const getPriorityText = (priority: string) => {
  const texts = {
    'low': '低优先级',
    'medium': '中优先级',
    'high': '高优先级',
    'urgent': '紧急'
  }
  return texts[priority as keyof typeof texts] || '中优先级'
}

const addMaterial = () => {
  const material = newMaterial.value.trim()
  if (material && !formData.materials.includes(material)) {
    formData.materials.push(material)
    newMaterial.value = ''
  }
}

const removeMaterial = (index: number) => {
  formData.materials.splice(index, 1)
}

const handleFileSelect = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files
  
  if (!files || files.length === 0) return
  
  // 检查图片数量限制
  if (formData.images.length + files.length > 6) {
    alert('最多只能上传6张图片')
    return
  }
  
  uploading.value = true
  
  try {
    for (const fileItem of Array.from(files)) {
      // 检查文件类型
      if (!fileItem.type.startsWith('image/')) {
        alert(`文件 ${fileItem.name} 不是有效的图片格式`)
        continue
      }

      // 检查文件大小（5MB限制）
      if (fileItem.size > 5 * 1024 * 1024) {
        alert(`文件 ${fileItem.name} 大小超过5MB限制`)
        continue
      }

      // 压缩图片（简化版，直接使用原文件）
      const compressedBlob = fileItem

      // 创建新的文件名
      const originalName = fileItem.name
      const nameWithoutExt = originalName.substring(0, originalName.lastIndexOf('.')) || originalName
      const compressedFileName = `repair_complete_${nameWithoutExt}_${Date.now()}.jpg`

      // 构造FormData
      const uploadFormData = new FormData()
      uploadFormData.append('token', '8e7057ee0ba0be565301980fb3e52763')
      uploadFormData.append('image', new File([compressedBlob], compressedFileName, { type: 'image/jpeg' }))

      // 上传到自建API
      const response = await fetch('https://www.junwei.bid:89/web/11/index.php', {
        method: 'POST',
        body: uploadFormData
      })

      if (response.ok) {
        const result = await response.json()
        // 兼容不同返回格式
        if (result.url) {
          formData.images.push(result.url)
        } else if (result.data && result.data.url) {
          formData.images.push(result.data.url)
        } else if (typeof result === 'string' && result.startsWith('http')) {
          formData.images.push(result)
        } else {
          alert(`上传文件 ${fileItem.name} 失败: 返回格式不正确`)
        }
      } else {
        const errorText = await response.text()
        alert(`上传文件 ${fileItem.name} 失败: ${errorText}`)
      }
    }
  } catch (error) {
    console.error('文件上传失败:', error)
    alert('文件上传失败，请稍后重试')
  } finally {
    uploading.value = false
    // 清空文件输入
    if (fileInput.value) {
      fileInput.value.value = ''
    }
  }
}

const removeImage = (index: number) => {
  formData.images.splice(index, 1)
}

const handleSubmit = async () => {
  if (!isFormValid.value || loading.value || !props.repair) return
  
  loading.value = true
  
  try {
    const completionData = {
      solution: formData.solution.trim(),
      actualTime: formData.actualTime,
      cost: formData.cost > 0 ? formData.cost : undefined,
      materials: formData.materials.length > 0 ? formData.materials : undefined
    }
    
    const result = await repairStore.completeRepair(props.repair.id, completionData)
    
    if (result.success) {
      emit('completed')
      handleClose()
    } else {
      alert(result.error || '完成失败')
    }
  } catch (error) {
    console.error('完成工单失败:', error)
    alert('完成失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 监听props变化
watch(() => props.show, (newValue) => {
  if (newValue) {
    resetForm()
    
    // 根据预计时间设置默认实际时间
    if (props.repair?.estimatedTime) {
      formData.actualTime = props.repair.estimatedTime
    }
  }
})

// 键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.show) {
    handleClose()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>
