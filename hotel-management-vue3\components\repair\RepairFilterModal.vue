<template>
  <div 
    v-if="show" 
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    @click="handleBackdropClick"
  >
    <div 
      class="bg-white rounded-lg w-full max-w-md"
      @click.stop
    >
      <!-- 头部 -->
      <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-900">筛选条件</h3>
        <button 
          @click="handleClose"
          class="text-gray-400 hover:text-gray-600"
        >
          <Icon name="mdi:close" size="24" />
        </button>
      </div>

      <!-- 内容 -->
      <div class="px-6 py-4">
        <form @submit.prevent="handleApply">
          <!-- 状态筛选 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              工单状态
            </label>
            <select 
              v-model="filters.status"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">全部状态</option>
              <option value="pending">待处理</option>
              <option value="assigned">已分配</option>
              <option value="in_progress">处理中</option>
              <option value="completed">已完成</option>
              <option value="cancelled">已取消</option>
              <option value="rejected">已拒绝</option>
            </select>
          </div>

          <!-- 优先级筛选 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              优先级
            </label>
            <select 
              v-model="filters.priority"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">全部优先级</option>
              <option value="low">低</option>
              <option value="medium">中</option>
              <option value="high">高</option>
              <option value="urgent">紧急</option>
            </select>
          </div>

          <!-- 分类筛选 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              报修分类
            </label>
            <select 
              v-model="filters.category"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">全部分类</option>
              <option value="空调设备">空调设备</option>
              <option value="电梯设备">电梯设备</option>
              <option value="照明设备">照明设备</option>
              <option value="水电设施">水电设施</option>
              <option value="门窗设施">门窗设施</option>
              <option value="网络设备">网络设备</option>
              <option value="安防设备">安防设备</option>
              <option value="清洁设备">清洁设备</option>
              <option value="其他设备">其他设备</option>
            </select>
          </div>

          <!-- 处理人筛选 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              处理人
            </label>
            <select 
              v-model="filters.assignee"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">全部处理人</option>
              <option value="engineer1">张工程师</option>
              <option value="engineer2">李工程师</option>
              <option value="engineer3">王工程师</option>
            </select>
          </div>

          <!-- 报修人筛选 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              报修人
            </label>
            <input 
              v-model="filters.reporterName"
              type="text"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="输入报修人姓名"
            />
          </div>

          <!-- 位置筛选 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              故障位置
            </label>
            <input 
              v-model="filters.location"
              type="text"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="输入位置关键词"
            />
          </div>

          <!-- 时间范围筛选 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              创建时间
            </label>
            <div class="grid grid-cols-2 gap-3">
              <div>
                <input 
                  v-model="filters.startDate"
                  type="date"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <div class="text-xs text-gray-500 mt-1">开始日期</div>
              </div>
              <div>
                <input 
                  v-model="filters.endDate"
                  type="date"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <div class="text-xs text-gray-500 mt-1">结束日期</div>
              </div>
            </div>
          </div>

          <!-- 快速筛选 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-3">
              快速筛选
            </label>
            <div class="space-y-2">
              <label class="flex items-center">
                <input 
                  v-model="filters.onlyUrgent"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span class="ml-2 text-sm text-gray-700">只显示紧急工单</span>
              </label>
              
              <label class="flex items-center">
                <input 
                  v-model="filters.onlyOverdue"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span class="ml-2 text-sm text-gray-700">只显示超时工单</span>
              </label>
              
              <label class="flex items-center">
                <input 
                  v-model="filters.onlyUnassigned"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span class="ml-2 text-sm text-gray-700">只显示未分配工单</span>
              </label>
            </div>
          </div>

          <!-- 关键词搜索 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              关键词搜索
            </label>
            <input 
              v-model="filters.keyword"
              type="text"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="搜索标题、描述、解决方案等"
            />
          </div>
        </form>
      </div>

      <!-- 底部按钮 -->
      <div class="px-6 py-4 border-t border-gray-200 flex justify-between">
        <button 
          type="button"
          @click="handleReset"
          class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
        >
          重置筛选
        </button>
        
        <div class="flex space-x-3">
          <button 
            type="button"
            @click="handleClose"
            class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
          >
            取消
          </button>
          <button 
            @click="handleApply"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            应用筛选
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  show: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:show': [value: boolean]
  apply: [filters: any]
}>()

const repairStore = useRepairStore()

// 响应式数据
const filters = reactive({
  status: '',
  priority: '',
  category: '',
  assignee: '',
  reporterName: '',
  location: '',
  startDate: '',
  endDate: '',
  onlyUrgent: false,
  onlyOverdue: false,
  onlyUnassigned: false,
  keyword: ''
})

// 方法
const resetFilters = () => {
  filters.status = ''
  filters.priority = ''
  filters.category = ''
  filters.assignee = ''
  filters.reporterName = ''
  filters.location = ''
  filters.startDate = ''
  filters.endDate = ''
  filters.onlyUrgent = false
  filters.onlyOverdue = false
  filters.onlyUnassigned = false
  filters.keyword = ''
}

const loadCurrentFilters = () => {
  const currentFilters = repairStore.filters
  
  filters.status = currentFilters.status || ''
  filters.priority = currentFilters.priority || ''
  filters.category = currentFilters.category || ''
  filters.assignee = currentFilters.assignee || ''
  filters.reporterName = currentFilters.reporterName || ''
  filters.location = currentFilters.location || ''
  filters.startDate = currentFilters.startDate ? new Date(currentFilters.startDate).toISOString().split('T')[0] : ''
  filters.endDate = currentFilters.endDate ? new Date(currentFilters.endDate).toISOString().split('T')[0] : ''
  filters.onlyUrgent = currentFilters.onlyUrgent || false
  filters.onlyOverdue = currentFilters.onlyOverdue || false
  filters.onlyUnassigned = currentFilters.onlyUnassigned || false
  filters.keyword = currentFilters.keyword || ''
}

const handleClose = () => {
  emit('update:show', false)
}

const handleBackdropClick = () => {
  handleClose()
}

const handleReset = () => {
  resetFilters()
}

const handleApply = () => {
  // 构建筛选对象，过滤掉空值
  const appliedFilters: any = {}
  
  if (filters.status) appliedFilters.status = filters.status
  if (filters.priority) appliedFilters.priority = filters.priority
  if (filters.category) appliedFilters.category = filters.category
  if (filters.assignee) appliedFilters.assignee = filters.assignee
  if (filters.reporterName.trim()) appliedFilters.reporterName = filters.reporterName.trim()
  if (filters.location.trim()) appliedFilters.location = filters.location.trim()
  if (filters.startDate) appliedFilters.startDate = new Date(filters.startDate)
  if (filters.endDate) appliedFilters.endDate = new Date(filters.endDate)
  if (filters.onlyUrgent) appliedFilters.onlyUrgent = filters.onlyUrgent
  if (filters.onlyOverdue) appliedFilters.onlyOverdue = filters.onlyOverdue
  if (filters.onlyUnassigned) appliedFilters.onlyUnassigned = filters.onlyUnassigned
  if (filters.keyword.trim()) appliedFilters.keyword = filters.keyword.trim()
  
  emit('apply', appliedFilters)
  handleClose()
}

// 监听props变化
watch(() => props.show, (newValue) => {
  if (newValue) {
    loadCurrentFilters()
  }
})

// 键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.show) {
    handleClose()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>
