<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>操作日志 - 系统管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <style>
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .btn-fix {
            touch-action: manipulation;
        }
        .log-level-info { @apply bg-blue-100 text-blue-800; }
        .log-level-warning { @apply bg-yellow-100 text-yellow-800; }
        .log-level-error { @apply bg-red-100 text-red-800; }
        .log-level-success { @apply bg-green-100 text-green-800; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="index.html" class="text-blue-600 hover:text-blue-800 mr-4">
                        ← 返回管理首页
                    </a>
                    <h1 class="text-xl font-semibold text-gray-900">操作日志</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="exportLogsBtn" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                        导出日志
                    </button>
                    <button id="clearLogsBtn" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                        清理日志
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">今日日志</dt>
                            <dd class="text-lg font-medium text-gray-900" id="todayLogs">0</dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">错误日志</dt>
                            <dd class="text-lg font-medium text-gray-900" id="errorLogs">0</dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">警告日志</dt>
                            <dd class="text-lg font-medium text-gray-900" id="warningLogs">0</dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">活跃用户</dt>
                            <dd class="text-lg font-medium text-gray-900" id="activeUsers">0</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="bg-white rounded-lg shadow mb-6 p-6">
            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div>
                    <input type="text" id="searchInput" placeholder="搜索用户或操作..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                </div>
                <div>
                    <select id="levelFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                        <option value="">全部级别</option>
                        <option value="info">信息</option>
                        <option value="warning">警告</option>
                        <option value="error">错误</option>
                        <option value="success">成功</option>
                    </select>
                </div>
                <div>
                    <select id="moduleFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                        <option value="">全部模块</option>
                        <option value="system">系统管理</option>
                        <option value="user">用户管理</option>
                        <option value="inventory">库存管理</option>
                        <option value="work">工作管理</option>
                        <option value="repair">维修管理</option>
                    </select>
                </div>
                <div>
                    <input type="date" id="dateFilter"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                </div>
                <div>
                    <button id="searchBtn" class="w-full bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md text-sm btn-fix">
                        搜索
                    </button>
                </div>
            </div>
        </div>

        <!-- 日志列表 -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-medium text-gray-900">操作日志</h2>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">模块</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">级别</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP地址</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">详情</th>
                        </tr>
                    </thead>
                    <tbody id="logsTableBody" class="bg-white divide-y divide-gray-200">
                        <!-- 日志列表将在这里动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span id="pageInfo">1-20</span> 条，共 <span id="totalCount">0</span> 条
                    </div>
                    <div class="flex space-x-2">
                        <button id="prevPageBtn" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 btn-fix">上一页</button>
                        <button id="nextPageBtn" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 btn-fix">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 日志详情弹窗 -->
    <div id="logDetailModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 fade-in">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-xl font-semibold text-gray-800">日志详情</h2>
                    <button id="closeLogDetailModal" class="text-gray-400 hover:text-gray-600 text-2xl btn-fix">&times;</button>
                </div>
            </div>
            <div class="p-6">
                <div id="logDetailContent" class="space-y-4">
                    <!-- 日志详情将在这里动态加载 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 加载指示器 -->
    <div id="loadingIndicator" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-40" style="display: none;">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <span class="text-gray-700">加载中...</span>
        </div>
    </div>

    <!-- 引入模块化JS文件 -->
    <script src="../js/config.js"></script>
    <script src="../js/permission-manager.js"></script>
    <script>
        // 操作日志管理应用类
        class LogManagementApp {
            constructor() {
                this.currentPage = 1;
                this.pageSize = 20;
                this.totalCount = 0;
            }

            init() {
                this.bindEvents();
                this.loadStatistics();
                this.loadLogs();
                this.createSampleLogs(); // 创建示例日志
            }

            bindEvents() {
                // 搜索按钮
                document.getElementById('searchBtn').addEventListener('click', () => {
                    this.currentPage = 1;
                    this.loadLogs();
                });

                // 搜索输入框回车
                document.getElementById('searchInput').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.currentPage = 1;
                        this.loadLogs();
                    }
                });

                // 筛选器变化
                document.getElementById('levelFilter').addEventListener('change', () => {
                    this.currentPage = 1;
                    this.loadLogs();
                });

                document.getElementById('moduleFilter').addEventListener('change', () => {
                    this.currentPage = 1;
                    this.loadLogs();
                });

                document.getElementById('dateFilter').addEventListener('change', () => {
                    this.currentPage = 1;
                    this.loadLogs();
                });

                // 分页按钮
                document.getElementById('prevPageBtn').addEventListener('click', () => {
                    if (this.currentPage > 1) {
                        this.currentPage--;
                        this.loadLogs();
                    }
                });

                document.getElementById('nextPageBtn').addEventListener('click', () => {
                    const maxPage = Math.ceil(this.totalCount / this.pageSize);
                    if (this.currentPage < maxPage) {
                        this.currentPage++;
                        this.loadLogs();
                    }
                });

                // 导出日志按钮
                document.getElementById('exportLogsBtn').addEventListener('click', () => {
                    this.exportLogs();
                });

                // 清理日志按钮
                document.getElementById('clearLogsBtn').addEventListener('click', () => {
                    this.clearLogs();
                });

                // 日志详情弹窗
                document.getElementById('closeLogDetailModal').addEventListener('click', () => {
                    document.getElementById('logDetailModal').style.display = 'none';
                });
            }

            async createSampleLogs() {
                try {
                    // 检查是否已有日志数据
                    const query = new AV.Query('SystemLog');
                    let count = 0;
                    try {
                        count = await query.count();
                    } catch (error) {
                        // 表不存在，继续创建示例数据
                        console.log('SystemLog 表不存在，将创建示例数据');
                    }

                    if (count > 0) return; // 已有数据，不创建示例

                    const sampleLogs = [
                        {
                            level: 'info',
                            module: 'user',
                            action: '用户登录',
                            message: '用户成功登录系统',
                            userId: 'admin',
                            userName: '管理员',
                            ipAddress: '*************',
                            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                            details: { loginTime: new Date().toISOString() }
                        },
                        {
                            level: 'success',
                            module: 'inventory',
                            action: '创建入库单',
                            message: '成功创建入库单 IN20241206001',
                            userId: 'user1',
                            userName: '张三',
                            ipAddress: '*************',
                            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                            details: { orderNo: 'IN20241206001', amount: 1500.00 }
                        },
                        {
                            level: 'warning',
                            module: 'inventory',
                            action: '库存预警',
                            message: '商品"办公用纸"库存不足，当前库存: 5',
                            userId: 'system',
                            userName: '系统',
                            ipAddress: '127.0.0.1',
                            userAgent: 'System',
                            details: { productName: '办公用纸', currentStock: 5, minStock: 10 }
                        },
                        {
                            level: 'error',
                            module: 'system',
                            action: '登录失败',
                            message: '用户登录失败，密码错误',
                            userId: 'unknown',
                            userName: '未知用户',
                            ipAddress: '*************',
                            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                            details: { attemptCount: 3, reason: 'invalid_password' }
                        },
                        {
                            level: 'info',
                            module: 'work',
                            action: '提交工作日志',
                            message: '用户提交了今日工作日志',
                            userId: 'user2',
                            userName: '李四',
                            ipAddress: '*************',
                            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                            details: { logDate: new Date().toISOString().split('T')[0] }
                        }
                    ];

                    for (const logData of sampleLogs) {
                        const log = new AV.Object('SystemLog');
                        Object.keys(logData).forEach(key => {
                            log.set(key, logData[key]);
                        });
                        await log.save();
                    }

                    console.log('示例日志创建完成');
                } catch (error) {
                    console.error('创建示例日志失败:', error);
                }
            }

            async loadStatistics() {
                try {
                    const today = new Date();
                    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());

                    let todayCount = 0, errorCount = 0, warningCount = 0, activeUsers = 0;

                    try {
                        // 查询今日日志数量
                        const todayQuery = new AV.Query('SystemLog');
                        todayQuery.greaterThanOrEqualTo('createdAt', startOfDay);
                        todayCount = await todayQuery.count();

                        // 查询错误日志数量
                        const errorQuery = new AV.Query('SystemLog');
                        errorQuery.equalTo('level', 'error');
                        errorQuery.greaterThanOrEqualTo('createdAt', startOfDay);
                        errorCount = await errorQuery.count();

                        // 查询警告日志数量
                        const warningQuery = new AV.Query('SystemLog');
                        warningQuery.equalTo('level', 'warning');
                        warningQuery.greaterThanOrEqualTo('createdAt', startOfDay);
                        warningCount = await warningQuery.count();

                        // 查询活跃用户数量（今日有操作的用户）
                        const activeQuery = new AV.Query('SystemLog');
                        activeQuery.greaterThanOrEqualTo('createdAt', startOfDay);
                        activeQuery.notEqualTo('userId', 'system');
                        const activeLogs = await activeQuery.find();
                        activeUsers = new Set(activeLogs.map(log => log.get('userId'))).size;
                    } catch (error) {
                        // 表不存在时使用默认值
                        console.log('SystemLog 表不存在，使用默认统计值');
                    }

                    // 更新显示
                    document.getElementById('todayLogs').textContent = todayCount;
                    document.getElementById('errorLogs').textContent = errorCount;
                    document.getElementById('warningLogs').textContent = warningCount;
                    document.getElementById('activeUsers').textContent = activeUsers;
                } catch (error) {
                    console.error('加载统计数据失败:', error);
                }
            }

            async loadLogs() {
                try {
                    this.showLoading();

                    let results = [];
                    let total = 0;

                    try {
                        const query = new AV.Query('SystemLog');

                        // 应用搜索条件
                        const searchText = document.getElementById('searchInput').value.trim();
                        if (searchText) {
                            const userQuery = new AV.Query('SystemLog');
                            userQuery.contains('userName', searchText);

                            const actionQuery = new AV.Query('SystemLog');
                            actionQuery.contains('action', searchText);

                            const messageQuery = new AV.Query('SystemLog');
                            messageQuery.contains('message', searchText);

                            query._orQuery([userQuery, actionQuery, messageQuery]);
                        }

                        // 应用级别筛选
                        const levelFilter = document.getElementById('levelFilter').value;
                        if (levelFilter) {
                            query.equalTo('level', levelFilter);
                        }

                        // 应用模块筛选
                        const moduleFilter = document.getElementById('moduleFilter').value;
                        if (moduleFilter) {
                            query.equalTo('module', moduleFilter);
                        }

                        // 应用日期筛选
                        const dateFilter = document.getElementById('dateFilter').value;
                        if (dateFilter) {
                            const filterDate = new Date(dateFilter);
                            const nextDay = new Date(filterDate);
                            nextDay.setDate(nextDay.getDate() + 1);
                            query.greaterThanOrEqualTo('createdAt', filterDate);
                            query.lessThan('createdAt', nextDay);
                        }

                        // 排序和分页
                        query.descending('createdAt');
                        query.limit(this.pageSize);
                        query.skip((this.currentPage - 1) * this.pageSize);

                        results = await query.find();
                        total = await query.count();
                    } catch (error) {
                        // 表不存在时，先创建示例数据
                        if (error.message.includes('Class or object doesn\'t exists')) {
                            console.log('SystemLog 表不存在，正在创建示例数据...');
                            await this.createSampleLogs();
                            // 重新查询
                            const query = new AV.Query('SystemLog');
                            query.descending('createdAt');
                            query.limit(this.pageSize);
                            query.skip((this.currentPage - 1) * this.pageSize);
                            results = await query.find();
                            total = await query.count();
                        } else {
                            throw error;
                        }
                    }

                    this.totalCount = total;
                    this.renderLogsList(results);
                    this.updatePagination();

                    this.hideLoading();
                } catch (error) {
                    console.error('加载日志列表失败:', error);
                    this.hideLoading();
                    alert('加载日志列表失败: ' + error.message);
                }
            }

            renderLogsList(logs) {
                const tbody = document.getElementById('logsTableBody');

                if (logs.length === 0) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="7" class="px-6 py-8 text-center text-gray-500">
                                暂无日志数据
                            </td>
                        </tr>
                    `;
                    return;
                }

                const html = logs.map(log => {
                    const level = log.get('level');
                    const levelClass = this.getLevelClass(level);
                    const levelText = this.getLevelText(level);
                    const moduleText = this.getModuleText(log.get('module'));

                    return `
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${log.createdAt.toLocaleString()}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                ${log.get('userName') || '-'}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${moduleText}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                ${log.get('action')}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium rounded-full ${levelClass}">
                                    ${levelText}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${log.get('ipAddress') || '-'}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="logApp.showLogDetail('${log.id}')"
                                        class="text-blue-600 hover:text-blue-900">查看详情</button>
                            </td>
                        </tr>
                    `;
                }).join('');

                tbody.innerHTML = html;
            }

            getLevelClass(level) {
                const classMap = {
                    'info': 'log-level-info',
                    'warning': 'log-level-warning',
                    'error': 'log-level-error',
                    'success': 'log-level-success'
                };
                return classMap[level] || 'log-level-info';
            }

            getLevelText(level) {
                const textMap = {
                    'info': '信息',
                    'warning': '警告',
                    'error': '错误',
                    'success': '成功'
                };
                return textMap[level] || level;
            }

            getModuleText(module) {
                const textMap = {
                    'system': '系统管理',
                    'user': '用户管理',
                    'inventory': '库存管理',
                    'work': '工作管理',
                    'repair': '维修管理'
                };
                return textMap[module] || module;
            }

            updatePagination() {
                const start = (this.currentPage - 1) * this.pageSize + 1;
                const end = Math.min(this.currentPage * this.pageSize, this.totalCount);

                document.getElementById('pageInfo').textContent = `${start}-${end}`;
                document.getElementById('totalCount').textContent = this.totalCount;

                // 更新分页按钮状态
                document.getElementById('prevPageBtn').disabled = this.currentPage <= 1;
                const maxPage = Math.ceil(this.totalCount / this.pageSize);
                document.getElementById('nextPageBtn').disabled = this.currentPage >= maxPage;
            }

            async showLogDetail(logId) {
                try {
                    const query = new AV.Query('SystemLog');
                    const log = await query.get(logId);

                    const content = document.getElementById('logDetailContent');
                    const details = log.get('details') || {};

                    content.innerHTML = `
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">时间</label>
                                <p class="text-sm text-gray-900">${log.createdAt.toLocaleString()}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">级别</label>
                                <span class="px-2 py-1 text-xs font-medium rounded-full ${this.getLevelClass(log.get('level'))}">
                                    ${this.getLevelText(log.get('level'))}
                                </span>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">用户</label>
                                <p class="text-sm text-gray-900">${log.get('userName')} (${log.get('userId')})</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">模块</label>
                                <p class="text-sm text-gray-900">${this.getModuleText(log.get('module'))}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">操作</label>
                                <p class="text-sm text-gray-900">${log.get('action')}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">IP地址</label>
                                <p class="text-sm text-gray-900">${log.get('ipAddress') || '-'}</p>
                            </div>
                            <div class="col-span-2">
                                <label class="block text-sm font-medium text-gray-700">消息</label>
                                <p class="text-sm text-gray-900">${log.get('message')}</p>
                            </div>
                            <div class="col-span-2">
                                <label class="block text-sm font-medium text-gray-700">用户代理</label>
                                <p class="text-sm text-gray-900 break-all">${log.get('userAgent') || '-'}</p>
                            </div>
                            <div class="col-span-2">
                                <label class="block text-sm font-medium text-gray-700">详细信息</label>
                                <pre class="text-sm text-gray-900 bg-gray-100 p-3 rounded mt-1 overflow-auto">${JSON.stringify(details, null, 2)}</pre>
                            </div>
                        </div>
                    `;

                    document.getElementById('logDetailModal').style.display = 'flex';
                } catch (error) {
                    console.error('加载日志详情失败:', error);
                    alert('加载日志详情失败: ' + error.message);
                }
            }

            exportLogs() {
                // 实现日志导出功能
                alert('日志导出功能开发中...');
            }

            async clearLogs() {
                if (!confirm('确定要清理日志吗？建议只清理30天前的日志。')) {
                    return;
                }

                const days = prompt('请输入要保留的天数（清理N天前的日志）:', '30');
                if (!days || isNaN(days)) {
                    return;
                }

                try {
                    this.showLoading();

                    const cutoffDate = new Date();
                    cutoffDate.setDate(cutoffDate.getDate() - parseInt(days));

                    const query = new AV.Query('SystemLog');
                    query.lessThan('createdAt', cutoffDate);
                    const oldLogs = await query.find();

                    if (oldLogs.length === 0) {
                        alert('没有需要清理的日志');
                        this.hideLoading();
                        return;
                    }

                    await AV.Object.destroyAll(oldLogs);

                    this.hideLoading();
                    alert(`成功清理了 ${oldLogs.length} 条日志`);
                    this.loadLogs();
                    this.loadStatistics();
                } catch (error) {
                    console.error('清理日志失败:', error);
                    this.hideLoading();
                    alert('清理日志失败: ' + error.message);
                }
            }

            showLoading() {
                document.getElementById('loadingIndicator').style.display = 'flex';
            }

            hideLoading() {
                document.getElementById('loadingIndicator').style.display = 'none';
            }
        }

        // 全局变量
        let logApp;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            function initApp() {
                if (typeof AV !== 'undefined') {
                    logApp = new LogManagementApp();
                    logApp.init();
                } else {
                    setTimeout(initApp, 100);
                }
            }
            initApp();
        });
    </script>
</body>
</html>