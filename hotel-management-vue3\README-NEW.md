# 🏨 酒店管理系统 (Vue3版)

一个现代化的酒店管理系统，基于 Vue3 + Nuxt3 + TypeScript 构建，提供完整的酒店运营管理功能。

## ✨ 功能特性

### 🔐 用户认证系统
- 用户注册、登录、权限管理
- 基于角色的访问控制 (RBAC)
- 部门管理和用户分组
- 安全的会话管理

### 📝 工作日志管理
- 多类型工作日志记录
- 图片上传和预览
- 高级筛选和搜索
- 企业微信通知集成

### 📦 库存管理系统
- 物品信息管理
- 入库、出库、调整操作
- 库存预警和统计分析
- 供应商管理

### 🔧 报修系统
- 报修工单创建和管理
- 工单分配和状态跟踪
- 完成反馈和评价
- 实时通知推送

### 👥 管理后台
- 用户管理和权限配置
- 系统设置和参数配置
- 数据统计和分析报表
- 系统监控和日志管理

### 💬 企业微信集成
- 消息通知推送
- API 集成配置
- 自定义通知模板

## 🛠 技术栈

### 前端技术
- **Vue 3** - 渐进式 JavaScript 框架
- **Nuxt 3** - Vue.js 全栈框架
- **TypeScript** - 类型安全的 JavaScript
- **Tailwind CSS** - 原子化 CSS 框架
- **Pinia** - Vue 状态管理库
- **Nuxt Icon** - 图标组件库

### 后端服务
- **LeanCloud** - 后端即服务 (BaaS)
- **企业微信 API** - 消息通知服务

### 开发工具
- **Vite** - 快速构建工具
- **ESLint** - 代码质量检查
- **Prettier** - 代码格式化

## 🚀 快速开始

### 环境要求
- Node.js 18.x 或更高版本
- npm 或 yarn 包管理器

### 安装依赖
```bash
# 克隆项目
git clone <repository-url>
cd hotel-management-vue3

# 安装依赖
npm install
```

### 环境配置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
nano .env
```

### 启动开发服务器
```bash
npm run dev
```

访问 http://localhost:3000 查看应用。

### 构建生产版本
```bash
npm run build
```

## 📁 项目结构

```
hotel-management-vue3/
├── components/           # Vue 组件
│   ├── common/          # 通用组件
│   ├── admin/           # 管理后台组件
│   ├── inventory/       # 库存管理组件
│   ├── repair/          # 报修系统组件
│   └── worklog/         # 工作日志组件
├── composables/         # 组合式函数
├── layouts/             # 布局组件
├── middleware/          # 中间件
├── pages/               # 页面组件
├── plugins/             # 插件
├── stores/              # Pinia 状态管理
├── types/               # TypeScript 类型定义
├── utils/               # 工具函数
└── server/              # 服务端代码
```

## 🔧 配置说明

### LeanCloud 配置
1. 注册 [LeanCloud](https://console.leancloud.cn/) 账号
2. 创建应用并获取配置信息
3. 在 `.env` 文件中配置相关参数

### 企业微信配置
1. 创建企业微信应用
2. 获取 Corp ID、Secret 和 Agent ID
3. 配置回调地址和权限

详细配置请参考 [部署文档](./DEPLOYMENT.md)。

## 📱 功能模块

### 工作日志
- 支持多种日志类型（日常工作、设备维护、清洁工作等）
- 图片上传和预览功能
- 按时间、类型、用户筛选
- 企业微信通知推送

### 库存管理
- 物品信息维护（名称、分类、规格等）
- 库存操作（入库、出库、调整）
- 库存预警和统计
- 供应商信息管理

### 报修系统
- 报修工单创建
- 工单分配给工程师
- 状态跟踪（待处理、处理中、已完成）
- 完成反馈和用户评价

### 用户管理
- 用户账户管理
- 角色权限配置
- 部门组织架构
- 登录日志记录

## 🎨 界面预览

系统采用现代化的设计风格，支持响应式布局，在桌面端和移动端都有良好的用户体验。

### 主要页面
- 🏠 **首页** - 系统概览和快速操作
- 📝 **工作日志** - 日志记录和管理
- 📦 **库存管理** - 物品和库存操作
- 🔧 **报修系统** - 工单管理和处理
- ⚙️ **系统设置** - 配置和管理功能

## 🔒 安全特性

- JWT 令牌认证
- 基于角色的权限控制
- 密码加密存储
- 会话管理和超时控制
- CSRF 防护
- XSS 防护

## 📊 性能优化

- 组件懒加载
- 图片压缩和优化
- 代码分割和按需加载
- 缓存策略优化
- 服务端渲染 (SSR)

## 🐳 部署方式

### Docker 部署
```bash
# 构建镜像
docker build -t hotel-management .

# 运行容器
docker run -p 3000:3000 hotel-management
```

### Docker Compose 部署
```bash
docker-compose up -d
```

### 传统部署
详细部署说明请参考 [DEPLOYMENT.md](./DEPLOYMENT.md)。

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

如果您有任何问题或建议，请通过以下方式联系我们：

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 📖 文档: [项目文档](https://docs.example.com)

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！

---

**注意**: 这是一个演示项目，请在生产环境中使用前进行充分的测试和安全评估。
