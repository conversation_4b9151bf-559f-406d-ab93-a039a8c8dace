/**
 * 统一错误处理模块
 * 提供全局错误处理、用户友好的错误提示、错误日志记录
 */

class ErrorHandler {
    constructor() {
        this.errorQueue = [];
        this.maxQueueSize = 100;
        this.retryAttempts = new Map();
        this.maxRetries = 3;
        
        // 初始化全局错误监听
        this.initGlobalErrorHandling();
    }

    /**
     * 初始化全局错误处理
     */
    initGlobalErrorHandling() {
        // 捕获未处理的JavaScript错误
        window.addEventListener('error', (event) => {
            this.handleGlobalError({
                type: 'javascript',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error,
                stack: event.error?.stack
            });
        });

        // 捕获未处理的Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            this.handleGlobalError({
                type: 'promise',
                message: event.reason?.message || 'Unhandled Promise Rejection',
                error: event.reason,
                stack: event.reason?.stack
            });
        });

        // 捕获资源加载错误
        window.addEventListener('error', (event) => {
            if (event.target !== window) {
                this.handleGlobalError({
                    type: 'resource',
                    message: `Failed to load resource: ${event.target.src || event.target.href}`,
                    element: event.target.tagName,
                    source: event.target.src || event.target.href
                });
            }
        }, true);
    }

    /**
     * 处理全局错误
     * @param {Object} errorInfo - 错误信息
     */
    handleGlobalError(errorInfo) {
        console.error('Global Error:', errorInfo);
        
        // 记录错误到队列
        this.logError(errorInfo);
        
        // 根据错误类型决定是否显示给用户
        if (errorInfo.type === 'resource') {
            // 资源加载错误通常不需要显示给用户
            return;
        }
        
        // 显示用户友好的错误信息
        this.showUserError('系统发生错误，请刷新页面重试');
    }

    /**
     * 处理业务错误
     * @param {Error|Object} error - 错误对象
     * @param {string} context - 错误上下文
     * @param {Object} options - 处理选项
     */
    handle(error, context = '', options = {}) {
        const {
            showToUser = true,
            allowRetry = false,
            retryCallback = null,
            logLevel = 'error'
        } = options;

        // 标准化错误对象
        const standardError = this.standardizeError(error, context);
        
        // 记录错误
        this.logError(standardError, logLevel);
        
        // 显示用户友好的错误信息
        if (showToUser) {
            const userMessage = this.getUserFriendlyMessage(standardError);
            this.showUserError(userMessage, allowRetry, retryCallback);
        }
        
        return standardError;
    }

    /**
     * 标准化错误对象
     * @param {Error|Object} error - 原始错误
     * @param {string} context - 错误上下文
     * @returns {Object} 标准化的错误对象
     */
    standardizeError(error, context) {
        const timestamp = new Date().toISOString();
        const userAgent = navigator.userAgent;
        const url = window.location.href;

        // LeanCloud错误处理
        if (error && typeof error === 'object' && error.code !== undefined) {
            return {
                type: 'leancloud',
                code: error.code,
                message: error.message || error.rawMessage || '未知错误',
                context,
                timestamp,
                userAgent,
                url,
                stack: error.stack
            };
        }

        // 标准JavaScript错误
        if (error instanceof Error) {
            return {
                type: 'javascript',
                code: error.name,
                message: error.message,
                context,
                timestamp,
                userAgent,
                url,
                stack: error.stack
            };
        }

        // 字符串错误
        if (typeof error === 'string') {
            return {
                type: 'custom',
                code: 'CUSTOM_ERROR',
                message: error,
                context,
                timestamp,
                userAgent,
                url
            };
        }

        // 其他类型错误
        return {
            type: 'unknown',
            code: 'UNKNOWN_ERROR',
            message: JSON.stringify(error),
            context,
            timestamp,
            userAgent,
            url
        };
    }

    /**
     * 获取用户友好的错误信息
     * @param {Object} error - 标准化错误对象
     * @returns {string} 用户友好的错误信息
     */
    getUserFriendlyMessage(error) {
        // LeanCloud特定错误
        if (error.type === 'leancloud') {
            switch (error.code) {
                case 101:
                    return '用户名或密码错误';
                case 119:
                    return '登录已过期，请重新登录';
                case 120:
                    return '用户名已存在';
                case 125:
                    return '邮箱地址无效';
                case 137:
                    return '手机号码已被使用';
                case 200:
                    return '用户名不能为空';
                case 201:
                    return '密码不能为空';
                case 202:
                    return '用户名已被占用';
                case 203:
                    return '邮箱已被占用';
                case 204:
                    return '邮箱地址不能为空';
                case 205:
                    return '找不到此邮箱对应的用户';
                case 206:
                    return '用户无法被修改';
                case 207:
                    return '只能通过注册创建用户';
                case 208:
                    return '用户名和密码都是必需的';
                case 209:
                    return '登录失败次数过多，请稍后再试';
                case 210:
                    return '用户名和密码不匹配';
                case 211:
                    return '找不到此用户';
                case 213:
                    return '手机号码格式不正确';
                case 214:
                    return '手机号码不能为空';
                case 215:
                    return '手机号码已被验证';
                case 216:
                    return '手机号码尚未验证';
                case 401:
                    return '权限不足，请联系管理员';
                case 403:
                    return '禁止访问，权限不足';
                case 404:
                    return '请求的资源不存在';
                case 429:
                    return '请求过于频繁，请稍后再试';
                case 500:
                    return '服务器内部错误，请稍后重试';
                case 503:
                    return '服务暂时不可用，请稍后重试';
                default:
                    return error.message || '操作失败，请重试';
            }
        }

        // 网络错误
        if (error.message && error.message.includes('网络')) {
            return '网络连接失败，请检查网络后重试';
        }

        // 权限错误
        if (error.message && error.message.includes('权限')) {
            return '权限不足，无法执行此操作';
        }

        // 默认错误信息
        return error.message || '操作失败，请重试';
    }

    /**
     * 显示用户错误信息
     * @param {string} message - 错误信息
     * @param {boolean} allowRetry - 是否允许重试
     * @param {Function} retryCallback - 重试回调函数
     */
    showUserError(message, allowRetry = false, retryCallback = null) {
        // 优先使用 WorkLogUtils.showMessage
        if (typeof WorkLogUtils !== 'undefined' && WorkLogUtils.showMessage) {
            WorkLogUtils.showMessage(message, 'error');
            
            if (allowRetry && retryCallback) {
                setTimeout(() => {
                    if (confirm(`${message}\n\n是否重试？`)) {
                        retryCallback();
                    }
                }, 2000);
            }
            return;
        }

        // 创建自定义错误提示
        this.createErrorToast(message, allowRetry, retryCallback);
    }

    /**
     * 创建错误提示Toast
     * @param {string} message - 错误信息
     * @param {boolean} allowRetry - 是否允许重试
     * @param {Function} retryCallback - 重试回调函数
     */
    createErrorToast(message, allowRetry = false, retryCallback = null) {
        // 移除已存在的错误提示
        const existingToast = document.querySelector('.error-toast');
        if (existingToast) {
            existingToast.remove();
        }

        // 创建错误提示元素
        const toast = document.createElement('div');
        toast.className = 'error-toast';
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #f56565;
            color: white;
            padding: 16px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            max-width: 400px;
            font-size: 14px;
            line-height: 1.4;
        `;

        const messageDiv = document.createElement('div');
        messageDiv.textContent = message;
        toast.appendChild(messageDiv);

        // 添加重试按钮
        if (allowRetry && retryCallback) {
            const retryBtn = document.createElement('button');
            retryBtn.textContent = '重试';
            retryBtn.style.cssText = `
                background: rgba(255,255,255,0.2);
                border: 1px solid rgba(255,255,255,0.3);
                color: white;
                padding: 4px 12px;
                border-radius: 4px;
                margin-top: 8px;
                cursor: pointer;
                font-size: 12px;
            `;
            retryBtn.onclick = () => {
                toast.remove();
                retryCallback();
            };
            toast.appendChild(retryBtn);
        }

        // 添加关闭按钮
        const closeBtn = document.createElement('button');
        closeBtn.innerHTML = '×';
        closeBtn.style.cssText = `
            position: absolute;
            top: 8px;
            right: 8px;
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            padding: 0;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        `;
        closeBtn.onclick = () => toast.remove();
        toast.appendChild(closeBtn);

        document.body.appendChild(toast);

        // 自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 5000);
    }

    /**
     * 记录错误到队列
     * @param {Object} error - 错误对象
     * @param {string} level - 日志级别
     */
    logError(error, level = 'error') {
        // 添加到错误队列
        this.errorQueue.push({
            ...error,
            level,
            id: Date.now() + Math.random()
        });

        // 限制队列大小
        if (this.errorQueue.length > this.maxQueueSize) {
            this.errorQueue.shift();
        }

        // 控制台输出
        console[level]('Error logged:', error);

        // 可以在这里添加发送到监控服务的逻辑
        // this.sendToMonitoring(error);
    }

    /**
     * 获取错误队列
     * @returns {Array} 错误队列
     */
    getErrorQueue() {
        return [...this.errorQueue];
    }

    /**
     * 清空错误队列
     */
    clearErrorQueue() {
        this.errorQueue = [];
    }

    /**
     * 重试机制
     * @param {Function} operation - 要重试的操作
     * @param {string} operationId - 操作ID
     * @param {Object} options - 重试选项
     */
    async retry(operation, operationId, options = {}) {
        const { maxRetries = this.maxRetries, delay = 1000 } = options;
        
        const currentAttempts = this.retryAttempts.get(operationId) || 0;
        
        if (currentAttempts >= maxRetries) {
            throw new Error(`操作失败，已重试${maxRetries}次`);
        }

        try {
            const result = await operation();
            this.retryAttempts.delete(operationId);
            return result;
        } catch (error) {
            this.retryAttempts.set(operationId, currentAttempts + 1);
            
            if (currentAttempts + 1 >= maxRetries) {
                this.retryAttempts.delete(operationId);
                throw error;
            }
            
            // 延迟后重试
            await new Promise(resolve => setTimeout(resolve, delay * (currentAttempts + 1)));
            return this.retry(operation, operationId, options);
        }
    }
}

// 创建全局错误处理器实例
const globalErrorHandler = new ErrorHandler();

// 导出到全局
if (typeof window !== 'undefined') {
    window.ErrorHandler = ErrorHandler;
    window.globalErrorHandler = globalErrorHandler;
}

// Node.js环境支持
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ErrorHandler, globalErrorHandler };
}
