// server/api/wechat/user.get.ts
export default defineEventHandler(async (event) => {
  const query = getQuery(event)
  const { userid } = query

  if (!userid) {
    throw createError({
      statusCode: 400,
      statusMessage: '缺少用户ID参数'
    })
  }

  try {
    // 首先获取access_token
    const config = useRuntimeConfig()
    const tokenResponse = await $fetch('https://qyapi.weixin.qq.com/cgi-bin/gettoken', {
      method: 'GET',
      query: {
        corpid: config.public.wechatCorpId,
        corpsecret: process.env.WECHAT_CORP_SECRET
      }
    })

    if (tokenResponse.errcode !== 0) {
      throw createError({
        statusCode: 400,
        statusMessage: `获取访问令牌失败: ${tokenResponse.errmsg}`
      })
    }

    // 获取用户信息
    const userResponse = await $fetch('https://qyapi.weixin.qq.com/cgi-bin/user/get', {
      method: 'GET',
      query: {
        access_token: tokenResponse.access_token,
        userid
      }
    })

    if (userResponse.errcode === 0) {
      return {
        userid: userResponse.userid,
        name: userResponse.name,
        department: userResponse.department,
        position: userResponse.position,
        mobile: userResponse.mobile,
        email: userResponse.email,
        avatar: userResponse.avatar
      }
    } else {
      throw createError({
        statusCode: 400,
        statusMessage: `获取用户信息失败: ${userResponse.errmsg}`
      })
    }
  } catch (error: any) {
    throw createError({
      statusCode: 500,
      statusMessage: error.message || '获取企业微信用户信息失败'
    })
  }
})
