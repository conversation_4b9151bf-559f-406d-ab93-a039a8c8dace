/**
 * 后台管理模块 - 实现用户管理功能
 * 继承自BaseWorkLogApp，实现管理员特定功能
 */

class AdminApp extends BaseWorkLogApp {
    constructor() {
        super({
            pageType: 'admin',
            requiredElements: ['adminPanel', 'usersPanel']
        });
        
        // 绑定方法上下文
        this.loadUsers = this.loadUsers.bind(this);
        this.editUser = this.editUser.bind(this);
        this.saveUser = this.saveUser.bind(this);
        this.switchTab = this.switchTab.bind(this);
        
        // 当前选中的标签
        this.currentTab = 'users';
        
        // 用户列表
        this.users = [];
    }

    /**
     * 获取页面特定的DOM元素
     */
    getPageElements() {
        return {
            // 页面区域
            welcomeSection: 'welcomeSection',
            adminPanel: 'adminPanel',
            loadingIndicator: 'loadingIndicator',
            
            // 标签页
            usersTab: 'usersTab',
            departmentsTab: 'departmentsTab',
            rolesTab: 'rolesTab',
            ordersTab: 'ordersTab',
            statsTab: 'statsTab',
            usersPanel: 'usersPanel',
            departmentsPanel: 'departmentsPanel',
            rolesPanel: 'rolesPanel',
            ordersPanel: 'ordersPanel',
            statsPanel: 'statsPanel',
            
            // 用户统计
            totalUsers: 'totalUsers',
            adminUsers: 'adminUsers',
            engineerUsers: 'engineerUsers',
            normalUsers: 'normalUsers',
            
            // 用户管理
            userSearch: 'userSearch',
            addUserBtn: 'addUserBtn',
            refreshUsers: 'refreshUsers',
            usersTableBody: 'usersTableBody',
            
            // 添加用户弹窗
            addUserModal: 'addUserModal',
            closeAddUserModal: 'closeAddUserModal',
            addUserForm: 'addUserForm',
            addUsername: 'addUsername',
            addRealName: 'addRealName',
            addPhone: 'addPhone',
            addDepartment: 'addDepartment',
            addPassword: 'addPassword',
            addUserRoles: 'addUserRoles',
            cancelAddUser: 'cancelAddUser',

            // 用户编辑弹窗
            userModal: 'userModal',
            modalTitle: 'modalTitle',
            closeUserModal: 'closeUserModal',
            userForm: 'userForm',
            editUserId: 'editUserId',
            editRealName: 'editRealName',
            editPhone: 'editPhone',
            editDepartment: 'editDepartment',
            editRoles: 'editRoles',
            cancelEdit: 'cancelEdit',
            
            // 登录相关
            loginBtn: 'loginBtn',
            logoutBtn: 'logoutBtn',
            userInfo: 'userInfo',
            realName: 'realName',
            welcomeLoginBtn: 'welcomeLoginBtn',
            loginModal: 'loginModal',
            closeLoginModal: 'closeLoginModal',
            modalTitle: 'modalTitle',

            // 登录表单
            loginForm: 'loginForm',
            loginUsername: 'loginUsername',
            loginPassword: 'loginPassword',

            // 注册表单
            registerForm: 'registerForm',
            registerUsername: 'registerUsername',
            registerRealName: 'registerRealName',
            registerPhone: 'registerPhone',
            registerDepartment: 'registerDepartment',
            registerPassword: 'registerPassword',
            confirmPassword: 'confirmPassword',

            // 表单切换
            showRegisterBtn: 'showRegisterBtn',
            showLoginBtn: 'showLoginBtn',
            loginSwitchText: 'loginSwitchText',
            registerSwitchText: 'registerSwitchText',

            // 部门管理
            totalDepartments: 'totalDepartments',
            maxDeptUsers: 'maxDeptUsers',
            avgDeptUsers: 'avgDeptUsers',
            addDepartmentBtn: 'addDepartmentBtn',
            refreshDepartments: 'refreshDepartments',
            departmentsTableBody: 'departmentsTableBody',
            departmentModal: 'departmentModal',
            departmentModalTitle: 'departmentModalTitle',
            closeDepartmentModal: 'closeDepartmentModal',
            departmentForm: 'departmentForm',
            editDepartmentId: 'editDepartmentId',
            departmentName: 'departmentName',
            departmentDescription: 'departmentDescription',
            cancelDepartmentEdit: 'cancelDepartmentEdit',

            // 角色管理
            totalRoles: 'totalRoles',
            systemRoles: 'systemRoles',
            customRoles: 'customRoles',
            addRoleBtn: 'addRoleBtn',
            refreshRoles: 'refreshRoles',
            rolesTableBody: 'rolesTableBody',
            roleModal: 'roleModal',
            roleModalTitle: 'roleModalTitle',
            closeRoleModal: 'closeRoleModal',
            roleForm: 'roleForm',
            editRoleId: 'editRoleId',
            roleName: 'roleName',
            roleDescription: 'roleDescription',
            permissionAdmin: 'permissionAdmin',
            permissionEngineer: 'permissionEngineer',
            permissionReporter: 'permissionReporter',
            cancelRoleEdit: 'cancelRoleEdit'
        };
    }

    /**
     * 绑定页面特定事件
     */
    bindPageEvents() {
        // 标签页切换
        if (this.elements.usersTab) {
            this.elements.usersTab.addEventListener('click', () => this.switchTab('users'));
        }
        if (this.elements.departmentsTab) {
            this.elements.departmentsTab.addEventListener('click', () => this.switchTab('departments'));
        }
        if (this.elements.rolesTab) {
            this.elements.rolesTab.addEventListener('click', () => this.switchTab('roles'));
        }
        if (this.elements.ordersTab) {
            this.elements.ordersTab.addEventListener('click', () => this.switchTab('orders'));
        }
        if (this.elements.statsTab) {
            this.elements.statsTab.addEventListener('click', () => this.switchTab('stats'));
        }
        
        // 用户管理
        if (this.elements.addUserBtn) {
            this.elements.addUserBtn.addEventListener('click', () => {
                this.showAddUserModal();
            });
        }

        if (this.elements.refreshUsers) {
            this.elements.refreshUsers.addEventListener('click', this.loadUsers);
        }

        if (this.elements.userSearch) {
            this.elements.userSearch.addEventListener('input', this.filterUsers.bind(this));
        }
        
        // 添加用户弹窗
        if (this.elements.closeAddUserModal) {
            this.elements.closeAddUserModal.addEventListener('click', () => {
                this.hideAddUserModal();
            });
        }

        if (this.elements.cancelAddUser) {
            this.elements.cancelAddUser.addEventListener('click', () => {
                this.hideAddUserModal();
            });
        }

        if (this.elements.addUserForm) {
            this.elements.addUserForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                await this.handleAddUser();
            });
        }

        // 用户编辑弹窗
        if (this.elements.closeUserModal) {
            this.elements.closeUserModal.addEventListener('click', this.hideUserModal.bind(this));
        }

        if (this.elements.cancelEdit) {
            this.elements.cancelEdit.addEventListener('click', this.hideUserModal.bind(this));
        }

        if (this.elements.userForm) {
            this.elements.userForm.addEventListener('submit', this.saveUser);
        }
        
        // 登录相关事件
        this.bindLoginEvents();

        // 初始化部门和角色管理功能
        if (typeof this.initDepartmentsAndRoles === 'function') {
            this.initDepartmentsAndRoles();
        }
    }

    /**
     * 绑定登录相关事件
     */
    bindLoginEvents() {
        // 登录按钮
        if (this.elements.loginBtn) {
            this.elements.loginBtn.addEventListener('click', () => {
                this.showLoginModal();
            });
        }
        
        // 欢迎页面登录按钮
        if (this.elements.welcomeLoginBtn) {
            this.elements.welcomeLoginBtn.addEventListener('click', () => {
                this.showLoginModal();
            });
        }
        
        // 登出按钮
        if (this.elements.logoutBtn) {
            this.elements.logoutBtn.addEventListener('click', () => {
                WorkLogAuth.logout();
            });
        }
        
        // 关闭登录弹窗
        if (this.elements.closeLoginModal) {
            this.elements.closeLoginModal.addEventListener('click', () => {
                this.hideLoginModal();
            });
        }
        
        // 点击弹窗外部关闭
        if (this.elements.loginModal) {
            this.elements.loginModal.addEventListener('click', (e) => {
                if (e.target === this.elements.loginModal) {
                    this.hideLoginModal();
                }
            });
        }
        
        // 登录表单提交
        if (this.elements.loginForm) {
            this.elements.loginForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                await this.handleLogin();
            });
        }

        // 注册表单提交
        if (this.elements.registerForm) {
            this.elements.registerForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                await this.handleRegister();
            });
        }

        // 显示注册表单
        if (this.elements.showRegisterBtn) {
            this.elements.showRegisterBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.showRegisterForm();
            });
        }

        // 显示登录表单
        if (this.elements.showLoginBtn) {
            this.elements.showLoginBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.showLoginForm();
            });
        }
    }

    /**
     * 用户登录后的回调
     */
    onUserLoggedIn() {
        console.log('管理员登录成功，检查权限...');
        if (this.isCurrentUserAdmin()) {
            this.showAdminInterface();
            this.loadUsers();
        } else {
            WorkLogUtils.showMessage('您没有管理员权限', 'error');
            WorkLogAuth.logout();
        }
    }

    /**
     * 用户登出后的回调
     */
    onUserLoggedOut() {
        this.showLoginInterface();
    }

    /**
     * 安全地解析用户角色
     * @param {*} roles 角色数据，可能是数组、字符串或null
     * @returns {Array} 角色数组
     */
    parseUserRoles(roles) {
        if (!roles) return [];

        // 如果已经是数组，直接返回
        if (Array.isArray(roles)) {
            return roles;
        }

        // 如果是字符串，尝试解析
        if (typeof roles === 'string') {
            try {
                const parsed = JSON.parse(roles);
                return Array.isArray(parsed) ? parsed : [parsed];
            } catch (e) {
                // 如果解析失败，将字符串作为单个角色
                return roles.trim() ? [roles] : [];
            }
        }

        // 其他类型转为空数组
        return [];
    }

    /**
     * 检查当前用户是否为管理员
     */
    isCurrentUserAdmin() {
        if (!this.currentUser) return false;

        const userRoles = this.parseUserRoles(this.currentUser.get('roles'));
        const isAdmin = userRoles.includes('admin');
        console.log('权限检查:', { userRoles, isAdmin });
        return isAdmin;
    }

    /**
     * 显示管理员界面
     */
    showAdminInterface() {
        super.showUserInterface();
        
        if (this.elements.adminPanel) {
            this.elements.adminPanel.classList.remove('hidden');
        }
        
        if (this.elements.welcomeSection) {
            this.elements.welcomeSection.classList.add('hidden');
        }
        
        // 默认显示用户管理标签
        this.switchTab('users');
    }

    /**
     * 显示登录界面
     */
    showLoginInterface() {
        super.showLoginPrompt();
        
        if (this.elements.adminPanel) {
            this.elements.adminPanel.classList.add('hidden');
        }
        
        if (this.elements.welcomeSection) {
            this.elements.welcomeSection.classList.remove('hidden');
        }
    }

    /**
     * 显示登录弹窗
     */
    showLoginModal() {
        if (this.elements.loginModal) {
            this.elements.loginModal.classList.remove('hidden');
            this.showLoginForm(); // 默认显示登录表单
        }
    }

    /**
     * 隐藏登录弹窗
     */
    hideLoginModal() {
        if (this.elements.loginModal) {
            this.elements.loginModal.classList.add('hidden');
        }
    }

    /**
     * 处理登录
     */
    async handleLogin() {
        const username = this.elements.loginUsername?.value?.trim();
        const password = this.elements.loginPassword?.value;

        if (!username || !password) {
            WorkLogUtils.showMessage('请输入用户名和密码', 'warning');
            return;
        }

        try {
            const user = await WorkLogAuth.login(username, password);
            if (user) {
                this.hideLoginModal();
                WorkLogUtils.showMessage('登录成功', 'success');

                // 触发登录成功事件
                window.dispatchEvent(new CustomEvent('userLoggedIn', { detail: user }));
            }
        } catch (error) {
            console.error('登录失败:', error);
            WorkLogUtils.showMessage('登录失败: ' + error.message, 'error');
        }
    }

    /**
     * 处理注册
     */
    async handleRegister() {
        const username = this.elements.registerUsername?.value?.trim();
        const realName = this.elements.registerRealName?.value?.trim();
        const phone = this.elements.registerPhone?.value?.trim();
        const department = this.elements.registerDepartment?.value;
        const password = this.elements.registerPassword?.value;
        const confirmPassword = this.elements.confirmPassword?.value;

        if (!username || !realName || !phone || !department || !password || !confirmPassword) {
            WorkLogUtils.showMessage('请填写所有字段', 'warning');
            return;
        }

        if (password !== confirmPassword) {
            WorkLogUtils.showMessage('两次输入的密码不一致', 'warning');
            return;
        }

        if (password.length < 6) {
            WorkLogUtils.showMessage('密码长度至少6位', 'warning');
            return;
        }

        // 验证电话号码格式
        if (!/^1[3-9]\d{9}$/.test(phone) && !/^\d{3,4}-?\d{7,8}$/.test(phone)) {
            WorkLogUtils.showMessage('请输入正确的电话号码', 'warning');
            return;
        }

        try {
            // 使用扩展的注册方法
            const user = await this.registerUserWithDetails(username, password, realName, phone, department);
            if (user) {
                this.hideLoginModal();
                WorkLogUtils.showMessage('注册成功！请登录', 'success');
                this.showLoginForm();

                // 清空表单
                this.clearForms();
            }
        } catch (error) {
            console.error('注册失败:', error);
            WorkLogUtils.showMessage('注册失败: ' + error.message, 'error');
        }
    }

    /**
     * 扩展的用户注册方法
     */
    async registerUserWithDetails(username, password, realName, phone, department) {
        try {
            const user = new AV.User();
            user.setUsername(username);
            user.setPassword(password);
            user.set('realName', realName);
            user.set('phone', phone);
            user.set('department', department);
            user.set('roles', '[]'); // 默认为普通用户

            const registeredUser = await user.signUp();
            console.log('注册成功:', registeredUser.get('username'));

            return registeredUser;
        } catch (error) {
            console.error('注册失败:', error);
            throw error;
        }
    }

    /**
     * 显示登录表单
     */
    showLoginForm() {
        if (this.elements.loginForm) {
            this.elements.loginForm.classList.remove('hidden');
        }
        if (this.elements.registerForm) {
            this.elements.registerForm.classList.add('hidden');
        }
        if (this.elements.modalTitle) {
            this.elements.modalTitle.textContent = '管理员登录';
        }
        if (this.elements.loginSwitchText) {
            this.elements.loginSwitchText.classList.remove('hidden');
        }
        if (this.elements.registerSwitchText) {
            this.elements.registerSwitchText.classList.add('hidden');
        }
    }

    /**
     * 显示注册表单
     */
    showRegisterForm() {
        if (this.elements.loginForm) {
            this.elements.loginForm.classList.add('hidden');
        }
        if (this.elements.registerForm) {
            this.elements.registerForm.classList.remove('hidden');
        }
        if (this.elements.modalTitle) {
            this.elements.modalTitle.textContent = '用户注册';
        }
        if (this.elements.loginSwitchText) {
            this.elements.loginSwitchText.classList.add('hidden');
        }
        if (this.elements.registerSwitchText) {
            this.elements.registerSwitchText.classList.remove('hidden');
        }
    }

    /**
     * 清空表单
     */
    clearForms() {
        if (this.elements.loginForm) {
            this.elements.loginForm.reset();
        }
        if (this.elements.registerForm) {
            this.elements.registerForm.reset();
        }
    }

    /**
     * 切换标签页
     */
    switchTab(tab) {
        this.currentTab = tab;

        // 更新标签样式
        const tabs = ['usersTab', 'departmentsTab', 'rolesTab', 'ordersTab', 'statsTab'];
        const panels = ['usersPanel', 'departmentsPanel', 'rolesPanel', 'ordersPanel', 'statsPanel'];

        tabs.forEach(tabId => {
            const element = this.elements[tabId];
            if (element) {
                if (tabId === tab + 'Tab') {
                    element.className = 'py-4 px-1 border-b-2 border-purple-500 font-medium text-sm text-purple-600';
                } else {
                    element.className = 'py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700';
                }
            }
        });

        // 显示对应面板
        panels.forEach(panelId => {
            const element = this.elements[panelId];
            if (element) {
                if (panelId === tab + 'Panel') {
                    element.classList.remove('hidden');
                } else {
                    element.classList.add('hidden');
                }
            }
        });

        // 根据标签页加载对应数据
        switch (tab) {
            case 'users':
                this.loadUsers();
                break;
            case 'departments':
                this.loadDepartments();
                break;
            case 'roles':
                this.loadRoles();
                break;
        }
    }

    /**
     * 加载用户列表
     */
    async loadUsers() {
        if (!this.currentUser || !this.isCurrentUserAdmin()) {
            console.log('无权限加载用户列表');
            return;
        }

        try {
            this.showLoading(true);

            // 直接查询用户列表
            const query = new AV.Query(AV.User);
            query.ascending('createdAt');
            query.limit(1000);

            this.users = await query.find();
            console.log(`加载了 ${this.users.length} 个用户`);

            this.updateUserStats();
            this.renderUsers();

        } catch (error) {
            console.error('加载用户列表失败:', error);

            // 如果查询失败，显示权限提示和解决方案
            this.showPermissionError();
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 显示/隐藏加载状态
     */
    showLoading(show) {
        if (this.elements.loadingIndicator) {
            if (show) {
                this.elements.loadingIndicator.classList.remove('hidden');
            } else {
                this.elements.loadingIndicator.classList.add('hidden');
            }
        }
    }

    /**
     * 显示权限错误提示
     */
    showPermissionError() {
        if (!this.elements.usersTableBody) return;

        this.elements.usersTableBody.innerHTML = `
            <tr>
                <td colspan="6" class="px-6 py-12 text-center">
                    <div class="space-y-6">
                        <div class="text-red-500 text-4xl">🔒</div>
                        <h3 class="text-lg font-semibold text-gray-800">用户管理权限受限</h3>
                        <div class="text-gray-600 space-y-2">
                            <p>由于LeanCloud安全限制，无法直接查询所有用户。</p>
                            <p class="text-sm">但您仍可以管理当前登录用户和搜索特定用户。</p>
                        </div>

                        <!-- 当前用户管理 -->
                        <div class="bg-green-50 rounded-lg p-4">
                            <h4 class="font-semibold text-green-800 mb-3">管理当前用户</h4>
                            <button onclick="adminApp.showCurrentUserManagement()"
                                    class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors text-sm font-medium btn-fix">
                                编辑我的信息
                            </button>
                        </div>

                        <!-- 用户搜索 -->
                        <div class="bg-yellow-50 rounded-lg p-4">
                            <h4 class="font-semibold text-yellow-800 mb-3">搜索特定用户</h4>
                            <div class="flex gap-2">
                                <input type="text" id="searchUsername" placeholder="输入用户名"
                                       class="flex-1 px-3 py-2 border border-yellow-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent text-sm">
                                <button onclick="adminApp.searchSpecificUser()"
                                        class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg transition-colors text-sm font-medium btn-fix">
                                    搜索
                                </button>
                            </div>
                        </div>

                        <!-- 配置说明 -->
                        <details class="bg-blue-50 rounded-lg p-4 text-left">
                            <summary class="font-semibold text-blue-800 cursor-pointer">完整用户管理配置说明</summary>
                            <div class="mt-3 space-y-3 text-sm text-blue-700">
                                <div>
                                    <h5 class="font-semibold">方案一：LeanCloud控制台配置</h5>
                                    <ol class="list-decimal list-inside space-y-1 ml-2">
                                        <li>登录LeanCloud控制台</li>
                                        <li>进入数据存储 → _User表</li>
                                        <li>点击"权限"标签</li>
                                        <li>在"find"权限中添加角色或设为公开</li>
                                    </ol>
                                </div>

                            </div>
                        </details>

                        <button onclick="adminApp.loadUsers()"
                                class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors text-sm font-medium btn-fix">
                            重新尝试加载
                        </button>
                    </div>
                </td>
            </tr>
        `;

        // 重置统计数据
        this.updateUserStats([]);
    }

    /**
     * 更新用户统计
     */
    updateUserStats(users = null) {
        const usersToCount = users || this.users || [];
        let adminCount = 0;
        let engineerCount = 0;
        let normalCount = 0;

        usersToCount.forEach(user => {
            const roles = this.parseUserRoles(user.get('roles'));

            if (roles.includes('admin')) {
                adminCount++;
            } else if (roles.includes('engineer')) {
                engineerCount++;
            } else {
                normalCount++;
            }
        });

        if (this.elements.totalUsers) this.elements.totalUsers.textContent = usersToCount.length;
        if (this.elements.adminUsers) this.elements.adminUsers.textContent = adminCount;
        if (this.elements.engineerUsers) this.elements.engineerUsers.textContent = engineerCount;
        if (this.elements.normalUsers) this.elements.normalUsers.textContent = normalCount;
    }

    /**
     * 渲染用户列表
     */
    renderUsers(filteredUsers = null) {
        const usersToRender = filteredUsers || this.users;

        if (!this.elements.usersTableBody) return;

        this.elements.usersTableBody.innerHTML = usersToRender.map(user => {
            const roles = this.parseUserRoles(user.get('roles'));
            const roleText = roles.length > 0 ? roles.join(', ') : '普通用户';
            const roleClass = roles.includes('admin') ? 'text-purple-600' :
                             roles.includes('engineer') ? 'text-green-600' : 'text-gray-600';

            return `
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div>
                            <div class="text-sm font-medium text-gray-900">${user.get('realName') || '未设置'}</div>
                            <div class="text-sm text-gray-500">${user.get('username')}</div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${user.get('phone') || '未设置'}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${user.get('department') || '未设置'}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="text-sm ${roleClass}">${roleText}</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${user.get('createdAt')?.toLocaleDateString('zh-CN') || '未知'}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button onclick="adminApp.editUser('${user.id}')"
                                class="text-purple-600 hover:text-purple-900">编辑</button>
                    </td>
                </tr>
            `;
        }).join('');
    }

    /**
     * 筛选用户
     */
    filterUsers() {
        const searchTerm = this.elements.userSearch?.value?.toLowerCase() || '';
        
        if (!searchTerm) {
            this.renderUsers();
            return;
        }
        
        const filteredUsers = this.users.filter(user => {
            const username = (user.get('username') || '').toLowerCase();
            const realName = (user.get('realName') || '').toLowerCase();
            const phone = (user.get('phone') || '').toLowerCase();
            const department = (user.get('department') || '').toLowerCase();
            
            return username.includes(searchTerm) || 
                   realName.includes(searchTerm) || 
                   phone.includes(searchTerm) || 
                   department.includes(searchTerm);
        });
        
        this.renderUsers(filteredUsers);
    }

    /**
     * 编辑用户
     */
    async editUser(userId) {
        try {
            const user = this.users.find(u => u.id === userId);
            if (!user) {
                WorkLogUtils.showMessage('用户不存在', 'error');
                return;
            }
            
            // 填充表单
            if (this.elements.editUserId) this.elements.editUserId.value = userId;
            if (this.elements.editRealName) this.elements.editRealName.value = user.get('realName') || '';
            if (this.elements.editPhone) this.elements.editPhone.value = user.get('phone') || '';
            if (this.elements.editDepartment) {
                // 动态更新部门选项
                this.updateDepartmentOptions();
                this.elements.editDepartment.value = user.get('department') || '';
            }
            
            // 设置角色选择
            if (this.elements.editRoles) {
                const roles = this.parseUserRoles(user.get('roles'));
                Array.from(this.elements.editRoles.options).forEach(option => {
                    option.selected = roles.includes(option.value);
                });
            }
            
            // 显示弹窗
            this.showUserModal();
            
        } catch (error) {
            console.error('编辑用户失败:', error);
            WorkLogUtils.showMessage('编辑用户失败: ' + error.message, 'error');
        }
    }

    /**
     * 保存用户
     */
    async saveUser(event) {
        event.preventDefault();

        const userId = this.elements.editUserId?.value;
        if (!userId) return;

        try {
            const realName = this.elements.editRealName?.value?.trim();
            const phone = this.elements.editPhone?.value?.trim();
            const department = this.elements.editDepartment?.value;

            // 处理角色
            const selectedRoles = Array.from(this.elements.editRoles?.selectedOptions || [])
                .map(option => option.value);

            // 将角色数组转换为JSON字符串，因为LeanCloud中roles字段是String类型
            const rolesString = JSON.stringify(selectedRoles);

            // 检查是否是当前用户
            const isCurrentUser = userId === this.currentUser?.id;

            if (isCurrentUser) {
                // 如果是当前用户，可以直接更新
                this.currentUser.set('realName', realName);
                this.currentUser.set('phone', phone);
                this.currentUser.set('department', department);
                this.currentUser.set('roles', rolesString);

                await this.currentUser.save();
                WorkLogUtils.showMessage('用户信息保存成功', 'success');
            } else {
                // 如果是其他用户，尝试更新（可能会失败）
                const user = AV.Object.createWithoutData('_User', userId);
                user.set('realName', realName);
                user.set('phone', phone);
                user.set('department', department);
                user.set('roles', rolesString);

                await user.save();
                WorkLogUtils.showMessage('用户信息保存成功', 'success');
            }

            this.hideUserModal();

            // 更新显示的用户信息
            const userIndex = this.users.findIndex(u => u.id === userId);
            if (userIndex !== -1) {
                this.users[userIndex].set('realName', realName);
                this.users[userIndex].set('phone', phone);
                this.users[userIndex].set('department', department);
                this.users[userIndex].set('roles', rolesString); // 保存为字符串格式
                this.updateUserStats(); // 重新计算统计
                this.renderUsers();
            }

        } catch (error) {
            console.error('保存用户失败:', error);
            if (error.code === 403) {
                this.showPermissionError();
            } else {
                WorkLogUtils.showMessage('保存用户失败: ' + error.message, 'error');
            }
        }
    }

    /**
     * 显示用户编辑弹窗
     */
    showUserModal() {
        if (this.elements.userModal) {
            this.elements.userModal.classList.remove('hidden');
        }
    }

    /**
     * 隐藏用户编辑弹窗
     */
    hideUserModal() {
        if (this.elements.userModal) {
            this.elements.userModal.classList.add('hidden');
        }
    }

    /**
     * 显示添加用户弹窗
     */
    showAddUserModal() {
        // 更新部门选项
        this.updateAddUserDepartmentOptions();

        // 清空表单
        if (this.elements.addUserForm) {
            this.elements.addUserForm.reset();
        }

        if (this.elements.addUserModal) {
            this.elements.addUserModal.classList.remove('hidden');
        }
    }

    /**
     * 隐藏添加用户弹窗
     */
    hideAddUserModal() {
        if (this.elements.addUserModal) {
            this.elements.addUserModal.classList.add('hidden');
        }
    }

    /**
     * 处理添加用户
     */
    async handleAddUser() {
        const username = this.elements.addUsername?.value?.trim();
        const realName = this.elements.addRealName?.value?.trim();
        const phone = this.elements.addPhone?.value?.trim();
        const department = this.elements.addDepartment?.value;
        const password = this.elements.addPassword?.value;

        // 获取选中的角色
        const selectedRoles = Array.from(this.elements.addUserRoles?.selectedOptions || [])
            .map(option => option.value);

        // 表单验证
        if (!username || !realName || !phone || !department || !password) {
            WorkLogUtils.showMessage('请填写所有必填字段', 'warning');
            return;
        }

        if (password.length < 6) {
            WorkLogUtils.showMessage('密码长度至少6位', 'warning');
            return;
        }

        // 验证电话号码格式
        if (!/^1[3-9]\d{9}$/.test(phone) && !/^\d{3,4}-?\d{7,8}$/.test(phone)) {
            WorkLogUtils.showMessage('请输入正确的电话号码', 'warning');
            return;
        }

        try {
            // 创建新用户
            const user = new AV.User();
            user.setUsername(username);
            user.setPassword(password);
            user.set('realName', realName);
            user.set('phone', phone);
            user.set('department', department);

            // 将角色数组转换为JSON字符串
            const rolesString = JSON.stringify(selectedRoles);
            user.set('roles', rolesString);

            await user.signUp();

            WorkLogUtils.showMessage('用户添加成功', 'success');
            this.hideAddUserModal();
            this.loadUsers(); // 重新加载用户列表

        } catch (error) {
            console.error('添加用户失败:', error);

            // 处理常见错误
            if (error.code === 202) {
                WorkLogUtils.showMessage('用户名已存在，请使用其他用户名', 'error');
            } else {
                WorkLogUtils.showMessage('添加用户失败: ' + error.message, 'error');
            }
        }
    }

    /**
     * 更新添加用户弹窗的部门选项
     */
    updateAddUserDepartmentOptions() {
        if (!this.elements.addDepartment) return;

        // 清空现有选项
        this.elements.addDepartment.innerHTML = '<option value="">请选择部门</option>';

        // 添加部门选项
        if (this.departments && this.departments.length > 0) {
            this.departments.forEach(dept => {
                const option = document.createElement('option');
                option.value = dept.get('name');
                option.textContent = dept.get('name');
                this.elements.addDepartment.appendChild(option);
            });
        } else {
            // 如果没有部门数据，使用默认选项
            const defaultDepts = ['前厅部', '客房部', '餐饮部', '工程部', '保安部', '财务部', '人事部', '销售部', '其他'];
            defaultDepts.forEach(deptName => {
                const option = document.createElement('option');
                option.value = deptName;
                option.textContent = deptName;
                this.elements.addDepartment.appendChild(option);
            });
        }
    }

    /**
     * 显示权限错误提示
     */
    showPermissionError() {
        const errorHtml = `
            <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">权限不足</h3>
                        <div class="mt-2 text-sm text-red-700">
                            <p>无法修改用户信息，这可能是由于以下原因：</p>
                            <ul class="list-disc list-inside mt-2 space-y-1">
                                <li>LeanCloud安全限制：用户只能修改自己的信息</li>
                                <li>需要在LeanCloud控制台配置admin角色权限</li>
                                <li>当前用户没有足够的权限</li>
                            </ul>
                        </div>
                        <div class="mt-4">
                            <div class="flex space-x-2">
                                <button onclick="window.open('https://console.leancloud.cn/', '_blank')"
                                        class="bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded text-xs">
                                    打开LeanCloud控制台
                                </button>
                                <button onclick="this.closest('.bg-red-50').remove()"
                                        class="bg-gray-100 hover:bg-gray-200 text-gray-800 px-3 py-1 rounded text-xs">
                                    关闭
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 在用户列表前插入错误提示
        const usersPanel = document.querySelector('#usersPanel .bg-white');
        if (usersPanel) {
            usersPanel.insertAdjacentHTML('afterbegin', errorHtml);
        } else {
            WorkLogUtils.showMessage('权限不足：无法修改用户信息，请检查LeanCloud权限配置', 'error');
        }
    }

    /**
     * 更新部门选项
     */
    updateDepartmentOptions() {
        if (!this.elements.editDepartment) return;

        // 保存当前选中的值
        const currentValue = this.elements.editDepartment.value;

        // 清空现有选项
        this.elements.editDepartment.innerHTML = '<option value="">请选择部门</option>';

        // 添加部门选项
        if (this.departments && this.departments.length > 0) {
            this.departments.forEach(dept => {
                const option = document.createElement('option');
                option.value = dept.get('name');
                option.textContent = dept.get('name');
                this.elements.editDepartment.appendChild(option);
            });
        } else {
            // 如果没有部门数据，使用默认选项
            const defaultDepts = ['前厅部', '客房部', '餐饮部', '工程部', '保安部', '财务部', '人事部', '销售部', '其他'];
            defaultDepts.forEach(deptName => {
                const option = document.createElement('option');
                option.value = deptName;
                option.textContent = deptName;
                this.elements.editDepartment.appendChild(option);
            });
        }

        // 恢复之前选中的值
        this.elements.editDepartment.value = currentValue;
    }

    /**
     * 显示当前用户管理
     */
    showCurrentUserManagement() {
        if (!this.currentUser) {
            WorkLogUtils.showMessage('当前用户信息不可用', 'error');
            return;
        }

        // 将当前用户添加到列表中
        this.users = [this.currentUser];
        this.updateUserStats();
        this.renderUsers();

        WorkLogUtils.showMessage('显示当前用户信息', 'success');
    }

    /**
     * 搜索特定用户（简化版）
     */
    async searchSpecificUser() {
        const searchInput = document.getElementById('searchUsername');
        const username = searchInput?.value?.trim();

        if (!username) {
            WorkLogUtils.showMessage('请输入用户名', 'warning');
            return;
        }

        try {
            this.showLoading(true);

            // 尝试通过用户名查询（可能会失败，但值得一试）
            const query = new AV.Query(AV.User);
            query.equalTo('username', username);

            const user = await query.first();

            if (user) {
                this.users = [user];
                this.updateUserStats();
                this.renderUsers();
                WorkLogUtils.showMessage(`找到用户: ${username}`, 'success');
            } else {
                WorkLogUtils.showMessage(`未找到用户: ${username}`, 'warning');
            }

        } catch (error) {
            console.error('搜索用户失败:', error);
            WorkLogUtils.showMessage('搜索失败，可能需要配置权限: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 通过用户名搜索用户（保留原方法兼容性）
     */
    async searchUserByUsername() {
        const searchInput = document.getElementById('searchUsername');
        const username = searchInput?.value?.trim();

        if (!username) {
            WorkLogUtils.showMessage('请输入用户名', 'warning');
            return;
        }

        try {
            this.showLoading(true);

            let userData = null;

            // 直接查询用户
            const query = new AV.Query(AV.User);
            query.equalTo('username', username);
            const user = await query.first();

            if (user) {
                userData = {
                    objectId: user.id,
                    username: user.get('username'),
                    realName: user.get('realName'),
                    phone: user.get('phone'),
                    department: user.get('department'),
                    roles: user.get('roles'),
                    createdAt: user.get('createdAt')
                };
            }

            if (userData) {
                // 创建用户对象
                const user = new AV.User();
                user.id = userData.objectId;
                user.set('username', userData.username);
                user.set('realName', userData.realName);
                user.set('phone', userData.phone);
                user.set('department', userData.department);
                user.set('roles', userData.roles);
                user.set('createdAt', new Date(userData.createdAt));

                this.users = [user];
                this.updateUserStats();
                this.renderUsers();
                WorkLogUtils.showMessage(`找到用户: ${username}`, 'success');
            } else {
                WorkLogUtils.showMessage(`未找到用户: ${username}`, 'warning');
                this.users = [];
                this.updateUserStats();
                this.renderUsers();
            }

        } catch (error) {
            console.error('搜索用户失败:', error);
            WorkLogUtils.showMessage('搜索用户失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }
}

// 导出模块
if (typeof window !== 'undefined') {
    window.AdminApp = AdminApp;
}
