<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>库存管理 - 酒店管理系统</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .module-card {
            transition: transform 0.2s, box-shadow 0.2s;
            border: none;
            border-radius: 12px;
            overflow: hidden;
        }
        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .module-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        .stats-card {
            background: linear-gradient(135deg, #26c6da 0%, #00acc1 100%);
            color: white;
            border-radius: 12px;
        }
        .breadcrumb {
            background: none;
            padding: 0;
        }
    </style>
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.html">
                <i class="fas fa-hotel me-2"></i>酒店管理系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.html">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../work-log/index.html">工作日志</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../repair/index.html">报修管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="index.html">库存管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../admin/index.html">系统管理</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> 用户
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-sign-out-alt me-2"></i>退出</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 面包屑导航 -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="../index.html">首页</a></li>
                <li class="breadcrumb-item active">库存管理</li>
            </ol>
        </nav>

        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-1">库存管理</h1>
                <p class="text-muted mb-0">物品库存、出入库和盘点管理</p>
            </div>
            <button class="btn btn-success">
                <i class="fas fa-plus me-2"></i>新增物品
            </button>
        </div>

        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-boxes fa-2x mb-2"></i>
                        <h4 class="mb-1">1,234</h4>
                        <small>库存物品</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card" style="background: linear-gradient(135deg, #ff7043 0%, #f4511e 100%);">
                    <div class="card-body text-center">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <h4 class="mb-1">23</h4>
                        <small>库存预警</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card" style="background: linear-gradient(135deg, #66bb6a 0%, #43a047 100%);">
                    <div class="card-body text-center">
                        <i class="fas fa-arrow-up fa-2x mb-2"></i>
                        <h4 class="mb-1">156</h4>
                        <small>今日入库</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card" style="background: linear-gradient(135deg, #ef5350 0%, #e53935 100%);">
                    <div class="card-body text-center">
                        <i class="fas fa-arrow-down fa-2x mb-2"></i>
                        <h4 class="mb-1">89</h4>
                        <small>今日出库</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能模块 -->
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card module-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-warehouse module-icon text-primary"></i>
                        <h5 class="card-title">库存查询</h5>
                        <p class="card-text text-muted">查看和管理所有库存物品信息</p>
                        <a href="../inventory/index.html" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-right me-2"></i>进入
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card module-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-plus-square module-icon text-success"></i>
                        <h5 class="card-title">入库管理</h5>
                        <p class="card-text text-muted">物品采购入库和库存增加</p>
                        <a href="../inventory/inbound.html" class="btn btn-outline-success">
                            <i class="fas fa-arrow-right me-2"></i>进入
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card module-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-minus-square module-icon text-danger"></i>
                        <h5 class="card-title">出库管理</h5>
                        <p class="card-text text-muted">物品领用出库和库存减少</p>
                        <a href="../inventory/outbound.html" class="btn btn-outline-danger">
                            <i class="fas fa-arrow-right me-2"></i>进入
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card module-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-exchange-alt module-icon text-warning"></i>
                        <h5 class="card-title">调拨管理</h5>
                        <p class="card-text text-muted">物品在不同部门间的调拨</p>
                        <a href="../inventory/transfer.html" class="btn btn-outline-warning">
                            <i class="fas fa-arrow-right me-2"></i>进入
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card module-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-clipboard-check module-icon text-info"></i>
                        <h5 class="card-title">盘点管理</h5>
                        <p class="card-text text-muted">定期库存盘点和差异处理</p>
                        <a href="../inventory/count.html" class="btn btn-outline-info">
                            <i class="fas fa-arrow-right me-2"></i>进入
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card module-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-bell module-icon text-secondary"></i>
                        <h5 class="card-title">库存预警</h5>
                        <p class="card-text text-muted">低库存和过期物品预警</p>
                        <a href="../inventory/alerts.html" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>进入
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 库存预警 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2 text-warning"></i>库存预警
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>物品名称</th>
                                <th>当前库存</th>
                                <th>最低库存</th>
                                <th>预警类型</th>
                                <th>最后更新</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>客房毛巾</td>
                                <td>15</td>
                                <td>50</td>
                                <td><span class="badge bg-warning">库存不足</span></td>
                                <td>2024-01-15 14:30</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary">补货</button>
                                </td>
                            </tr>
                            <tr>
                                <td>洗发水</td>
                                <td>8</td>
                                <td>20</td>
                                <td><span class="badge bg-danger">严重不足</span></td>
                                <td>2024-01-15 13:45</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary">补货</button>
                                </td>
                            </tr>
                            <tr>
                                <td>打印纸</td>
                                <td>25</td>
                                <td>30</td>
                                <td><span class="badge bg-warning">库存不足</span></td>
                                <td>2024-01-15 12:20</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary">补货</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
</body>
</html>
