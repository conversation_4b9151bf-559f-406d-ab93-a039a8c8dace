<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">权限管理</h1>
          <p class="mt-1 text-sm text-gray-600">
            管理系统角色和权限配置
          </p>
        </div>
      </div>
    </div>

    <!-- 角色权限矩阵 -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">角色权限矩阵</h3>
        <p class="mt-1 text-sm text-gray-600">查看和管理不同角色的权限配置</p>
      </div>
      
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                权限
              </th>
              <th 
                v-for="role in roles" 
                :key="role.key"
                class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                {{ role.name }}
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="(group, groupName) in permissionGroups" :key="groupName">
              <td colspan="100%" class="px-6 py-3 bg-gray-100 text-sm font-medium text-gray-900">
                {{ groupName }}
              </td>
            </tr>
            <tr v-for="permission in group" :key="permission.key" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div>
                  <div class="text-sm font-medium text-gray-900">{{ permission.name }}</div>
                  <div class="text-sm text-gray-500">{{ permission.description }}</div>
                </div>
              </td>
              <td 
                v-for="role in roles" 
                :key="role.key"
                class="px-6 py-4 whitespace-nowrap text-center"
              >
                <span 
                  v-if="hasRolePermission(role.key, permission.key)"
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                >
                  <Icon name="mdi:check" size="12" class="mr-1" />
                  有权限
                </span>
                <span 
                  v-else
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                >
                  <Icon name="mdi:close" size="12" class="mr-1" />
                  无权限
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 权限说明 -->
    <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
      <div class="flex">
        <Icon name="mdi:information" size="20" class="text-blue-600 mr-3 mt-0.5" />
        <div>
          <h3 class="text-sm font-medium text-blue-900">权限说明</h3>
          <div class="mt-2 text-sm text-blue-700">
            <ul class="list-disc list-inside space-y-1">
              <li><strong>管理员</strong>：拥有系统所有权限，可以管理用户、角色和系统设置</li>
              <li><strong>部门经理</strong>：可以管理本部门用户，查看报表和分配工单</li>
              <li><strong>主管</strong>：可以查看用户信息和报表，协助管理工单</li>
              <li><strong>普通员工</strong>：可以创建工作日志和报修工单，查看库存</li>
              <li><strong>工程师</strong>：可以处理报修工单，调整库存，创建工作日志</li>
              <li><strong>客房服务员</strong>：可以创建工作日志和报修工单，查看库存</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PERMISSIONS, ROLE_PERMISSIONS } from '~/composables/usePermissions'

// 页面元数据
definePageMeta({
  title: '权限管理',
  middleware: ['auth', 'permission'],
  permissions: ['system.settings'],
  roles: ['admin']
})

// 角色定义
const roles = [
  { key: 'admin', name: '管理员' },
  { key: 'manager', name: '部门经理' },
  { key: 'supervisor', name: '主管' },
  { key: 'employee', name: '普通员工' },
  { key: 'engineer', name: '工程师' },
  { key: 'housekeeper', name: '客房服务员' }
]

// 权限分组
const permissionGroups = {
  '用户管理': [
    { key: PERMISSIONS.USER_VIEW, name: '查看用户', description: '查看用户列表和基本信息' },
    { key: PERMISSIONS.USER_CREATE, name: '创建用户', description: '创建新用户账号' },
    { key: PERMISSIONS.USER_EDIT, name: '编辑用户', description: '修改用户信息' },
    { key: PERMISSIONS.USER_DELETE, name: '删除用户', description: '删除用户账号' },
    { key: PERMISSIONS.USER_MANAGE, name: '管理用户', description: '完整的用户管理权限' }
  ],
  '工作日志': [
    { key: PERMISSIONS.WORKLOG_VIEW, name: '查看日志', description: '查看自己的工作日志' },
    { key: PERMISSIONS.WORKLOG_VIEW_ALL, name: '查看所有日志', description: '查看所有人的工作日志' },
    { key: PERMISSIONS.WORKLOG_CREATE, name: '创建日志', description: '创建工作日志' },
    { key: PERMISSIONS.WORKLOG_EDIT, name: '编辑日志', description: '编辑工作日志' },
    { key: PERMISSIONS.WORKLOG_DELETE, name: '删除日志', description: '删除工作日志' }
  ],
  '报修管理': [
    { key: PERMISSIONS.REPAIR_VIEW, name: '查看报修', description: '查看报修工单' },
    { key: PERMISSIONS.REPAIR_CREATE, name: '创建报修', description: '提交报修工单' },
    { key: PERMISSIONS.REPAIR_EDIT, name: '编辑报修', description: '编辑报修工单' },
    { key: PERMISSIONS.REPAIR_ASSIGN, name: '分配工单', description: '分配报修工单给工程师' },
    { key: PERMISSIONS.REPAIR_MANAGE, name: '管理报修', description: '完整的报修管理权限' }
  ],
  '库存管理': [
    { key: PERMISSIONS.INVENTORY_VIEW, name: '查看库存', description: '查看库存信息' },
    { key: PERMISSIONS.INVENTORY_CREATE, name: '创建库存', description: '添加新的库存项目' },
    { key: PERMISSIONS.INVENTORY_EDIT, name: '编辑库存', description: '修改库存信息' },
    { key: PERMISSIONS.INVENTORY_ADJUST, name: '调整库存', description: '调整库存数量' },
    { key: PERMISSIONS.INVENTORY_COUNT, name: '库存盘点', description: '执行库存盘点' }
  ],
  '报表管理': [
    { key: PERMISSIONS.REPORT_VIEW, name: '查看报表', description: '查看各类统计报表' },
    { key: PERMISSIONS.REPORT_EXPORT, name: '导出报表', description: '导出报表数据' }
  ],
  '系统管理': [
    { key: PERMISSIONS.SYSTEM_SETTINGS, name: '系统设置', description: '管理系统配置' },
    { key: PERMISSIONS.SYSTEM_LOGS, name: '系统日志', description: '查看系统操作日志' },
    { key: PERMISSIONS.DEPARTMENT_VIEW, name: '查看部门', description: '查看部门信息' },
    { key: PERMISSIONS.DEPARTMENT_MANAGE, name: '管理部门', description: '管理部门信息' },
    { key: PERMISSIONS.ROLE_VIEW, name: '查看角色', description: '查看角色信息' },
    { key: PERMISSIONS.ROLE_MANAGE, name: '管理角色', description: '管理角色权限' }
  ]
}

// 检查角色是否有指定权限
const hasRolePermission = (roleKey: string, permission: string): boolean => {
  const rolePermissions = ROLE_PERMISSIONS[roleKey as keyof typeof ROLE_PERMISSIONS] || []
  return rolePermissions.includes('*') || rolePermissions.includes(permission)
}
</script>
