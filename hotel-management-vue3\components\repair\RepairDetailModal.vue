<template>
  <div 
    v-if="show && repair" 
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    @click="handleBackdropClick"
  >
    <div 
      class="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden"
      @click.stop
    >
      <!-- 头部 -->
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <h3 class="text-lg font-semibold text-gray-900">报修工单详情</h3>
            <span 
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
              :class="getStatusStyle(repair.status)"
            >
              <Icon :name="getStatusIcon(repair.status)" size="12" class="mr-1" />
              {{ getStatusText(repair.status) }}
            </span>
            <span 
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
              :class="getPriorityStyle(repair.priority)"
            >
              <Icon :name="getPriorityIcon(repair.priority)" size="12" class="mr-1" />
              {{ getPriorityText(repair.priority) }}
            </span>
          </div>
          
          <button 
            @click="handleClose"
            class="text-gray-400 hover:text-gray-600"
          >
            <Icon name="mdi:close" size="24" />
          </button>
        </div>
      </div>

      <!-- 内容 -->
      <div class="px-6 py-4 max-h-[calc(90vh-140px)] overflow-y-auto">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <!-- 左侧：基本信息 -->
          <div class="lg:col-span-2 space-y-6">
            <!-- 基本信息卡片 -->
            <div class="bg-gray-50 rounded-lg p-4">
              <h4 class="text-md font-medium text-gray-900 mb-4">基本信息</h4>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-600">工单标题</label>
                  <p class="mt-1 text-sm text-gray-900">{{ repair.title }}</p>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-600">故障位置</label>
                  <p class="mt-1 text-sm text-gray-900">{{ repair.location }}</p>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-600">报修分类</label>
                  <p class="mt-1 text-sm text-gray-900">{{ repair.category }}</p>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-600">创建时间</label>
                  <p class="mt-1 text-sm text-gray-900">{{ formatDateTime(repair.createdAt) }}</p>
                </div>
              </div>
            </div>

            <!-- 问题描述 -->
            <div class="bg-white border border-gray-200 rounded-lg p-4">
              <h4 class="text-md font-medium text-gray-900 mb-3">问题描述</h4>
              <p class="text-sm text-gray-700 whitespace-pre-wrap">{{ repair.description }}</p>
            </div>

            <!-- 现场照片 -->
            <div v-if="repair.images && repair.images.length > 0" class="bg-white border border-gray-200 rounded-lg p-4">
              <h4 class="text-md font-medium text-gray-900 mb-3">现场照片</h4>
              <ImageGallery :images="repair.images" />
            </div>

            <!-- 解决方案 -->
            <div v-if="repair.solution" class="bg-green-50 border border-green-200 rounded-lg p-4">
              <h4 class="text-md font-medium text-gray-900 mb-3 flex items-center">
                <Icon name="mdi:lightbulb" size="16" class="text-green-600 mr-2" />
                解决方案
              </h4>
              <p class="text-sm text-gray-700 whitespace-pre-wrap">{{ repair.solution }}</p>
            </div>

            <!-- 使用材料 -->
            <div v-if="repair.materials && repair.materials.length > 0" class="bg-white border border-gray-200 rounded-lg p-4">
              <h4 class="text-md font-medium text-gray-900 mb-3">使用材料</h4>
              <div class="flex flex-wrap gap-2">
                <span 
                  v-for="material in repair.materials" 
                  :key="material"
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {{ material }}
                </span>
              </div>
            </div>
          </div>

          <!-- 右侧：状态和操作 -->
          <div class="space-y-6">
            <!-- 人员信息 -->
            <div class="bg-white border border-gray-200 rounded-lg p-4">
              <h4 class="text-md font-medium text-gray-900 mb-4">人员信息</h4>
              
              <div class="space-y-4">
                <div v-if="repair.reporter">
                  <label class="block text-sm font-medium text-gray-600">报修人</label>
                  <div class="mt-1 flex items-center">
                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium mr-3">
                      {{ (repair.reporter.realName || repair.reporter.username).charAt(0) }}
                    </div>
                    <div>
                      <p class="text-sm font-medium text-gray-900">{{ repair.reporter.realName || repair.reporter.username }}</p>
                      <p class="text-xs text-gray-500">{{ repair.reporter.department || '未设置部门' }}</p>
                    </div>
                  </div>
                </div>
                
                <div v-if="repair.assignee">
                  <label class="block text-sm font-medium text-gray-600">处理人</label>
                  <div class="mt-1 flex items-center">
                    <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white text-sm font-medium mr-3">
                      {{ (repair.assignee.realName || repair.assignee.username).charAt(0) }}
                    </div>
                    <div>
                      <p class="text-sm font-medium text-gray-900">{{ repair.assignee.realName || repair.assignee.username }}</p>
                      <p class="text-xs text-gray-500">{{ repair.assignee.department || '未设置部门' }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 时间轴 -->
            <div class="bg-white border border-gray-200 rounded-lg p-4">
              <h4 class="text-md font-medium text-gray-900 mb-4">处理进度</h4>
              
              <div class="space-y-4">
                <!-- 创建 -->
                <div class="flex items-start">
                  <div class="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">工单创建</p>
                    <p class="text-xs text-gray-500">{{ formatDateTime(repair.createdAt) }}</p>
                  </div>
                </div>
                
                <!-- 分配 -->
                <div v-if="repair.assignedAt" class="flex items-start">
                  <div class="flex-shrink-0 w-2 h-2 bg-yellow-600 rounded-full mt-2"></div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">工单分配</p>
                    <p class="text-xs text-gray-500">{{ formatDateTime(repair.assignedAt) }}</p>
                  </div>
                </div>
                
                <!-- 开始处理 -->
                <div v-if="repair.startedAt" class="flex items-start">
                  <div class="flex-shrink-0 w-2 h-2 bg-purple-600 rounded-full mt-2"></div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">开始处理</p>
                    <p class="text-xs text-gray-500">{{ formatDateTime(repair.startedAt) }}</p>
                  </div>
                </div>
                
                <!-- 完成 -->
                <div v-if="repair.completedAt" class="flex items-start">
                  <div class="flex-shrink-0 w-2 h-2 bg-green-600 rounded-full mt-2"></div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">处理完成</p>
                    <p class="text-xs text-gray-500">{{ formatDateTime(repair.completedAt) }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 统计信息 -->
            <div class="bg-white border border-gray-200 rounded-lg p-4">
              <h4 class="text-md font-medium text-gray-900 mb-4">统计信息</h4>
              
              <div class="space-y-3">
                <div v-if="repair.estimatedTime" class="flex justify-between">
                  <span class="text-sm text-gray-600">预计时长</span>
                  <span class="text-sm font-medium text-gray-900">{{ repair.estimatedTime }} 小时</span>
                </div>
                
                <div v-if="repair.actualTime" class="flex justify-between">
                  <span class="text-sm text-gray-600">实际时长</span>
                  <span class="text-sm font-medium text-gray-900">{{ repair.actualTime }} 小时</span>
                </div>
                
                <div v-if="repair.cost" class="flex justify-between">
                  <span class="text-sm text-gray-600">维修成本</span>
                  <span class="text-sm font-medium text-gray-900">¥{{ repair.cost.toFixed(2) }}</span>
                </div>
                
                <div v-if="repair.rating" class="flex justify-between">
                  <span class="text-sm text-gray-600">用户评分</span>
                  <div class="flex items-center">
                    <Icon 
                      v-for="i in 5" 
                      :key="i"
                      name="mdi:star" 
                      size="14" 
                      :class="i <= repair.rating ? 'text-yellow-400' : 'text-gray-300'"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- 用户反馈 -->
            <div v-if="repair.feedback" class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 class="text-md font-medium text-gray-900 mb-3 flex items-center">
                <Icon name="mdi:comment-text" size="16" class="text-yellow-600 mr-2" />
                用户反馈
              </h4>
              <p class="text-sm text-gray-700">{{ repair.feedback }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作按钮 -->
      <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-500">
            工单编号：{{ repair.id }}
          </div>
          
          <div class="flex space-x-3">
            <button 
              @click="handleClose"
              class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
            >
              关闭
            </button>
            
            <!-- 根据状态和权限显示操作按钮 -->
            <button 
              v-if="canEdit"
              @click="$emit('edit', repair)"
              class="px-4 py-2 border border-blue-300 text-blue-700 rounded-md hover:bg-blue-50 transition-colors"
            >
              编辑
            </button>
            
            <button 
              v-if="canAssign"
              @click="$emit('assign', repair)"
              class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              分配
            </button>
            
            <button 
              v-if="canStart"
              @click="$emit('start', repair)"
              class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            >
              开始处理
            </button>
            
            <button 
              v-if="canComplete"
              @click="$emit('complete', repair)"
              class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            >
              完成
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Repair } from '~/types'

interface Props {
  show: boolean
  repair?: Repair | null
}

const props = withDefaults(defineProps<Props>(), {
  repair: null
})

const emit = defineEmits<{
  'update:show': [value: boolean]
  edit: [repair: Repair]
  assign: [repair: Repair]
  start: [repair: Repair]
  complete: [repair: Repair]
}>()

const authStore = useAuthStore()

// 计算属性
const canEdit = computed(() => {
  if (!authStore.user || !props.repair) return false
  if (authStore.isAdmin) return true
  return authStore.user.id === props.repair.reporter?.id && 
         ['pending', 'assigned'].includes(props.repair.status)
})

const canAssign = computed(() => {
  if (!authStore.user || !props.repair) return false
  return (authStore.isAdmin || authStore.user.roles.includes('engineer')) &&
         props.repair.status === 'pending'
})

const canStart = computed(() => {
  if (!authStore.user || !props.repair) return false
  return authStore.user.id === props.repair.assignee?.id &&
         props.repair.status === 'assigned'
})

const canComplete = computed(() => {
  if (!authStore.user || !props.repair) return false
  return authStore.user.id === props.repair.assignee?.id &&
         props.repair.status === 'in_progress'
})

// 方法
const handleClose = () => {
  emit('update:show', false)
}

const handleBackdropClick = () => {
  handleClose()
}

const getStatusStyle = (status: string) => {
  const styles = {
    'pending': 'bg-yellow-100 text-yellow-800',
    'assigned': 'bg-blue-100 text-blue-800',
    'in_progress': 'bg-purple-100 text-purple-800',
    'completed': 'bg-green-100 text-green-800',
    'cancelled': 'bg-gray-100 text-gray-800',
    'rejected': 'bg-red-100 text-red-800'
  }
  return styles[status as keyof typeof styles] || styles.pending
}

const getStatusIcon = (status: string) => {
  const icons = {
    'pending': 'mdi:clock-outline',
    'assigned': 'mdi:account-check',
    'in_progress': 'mdi:progress-wrench',
    'completed': 'mdi:check-circle',
    'cancelled': 'mdi:close-circle',
    'rejected': 'mdi:close-circle-outline'
  }
  return icons[status as keyof typeof icons] || icons.pending
}

const getStatusText = (status: string) => {
  const texts = {
    'pending': '待处理',
    'assigned': '已分配',
    'in_progress': '处理中',
    'completed': '已完成',
    'cancelled': '已取消',
    'rejected': '已拒绝'
  }
  return texts[status as keyof typeof texts] || '待处理'
}

const getPriorityStyle = (priority: string) => {
  const styles = {
    'low': 'bg-gray-100 text-gray-800',
    'medium': 'bg-yellow-100 text-yellow-800',
    'high': 'bg-orange-100 text-orange-800',
    'urgent': 'bg-red-100 text-red-800'
  }
  return styles[priority as keyof typeof styles] || styles.medium
}

const getPriorityIcon = (priority: string) => {
  const icons = {
    'low': 'mdi:arrow-down',
    'medium': 'mdi:minus',
    'high': 'mdi:arrow-up',
    'urgent': 'mdi:alert'
  }
  return icons[priority as keyof typeof icons] || icons.medium
}

const getPriorityText = (priority: string) => {
  const texts = {
    'low': '低优先级',
    'medium': '中优先级',
    'high': '高优先级',
    'urgent': '紧急'
  }
  return texts[priority as keyof typeof texts] || '中优先级'
}

const formatDateTime = (date: Date) => {
  return new Date(date).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.show) {
    handleClose()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>
