<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-4 overflow-hidden hover:shadow-md transition-shadow">
    <!-- 头部信息 -->
    <div class="px-4 py-3 border-b border-gray-100 flex justify-between items-center">
      <div class="flex items-center space-x-3">
        <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium flex-shrink-0">
          {{ log.user?.realName?.charAt(0) || log.user?.username?.charAt(0) || 'U' }}
        </div>
        <div>
          <div class="font-medium text-gray-900 text-sm">
            {{ log.user?.realName || log.user?.username || '未知用户' }}
          </div>
          <div class="text-xs text-gray-500">
            {{ log.user?.department || '未设置部门' }}
          </div>
        </div>
      </div>
      
      <div class="flex items-center space-x-2">
        <span 
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
          :class="getPageTypeStyle(log.pageType)"
        >
          {{ getPageTypeName(log.pageType) }}
        </span>
        
        <div v-if="canDelete" class="relative" ref="dropdownRef">
          <button 
            @click="dropdownOpen = !dropdownOpen"
            class="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100"
          >
            <Icon name="mdi:dots-vertical" size="16" />
          </button>
          
          <!-- 下拉菜单 -->
          <div 
            v-if="dropdownOpen"
            class="absolute right-0 mt-1 w-32 bg-white rounded-md shadow-lg py-1 z-10 border border-gray-200"
          >
            <button 
              @click="handleEdit"
              class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            >
              <Icon name="mdi:pencil" size="14" class="inline mr-2" />
              编辑
            </button>
            <button 
              @click="handleDelete"
              class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50"
            >
              <Icon name="mdi:delete" size="14" class="inline mr-2" />
              删除
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="px-4 py-3">
      <!-- 文本内容 -->
      <div class="text-gray-800 text-sm leading-relaxed mb-3 whitespace-pre-wrap">
        {{ log.content }}
      </div>

      <!-- 图片展示 -->
      <div v-if="log.images && log.images.length > 0" class="mb-3">
        <ImageGallery :images="log.images" />
      </div>

      <!-- 底部时间 -->
      <div class="flex justify-between items-center text-xs text-gray-500">
        <span>{{ formatTime(log.createdAt) }}</span>
        <span v-if="log.updatedAt && log.updatedAt !== log.createdAt">
          编辑于 {{ formatTime(log.updatedAt) }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { WorkLog } from '~/types'

interface Props {
  log: WorkLog
}

const props = defineProps<Props>()
const emit = defineEmits<{
  delete: [id: string]
  edit: [log: WorkLog]
}>()

const authStore = useAuthStore()
const dropdownOpen = ref(false)
const dropdownRef = ref()

// 点击外部关闭下拉菜单
onClickOutside(dropdownRef, () => {
  dropdownOpen.value = false
})

// 计算属性
const canDelete = computed(() => {
  if (!authStore.isLoggedIn) return false
  if (authStore.isAdmin) return true
  return authStore.user?.id === props.log.user?.id
})

// 方法
const getPageTypeName = (pageType: string) => {
  const typeMap: Record<string, string> = {
    'main': '工作日志',
    'powerstation': '变电站',
    'waterfilter': '净水器',
    'aircondition': '空调',
    'construction': '施工登记'
  }
  return typeMap[pageType] || pageType
}

const getPageTypeStyle = (pageType: string) => {
  const styleMap: Record<string, string> = {
    'main': 'bg-blue-100 text-blue-800',
    'powerstation': 'bg-yellow-100 text-yellow-800',
    'waterfilter': 'bg-cyan-100 text-cyan-800',
    'aircondition': 'bg-green-100 text-green-800',
    'construction': 'bg-red-100 text-red-800'
  }
  return styleMap[pageType] || 'bg-gray-100 text-gray-800'
}

const formatTime = (date: Date) => {
  const now = new Date()
  const logDate = new Date(date)
  const diffInSeconds = Math.floor((now.getTime() - logDate.getTime()) / 1000)
  
  if (diffInSeconds < 60) {
    return '刚刚'
  } else if (diffInSeconds < 3600) {
    return `${Math.floor(diffInSeconds / 60)}分钟前`
  } else if (diffInSeconds < 86400) {
    return `${Math.floor(diffInSeconds / 3600)}小时前`
  } else if (diffInSeconds < 604800) {
    return `${Math.floor(diffInSeconds / 86400)}天前`
  } else {
    return logDate.toLocaleDateString('zh-CN')
  }
}

const handleEdit = () => {
  dropdownOpen.value = false
  emit('edit', props.log)
}

const handleDelete = () => {
  dropdownOpen.value = false
  
  if (confirm('确定要删除这条工作日志吗？此操作不可恢复。')) {
    emit('delete', props.log.id)
  }
}
</script>

<style scoped>
.whitespace-pre-wrap {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
