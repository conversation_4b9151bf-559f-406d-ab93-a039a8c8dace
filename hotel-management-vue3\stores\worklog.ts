// stores/worklog.ts
import { defineStore } from 'pinia'
import type { WorkLog, PaginationOptions, FilterOptions } from '~/types'

interface WorkLogState {
  logs: WorkLog[]
  loading: boolean
  currentPage: number
  totalPages: number
  filters: FilterOptions
}

export const useWorkLogStore = defineStore('worklog', {
  state: (): WorkLogState => ({
    logs: [],
    loading: false,
    currentPage: 0,
    totalPages: 0,
    filters: {}
  }),

  getters: {
    filteredLogs: (state) => {
      let filtered = [...state.logs]
      
      // 按页面类型过滤
      if (state.filters.pageType) {
        filtered = filtered.filter(log => log.pageType === state.filters.pageType)
      }
      
      // 按用户过滤
      if (state.filters.userId) {
        filtered = filtered.filter(log => log.user?.id === state.filters.userId)
      }
      
      // 按日期范围过滤
      if (state.filters.startDate) {
        filtered = filtered.filter(log => new Date(log.createdAt) >= state.filters.startDate!)
      }
      
      if (state.filters.endDate) {
        filtered = filtered.filter(log => new Date(log.createdAt) <= state.filters.endDate!)
      }
      
      return filtered
    },

    logsByPageType: (state) => {
      const grouped: Record<string, WorkLog[]> = {}
      
      state.logs.forEach(log => {
        if (!grouped[log.pageType]) {
          grouped[log.pageType] = []
        }
        grouped[log.pageType].push(log)
      })
      
      return grouped
    },

    recentLogs: (state) => {
      return state.logs.slice(0, 5)
    }
  },

  actions: {
    // 获取日志列表
    async fetchLogs(options: PaginationOptions & FilterOptions = {}) {
      this.loading = true
      
      try {
        const { workLog } = useLeanCloud()
        const result = await workLog.getList({
          pageType: options.pageType,
          page: options.page || 0,
          limit: options.limit || 20,
          userId: options.userId
        })
        
        if (result.success && result.data) {
          if (options.page === 0) {
            // 第一页，替换数据
            this.logs = result.data
          } else {
            // 后续页，追加数据
            this.logs.push(...result.data)
          }
          
          this.currentPage = options.page || 0
        }
        
        return result
      } catch (error) {
        console.error('获取日志失败:', error)
        return { success: false, error: '获取日志失败' }
      } finally {
        this.loading = false
      }
    },

    // 创建日志
    async createLog(logData: {
      content: string
      images?: string[]
      pageType: string
    }) {
      try {
        const { workLog } = useLeanCloud()
        const result = await workLog.create(logData)

        if (result.success && result.data) {
          // 将新日志添加到列表开头
          this.logs.unshift(result.data)

          // 发送企业微信通知（如果启用）
          await this.sendWeChatNotification(result.data)
        }

        return result
      } catch (error) {
        console.error('创建日志失败:', error)
        return { success: false, error: '创建日志失败' }
      }
    },

    // 删除日志
    async deleteLog(logId: string) {
      try {
        const { workLog } = useLeanCloud()
        const result = await workLog.delete(logId)
        
        if (result.success) {
          // 从列表中移除
          const index = this.logs.findIndex(log => log.id === logId)
          if (index > -1) {
            this.logs.splice(index, 1)
          }
        }
        
        return result
      } catch (error) {
        console.error('删除日志失败:', error)
        return { success: false, error: '删除日志失败' }
      }
    },

    // 设置过滤条件
    setFilters(filters: FilterOptions) {
      this.filters = { ...this.filters, ...filters }
    },

    // 清除过滤条件
    clearFilters() {
      this.filters = {}
    },

    // 重置状态
    reset() {
      this.logs = []
      this.loading = false
      this.currentPage = 0
      this.totalPages = 0
      this.filters = {}
    },

    // 获取统计数据
    async getStats() {
      try {
        const { workLog } = useLeanCloud()
        
        // 获取总数
        const totalResult = await workLog.getList({ limit: 1000 })
        
        if (totalResult.success && totalResult.data) {
          const logs = totalResult.data
          const now = new Date()
          
          // 今日统计
          const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
          const todayLogs = logs.filter(log => new Date(log.createdAt) >= today)
          
          // 本周统计
          const weekStart = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay())
          const weekLogs = logs.filter(log => new Date(log.createdAt) >= weekStart)
          
          // 本月统计
          const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)
          const monthLogs = logs.filter(log => new Date(log.createdAt) >= monthStart)
          
          return {
            success: true,
            data: {
              total: logs.length,
              today: todayLogs.length,
              week: weekLogs.length,
              month: monthLogs.length
            }
          }
        }
        
        return { success: false, error: '获取统计失败' }
      } catch (error) {
        console.error('获取统计失败:', error)
        return { success: false, error: '获取统计失败' }
      }
    },

    // 发送企业微信通知
    async sendWeChatNotification(log: WorkLog) {
      try {
        const authStore = useAuthStore()
        const { sendWorkLogNotification } = useWechat()

        if (!authStore.user) return

        // 只有在特定类型的日志才发送通知
        const notifyTypes = ['powerstation', 'construction']
        if (!notifyTypes.includes(log.pageType)) return

        await sendWorkLogNotification({
          userName: authStore.user.realName || authStore.user.username,
          userDepartment: authStore.user.department || '未设置部门',
          pageType: log.pageType,
          content: log.content,
          createdAt: log.createdAt
        })
      } catch (error) {
        console.error('发送企业微信通知失败:', error)
        // 通知失败不影响日志创建，只记录错误
      }
    }
  }
})
