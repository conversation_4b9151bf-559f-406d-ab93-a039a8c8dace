<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报修管理 - 酒店管理系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <style>
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .btn-fix {
            touch-action: manipulation;
        }
        .module-card {
            transition: all 0.3s ease;
        }
        .module-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="../index.html" class="text-blue-600 hover:text-blue-800 mr-4">
                        ← 返回主页
                    </a>
                    <h1 class="text-xl font-semibold text-gray-900">报修管理</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- 用户信息 -->
                    <div id="userInfo" class="flex items-center space-x-2" style="display: none;">
                        <span class="text-sm text-gray-700">用户：</span>
                        <span id="realName" class="text-sm font-medium text-gray-900"></span>
                        <button id="logoutBtn" class="text-sm text-red-600 hover:text-red-800 btn-fix">退出</button>
                    </div>
                    <button id="loginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                        登录
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 权限提示 -->
    <div id="accessDenied" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8" style="display: none;">
        <div class="bg-white rounded-lg shadow p-8 text-center">
            <div class="mb-4">
                <svg class="mx-auto h-12 w-12 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">访问受限</h3>
            <p class="text-gray-500 mb-4">您需要登录才能访问报修管理功能</p>
            <button id="loginPromptBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg btn-fix">
                立即登录
            </button>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div id="repairSection" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8" style="display: none;">
        <!-- 页面标题 -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">报修管理</h2>
                <p class="text-gray-500 mt-1">设备故障报修和维修工单管理</p>
            </div>
            <a href="repair.html" class="bg-red-500 hover:bg-red-600 text-white px-6 py-2 rounded-lg btn-fix">
                新建报修
            </a>
        </div>

        <!-- 报修功能 -->
        <div class="mb-8">
            <h3 class="text-lg font-medium text-gray-900 mb-4">报修功能</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- 提交报修 -->
                <a href="repair.html" class="module-card block bg-white rounded-lg shadow p-6 hover:shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </div>
                        <h4 class="ml-4 text-lg font-medium text-gray-900">提交报修</h4>
                    </div>
                    <p class="text-gray-500 text-sm">提交设备故障报修申请，快速响应维修需求</p>
                </a>

                <!-- 工单管理 -->
                <a href="repair-manage.html" class="module-card block bg-white rounded-lg shadow p-6 hover:shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                            </svg>
                        </div>
                        <h4 class="ml-4 text-lg font-medium text-gray-900">工单管理</h4>
                    </div>
                    <p class="text-gray-500 text-sm">查看和管理维修工单，跟踪维修进度</p>
                </a>

                <!-- 报修查询 -->
                <a href="repair-query.html" class="module-card block bg-white rounded-lg shadow p-6 hover:shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <h4 class="ml-4 text-lg font-medium text-gray-900">报修查询</h4>
                    </div>
                    <p class="text-gray-500 text-sm">查询报修记录和维修历史</p>
                </a>

                <!-- 维修统计 -->
                <div class="module-card block bg-white rounded-lg shadow p-6 hover:shadow-lg opacity-50 cursor-not-allowed">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                        <h4 class="ml-4 text-lg font-medium text-gray-900">维修统计</h4>
                    </div>
                    <p class="text-gray-500 text-sm">查看维修数据统计和分析报告（开发中）</p>
                </div>
            </div>
        </div>

    </div>

    <!-- 登录弹窗 -->
    <div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-40" style="display: none;">
        <div class="bg-white rounded-lg p-6 w-96 max-w-md mx-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">用户登录</h3>
            <form id="loginForm">
                <div class="mb-4">
                    <label for="loginUsername" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                    <input type="text" id="loginUsername" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                <div class="mb-6">
                    <label for="loginPassword" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                    <input type="password" id="loginPassword" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" id="cancelLoginBtn" class="px-4 py-2 text-gray-600 hover:text-gray-800">取消</button>
                    <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-md">登录</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 引入模块化JS文件 -->
    <script src="../js/config.js"></script>
    <script>
        // 报修管理应用类
        class RepairApp {
            constructor() {
                this.elements = {};
            }

            init() {
                this.initElements();
                this.bindEvents();
                this.checkAccess();
            }

            initElements() {
                this.elements = {
                    accessDenied: document.getElementById('accessDenied'),
                    repairSection: document.getElementById('repairSection'),
                    realName: document.getElementById('realName'),
                    userInfo: document.getElementById('userInfo'),
                    loginBtn: document.getElementById('loginBtn'),
                    logoutBtn: document.getElementById('logoutBtn'),
                    loginPromptBtn: document.getElementById('loginPromptBtn'),
                    loginModal: document.getElementById('loginModal'),
                    loginForm: document.getElementById('loginForm'),
                    cancelLoginBtn: document.getElementById('cancelLoginBtn')
                };
            }

            bindEvents() {
                // 登录相关事件
                if (this.elements.loginBtn) {
                    this.elements.loginBtn.addEventListener('click', () => {
                        this.showLoginModal();
                    });
                }

                if (this.elements.loginPromptBtn) {
                    this.elements.loginPromptBtn.addEventListener('click', () => {
                        this.showLoginModal();
                    });
                }

                if (this.elements.logoutBtn) {
                    this.elements.logoutBtn.addEventListener('click', async () => {
                        try {
                            await AV.User.logOut();
                            this.checkAccess();
                        } catch (error) {
                            console.error('退出登录失败:', error);
                        }
                    });
                }

                // 登录表单事件
                this.elements.loginForm.addEventListener('submit', async (e) => {
                    e.preventDefault();
                    await this.handleLogin();
                });

                // 关闭登录弹窗
                document.addEventListener('click', (e) => {
                    if (e.target.id === 'loginModal' || e.target.id === 'cancelLoginBtn') {
                        this.elements.loginModal.style.display = 'none';
                    }
                });
            }

            async checkAccess() {
                const currentUser = AV.User.current();

                if (currentUser) {
                    this.elements.accessDenied.style.display = 'none';
                    this.elements.repairSection.style.display = 'block';

                    // 更新用户信息显示
                    this.elements.realName.textContent = currentUser.get('realName') || currentUser.get('username');
                    this.elements.userInfo.style.display = 'flex';
                    this.elements.loginBtn.style.display = 'none';
                } else {
                    // 用户未登录
                    this.elements.accessDenied.style.display = 'block';
                    this.elements.repairSection.style.display = 'none';
                    this.elements.userInfo.style.display = 'none';
                    this.elements.loginBtn.style.display = 'block';
                }
            }

            showLoginModal() {
                this.elements.loginModal.style.display = 'flex';
            }

            async handleLogin() {
                const username = document.getElementById('loginUsername').value;
                const password = document.getElementById('loginPassword').value;

                try {
                    await AV.User.logIn(username, password);
                    this.elements.loginModal.style.display = 'none';
                    this.checkAccess();
                    alert('登录成功');
                } catch (error) {
                    console.error('登录失败:', error);
                    alert('登录失败: ' + error.message);
                }
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            function initApp() {
                if (typeof AV !== 'undefined') {
                    const repairApp = new RepairApp();
                    repairApp.init();
                } else {
                    setTimeout(initApp, 100);
                }
            }
            initApp();
        });
    </script>
</body>
</html>
