<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报修管理 - 酒店管理系统</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .module-card {
            transition: transform 0.2s, box-shadow 0.2s;
            border: none;
            border-radius: 12px;
            overflow: hidden;
        }
        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .module-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        .stats-card {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border-radius: 12px;
        }
        .breadcrumb {
            background: none;
            padding: 0;
        }
    </style>
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.html">
                <i class="fas fa-hotel me-2"></i>酒店管理系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.html">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../work-log/index.html">工作日志</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="index.html">报修管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../inventory-management/index.html">库存管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../admin/index.html">系统管理</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> 用户
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-sign-out-alt me-2"></i>退出</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 面包屑导航 -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="../index.html">首页</a></li>
                <li class="breadcrumb-item active">报修管理</li>
            </ol>
        </nav>

        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-1">报修管理</h1>
                <p class="text-muted mb-0">设备故障报修和维修工单管理</p>
            </div>
            <button class="btn btn-danger">
                <i class="fas fa-plus me-2"></i>新建报修
            </button>
        </div>

        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <h4 class="mb-1">23</h4>
                        <small>待处理</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card" style="background: linear-gradient(135deg, #ffa726 0%, #ff7043 100%);">
                    <div class="card-body text-center">
                        <i class="fas fa-tools fa-2x mb-2"></i>
                        <h4 class="mb-1">15</h4>
                        <small>维修中</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card" style="background: linear-gradient(135deg, #66bb6a 0%, #43a047 100%);">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <h4 class="mb-1">89</h4>
                        <small>已完成</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card" style="background: linear-gradient(135deg, #42a5f5 0%, #1e88e5 100%);">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-day fa-2x mb-2"></i>
                        <h4 class="mb-1">12</h4>
                        <small>今日新增</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能模块 -->
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card module-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-plus-circle module-icon text-danger"></i>
                        <h5 class="card-title">提交报修</h5>
                        <p class="card-text text-muted">提交设备故障报修申请，快速响应维修需求</p>
                        <a href="repair.html" class="btn btn-outline-danger">
                            <i class="fas fa-arrow-right me-2"></i>立即报修
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 mb-4">
                <div class="card module-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-tasks module-icon text-warning"></i>
                        <h5 class="card-title">工单管理</h5>
                        <p class="card-text text-muted">查看和管理维修工单，跟踪维修进度</p>
                        <a href="repair-manage.html" class="btn btn-outline-warning">
                            <i class="fas fa-arrow-right me-2"></i>管理工单
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 mb-4">
                <div class="card module-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-search module-icon text-info"></i>
                        <h5 class="card-title">报修查询</h5>
                        <p class="card-text text-muted">查询报修记录和维修历史</p>
                        <a href="repair-query.html" class="btn btn-outline-info">
                            <i class="fas fa-arrow-right me-2"></i>查询记录
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 mb-4">
                <div class="card module-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-bar module-icon text-success"></i>
                        <h5 class="card-title">维修统计</h5>
                        <p class="card-text text-muted">查看维修数据统计和分析报告</p>
                        <a href="#" class="btn btn-outline-success">
                            <i class="fas fa-arrow-right me-2"></i>查看统计
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最近报修 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>最近报修
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>工单号</th>
                                <th>设备位置</th>
                                <th>故障描述</th>
                                <th>状态</th>
                                <th>提交时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>#R2024001</td>
                                <td>客房301空调</td>
                                <td>制冷效果差</td>
                                <td><span class="badge bg-warning">待处理</span></td>
                                <td>2024-01-15 14:30</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary">查看</button>
                                </td>
                            </tr>
                            <tr>
                                <td>#R2024002</td>
                                <td>大堂电梯</td>
                                <td>按钮失灵</td>
                                <td><span class="badge bg-danger">紧急</span></td>
                                <td>2024-01-15 13:45</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary">查看</button>
                                </td>
                            </tr>
                            <tr>
                                <td>#R2024003</td>
                                <td>餐厅照明</td>
                                <td>灯泡烧坏</td>
                                <td><span class="badge bg-info">维修中</span></td>
                                <td>2024-01-15 12:20</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary">查看</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
</body>
</html>
