import AV from 'leancloud-storage'

export default defineNuxtPlugin(() => {
  const config = useRuntimeConfig()

  // 检查配置是否存在
  if (!config.public.leancloudAppId || !config.public.leancloudAppKey || !config.public.leancloudServerUrl) {
    console.error('LeanCloud配置缺失:', {
      appId: config.public.leancloudAppId ? '已配置' : '缺失',
      appKey: config.public.leancloudAppKey ? '已配置' : '缺失',
      serverUrl: config.public.leancloudServerUrl ? '已配置' : '缺失'
    })
    throw new Error('LeanCloud配置缺失，请检查环境变量')
  }

  try {
    AV.init({
      appId: config.public.leancloudAppId,
      appKey: config.public.leancloudAppKey,
      serverURL: config.public.leancloudServerUrl
    })

    console.log('LeanCloud初始化成功:', {
      appId: config.public.leancloudAppId,
      serverUrl: config.public.leancloudServerUrl
    })
  } catch (error) {
    console.error('LeanCloud初始化失败:', error)
    throw error
  }

  return {
    provide: {
      AV
    }
  }
})
