<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>酒店管理系统 - 首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <style>
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .btn-fix {
            touch-action: manipulation;
        }
        .module-card {
            transition: all 0.3s ease;
        }
        .module-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-900">酒店管理系统</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- 用户信息 -->
                    <div id="userInfo" class="flex items-center space-x-2" style="display: none;">
                        <span class="text-sm text-gray-700">用户：</span>
                        <span id="realName" class="text-sm font-medium text-gray-900"></span>
                        <button id="logoutBtn" class="text-sm text-red-600 hover:text-red-800 btn-fix">退出</button>
                    </div>
                    <button id="loginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                        登录
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 欢迎区域 -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl font-bold mb-4">欢迎使用酒店管理系统</h1>
            <p class="text-xl opacity-90 mb-6">高效管理，智能运营，提升酒店服务品质</p>
            <div class="flex justify-center space-x-4">
                <span class="bg-white bg-opacity-20 px-4 py-2 rounded-full text-sm">
                    <i class="fas fa-calendar-day mr-2"></i>
                    <span id="currentDate"></span>
                </span>
                <span class="bg-white bg-opacity-20 px-4 py-2 rounded-full text-sm">
                    <i class="fas fa-clock mr-2"></i>
                    <span id="currentTime"></span>
                </span>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 功能模块 -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">功能模块</h2>
                <!-- 工作日志 -->
                <a href="work-log/index.html" class="module-card block bg-white rounded-lg shadow p-6 hover:shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <h3 class="ml-4 text-lg font-medium text-gray-900">工作日志</h3>
                    </div>
                    <p class="text-gray-500 text-sm">记录日常工作内容，包括设备维护、客房服务等各类工作日志</p>
                </a>

                <!-- 工单管理 -->
                <a href="workorder/index.html" class="module-card block bg-white rounded-lg shadow p-6 hover:shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                            </svg>
                        </div>
                        <h3 class="ml-4 text-lg font-medium text-gray-900">工单管理</h3>
                    </div>
                    <p class="text-gray-500 text-sm">统一管理各类工作任务、服务请求、维修工单等</p>
                </a>

                <!-- 库存管理 -->
                <a href="inventory/index.html" class="module-card block bg-white rounded-lg shadow p-6 hover:shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                        <h3 class="ml-4 text-lg font-medium text-gray-900">库存管理</h3>
                    </div>
                    <p class="text-gray-500 text-sm">物品库存管理、出入库记录、盘点功能</p>
                </a>
            </div>
        </div>

        <!-- 快捷操作 -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">快捷操作</h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button onclick="location.href='work-log/work-log.html'" class="bg-blue-50 hover:bg-blue-100 text-blue-600 p-4 rounded-lg text-center transition-colors btn-fix">
                    <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    <div class="text-sm font-medium">新建日志</div>
                </button>

                <button onclick="location.href='workorder/workorder.html'" class="bg-red-50 hover:bg-red-100 text-red-600 p-4 rounded-lg text-center transition-colors btn-fix">
                    <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                    <div class="text-sm font-medium">创建工单</div>
                </button>

                <button onclick="location.href='inventory/index.html'" class="bg-green-50 hover:bg-green-100 text-green-600 p-4 rounded-lg text-center transition-colors btn-fix">
                    <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    <div class="text-sm font-medium">库存查询</div>
                </button>

                <button onclick="location.href='tasks/index.html'" class="bg-indigo-50 hover:bg-indigo-100 text-indigo-600 p-4 rounded-lg text-center transition-colors btn-fix">
                    <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                    </svg>
                    <div class="text-sm font-medium">任务管理</div>
                </button>

                <button onclick="location.href='admin/index.html'" class="bg-purple-50 hover:bg-purple-100 text-purple-600 p-4 rounded-lg text-center transition-colors btn-fix">
                    <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <div class="text-sm font-medium">系统管理</div>
                </button>
            </div>
        </div>
    </div>

    <!-- 登录弹窗 -->
    <div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-40" style="display: none;">
        <div class="bg-white rounded-lg p-6 w-96 max-w-md mx-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">用户登录</h3>
            <form id="loginForm">
                <div class="mb-4">
                    <label for="loginUsername" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                    <input type="text" id="loginUsername" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                <div class="mb-6">
                    <label for="loginPassword" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                    <input type="password" id="loginPassword" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" id="cancelLoginBtn" class="px-4 py-2 text-gray-600 hover:text-gray-800">取消</button>
                    <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-md">登录</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 引入模块化JS文件 -->
    <script src="js/config.js"></script>
    <script>
        // 主页应用类
        class HomeApp {
            constructor() {
                this.elements = {};
            }

            init() {
                this.initElements();
                this.bindEvents();
                this.checkLoginStatus();
                this.updateTime();
                setInterval(() => this.updateTime(), 1000);
            }

            initElements() {
                this.elements = {
                    realName: document.getElementById('realName'),
                    userInfo: document.getElementById('userInfo'),
                    loginBtn: document.getElementById('loginBtn'),
                    logoutBtn: document.getElementById('logoutBtn'),
                    loginModal: document.getElementById('loginModal'),
                    loginForm: document.getElementById('loginForm'),
                    cancelLoginBtn: document.getElementById('cancelLoginBtn'),
                    currentDate: document.getElementById('currentDate'),
                    currentTime: document.getElementById('currentTime')
                };
            }

            bindEvents() {
                // 登录按钮事件
                if (this.elements.loginBtn) {
                    this.elements.loginBtn.addEventListener('click', () => {
                        this.showLoginModal();
                    });
                }

                // 退出登录事件
                if (this.elements.logoutBtn) {
                    this.elements.logoutBtn.addEventListener('click', async () => {
                        try {
                            await AV.User.logOut();
                            this.checkLoginStatus();
                        } catch (error) {
                            console.error('退出登录失败:', error);
                        }
                    });
                }

                // 登录表单事件
                this.elements.loginForm.addEventListener('submit', async (e) => {
                    e.preventDefault();
                    await this.handleLogin();
                });

                // 关闭登录弹窗
                document.addEventListener('click', (e) => {
                    if (e.target.id === 'loginModal' || e.target.id === 'cancelLoginBtn') {
                        this.elements.loginModal.style.display = 'none';
                    }
                });
            }

            checkLoginStatus() {
                const currentUser = AV.User.current();

                if (currentUser) {
                    this.elements.realName.textContent = currentUser.get('realName') || currentUser.get('username');
                    this.elements.userInfo.style.display = 'flex';
                    this.elements.loginBtn.style.display = 'none';
                } else {
                    this.elements.userInfo.style.display = 'none';
                    this.elements.loginBtn.style.display = 'block';
                }
            }

            showLoginModal() {
                this.elements.loginModal.style.display = 'flex';
            }

            async handleLogin() {
                const username = document.getElementById('loginUsername').value;
                const password = document.getElementById('loginPassword').value;

                try {
                    await AV.User.logIn(username, password);
                    this.elements.loginModal.style.display = 'none';
                    this.checkLoginStatus();
                    alert('登录成功');
                } catch (error) {
                    console.error('登录失败:', error);
                    alert('登录失败: ' + error.message);
                }
            }

            updateTime() {
                const now = new Date();
                const dateStr = now.toLocaleDateString('zh-CN', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    weekday: 'long'
                });
                const timeStr = now.toLocaleTimeString('zh-CN');

                this.elements.currentDate.textContent = dateStr;
                this.elements.currentTime.textContent = timeStr;
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            function initApp() {
                if (typeof AV !== 'undefined') {
                    const homeApp = new HomeApp();
                    homeApp.init();
                } else {
                    setTimeout(initApp, 100);
                }
            }
            initApp();
        });
    </script>
</body>
</html>
