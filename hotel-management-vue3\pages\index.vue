<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 用户未登录时显示登录提示 -->
    <div v-if="!authStore.isLoggedIn" class="text-center py-12">
      <div class="bg-white rounded-lg shadow-sm p-8 max-w-md mx-auto">
        <Icon name="mdi:account-circle" size="64" class="text-gray-400 mx-auto mb-4" />
        <h2 class="text-xl font-semibold text-gray-800 mb-2">欢迎使用酒店管理系统</h2>
        <p class="text-gray-600 mb-6">请先登录以使用完整功能</p>
        <div class="space-y-3">
          <button
            @click="navigateTo('/auth/login')"
            class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            立即登录
          </button>
          <button
            @click="navigateTo('/auth/register')"
            class="w-full border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 transition-colors"
          >
            注册账号
          </button>
        </div>
      </div>
    </div>

    <!-- 用户已登录时显示主要内容 -->
    <div v-else>
      <!-- 欢迎区域 -->
      <div class="mb-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">
              {{ getGreeting() }}，{{ authStore.user?.name || authStore.user?.username }}！
            </h1>
            <p class="mt-2 text-lg text-gray-600">
              {{ authStore.user?.department || '未设置部门' }} · {{ getCurrentDate() }}
            </p>
          </div>

          <div class="mt-4 sm:mt-0 flex items-center space-x-3">
            <button
              @click="navigateTo('/work-log')"
              class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center"
            >
              <Icon name="mdi:plus" size="16" class="mr-1" />
              写日志
            </button>
            <button
              @click="navigateTo('/repair')"
              class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors flex items-center"
            >
              <Icon name="mdi:wrench" size="16" class="mr-1" />
              报修
            </button>
          </div>
        </div>
      </div>

      <!-- 系统概览统计 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <Icon name="mdi:file-document" size="24" class="text-blue-600 mr-3" />
            <div>
              <p class="text-sm text-gray-600">工作日志</p>
              <p class="text-2xl font-semibold text-gray-900">{{ systemStats.workLogs }}</p>
              <p class="text-xs text-green-600">今日 +{{ systemStats.todayWorkLogs }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <Icon name="mdi:wrench" size="24" class="text-orange-600 mr-3" />
            <div>
              <p class="text-sm text-gray-600">报修工单</p>
              <p class="text-2xl font-semibold text-gray-900">{{ systemStats.repairs }}</p>
              <p class="text-xs text-orange-600">待处理 {{ systemStats.pendingRepairs }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <Icon name="mdi:package-variant" size="24" class="text-purple-600 mr-3" />
            <div>
              <p class="text-sm text-gray-600">库存物品</p>
              <p class="text-2xl font-semibold text-gray-900">{{ systemStats.inventory }}</p>
              <p class="text-xs text-red-600">低库存 {{ systemStats.lowStock }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <Icon name="mdi:account-group" size="24" class="text-green-600 mr-3" />
            <div>
              <p class="text-sm text-gray-600">在线用户</p>
              <p class="text-2xl font-semibold text-gray-900">{{ systemStats.onlineUsers }}</p>
              <p class="text-xs text-green-600">活跃中</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 功能模块导航 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <!-- 工作日志模块 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer" @click="navigateTo('/work-log')">
          <div class="flex items-center mb-4">
            <Icon name="mdi:file-document-edit" size="32" class="text-blue-600 mr-3" />
            <div>
              <h3 class="text-lg font-semibold text-gray-900">工作日志</h3>
              <p class="text-sm text-gray-600">记录和管理日常工作</p>
            </div>
          </div>
          <div class="flex items-center justify-between text-sm">
            <span class="text-gray-500">今日已记录 {{ systemStats.todayWorkLogs }} 条</span>
            <Icon name="mdi:arrow-right" size="16" class="text-gray-400" />
          </div>
        </div>

        <!-- 报修管理模块 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer" @click="navigateTo('/repair')">
          <div class="flex items-center mb-4">
            <Icon name="mdi:wrench" size="32" class="text-orange-600 mr-3" />
            <div>
              <h3 class="text-lg font-semibold text-gray-900">报修管理</h3>
              <p class="text-sm text-gray-600">设备报修和维护</p>
            </div>
          </div>
          <div class="flex items-center justify-between text-sm">
            <span class="text-gray-500">待处理 {{ systemStats.pendingRepairs }} 个工单</span>
            <Icon name="mdi:arrow-right" size="16" class="text-gray-400" />
          </div>
        </div>

        <!-- 库存管理模块 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer" @click="navigateTo('/inventory')">
          <div class="flex items-center mb-4">
            <Icon name="mdi:package-variant" size="32" class="text-purple-600 mr-3" />
            <div>
              <h3 class="text-lg font-semibold text-gray-900">库存管理</h3>
              <p class="text-sm text-gray-600">物品库存和盘点</p>
            </div>
          </div>
          <div class="flex items-center justify-between text-sm">
            <span class="text-gray-500">{{ systemStats.inventory }} 种物品在库</span>
            <Icon name="mdi:arrow-right" size="16" class="text-gray-400" />
          </div>
        </div>

        <!-- 系统管理模块（仅管理员可见） -->
        <div
          v-if="authStore.isAdmin"
          class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
          @click="navigateTo('/admin')"
        >
          <div class="flex items-center mb-4">
            <Icon name="mdi:cog" size="32" class="text-red-600 mr-3" />
            <div>
              <h3 class="text-lg font-semibold text-gray-900">系统管理</h3>
              <p class="text-sm text-gray-600">用户和系统配置</p>
            </div>
          </div>
          <div class="flex items-center justify-between text-sm">
            <span class="text-gray-500">管理员专用功能</span>
            <Icon name="mdi:arrow-right" size="16" class="text-gray-400" />
          </div>
        </div>
      </div>

      <!-- 最近活动和通知 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 最近活动 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">最近活动</h3>
            <button
              @click="navigateTo('/work-log')"
              class="text-blue-600 hover:text-blue-700 text-sm flex items-center"
            >
              查看全部
              <Icon name="mdi:arrow-right" size="14" class="ml-1" />
            </button>
          </div>

          <div v-if="loading" class="space-y-3">
            <div v-for="i in 5" :key="i" class="animate-pulse flex items-center space-x-3">
              <div class="w-8 h-8 bg-gray-200 rounded-full"></div>
              <div class="flex-1">
                <div class="h-4 bg-gray-200 rounded w-3/4 mb-1"></div>
                <div class="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          </div>

          <div v-else-if="recentActivities.length === 0" class="text-center py-8">
            <Icon name="mdi:history" size="48" class="text-gray-400 mx-auto mb-2" />
            <p class="text-gray-500 text-sm">暂无活动记录</p>
          </div>

          <div v-else class="space-y-3">
            <div
              v-for="activity in recentActivities"
              :key="activity.id"
              class="flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors"
            >
              <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                <Icon :name="getActivityIcon(activity.type)" size="16" class="text-blue-600" />
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm text-gray-900">{{ activity.description }}</p>
                <p class="text-xs text-gray-500">{{ formatTime(activity.createdAt) }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 系统通知 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">系统通知</h3>
            <button class="text-blue-600 hover:text-blue-700 text-sm">
              标记已读
            </button>
          </div>

          <div v-if="notifications.length === 0" class="text-center py-8">
            <Icon name="mdi:bell-outline" size="48" class="text-gray-400 mx-auto mb-2" />
            <p class="text-gray-500 text-sm">暂无新通知</p>
          </div>

          <div v-else class="space-y-3">
            <div
              v-for="notification in notifications"
              :key="notification.id"
              class="flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors"
              :class="{ 'bg-blue-50': !notification.read }"
            >
              <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0">
                <Icon name="mdi:bell" size="16" class="text-orange-600" />
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm text-gray-900">{{ notification.title }}</p>
                <p class="text-xs text-gray-600 mt-1">{{ notification.content }}</p>
                <p class="text-xs text-gray-500 mt-1">{{ formatTime(notification.createdAt) }}</p>
              </div>
              <div v-if="!notification.read" class="w-2 h-2 bg-blue-600 rounded-full flex-shrink-0 mt-2"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面元数据
definePageMeta({
  title: '酒店管理系统 - 首页',
  middleware: 'auth'
})

// 状态管理
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)

// 系统统计数据
const systemStats = ref({
  workLogs: 0,
  todayWorkLogs: 0,
  repairs: 0,
  pendingRepairs: 0,
  inventory: 0,
  lowStock: 0,
  onlineUsers: 0
})

// 最近活动数据
const recentActivities = ref([
  {
    id: '1',
    type: 'worklog',
    description: '张三提交了工作日志',
    createdAt: new Date(Date.now() - 5 * 60 * 1000)
  },
  {
    id: '2',
    type: 'repair',
    description: '李四创建了报修工单 #001',
    createdAt: new Date(Date.now() - 15 * 60 * 1000)
  },
  {
    id: '3',
    type: 'inventory',
    description: '王五完成了库存盘点',
    createdAt: new Date(Date.now() - 30 * 60 * 1000)
  },
  {
    id: '4',
    type: 'user',
    description: '赵六登录了系统',
    createdAt: new Date(Date.now() - 45 * 60 * 1000)
  }
])

// 系统通知数据
const notifications = ref([
  {
    id: '1',
    title: '系统维护通知',
    content: '系统将于今晚22:00-24:00进行维护',
    read: false,
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000)
  },
  {
    id: '2',
    title: '新功能上线',
    content: '库存管理模块已更新，新增盘点功能',
    read: true,
    createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000)
  }
])

// 方法
const getGreeting = () => {
  const hour = new Date().getHours()
  if (hour < 6) return '夜深了'
  if (hour < 9) return '早上好'
  if (hour < 12) return '上午好'
  if (hour < 14) return '中午好'
  if (hour < 18) return '下午好'
  if (hour < 22) return '晚上好'
  return '夜深了'
}

const getCurrentDate = () => {
  return new Date().toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
}

const getActivityIcon = (type: string) => {
  const icons = {
    worklog: 'mdi:file-document',
    repair: 'mdi:wrench',
    inventory: 'mdi:package-variant',
    user: 'mdi:account',
    system: 'mdi:cog'
  }
  return icons[type] || 'mdi:information'
}

const formatTime = (date: Date) => {
  const now = new Date()
  const targetDate = new Date(date)
  const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000)

  if (diffInSeconds < 60) {
    return '刚刚'
  } else if (diffInSeconds < 3600) {
    return `${Math.floor(diffInSeconds / 60)}分钟前`
  } else if (diffInSeconds < 86400) {
    return `${Math.floor(diffInSeconds / 3600)}小时前`
  } else {
    return `${Math.floor(diffInSeconds / 86400)}天前`
  }
}

const loadSystemStats = async () => {
  loading.value = true
  try {
    // 模拟加载系统统计数据
    // 在实际应用中，这里应该调用API获取真实数据
    await new Promise(resolve => setTimeout(resolve, 500))

    systemStats.value = {
      workLogs: 156,
      todayWorkLogs: 8,
      repairs: 23,
      pendingRepairs: 5,
      inventory: 89,
      lowStock: 3,
      onlineUsers: 12
    }
  } catch (error) {
    console.error('加载系统统计失败:', error)
  } finally {
    loading.value = false
  }
}

const refreshData = async () => {
  await loadSystemStats()
}



// 生命周期
onMounted(async () => {
  // 登录状态检查已在全局插件中处理
  // 如果已登录，加载数据
  if (authStore.isLoggedIn) {
    await loadSystemStats()
  }
})

// 页面标题
useHead({
  title: '酒店管理系统 - 首页'
})
</script>
