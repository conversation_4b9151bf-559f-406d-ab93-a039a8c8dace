/**
 * 权限检查装饰器
 * 提供方法级别的权限控制
 */

class PermissionDecorator {
    /**
     * 权限检查装饰器
     * @param {string} module - 模块名
     * @param {string} action - 操作名
     * @param {Object} options - 选项
     */
    static requirePermission(module, action, options = {}) {
        const {
            showError = true,
            redirectOnFail = false,
            redirectUrl = '/login.html',
            customErrorMessage = null
        } = options;

        return function(target, propertyName, descriptor) {
            const originalMethod = descriptor.value;

            descriptor.value = async function(...args) {
                try {
                    // 检查权限管理器是否存在
                    if (typeof permissionManager === 'undefined') {
                        throw new Error('权限管理器未初始化');
                    }

                    // 检查用户是否登录
                    if (!permissionManager.isLoggedIn()) {
                        const error = new Error('用户未登录');
                        error.code = 'NOT_LOGGED_IN';
                        throw error;
                    }

                    // 检查权限
                    const hasPermission = await permissionManager.hasPermission(module, action);
                    if (!hasPermission) {
                        const error = new Error(customErrorMessage || `权限不足：需要${module}.${action}权限`);
                        error.code = 'PERMISSION_DENIED';
                        error.module = module;
                        error.action = action;
                        throw error;
                    }

                    // 权限检查通过，执行原方法
                    return await originalMethod.apply(this, args);

                } catch (error) {
                    // 处理权限相关错误
                    if (error.code === 'NOT_LOGGED_IN') {
                        if (showError) {
                            globalErrorHandler.showUserError('请先登录');
                        }
                        if (redirectOnFail) {
                            setTimeout(() => {
                                window.location.href = redirectUrl;
                            }, 1500);
                        }
                    } else if (error.code === 'PERMISSION_DENIED') {
                        if (showError) {
                            globalErrorHandler.showUserError(error.message);
                        }
                        // 记录权限拒绝日志
                        console.warn('Permission denied:', {
                            module,
                            action,
                            user: permissionManager.getCurrentUser()?.get('username'),
                            timestamp: new Date().toISOString()
                        });
                    } else {
                        // 其他错误，重新抛出
                        throw error;
                    }
                }
            };

            return descriptor;
        };
    }

    /**
     * 角色检查装饰器
     * @param {string|Array} roles - 允许的角色
     * @param {Object} options - 选项
     */
    static requireRole(roles, options = {}) {
        const roleArray = Array.isArray(roles) ? roles : [roles];
        const {
            showError = true,
            customErrorMessage = null
        } = options;

        return function(target, propertyName, descriptor) {
            const originalMethod = descriptor.value;

            descriptor.value = async function(...args) {
                try {
                    // 检查权限管理器是否存在
                    if (typeof permissionManager === 'undefined') {
                        throw new Error('权限管理器未初始化');
                    }

                    // 检查用户是否登录
                    if (!permissionManager.isLoggedIn()) {
                        const error = new Error('用户未登录');
                        error.code = 'NOT_LOGGED_IN';
                        throw error;
                    }

                    // 检查角色
                    const userRoles = await permissionManager.getUserRoles();
                    const hasRole = roleArray.some(role => userRoles.includes(role));
                    
                    if (!hasRole) {
                        const error = new Error(
                            customErrorMessage || 
                            `角色权限不足：需要以下角色之一：${roleArray.join(', ')}`
                        );
                        error.code = 'ROLE_DENIED';
                        error.requiredRoles = roleArray;
                        error.userRoles = userRoles;
                        throw error;
                    }

                    // 角色检查通过，执行原方法
                    return await originalMethod.apply(this, args);

                } catch (error) {
                    if (error.code === 'NOT_LOGGED_IN') {
                        if (showError) {
                            globalErrorHandler.showUserError('请先登录');
                        }
                    } else if (error.code === 'ROLE_DENIED') {
                        if (showError) {
                            globalErrorHandler.showUserError(error.message);
                        }
                        // 记录角色拒绝日志
                        console.warn('Role denied:', {
                            requiredRoles: roleArray,
                            userRoles: error.userRoles,
                            user: permissionManager.getCurrentUser()?.get('username'),
                            timestamp: new Date().toISOString()
                        });
                    } else {
                        throw error;
                    }
                }
            };

            return descriptor;
        };
    }

    /**
     * 登录检查装饰器
     * @param {Object} options - 选项
     */
    static requireLogin(options = {}) {
        const {
            showError = true,
            redirectOnFail = true,
            redirectUrl = '/login.html'
        } = options;

        return function(target, propertyName, descriptor) {
            const originalMethod = descriptor.value;

            descriptor.value = async function(...args) {
                try {
                    // 检查权限管理器是否存在
                    if (typeof permissionManager === 'undefined') {
                        throw new Error('权限管理器未初始化');
                    }

                    // 检查用户是否登录
                    if (!permissionManager.isLoggedIn()) {
                        const error = new Error('用户未登录');
                        error.code = 'NOT_LOGGED_IN';
                        throw error;
                    }

                    // 登录检查通过，执行原方法
                    return await originalMethod.apply(this, args);

                } catch (error) {
                    if (error.code === 'NOT_LOGGED_IN') {
                        if (showError) {
                            globalErrorHandler.showUserError('请先登录');
                        }
                        if (redirectOnFail) {
                            setTimeout(() => {
                                window.location.href = redirectUrl;
                            }, 1500);
                        }
                    } else {
                        throw error;
                    }
                }
            };

            return descriptor;
        };
    }

    /**
     * 操作日志装饰器
     * @param {string} operation - 操作描述
     * @param {Object} options - 选项
     */
    static logOperation(operation, options = {}) {
        const {
            logParams = false,
            logResult = false,
            logError = true
        } = options;

        return function(target, propertyName, descriptor) {
            const originalMethod = descriptor.value;

            descriptor.value = async function(...args) {
                const startTime = Date.now();
                const user = permissionManager?.getCurrentUser();
                const username = user?.get('username') || 'anonymous';

                // 记录操作开始
                const logData = {
                    operation,
                    method: propertyName,
                    user: username,
                    timestamp: new Date().toISOString(),
                    startTime
                };

                if (logParams) {
                    logData.params = args;
                }

                console.log('Operation started:', logData);

                try {
                    const result = await originalMethod.apply(this, args);
                    
                    // 记录操作成功
                    const endTime = Date.now();
                    const successLog = {
                        ...logData,
                        status: 'success',
                        duration: endTime - startTime,
                        endTime
                    };

                    if (logResult) {
                        successLog.result = result;
                    }

                    console.log('Operation completed:', successLog);

                    // 可以在这里发送到审计日志服务
                    // this.sendAuditLog(successLog);

                    return result;

                } catch (error) {
                    // 记录操作失败
                    if (logError) {
                        const endTime = Date.now();
                        const errorLog = {
                            ...logData,
                            status: 'error',
                            duration: endTime - startTime,
                            endTime,
                            error: {
                                message: error.message,
                                code: error.code,
                                stack: error.stack
                            }
                        };

                        console.error('Operation failed:', errorLog);

                        // 可以在这里发送到审计日志服务
                        // this.sendAuditLog(errorLog);
                    }

                    throw error;
                }
            };

            return descriptor;
        };
    }

    /**
     * 限流装饰器
     * @param {number} maxCalls - 最大调用次数
     * @param {number} timeWindow - 时间窗口（毫秒）
     * @param {Object} options - 选项
     */
    static rateLimit(maxCalls, timeWindow, options = {}) {
        const {
            keyGenerator = null, // 自定义key生成器
            showError = true
        } = options;

        const callCounts = new Map();

        return function(target, propertyName, descriptor) {
            const originalMethod = descriptor.value;

            descriptor.value = async function(...args) {
                // 生成限流key
                let key = propertyName;
                if (keyGenerator) {
                    key = keyGenerator.call(this, ...args);
                } else if (permissionManager?.isLoggedIn()) {
                    const user = permissionManager.getCurrentUser();
                    key = `${user.get('username')}_${propertyName}`;
                }

                const now = Date.now();
                const windowStart = now - timeWindow;

                // 获取当前时间窗口内的调用记录
                let calls = callCounts.get(key) || [];
                calls = calls.filter(timestamp => timestamp > windowStart);

                // 检查是否超过限制
                if (calls.length >= maxCalls) {
                    const error = new Error(`操作过于频繁，请${Math.ceil(timeWindow / 1000)}秒后再试`);
                    error.code = 'RATE_LIMIT_EXCEEDED';
                    
                    if (showError) {
                        globalErrorHandler.showUserError(error.message);
                    }
                    
                    throw error;
                }

                // 记录本次调用
                calls.push(now);
                callCounts.set(key, calls);

                // 执行原方法
                return await originalMethod.apply(this, args);
            };

            return descriptor;
        };
    }
}

// 导出到全局
if (typeof window !== 'undefined') {
    window.PermissionDecorator = PermissionDecorator;
    
    // 提供简化的装饰器函数
    window.requirePermission = PermissionDecorator.requirePermission;
    window.requireRole = PermissionDecorator.requireRole;
    window.requireLogin = PermissionDecorator.requireLogin;
    window.logOperation = PermissionDecorator.logOperation;
    window.rateLimit = PermissionDecorator.rateLimit;
}

// Node.js环境支持
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PermissionDecorator;
}
