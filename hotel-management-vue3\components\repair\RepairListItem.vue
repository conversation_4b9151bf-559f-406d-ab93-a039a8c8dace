<template>
  <div class="p-6 hover:bg-gray-50 transition-colors">
    <div class="flex items-start space-x-4">
      <!-- 优先级指示器 -->
      <div class="flex-shrink-0 mt-1">
        <div 
          class="w-3 h-3 rounded-full"
          :class="getPriorityColor(repair.priority)"
          :title="getPriorityText(repair.priority)"
        ></div>
      </div>

      <!-- 主要内容 -->
      <div class="flex-1 min-w-0">
        <div class="flex items-start justify-between">
          <!-- 左侧信息 -->
          <div class="flex-1">
            <!-- 标题和状态 -->
            <div class="flex items-center space-x-3 mb-2">
              <h3 class="text-lg font-semibold text-gray-900 cursor-pointer hover:text-blue-600" @click="$emit('view', repair)">
                {{ repair.title }}
              </h3>
              
              <span 
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                :class="getStatusStyle(repair.status)"
              >
                <Icon :name="getStatusIcon(repair.status)" size="12" class="mr-1" />
                {{ getStatusText(repair.status) }}
              </span>
              
              <span 
                class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium"
                :class="getPriorityStyle(repair.priority)"
              >
                {{ getPriorityText(repair.priority) }}
              </span>
            </div>

            <!-- 位置和分类 -->
            <div class="flex items-center space-x-4 mb-2 text-sm text-gray-600">
              <div class="flex items-center">
                <Icon name="mdi:map-marker" size="14" class="mr-1" />
                <span>{{ repair.location }}</span>
              </div>
              
              <div class="flex items-center">
                <Icon name="mdi:tag" size="14" class="mr-1" />
                <span>{{ repair.category }}</span>
              </div>
            </div>

            <!-- 描述 -->
            <p class="text-sm text-gray-600 mb-3 line-clamp-2">{{ repair.description }}</p>

            <!-- 人员和时间信息 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
              <!-- 报修人 -->
              <div v-if="repair.reporter" class="flex items-center">
                <Icon name="mdi:account" size="14" class="text-gray-400 mr-2" />
                <div>
                  <div class="text-gray-600">报修人</div>
                  <div class="font-medium text-gray-900">{{ repair.reporter.realName || repair.reporter.username }}</div>
                </div>
              </div>

              <!-- 处理人 -->
              <div v-if="repair.assignee" class="flex items-center">
                <Icon name="mdi:account-wrench" size="14" class="text-gray-400 mr-2" />
                <div>
                  <div class="text-gray-600">处理人</div>
                  <div class="font-medium text-gray-900">{{ repair.assignee.realName || repair.assignee.username }}</div>
                </div>
              </div>

              <!-- 创建时间 -->
              <div class="flex items-center">
                <Icon name="mdi:clock-outline" size="14" class="text-gray-400 mr-2" />
                <div>
                  <div class="text-gray-600">创建时间</div>
                  <div class="font-medium text-gray-900">{{ formatTime(repair.createdAt) }}</div>
                </div>
              </div>

              <!-- 预计时间或完成时间 -->
              <div v-if="repair.estimatedTime || repair.completedAt" class="flex items-center">
                <Icon :name="repair.completedAt ? 'mdi:check-circle' : 'mdi:timer'" size="14" class="text-gray-400 mr-2" />
                <div>
                  <div class="text-gray-600">{{ repair.completedAt ? '完成时间' : '预计时长' }}</div>
                  <div class="font-medium text-gray-900">
                    {{ repair.completedAt ? formatTime(repair.completedAt) : `${repair.estimatedTime}小时` }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 图片预览 -->
            <div v-if="repair.images && repair.images.length > 0" class="mt-3">
              <div class="flex space-x-2">
                <img 
                  v-for="(image, index) in repair.images.slice(0, 4)" 
                  :key="index"
                  :src="image" 
                  :alt="`图片 ${index + 1}`"
                  class="w-12 h-12 object-cover rounded border border-gray-200 cursor-pointer"
                  @click="$emit('view', repair)"
                />
                <div 
                  v-if="repair.images.length > 4"
                  class="w-12 h-12 bg-gray-100 rounded border border-gray-200 flex items-center justify-center text-xs text-gray-500 cursor-pointer"
                  @click="$emit('view', repair)"
                >
                  +{{ repair.images.length - 4 }}
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧操作按钮 -->
          <div class="flex-shrink-0 ml-4">
            <div class="flex items-center space-x-2">
              <!-- 根据状态显示不同的操作按钮 -->
              <template v-if="repair.status === 'pending'">
                <button 
                  v-if="canAssign"
                  @click="$emit('assign', repair)"
                  class="bg-blue-600 text-white px-3 py-1.5 rounded text-sm hover:bg-blue-700 transition-colors"
                >
                  分配
                </button>
                <button 
                  v-if="canEdit"
                  @click="$emit('edit', repair)"
                  class="border border-gray-300 text-gray-700 px-3 py-1.5 rounded text-sm hover:bg-gray-50 transition-colors"
                >
                  编辑
                </button>
              </template>
              
              <template v-else-if="repair.status === 'assigned'">
                <button 
                  v-if="canStart"
                  @click="$emit('start', repair)"
                  class="bg-green-600 text-white px-3 py-1.5 rounded text-sm hover:bg-green-700 transition-colors"
                >
                  开始处理
                </button>
              </template>
              
              <template v-else-if="repair.status === 'in_progress'">
                <button 
                  v-if="canComplete"
                  @click="$emit('complete', repair)"
                  class="bg-green-600 text-white px-3 py-1.5 rounded text-sm hover:bg-green-700 transition-colors"
                >
                  完成
                </button>
              </template>

              <!-- 查看详情按钮 -->
              <button 
                @click="$emit('view', repair)"
                class="border border-gray-300 text-gray-700 px-3 py-1.5 rounded text-sm hover:bg-gray-50 transition-colors"
              >
                详情
              </button>

              <!-- 更多操作下拉菜单 -->
              <div class="relative" ref="dropdownRef">
                <button 
                  @click="dropdownOpen = !dropdownOpen"
                  class="text-gray-400 hover:text-gray-600 p-1.5 rounded hover:bg-gray-100"
                >
                  <Icon name="mdi:dots-vertical" size="16" />
                </button>
                
                <div 
                  v-if="dropdownOpen"
                  class="absolute right-0 mt-1 w-32 bg-white rounded-md shadow-lg py-1 z-10 border border-gray-200"
                >
                  <button 
                    v-if="canEdit"
                    @click="handleEdit"
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <Icon name="mdi:pencil" size="14" class="inline mr-2" />
                    编辑
                  </button>
                  
                  <button 
                    v-if="canCancel"
                    @click="handleCancel"
                    class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                  >
                    <Icon name="mdi:close" size="14" class="inline mr-2" />
                    取消
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Repair } from '~/types'

interface Props {
  repair: Repair
}

const props = defineProps<Props>()
const emit = defineEmits<{
  view: [repair: Repair]
  edit: [repair: Repair]
  assign: [repair: Repair]
  start: [repair: Repair]
  complete: [repair: Repair]
  cancel: [repair: Repair]
}>()

const authStore = useAuthStore()
const dropdownOpen = ref(false)
const dropdownRef = ref()

// 点击外部关闭下拉菜单
onClickOutside(dropdownRef, () => {
  dropdownOpen.value = false
})

// 计算属性
const canEdit = computed(() => {
  if (!authStore.user) return false
  if (authStore.isAdmin) return true
  return authStore.user.id === props.repair.reporter?.id && 
         ['pending', 'assigned'].includes(props.repair.status)
})

const canAssign = computed(() => {
  if (!authStore.user) return false
  return authStore.isAdmin || authStore.user.roles.includes('engineer')
})

const canStart = computed(() => {
  if (!authStore.user) return false
  return authStore.user.id === props.repair.assignee?.id
})

const canComplete = computed(() => {
  if (!authStore.user) return false
  return authStore.user.id === props.repair.assignee?.id
})

const canCancel = computed(() => {
  if (!authStore.user) return false
  if (authStore.isAdmin) return true
  return authStore.user.id === props.repair.reporter?.id && 
         !['completed', 'cancelled'].includes(props.repair.status)
})

// 方法
const getPriorityColor = (priority: string) => {
  const colors = {
    'low': 'bg-gray-400',
    'medium': 'bg-yellow-400',
    'high': 'bg-orange-400',
    'urgent': 'bg-red-400'
  }
  return colors[priority as keyof typeof colors] || colors.medium
}

const getPriorityStyle = (priority: string) => {
  const styles = {
    'low': 'bg-gray-100 text-gray-800',
    'medium': 'bg-yellow-100 text-yellow-800',
    'high': 'bg-orange-100 text-orange-800',
    'urgent': 'bg-red-100 text-red-800'
  }
  return styles[priority as keyof typeof styles] || styles.medium
}

const getPriorityText = (priority: string) => {
  const texts = {
    'low': '低',
    'medium': '中',
    'high': '高',
    'urgent': '紧急'
  }
  return texts[priority as keyof typeof texts] || '中'
}

const getStatusStyle = (status: string) => {
  const styles = {
    'pending': 'bg-yellow-100 text-yellow-800',
    'assigned': 'bg-blue-100 text-blue-800',
    'in_progress': 'bg-purple-100 text-purple-800',
    'completed': 'bg-green-100 text-green-800',
    'cancelled': 'bg-gray-100 text-gray-800',
    'rejected': 'bg-red-100 text-red-800'
  }
  return styles[status as keyof typeof styles] || styles.pending
}

const getStatusIcon = (status: string) => {
  const icons = {
    'pending': 'mdi:clock-outline',
    'assigned': 'mdi:account-check',
    'in_progress': 'mdi:progress-wrench',
    'completed': 'mdi:check-circle',
    'cancelled': 'mdi:close-circle',
    'rejected': 'mdi:close-circle-outline'
  }
  return icons[status as keyof typeof icons] || icons.pending
}

const getStatusText = (status: string) => {
  const texts = {
    'pending': '待处理',
    'assigned': '已分配',
    'in_progress': '处理中',
    'completed': '已完成',
    'cancelled': '已取消',
    'rejected': '已拒绝'
  }
  return texts[status as keyof typeof texts] || '待处理'
}

const formatTime = (date: Date) => {
  const now = new Date()
  const targetDate = new Date(date)
  const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000)
  
  if (diffInSeconds < 60) {
    return '刚刚'
  } else if (diffInSeconds < 3600) {
    return `${Math.floor(diffInSeconds / 60)}分钟前`
  } else if (diffInSeconds < 86400) {
    return `${Math.floor(diffInSeconds / 3600)}小时前`
  } else if (diffInSeconds < 604800) {
    return `${Math.floor(diffInSeconds / 86400)}天前`
  } else {
    return targetDate.toLocaleDateString('zh-CN')
  }
}

const handleEdit = () => {
  dropdownOpen.value = false
  emit('edit', props.repair)
}

const handleCancel = () => {
  dropdownOpen.value = false
  emit('cancel', props.repair)
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
