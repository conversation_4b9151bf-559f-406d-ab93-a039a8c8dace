<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>盘点分析报告 - 酒店管理系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .btn-fix {
            touch-action: manipulation;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="inventory-count.html" class="text-indigo-600 hover:text-indigo-800 mr-4">
                        ← 返回盘点管理
                    </a>
                    <h1 class="text-xl font-semibold text-gray-900">盘点分析报告</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="exportReport" class="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                        导出报告
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 筛选条件 -->
        <div class="bg-white rounded-lg shadow mb-6 p-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">时间范围</label>
                    <select id="timeRange" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm">
                        <option value="7">最近7天</option>
                        <option value="30" selected>最近30天</option>
                        <option value="90">最近90天</option>
                        <option value="custom">自定义</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">仓库</label>
                    <select id="warehouseFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm">
                        <option value="">全部仓库</option>
                        <!-- 仓库选项将动态加载 -->
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">盘点类型</label>
                    <select id="typeFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm">
                        <option value="">全部类型</option>
                        <option value="full">全盘</option>
                        <option value="partial">抽盘</option>
                        <option value="cycle">循环盘点</option>
                        <option value="spot">临时盘点</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button id="analyzeBtn" class="w-full bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-md text-sm btn-fix">
                        分析
                    </button>
                </div>
            </div>
        </div>

        <!-- 统计概览 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">盘点次数</dt>
                            <dd class="text-lg font-medium text-gray-900" id="totalCounts">0</dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">准确率</dt>
                            <dd class="text-lg font-medium text-gray-900" id="accuracyRate">0%</dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">差异商品</dt>
                            <dd class="text-lg font-medium text-gray-900" id="differenceItems">0</dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">差异金额</dt>
                            <dd class="text-lg font-medium text-gray-900" id="differenceAmount">¥0</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表分析 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- 盘点趋势图 -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">盘点趋势</h3>
                <canvas id="countTrendChart" width="400" height="200"></canvas>
            </div>

            <!-- 差异分布图 -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">差异分布</h3>
                <canvas id="differenceChart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- 详细分析表格 -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">详细分析</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">盘点计划</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">仓库</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">盘点商品</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">差异商品</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">准确率</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">完成日期</th>
                        </tr>
                    </thead>
                    <tbody id="analysisTableBody" class="bg-white divide-y divide-gray-200">
                        <!-- 分析数据将在这里动态加载 -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 加载指示器 -->
        <div id="loadingIndicator" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-40" style="display: none;">
            <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-500"></div>
                <span class="text-gray-700">分析中...</span>
            </div>
        </div>
    </div>

    <!-- 引入模块化JS文件 -->
    <script src="js/config.js"></script>
    <script>
        let analysisApp;
        
        // 初始化分析应用
        function initAnalysisApp() {
            if (typeof AV !== 'undefined') {
                try {
                    analysisApp = new CountAnalysisApp();
                    analysisApp.init();
                    console.log('盘点分析应用初始化成功');
                } catch (error) {
                    console.error('盘点分析应用初始化失败:', error);
                    alert('应用初始化失败: ' + error.message);
                }
            } else {
                setTimeout(initAnalysisApp, 100);
            }
        }

        // 盘点分析应用类
        class CountAnalysisApp {
            constructor() {
                this.charts = {};
            }

            init() {
                this.bindEvents();
                this.loadWarehouses();
                this.loadAnalysis();
            }

            bindEvents() {
                document.getElementById('analyzeBtn').addEventListener('click', () => {
                    this.loadAnalysis();
                });

                document.getElementById('exportReport').addEventListener('click', () => {
                    this.exportReport();
                });
            }

            async loadWarehouses() {
                try {
                    const query = new AV.Query('Warehouse');
                    query.equalTo('status', 'active');
                    query.ascending('name');
                    const warehouses = await query.find();
                    
                    const select = document.getElementById('warehouseFilter');
                    select.innerHTML = '<option value="">全部仓库</option>';
                    
                    warehouses.forEach(warehouse => {
                        const option = document.createElement('option');
                        option.value = warehouse.id;
                        option.textContent = warehouse.get('name');
                        select.appendChild(option);
                    });
                } catch (error) {
                    console.error('加载仓库失败:', error);
                }
            }

            async loadAnalysis() {
                try {
                    this.showLoading();
                    
                    // 获取筛选条件
                    const timeRange = document.getElementById('timeRange').value;
                    const warehouseId = document.getElementById('warehouseFilter').value;
                    const type = document.getElementById('typeFilter').value;
                    
                    // 计算时间范围
                    const endDate = new Date();
                    const startDate = new Date();
                    startDate.setDate(startDate.getDate() - parseInt(timeRange));
                    
                    // 查询盘点计划
                    const planQuery = new AV.Query('CountPlan');
                    planQuery.equalTo('status', 'completed');
                    planQuery.greaterThanOrEqualTo('completedDate', startDate);
                    planQuery.lessThanOrEqualTo('completedDate', endDate);
                    
                    if (warehouseId) {
                        planQuery.equalTo('warehouseId', AV.Object.createWithoutData('Warehouse', warehouseId));
                    }
                    if (type) {
                        planQuery.equalTo('type', type);
                    }
                    
                    planQuery.include('warehouseId');
                    planQuery.descending('completedDate');
                    const plans = await planQuery.find();
                    
                    // 分析数据
                    const analysisData = await this.analyzeData(plans);
                    
                    // 更新统计
                    this.updateStatistics(analysisData);
                    
                    // 更新图表
                    this.updateCharts(analysisData);
                    
                    // 更新表格
                    this.updateTable(analysisData.plans);
                    
                    this.hideLoading();
                } catch (error) {
                    console.error('加载分析数据失败:', error);
                    this.hideLoading();
                    alert('加载分析数据失败: ' + error.message);
                }
            }

            async analyzeData(plans) {
                const analysisData = {
                    totalCounts: plans.length,
                    totalItems: 0,
                    accurateItems: 0,
                    differenceItems: 0,
                    differenceAmount: 0,
                    plans: []
                };
                
                for (const plan of plans) {
                    // 查询盘点记录
                    const recordQuery = new AV.Query('CountRecord');
                    recordQuery.equalTo('planId', plan);
                    const records = await recordQuery.find();
                    
                    const planData = {
                        plan: plan,
                        totalItems: records.length,
                        accurateItems: records.filter(r => r.get('difference') === 0).length,
                        differenceItems: records.filter(r => r.get('difference') !== 0).length,
                        accuracyRate: records.length > 0 ? (records.filter(r => r.get('difference') === 0).length / records.length * 100).toFixed(1) : 0
                    };
                    
                    analysisData.plans.push(planData);
                    analysisData.totalItems += planData.totalItems;
                    analysisData.accurateItems += planData.accurateItems;
                    analysisData.differenceItems += planData.differenceItems;
                }
                
                analysisData.accuracyRate = analysisData.totalItems > 0 ? 
                    (analysisData.accurateItems / analysisData.totalItems * 100).toFixed(1) : 0;
                
                return analysisData;
            }

            updateStatistics(data) {
                document.getElementById('totalCounts').textContent = data.totalCounts;
                document.getElementById('accuracyRate').textContent = data.accuracyRate + '%';
                document.getElementById('differenceItems').textContent = data.differenceItems;
                document.getElementById('differenceAmount').textContent = '¥' + data.differenceAmount.toFixed(2);
            }

            updateCharts(data) {
                // 实现图表更新
                console.log('更新图表:', data);
            }

            updateTable(plans) {
                const tbody = document.getElementById('analysisTableBody');
                
                if (plans.length === 0) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="7" class="px-6 py-8 text-center text-gray-500">
                                暂无分析数据
                            </td>
                        </tr>
                    `;
                    return;
                }
                
                const html = plans.map(planData => {
                    const plan = planData.plan;
                    const warehouse = plan.get('warehouseId');
                    
                    return `
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                ${plan.get('name')}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${warehouse ? warehouse.get('name') : '-'}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${this.getTypeText(plan.get('type'))}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${planData.totalItems}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${planData.differenceItems}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${planData.accuracyRate}%
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${plan.get('completedDate') ? plan.get('completedDate').toLocaleDateString() : '-'}
                            </td>
                        </tr>
                    `;
                }).join('');
                
                tbody.innerHTML = html;
            }

            getTypeText(type) {
                const typeMap = {
                    'full': '全盘',
                    'partial': '抽盘',
                    'cycle': '循环盘点',
                    'spot': '临时盘点'
                };
                return typeMap[type] || type;
            }

            exportReport() {
                // 实现报告导出功能
                alert('报告导出功能开发中...');
            }

            showLoading() {
                document.getElementById('loadingIndicator').style.display = 'flex';
            }

            hideLoading() {
                document.getElementById('loadingIndicator').style.display = 'none';
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initAnalysisApp();
        });
    </script>
</body>
</html>
