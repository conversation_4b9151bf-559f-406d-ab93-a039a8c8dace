<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- 头部 -->
      <div class="text-center">
        <Icon name="mdi:hotel" size="48" class="mx-auto text-blue-600" />
        <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
          注册账户
        </h2>
        <p class="mt-2 text-sm text-gray-600">
          已有账户？
          <NuxtLink to="/auth/login" class="font-medium text-blue-600 hover:text-blue-500">
            立即登录
          </NuxtLink>
        </p>
      </div>

      <!-- 注册表单 -->
      <div class="bg-white py-8 px-6 shadow-lg rounded-lg">
        <form @submit.prevent="handleSubmit">
          <div class="mb-4">
            <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
              用户名 *
            </label>
            <div class="relative">
              <Icon name="mdi:account" size="16" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                id="username"
                v-model="formData.username"
                type="text"
                required
                :disabled="loading"
                class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="请输入用户名"
              />
            </div>
          </div>

          <div class="mb-4">
            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
              密码 *
            </label>
            <div class="relative">
              <Icon name="mdi:lock" size="16" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                id="password"
                v-model="formData.password"
                :type="showPassword ? 'text' : 'password'"
                required
                :disabled="loading"
                class="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="请输入密码"
              />
              <button
                type="button"
                @click="showPassword = !showPassword"
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <Icon :name="showPassword ? 'mdi:eye-off' : 'mdi:eye'" size="16" />
              </button>
            </div>
          </div>

          <div class="mb-4">
            <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-2">
              确认密码 *
            </label>
            <div class="relative">
              <Icon name="mdi:lock-check" size="16" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                id="confirmPassword"
                v-model="formData.confirmPassword"
                :type="showConfirmPassword ? 'text' : 'password'"
                required
                :disabled="loading"
                class="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="请再次输入密码"
              />
              <button
                type="button"
                @click="showConfirmPassword = !showConfirmPassword"
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <Icon :name="showConfirmPassword ? 'mdi:eye-off' : 'mdi:eye'" size="16" />
              </button>
            </div>
          </div>

          <div class="mb-4">
            <label for="realName" class="block text-sm font-medium text-gray-700 mb-2">
              真实姓名
            </label>
            <div class="relative">
              <Icon name="mdi:account-circle" size="16" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                id="realName"
                v-model="formData.realName"
                type="text"
                :disabled="loading"
                class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="请输入真实姓名"
              />
            </div>
          </div>

          <div class="mb-4">
            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
              手机号码
            </label>
            <div class="relative">
              <Icon name="mdi:phone" size="16" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                id="phone"
                v-model="formData.phone"
                type="tel"
                :disabled="loading"
                class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="请输入手机号码"
              />
            </div>
          </div>

          <div class="mb-6">
            <label for="department" class="block text-sm font-medium text-gray-700 mb-2">
              部门
            </label>
            <div class="relative">
              <Icon name="mdi:office-building" size="16" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <select
                id="department"
                v-model="formData.department"
                :disabled="loading"
                class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">请选择部门</option>
                <option value="工程部">工程部</option>
                <option value="客房部">客房部</option>
                <option value="餐饮部">餐饮部</option>
                <option value="前厅部">前厅部</option>
                <option value="保安部">保安部</option>
                <option value="财务部">财务部</option>
                <option value="人事部">人事部</option>
                <option value="其他">其他</option>
              </select>
            </div>
          </div>

          <button
            type="submit"
            :disabled="loading"
            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {{ loading ? '注册中...' : '注册' }}
          </button>
        </form>

        <!-- 错误提示 -->
        <div v-if="errorMessage" class="mt-4">
          <div class="bg-red-50 border border-red-200 rounded-md p-3">
            <div class="flex">
              <Icon name="mdi:alert-circle" size="16" class="text-red-400 mr-2 mt-0.5" />
              <div class="text-sm text-red-700">
                {{ errorMessage }}
              </div>
            </div>
          </div>
        </div>

        <!-- 成功提示 -->
        <div v-if="successMessage" class="mt-4">
          <div class="bg-green-50 border border-green-200 rounded-md p-3">
            <div class="flex">
              <Icon name="mdi:check-circle" size="16" class="text-green-400 mr-2 mt-0.5" />
              <div class="text-sm text-green-700">
                {{ successMessage }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部链接 -->
      <div class="text-center">
        <p class="text-sm text-gray-600">
          已有账户？
          <NuxtLink to="/auth/login" class="font-medium text-blue-600 hover:text-blue-500">
            立即登录
          </NuxtLink>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面元数据
definePageMeta({
  layout: false,
  title: '用户注册'
})

// 状态管理
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const errorMessage = ref('')
const successMessage = ref('')
const showPassword = ref(false)
const showConfirmPassword = ref(false)

const formData = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  realName: '',
  phone: '',
  department: ''
})

// 方法
const validateForm = () => {
  if (!formData.username.trim()) {
    return '请输入用户名'
  }
  
  if (formData.username.length < 3 || formData.username.length > 20) {
    return '用户名长度应为3-20个字符'
  }
  
  if (!formData.password.trim()) {
    return '请输入密码'
  }
  
  if (formData.password.length < 6) {
    return '密码长度不能少于6个字符'
  }
  
  if (formData.password !== formData.confirmPassword) {
    return '两次输入的密码不一致'
  }
  
  if (formData.phone && !/^1[3-9]\d{9}$/.test(formData.phone)) {
    return '请输入正确的手机号码'
  }
  
  return null
}

const handleSubmit = async () => {
  // 表单验证
  const validationError = validateForm()
  if (validationError) {
    errorMessage.value = validationError
    return
  }
  
  loading.value = true
  errorMessage.value = ''
  successMessage.value = ''
  
  try {
    // 执行注册
    const result = await authStore.register({
      username: formData.username,
      password: formData.password,
      realName: formData.realName || undefined,
      phone: formData.phone || undefined,
      department: formData.department || undefined
    })
    
    if (result.success) {
      successMessage.value = '注册成功！正在跳转...'
      
      // 延迟跳转到首页
      setTimeout(() => {
        navigateTo('/')
      }, 1500)
    } else {
      errorMessage.value = result.error || '注册失败，请稍后重试'
    }
  } catch (error) {
    console.error('注册错误:', error)
    errorMessage.value = '注册过程中发生错误，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 检查是否已登录
onMounted(() => {
  if (authStore.isLoggedIn) {
    navigateTo('/')
  }
})

// 页面标题
useHead({
  title: '用户注册 - 酒店管理系统'
})
</script>

<style scoped>
/* 自定义样式 */
.form-input {
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
</style>
