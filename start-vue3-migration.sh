#!/bin/bash

# Vue3 + LeanCloud 项目迁移启动脚本
# 使用方法: ./start-vue3-migration.sh

echo "🚀 开始创建 Vue3 + LeanCloud 酒店管理系统..."

# 检查是否安装了必要的工具
check_requirements() {
    echo "📋 检查环境要求..."
    
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 未安装，请先安装 Node.js 16+ 版本"
        exit 1
    fi
    
    if ! command -v pnpm &> /dev/null; then
        echo "📦 pnpm 未安装，正在安装..."
        npm install -g pnpm
    fi
    
    echo "✅ 环境检查完成"
}

# 创建项目
create_project() {
    echo "🏗️ 创建 Nuxt3 项目..."
    
    # 创建项目
    npx nuxi@latest init hotel-management-vue3
    cd hotel-management-vue3
    
    echo "✅ 项目创建完成"
}

# 安装依赖
install_dependencies() {
    echo "📦 安装项目依赖..."
    
    # 安装基础依赖
    pnpm install
    
    # 安装项目特定依赖
    pnpm add leancloud-storage @vueuse/core @vueuse/nuxt
    pnpm add naive-ui @naive-ui/nuxt
    pnpm add @pinia/nuxt pinia
    pnpm add @nuxtjs/tailwindcss
    pnpm add @nuxt/icon
    
    # 安装开发依赖
    pnpm add -D @nuxt/typescript typescript
    
    echo "✅ 依赖安装完成"
}

# 创建目录结构
create_structure() {
    echo "📁 创建项目目录结构..."
    
    # 创建主要目录
    mkdir -p components/{ui,work-log,inventory,repair,auth}
    mkdir -p composables
    mkdir -p stores
    mkdir -p types
    mkdir -p plugins
    mkdir -p middleware
    mkdir -p pages/{auth,work-log,inventory,repair,admin}
    mkdir -p server/api
    mkdir -p assets/{css,images}
    
    echo "✅ 目录结构创建完成"
}

# 创建配置文件
create_config() {
    echo "⚙️ 创建配置文件..."
    
    # 创建 nuxt.config.ts
    cat > nuxt.config.ts << 'EOF'
export default defineNuxtConfig({
  devtools: { enabled: true },
  modules: [
    '@nuxtjs/tailwindcss',
    '@vueuse/nuxt',
    '@pinia/nuxt',
    '@naive-ui/nuxt',
    '@nuxt/icon'
  ],
  css: ['~/assets/css/main.css'],
  runtimeConfig: {
    public: {
      leancloudAppId: process.env.LEANCLOUD_APP_ID,
      leancloudAppKey: process.env.LEANCLOUD_APP_KEY,
      leancloudServerUrl: process.env.LEANCLOUD_SERVER_URL,
      wechatCorpId: process.env.WECHAT_CORP_ID,
      wechatAgentId: process.env.WECHAT_AGENT_ID
    }
  },
  app: {
    head: {
      title: '酒店管理系统',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'format-detection', content: 'telephone=no' }
      ]
    }
  },
  ssr: false // 由于使用LeanCloud，建议使用SPA模式
})
EOF

    # 创建环境变量文件
    cat > .env << 'EOF'
# LeanCloud 配置
LEANCLOUD_APP_ID=epbCQbfBnJNaZv0O5CCLacgJ-gzGzoHsz
LEANCLOUD_APP_KEY=9atvXPb61ih8GXsOVHD8dRCh
LEANCLOUD_SERVER_URL=https://epbcqbfb.lc-cn-n1-shared.com

# 企业微信配置
WECHAT_CORP_ID=ww92ddfdb34a050990
WECHAT_AGENT_ID=1000003
EOF

    # 创建基础CSS文件
    cat > assets/css/main.css << 'EOF'
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义样式 */
body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* 微信浏览器兼容性 */
* {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
}

/* 加载动画 */
.loading {
  @apply animate-pulse bg-gray-200;
}
EOF

    echo "✅ 配置文件创建完成"
}

# 创建基础文件
create_basic_files() {
    echo "📄 创建基础文件..."
    
    # 创建 LeanCloud 插件
    cat > plugins/leancloud.client.ts << 'EOF'
import AV from 'leancloud-storage'

export default defineNuxtPlugin(() => {
  const config = useRuntimeConfig()
  
  AV.init({
    appId: config.public.leancloudAppId,
    appKey: config.public.leancloudAppKey,
    serverURL: config.public.leancloudServerUrl
  })
  
  return {
    provide: {
      AV
    }
  }
})
EOF

    # 创建类型定义
    cat > types/index.ts << 'EOF'
export interface User {
  id: string
  username: string
  realName?: string
  phone?: string
  department?: string
  roles: string[]
  createdAt: Date
}

export interface WorkLog {
  id: string
  content: string
  images?: string[]
  pageType: string
  user?: User
  createdAt: Date
  updatedAt?: Date
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
}
EOF

    echo "✅ 基础文件创建完成"
}

# 创建启动脚本
create_scripts() {
    echo "📝 更新 package.json 脚本..."
    
    # 更新 package.json 中的脚本
    cat > package.json << 'EOF'
{
  "name": "hotel-management-vue3",
  "private": true,
  "scripts": {
    "build": "nuxt build",
    "dev": "nuxt dev",
    "generate": "nuxt generate",
    "preview": "nuxt preview",
    "postinstall": "nuxt prepare",
    "start": "node .output/server/index.mjs"
  }
}
EOF

    echo "✅ 脚本配置完成"
}

# 显示下一步指引
show_next_steps() {
    echo ""
    echo "🎉 Vue3 + LeanCloud 项目初始化完成！"
    echo ""
    echo "📋 下一步操作："
    echo "1. cd hotel-management-vue3"
    echo "2. pnpm dev                    # 启动开发服务器"
    echo "3. 访问 http://localhost:3000  # 查看项目"
    echo ""
    echo "📚 重要文件说明："
    echo "- nuxt.config.ts              # Nuxt 配置文件"
    echo "- .env                        # 环境变量配置"
    echo "- plugins/leancloud.client.ts # LeanCloud 初始化"
    echo "- types/index.ts              # TypeScript 类型定义"
    echo ""
    echo "🔧 接下来需要手动创建的文件："
    echo "- composables/useLeanCloud.ts # LeanCloud 封装"
    echo "- stores/auth.ts              # 用户认证状态"
    echo "- components/WorkLogCard.vue  # 工作日志组件"
    echo "- pages/index.vue             # 主页面"
    echo "- pages/auth/login.vue        # 登录页面"
    echo ""
    echo "📖 参考文档："
    echo "- Nuxt3: https://nuxt.com/"
    echo "- Naive UI: https://www.naiveui.com/"
    echo "- LeanCloud: https://leancloud.cn/docs/"
    echo ""
}

# 主函数
main() {
    check_requirements
    create_project
    install_dependencies
    create_structure
    create_config
    create_basic_files
    create_scripts
    show_next_steps
}

# 执行主函数
main
