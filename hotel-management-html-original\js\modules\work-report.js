/**
 * 工作汇报模块
 * 生成和展示每日工作汇报
 */

class WorkReportApp extends BaseWorkLogApp {
    constructor() {
        super({
            pageType: 'work-report',
            requiredElements: ['controlPanel', 'reportContainer']
        });
        
        // 绑定方法上下文
        this.generateReport = this.generateReport.bind(this);
        this.exportReport = this.exportReport.bind(this);
    }

    /**
     * 获取页面特定的DOM元素
     */
    getPageElements() {
        return {
            controlPanel: 'controlPanel',
            reportContainer: 'reportContainer',
            loadingIndicator: 'loadingIndicator',
            welcomeSection: 'welcomeSection',
            reportDate: 'reportDate',
            userSelect: 'userSelect',
            userSelectContainer: 'userSelectContainer',
            cutoffTime: 'cutoffTime',
            generateReport: 'generateReport',
            exportReport: 'exportReport',
            realName: 'realName',
            username: 'username'
        };
    }

    /**
     * 绑定页面特定事件
     */
    bindPageEvents() {
        // 生成汇报按钮
        if (this.elements.generateReport) {
            this.elements.generateReport.addEventListener('click', this.generateReport);
        }

        // 汇报文本按钮
        if (this.elements.exportReport) {
            this.elements.exportReport.addEventListener('click', this.exportReport);
        }

        // 设置默认日期为今天
        if (this.elements.reportDate) {
            const today = new Date();
            this.elements.reportDate.value = today.toISOString().split('T')[0];
        }
    }

    /**
     * 用户登录后的回调
     */
    onUserLoggedIn() {
        this.showUserInterface();
        this.loadUserOptions();
    }

    /**
     * 用户登出后的回调
     */
    onUserLoggedOut() {
        this.showLoginInterface();
    }

    /**
     * 显示用户界面
     */
    showUserInterface() {
        super.showUserInterface();
        
        if (this.elements.controlPanel) {
            this.elements.controlPanel.classList.remove('hidden');
        }
        
        if (this.elements.welcomeSection) {
            this.elements.welcomeSection.classList.add('hidden');
        }
        
        // 检查用户权限，决定是否显示用户选择
        this.updateUserSelectVisibility();
    }

    /**
     * 显示登录界面
     */
    showLoginInterface() {
        super.showLoginPrompt();
        
        if (this.elements.controlPanel) {
            this.elements.controlPanel.classList.add('hidden');
        }
        
        if (this.elements.reportContainer) {
            this.elements.reportContainer.classList.add('hidden');
        }
        
        if (this.elements.welcomeSection) {
            this.elements.welcomeSection.classList.remove('hidden');
        }
    }

    /**
     * 检查当前用户是否为管理员
     */
    isCurrentUserAdmin() {
        if (!this.currentUser) return false;
        const userRoles = this.currentUser.get('roles') || [];
        return userRoles.includes && userRoles.includes('admin');
    }

    /**
     * 更新用户选择的可见性
     */
    updateUserSelectVisibility() {
        if (this.elements.userSelectContainer) {
            if (this.isCurrentUserAdmin()) {
                this.elements.userSelectContainer.classList.remove('hidden');
            } else {
                this.elements.userSelectContainer.classList.add('hidden');
            }
        }
    }

    /**
     * 加载用户选项（管理员功能）
     */
    async loadUserOptions() {
        if (!this.isCurrentUserAdmin() || !this.elements.userSelect) {
            return;
        }

        try {
            // 从日志中提取用户信息
            const WorkLog = AV.Object.extend('WorkLog');
            const query = new AV.Query(WorkLog);
            query.include('user');
            query.limit(1000);
            query.descending('createdAt');
            
            const logs = await query.find();
            const userMap = new Map();
            
            logs.forEach(log => {
                const user = log.get('user');
                if (user && user.id) {
                    const userId = user.id;
                    const realName = user.get('realName') || user.get('username') || '未知用户';
                    const username = user.get('username') || '未知';
                    
                    if (!userMap.has(userId)) {
                        userMap.set(userId, {
                            id: userId,
                            realName: realName,
                            username: username
                        });
                    }
                }
            });
            
            // 清空现有选项
            this.elements.userSelect.innerHTML = '<option value="">当前用户</option>';
            
            // 添加用户选项
            const sortedUsers = Array.from(userMap.values()).sort((a, b) => 
                (a.username || '').localeCompare(b.username || '')
            );
            
            sortedUsers.forEach(user => {
                const option = document.createElement('option');
                option.value = user.id;
                option.textContent = `${user.realName} (${user.username})`;
                this.elements.userSelect.appendChild(option);
            });
            
        } catch (error) {
            console.error('加载用户列表失败:', error);
        }
    }

    /**
     * 生成工作汇报
     */
    async generateReport() {
        const reportDate = this.elements.reportDate?.value;
        const selectedUserId = this.elements.userSelect?.value;
        const cutoffTime = this.elements.cutoffTime?.value || '20:00';
        
        if (!reportDate) {
            WorkLogUtils.showMessage('请选择汇报日期', 'warning');
            return;
        }

        // 确定要查询的用户
        let targetUser = this.currentUser;
        if (this.isCurrentUserAdmin() && selectedUserId) {
            targetUser = AV.Object.createWithoutData('_User', selectedUserId);
        }

        try {
            // 显示加载状态
            this.showLoading(true);
            
            // 生成汇报数据
            const reportData = await this.generateReportData(targetUser, reportDate, cutoffTime);

            // 保存当前汇报数据供导出使用
            this.currentReportData = reportData;

            // 渲染汇报
            this.renderReport(reportData);

            // 显示导出按钮
            if (this.elements.exportReport) {
                this.elements.exportReport.classList.remove('hidden');
            }
            
            WorkLogUtils.showMessage('汇报生成成功', 'success');
            
        } catch (error) {
            console.error('生成汇报失败:', error);
            WorkLogUtils.showMessage('生成汇报失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 显示/隐藏加载状态
     */
    showLoading(show) {
        if (this.elements.loadingIndicator) {
            if (show) {
                this.elements.loadingIndicator.classList.remove('hidden');
            } else {
                this.elements.loadingIndicator.classList.add('hidden');
            }
        }
    }

    /**
     * 生成汇报数据
     */
    async generateReportData(user, date, cutoffTime) {
        const [cutoffHour, cutoffMinute] = cutoffTime.split(':').map(Number);

        // 如果传入的是用户名字符串，需要查询获取完整用户信息
        let userInfo = user;
        if (typeof user === 'string') {
            try {
                const query = new AV.Query(AV.User);
                query.equalTo('username', user);
                const userObj = await query.first();
                if (userObj) {
                    userInfo = userObj;
                } else {
                    // 如果找不到用户，创建一个包含用户名的对象
                    userInfo = { username: user, realName: user };
                }
            } catch (error) {
                console.error('查询用户信息失败:', error);
                // 查询失败时，创建一个包含用户名的对象
                userInfo = { username: user, realName: user };
            }
        }

        // 设置查询时间范围
        const startTime = new Date(date);
        startTime.setHours(0, 0, 0, 0);

        const endTime = new Date(date);
        endTime.setHours(cutoffHour, cutoffMinute, 0, 0);

        console.log(`查询时间范围: ${startTime.toLocaleString()} - ${endTime.toLocaleString()}`);

        // 查询工作日志
        const workLogs = await this.queryWorkLogs(user, startTime, endTime);

        // 查询报修记录
        const repairLogs = await this.queryRepairLogs(user, startTime, endTime);

        // 生成汇报数据，传入完整的用户信息
        return this.processReportData(userInfo, date, workLogs, repairLogs);
    }

    /**
     * 查询工作日志
     */
    async queryWorkLogs(user, startTime, endTime) {
        const WorkLog = AV.Object.extend('WorkLog');
        const query = new AV.Query(WorkLog);
        
        query.equalTo('user', user);
        query.greaterThanOrEqualTo('createdAt', startTime);
        query.lessThanOrEqualTo('createdAt', endTime);
        query.descending('createdAt');
        query.limit(1000);
        
        return await query.find();
    }

    /**
     * 查询报修记录
     */
    async queryRepairLogs(user, startTime, endTime) {
        const WorkLog = AV.Object.extend('WorkLog');
        const query = new AV.Query(WorkLog);
        
        query.equalTo('user', user);
        query.equalTo('pageType', 'repair');
        query.greaterThanOrEqualTo('createdAt', startTime);
        query.lessThanOrEqualTo('createdAt', endTime);
        query.descending('createdAt');
        query.limit(1000);
        
        return await query.find();
    }

    /**
     * 处理汇报数据
     */
    processReportData(user, date, workLogs, repairLogs) {
        // 获取用户信息
        let userName, realName;

        if (typeof user === 'string') {
            // 如果user是字符串（用户名）
            userName = user;
            realName = user;
        } else if (user && user.get) {
            // 如果user是AV.User对象
            userName = user.get('username') || '未知用户';
            realName = user.get('realName') || userName;
        } else if (user && typeof user === 'object') {
            // 如果user是普通对象（从查询中构造的）
            userName = user.username || '未知用户';
            realName = user.realName || userName;
        } else {
            // 默认情况
            userName = '当前用户';
            realName = '当前用户';
        }
        
        // 处理报修数据
        const repairSummary = this.processRepairData(repairLogs);
        
        // 处理工作数据
        const workSummary = this.processWorkData(workLogs);
        
        // 计算完成度
        const completionRate = this.calculateCompletionRate(repairSummary, workSummary);
        
        return {
            date: new Date(date).toLocaleDateString('zh-CN'),
            userName,
            realName,
            repairSummary,
            workSummary,
            completionRate,
            rawData: {
                workLogs,
                repairLogs
            }
        };
    }

    /**
     * 处理报修数据
     */
    processRepairData(repairLogs) {
        const total = repairLogs.length;
        let completed = 0;
        let pending = 0;
        const items = [];
        
        repairLogs.forEach(log => {
            const content = log.get('content') || '';
            const status = this.extractRepairStatus(content);
            
            if (status === '已完成') {
                completed++;
            } else {
                pending++;
            }
            
            items.push({
                content: content,
                status: status,
                createdAt: log.get('createdAt'),
                serialNumber: this.extractSerialNumber(content)
            });
        });
        
        return {
            total,
            completed,
            pending,
            items
        };
    }

    /**
     * 处理工作数据
     */
    processWorkData(workLogs) {
        const mainLogs = workLogs.filter(log => 
            (log.get('pageType') || 'main') === 'main'
        );
        
        const items = mainLogs.map(log => ({
            content: log.get('content') || '',
            createdAt: log.get('createdAt'),
            pageType: log.get('pageType') || 'main'
        }));
        
        return {
            total: items.length,
            items
        };
    }

    /**
     * 计算完成度
     */
    calculateCompletionRate(repairSummary, workSummary) {
        const totalTasks = repairSummary.total + workSummary.total;
        const completedTasks = repairSummary.completed + workSummary.total; // 工作日志默认为已完成
        
        const percentage = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 100;
        
        return {
            percentage,
            completedTasks,
            totalTasks
        };
    }

    /**
     * 从内容中提取报修状态
     */
    extractRepairStatus(content) {
        if (content.includes('已完成') || content.includes('完成')) {
            return '已完成';
        } else if (content.includes('待处理') || content.includes('待完成')) {
            return '待处理';
        }
        return '待处理'; // 默认状态
    }

    /**
     * 从内容中提取流水号
     */
    extractSerialNumber(content) {
        const match = content.match(/流水号[：:]\s*(\w+)/);
        return match ? match[1] : '';
    }

    /**
     * 渲染汇报
     */
    renderReport(reportData) {
        if (!this.elements.reportContainer) return;
        
        const reportHtml = this.generateReportHtml(reportData);
        this.elements.reportContainer.innerHTML = reportHtml;
        this.elements.reportContainer.classList.remove('hidden');
        
        // 滚动到汇报区域
        this.elements.reportContainer.scrollIntoView({ behavior: 'smooth' });
    }

    /**
     * 生成汇报HTML
     */
    generateReportHtml(data) {
        return `
            <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-8 mb-6 fade-in">
                <!-- 汇报标题 -->
                <div class="text-center mb-8">
                    <h1 class="text-2xl font-bold text-gray-800 mb-2">${data.date} 工作汇报</h1>
                    <p class="text-lg text-gray-600">用户：${data.realName}</p>
                </div>
                
                <!-- 统计概览 -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <div class="bg-blue-50 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-blue-600">${data.repairSummary.total}</div>
                        <div class="text-sm text-gray-600">今日报修</div>
                    </div>
                    <div class="bg-green-50 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-green-600">${data.workSummary.total}</div>
                        <div class="text-sm text-gray-600">今日工作</div>
                    </div>
                    <div class="bg-purple-50 rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-purple-600">${data.completionRate.percentage}%</div>
                        <div class="text-sm text-gray-600">完成度</div>
                    </div>
                </div>
                
                <!-- 报修详情 -->
                <div class="mb-8">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">今日报修</h2>
                    <div class="bg-gray-50 rounded-lg p-4 mb-4">
                        <div class="grid grid-cols-3 gap-4 text-center">
                            <div>
                                <span class="text-lg font-semibold text-gray-800">${data.repairSummary.total}</span>
                                <div class="text-sm text-gray-600">总计</div>
                            </div>
                            <div>
                                <span class="text-lg font-semibold text-green-600">${data.repairSummary.completed}</span>
                                <div class="text-sm text-gray-600">已完成</div>
                            </div>
                            <div>
                                <span class="text-lg font-semibold text-orange-600">${data.repairSummary.pending}</span>
                                <div class="text-sm text-gray-600">待处理</div>
                            </div>
                        </div>
                    </div>
                    ${this.generateRepairItemsHtml(data.repairSummary.items)}
                </div>
                
                <!-- 工作详情 -->
                <div class="mb-8">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">今日工作</h2>
                    <div class="bg-gray-50 rounded-lg p-4 mb-4">
                        <div class="text-center">
                            <span class="text-lg font-semibold text-gray-800">${data.workSummary.total}</span>
                            <div class="text-sm text-gray-600">总计</div>
                        </div>
                    </div>
                    ${this.generateWorkItemsHtml(data.workSummary.items)}
                </div>
                
                <!-- 完成度 -->
                <div>
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">今日工作完成度</h2>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm text-gray-600">完成进度</span>
                            <span class="text-sm font-semibold text-gray-800">${data.completionRate.completedTasks}/${data.completionRate.totalTasks}项</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-3">
                            <div class="bg-blue-500 h-3 rounded-full progress-bar" style="width: ${data.completionRate.percentage}%"></div>
                        </div>
                        <div class="text-center mt-2">
                            <span class="text-lg font-bold text-blue-600">${data.completionRate.percentage}%</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 生成报修项目HTML
     */
    generateRepairItemsHtml(items) {
        if (items.length === 0) {
            return '<div class="text-gray-500 text-center py-4">今日无报修记录</div>';
        }
        
        return `
            <div class="space-y-3">
                ${items.map((item, index) => `
                    <div class="border border-gray-200 rounded-lg p-3">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <div class="font-medium text-gray-800">${index + 1}. ${item.content.substring(0, 100)}${item.content.length > 100 ? '...' : ''}</div>
                                <div class="text-sm text-gray-500 mt-1">
                                    ${item.createdAt.toLocaleString('zh-CN')}
                                    ${item.serialNumber ? ` | 流水号: ${item.serialNumber}` : ''}
                                </div>
                            </div>
                            <span class="ml-3 px-2 py-1 text-xs rounded-full ${
                                item.status === '已完成' 
                                    ? 'bg-green-100 text-green-800' 
                                    : 'bg-orange-100 text-orange-800'
                            }">
                                ${item.status}
                            </span>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    /**
     * 生成工作项目HTML
     */
    generateWorkItemsHtml(items) {
        if (items.length === 0) {
            return '<div class="text-gray-500 text-center py-4">今日无工作记录</div>';
        }
        
        return `
            <div class="space-y-3">
                ${items.map((item, index) => `
                    <div class="border border-gray-200 rounded-lg p-3">
                        <div class="font-medium text-gray-800">${index + 1}. ${item.content}</div>
                        <div class="text-sm text-gray-500 mt-1">
                            ${item.createdAt.toLocaleString('zh-CN')}
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    /**
     * 生成汇报文本
     */
    exportReport() {
        if (!this.currentReportData) {
            WorkLogUtils.showMessage('请先生成汇报', 'warning');
            return;
        }

        // 生成文本格式的汇报
        const textReport = this.generateTextReport(this.currentReportData);

        // 显示文本弹窗
        this.showTextModal(textReport);
    }

    /**
     * 生成文本格式的汇报
     */
    generateTextReport(reportData) {
        const { userName, realName, date, repairSummary, workSummary, completionRate, rawData } = reportData;

        let text = '';

        // 标题
        text += `📊 工作汇报\n`;
        text += `==========================================\n\n`;

        // 基本信息
        text += `👤 汇报人：${realName || userName}\n`;
        text += `📅 汇报日期：${date}\n`;
        text += `⏰ 截止时间：20:00\n\n`;

        // 工作概览
        text += `📈 工作概览\n`;
        text += `------------------------------------------\n`;
        text += `• 工作日志：${workSummary.total || 0} 条\n`;

        // 统计页面类型
        const pageTypes = new Set();
        if (rawData.workLogs) {
            rawData.workLogs.forEach(log => {
                const pageType = log.get('pageType') || '主页面';
                pageTypes.add(pageType);
            });
        }
        text += `• 工作页面：${pageTypes.size} 个\n`;

        if (repairSummary.total > 0) {
            text += `• 报修记录：${repairSummary.total} 条\n`;
            text += `• 完成率：${completionRate}%\n`;
        }
        text += `\n`;

        // 详细工作内容
        if (rawData.workLogs && rawData.workLogs.length > 0) {
            text += `📝 详细工作内容\n`;
            text += `------------------------------------------\n`;

            rawData.workLogs.forEach((log, index) => {
                const time = log.get('createdAt').toLocaleTimeString('zh-CN', {
                    hour: '2-digit',
                    minute: '2-digit'
                });
                const pageType = log.get('pageType') || '主页面';

                // 尝试从多个字段获取活动内容
                let activity = log.get('activity') || log.get('content') || log.get('description');

                // 如果还是没有内容，尝试从details中提取
                if (!activity || activity === '无描述') {
                    const details = log.get('details');
                    if (details && typeof details === 'object') {
                        // 尝试从details中找到有意义的内容
                        activity = details.content || details.description || details.activity ||
                                  details.action || details.task || details.work;

                        // 如果details中有多个字段，组合显示
                        if (!activity) {
                            const meaningfulKeys = Object.keys(details).filter(key =>
                                details[key] && typeof details[key] === 'string' && details[key].trim()
                            );
                            if (meaningfulKeys.length > 0) {
                                activity = meaningfulKeys.map(key => `${key}: ${details[key]}`).join(', ');
                            }
                        }
                    }
                }

                // 最终默认值
                if (!activity) {
                    activity = `${pageType}页面操作`;
                }

                text += `${index + 1}. ${pageType}\n`;
                text += `   时间：${time}\n`;
                text += `   内容：${activity}\n`;

                // 添加其他详细信息（排除已经显示的内容）
                const details = log.get('details');
                if (details && typeof details === 'object') {
                    Object.keys(details).forEach(key => {
                        const value = details[key];
                        // 跳过已经在activity中显示的内容和空值
                        if (value && typeof value === 'string' && value.trim() &&
                            !activity.includes(value) &&
                            !['content', 'description', 'activity', 'action', 'task', 'work'].includes(key)) {
                            text += `   ${key}：${value}\n`;
                        }
                    });
                }
                text += `\n`;
            });
        }

        // 报修统计
        if (repairSummary.total > 0) {
            text += `🔧 报修工作统计\n`;
            text += `------------------------------------------\n`;
            text += `• 总计：${repairSummary.total} 条\n`;
            text += `• 已完成：${repairSummary.completed} 条\n`;
            text += `• 待处理：${repairSummary.pending} 条\n`;

            if (repairSummary.items && repairSummary.items.length > 0) {
                text += `\n详细记录：\n`;
                repairSummary.items.forEach((item, index) => {
                    text += `${index + 1}. ${item.content}\n`;
                    text += `   状态：${item.status}\n`;
                    text += `\n`;
                });
            }
            text += `\n`;
        }

        // 页面类型统计
        if (pageTypes.size > 0) {
            text += `📊 工作模块统计\n`;
            text += `------------------------------------------\n`;

            // 统计每个页面类型的数量
            const pageTypeCounts = {};
            if (rawData.workLogs) {
                rawData.workLogs.forEach(log => {
                    const pageType = log.get('pageType') || '主页面';
                    pageTypeCounts[pageType] = (pageTypeCounts[pageType] || 0) + 1;
                });
            }

            Array.from(pageTypes).forEach(pageType => {
                const count = pageTypeCounts[pageType] || 0;
                text += `• ${pageType}：${count} 条\n`;
            });
            text += `\n`;
        }

        // 工作总结
        text += `💡 工作总结\n`;
        text += `------------------------------------------\n`;
        if (workSummary.total > 0) {
            text += `今日共完成 ${workSummary.total} 项工作，涉及 ${pageTypes.size} 个不同的工作模块。`;
            if (repairSummary.total > 0) {
                text += `其中报修相关工作 ${repairSummary.total} 条，完成率达到 ${completionRate}%。`;
            }
            text += `工作内容主要集中在系统维护、用户服务等方面，整体工作进展顺利。`;
        } else {
            text += `今日暂无记录的工作内容。`;
        }
        text += `\n\n`;

        // 生成时间
        text += `📄 报告生成时间：${new Date().toLocaleString('zh-CN')}\n`;
        text += `==========================================`;

        return text;
    }

    /**
     * 显示文本弹窗
     */
    showTextModal(textContent) {
        // 创建弹窗HTML
        const modalHtml = `
            <div id="textModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col">
                    <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                        <h2 class="text-xl font-semibold text-gray-800">汇报文本</h2>
                        <button id="closeTextModal" class="text-gray-400 hover:text-gray-600 text-2xl">&times;</button>
                    </div>
                    <div class="p-4 flex-1 overflow-hidden flex flex-col">
                        <div class="mb-4 flex gap-2">
                            <button id="copyTextBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium">
                                📋 复制文本
                            </button>
                            <button id="selectAllBtn" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium">
                                ✅ 全选
                            </button>
                            <span class="text-sm text-gray-500 flex items-center ml-auto">
                                💡 可直接复制粘贴到微信、邮件等
                            </span>
                        </div>
                        <textarea id="reportTextArea" readonly
                                  class="w-full flex-1 p-4 border border-gray-300 rounded-lg font-mono text-sm resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                  style="min-height: 400px;">${textContent}</textarea>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 绑定事件
        this.bindTextModalEvents();
    }

    /**
     * 绑定文本弹窗事件
     */
    bindTextModalEvents() {
        const modal = document.getElementById('textModal');
        const closeBtn = document.getElementById('closeTextModal');
        const copyBtn = document.getElementById('copyTextBtn');
        const selectAllBtn = document.getElementById('selectAllBtn');
        const textArea = document.getElementById('reportTextArea');

        // 关闭弹窗
        const closeModal = () => {
            if (modal) {
                modal.remove();
            }
        };

        if (closeBtn) {
            closeBtn.addEventListener('click', closeModal);
        }

        // 点击背景关闭
        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeModal();
                }
            });
        }

        // 复制文本
        if (copyBtn && textArea) {
            copyBtn.addEventListener('click', async () => {
                try {
                    await navigator.clipboard.writeText(textArea.value);
                    copyBtn.innerHTML = '✅ 已复制';
                    copyBtn.classList.remove('bg-blue-500', 'hover:bg-blue-600');
                    copyBtn.classList.add('bg-green-500', 'hover:bg-green-600');

                    setTimeout(() => {
                        copyBtn.innerHTML = '📋 复制文本';
                        copyBtn.classList.remove('bg-green-500', 'hover:bg-green-600');
                        copyBtn.classList.add('bg-blue-500', 'hover:bg-blue-600');
                    }, 2000);
                } catch (error) {
                    // 降级到选中文本
                    textArea.select();
                    WorkLogUtils.showMessage('请手动复制选中的文本', 'info');
                }
            });
        }

        // 全选文本
        if (selectAllBtn && textArea) {
            selectAllBtn.addEventListener('click', () => {
                textArea.select();
                textArea.focus();
            });
        }

        // ESC键关闭
        document.addEventListener('keydown', function escHandler(e) {
            if (e.key === 'Escape') {
                closeModal();
                document.removeEventListener('keydown', escHandler);
            }
        });
    }
}

// 导出模块
if (typeof window !== 'undefined') {
    window.WorkReportApp = WorkReportApp;
}
