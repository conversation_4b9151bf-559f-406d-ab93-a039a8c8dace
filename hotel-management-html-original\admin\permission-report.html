<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限检查报告 - 系统管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
</head>
<body class="bg-gray-50 min-h-screen p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold mb-8">权限系统检查报告</h1>
        
        <!-- 总体状态 -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-medium mb-4">总体状态</h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="text-center p-4 border rounded-lg">
                    <div class="text-2xl font-bold" id="userStatus">❓</div>
                    <div class="text-sm text-gray-500">用户状态</div>
                </div>
                <div class="text-center p-4 border rounded-lg">
                    <div class="text-2xl font-bold" id="roleStatus">❓</div>
                    <div class="text-sm text-gray-500">角色状态</div>
                </div>
                <div class="text-center p-4 border rounded-lg">
                    <div class="text-2xl font-bold" id="permissionStatus">❓</div>
                    <div class="text-sm text-gray-500">权限状态</div>
                </div>
                <div class="text-center p-4 border rounded-lg">
                    <div class="text-2xl font-bold" id="systemStatus">❓</div>
                    <div class="text-sm text-gray-500">系统状态</div>
                </div>
            </div>
        </div>

        <!-- 详细检查结果 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 用户信息检查 -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium mb-4">用户信息检查</h3>
                <div id="userCheck" class="space-y-2 text-sm">
                    <!-- 用户检查结果 -->
                </div>
            </div>

            <!-- 角色数据检查 -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium mb-4">角色数据检查</h3>
                <div id="roleCheck" class="space-y-2 text-sm">
                    <!-- 角色检查结果 -->
                </div>
            </div>

            <!-- 权限管理器检查 -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium mb-4">权限管理器检查</h3>
                <div id="permissionManagerCheck" class="space-y-2 text-sm">
                    <!-- 权限管理器检查结果 -->
                </div>
            </div>

            <!-- 页面元素检查 -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium mb-4">页面元素检查</h3>
                <div id="elementCheck" class="space-y-2 text-sm">
                    <!-- 页面元素检查结果 -->
                </div>
            </div>
        </div>

        <!-- 修复建议 -->
        <div class="bg-white rounded-lg shadow p-6 mt-6">
            <h3 class="text-lg font-medium mb-4">修复建议</h3>
            <div id="suggestions" class="space-y-2 text-sm">
                <!-- 修复建议 -->
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="mt-6 space-x-4">
            <button id="runCheckBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded">
                重新检查
            </button>
            <a href="index.html" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded inline-block">
                返回管理首页
            </a>
            <a href="debug-permissions.html" class="bg-purple-500 hover:bg-purple-600 text-white px-6 py-2 rounded inline-block">
                详细调试
            </a>
        </div>
    </div>

    <!-- 引入配置文件 -->
    <script src="../js/config.js"></script>
    <script src="../js/permission-manager.js"></script>
    <script>
        class PermissionReportApp {
            constructor() {
                this.results = {
                    user: { status: 'unknown', issues: [] },
                    role: { status: 'unknown', issues: [] },
                    permission: { status: 'unknown', issues: [] },
                    system: { status: 'unknown', issues: [] }
                };
            }

            init() {
                this.bindEvents();
                this.runFullCheck();
            }

            bindEvents() {
                document.getElementById('runCheckBtn').addEventListener('click', () => {
                    this.runFullCheck();
                });
            }

            async runFullCheck() {
                this.clearResults();
                this.updateStatus('检查中...');

                await this.checkUser();
                await this.checkRole();
                await this.checkPermissionManager();
                await this.checkPageElements();
                
                this.updateOverallStatus();
                this.generateSuggestions();
            }

            clearResults() {
                ['userCheck', 'roleCheck', 'permissionManagerCheck', 'elementCheck', 'suggestions'].forEach(id => {
                    document.getElementById(id).innerHTML = '';
                });
            }

            updateStatus(status) {
                ['userStatus', 'roleStatus', 'permissionStatus', 'systemStatus'].forEach(id => {
                    document.getElementById(id).textContent = status;
                });
            }

            log(containerId, message, type = 'info') {
                const colors = {
                    'success': 'text-green-600',
                    'error': 'text-red-600',
                    'warning': 'text-yellow-600',
                    'info': 'text-blue-600'
                };

                const div = document.createElement('div');
                div.className = colors[type] || 'text-gray-600';
                div.textContent = message;
                document.getElementById(containerId).appendChild(div);
            }

            async checkUser() {
                this.log('userCheck', '🔍 检查用户登录状态...', 'info');

                const currentUser = AV.User.current();
                if (!currentUser) {
                    this.log('userCheck', '❌ 用户未登录', 'error');
                    this.results.user.status = 'error';
                    this.results.user.issues.push('用户未登录');
                    return;
                }

                this.log('userCheck', `✅ 用户已登录: ${currentUser.get('username')}`, 'success');
                this.log('userCheck', `📋 真实姓名: ${currentUser.get('realName') || '未设置'}`, 'info');
                this.log('userCheck', `📋 部门: ${currentUser.get('department') || '未设置'}`, 'info');
                this.log('userCheck', `📋 用户ID: ${currentUser.id}`, 'info');

                this.results.user.status = 'success';
            }

            async checkRole() {
                this.log('roleCheck', '🔍 检查用户角色数据...', 'info');

                const currentUser = AV.User.current();
                if (!currentUser) {
                    this.log('roleCheck', '❌ 无法检查角色：用户未登录', 'error');
                    return;
                }

                const roles = currentUser.get('roles');
                this.log('roleCheck', `📋 角色数据 (原始): ${JSON.stringify(roles)}`, 'info');
                this.log('roleCheck', `📋 数据类型: ${typeof roles}`, 'info');
                this.log('roleCheck', `📋 是否为数组: ${Array.isArray(roles)}`, 'info');

                // 解析角色数据
                let roleArray = [];
                let parseSuccess = false;

                if (typeof roles === 'string') {
                    try {
                        roleArray = JSON.parse(roles);
                        parseSuccess = true;
                        this.log('roleCheck', `✅ JSON解析成功: ${JSON.stringify(roleArray)}`, 'success');
                    } catch (e) {
                        roleArray = [roles];
                        this.log('roleCheck', `⚠️ JSON解析失败，作为单个角色处理: ${roles}`, 'warning');
                        this.results.role.issues.push('角色数据不是有效的JSON格式');
                    }
                } else if (Array.isArray(roles)) {
                    roleArray = roles;
                    parseSuccess = true;
                    this.log('roleCheck', `✅ 角色已是数组格式`, 'success');
                } else if (roles === null || roles === undefined) {
                    this.log('roleCheck', `❌ 角色数据为空`, 'error');
                    this.results.role.status = 'error';
                    this.results.role.issues.push('用户没有分配角色');
                    return;
                } else {
                    this.log('roleCheck', `❌ 未知的角色数据格式`, 'error');
                    this.results.role.status = 'error';
                    this.results.role.issues.push('角色数据格式不正确');
                    return;
                }

                // 检查管理员权限
                const isAdmin = roleArray.includes('admin') || roleArray.includes('super_admin');
                this.log('roleCheck', `🔍 管理员权限检查: ${isAdmin}`, isAdmin ? 'success' : 'warning');

                if (isAdmin) {
                    this.results.role.status = 'success';
                } else {
                    this.results.role.status = 'warning';
                    this.results.role.issues.push('用户没有管理员权限');
                }

                // 列出所有角色
                this.log('roleCheck', `📝 用户角色列表:`, 'info');
                roleArray.forEach(role => {
                    this.log('roleCheck', `  • ${role}`, 'info');
                });
            }

            async checkPermissionManager() {
                this.log('permissionManagerCheck', '🔍 检查权限管理器...', 'info');

                if (!window.permissionManager) {
                    this.log('permissionManagerCheck', '❌ 权限管理器未加载', 'error');
                    this.results.permission.status = 'error';
                    this.results.permission.issues.push('权限管理器未加载');
                    return;
                }

                this.log('permissionManagerCheck', '✅ 权限管理器已加载', 'success');

                try {
                    await window.permissionManager.init();
                    this.log('permissionManagerCheck', '✅ 权限管理器初始化成功', 'success');

                    const isAdmin = window.permissionManager.isAdmin();
                    const isSuperAdmin = window.permissionManager.isSuperAdmin();
                    const userRoleCodes = window.permissionManager.getUserRoleCodes();

                    this.log('permissionManagerCheck', `🔍 是管理员: ${isAdmin}`, isAdmin ? 'success' : 'warning');
                    this.log('permissionManagerCheck', `🔍 是超级管理员: ${isSuperAdmin}`, isSuperAdmin ? 'success' : 'info');
                    this.log('permissionManagerCheck', `📋 角色代码: ${JSON.stringify(userRoleCodes)}`, 'info');

                    if (isAdmin) {
                        this.results.permission.status = 'success';
                    } else {
                        this.results.permission.status = 'warning';
                        this.results.permission.issues.push('权限管理器检测到用户没有管理员权限');
                    }

                } catch (error) {
                    this.log('permissionManagerCheck', `❌ 权限管理器初始化失败: ${error.message}`, 'error');
                    this.results.permission.status = 'error';
                    this.results.permission.issues.push('权限管理器初始化失败');
                }
            }

            async checkPageElements() {
                this.log('elementCheck', '🔍 检查管理主页元素...', 'info');
                this.log('elementCheck', '⚠️ 注意：此检查需要在管理主页上下文中运行才准确', 'warning');

                // 由于这是在报告页面运行，我们无法直接检查管理主页的元素
                // 但我们可以检查一些通用的元素和提供建议

                this.log('elementCheck', '📋 检查当前页面的基本结构...', 'info');

                // 检查当前页面的基本元素
                const currentPageElements = {
                    'userStatus': '用户状态显示',
                    'roleStatus': '角色状态显示',
                    'permissionStatus': '权限状态显示',
                    'systemStatus': '系统状态显示'
                };

                let missingCurrentElements = 0;
                Object.entries(currentPageElements).forEach(([id, name]) => {
                    const element = document.getElementById(id);
                    if (element) {
                        this.log('elementCheck', `✅ ${name} (${id}) 存在`, 'success');
                    } else {
                        this.log('elementCheck', `❌ ${name} (${id}) 不存在`, 'error');
                        missingCurrentElements++;
                    }
                });

                // 提供管理主页元素检查的建议
                this.log('elementCheck', '', 'info');
                this.log('elementCheck', '📋 管理主页应包含的关键元素:', 'info');

                const requiredElements = [
                    'accessDenied - 访问受限提示',
                    'adminSection - 管理功能区域',
                    'userInfo - 用户信息区域',
                    'loginBtn - 登录按钮',
                    'realName - 用户姓名显示',
                    'totalUsers - 总用户数统计',
                    'activeUsers - 活跃用户统计',
                    'todayOperations - 今日操作统计',
                    'systemWarnings - 系统警告统计'
                ];

                requiredElements.forEach(element => {
                    this.log('elementCheck', `  • ${element}`, 'info');
                });

                // 检查是否在管理主页
                const isAdminPage = window.location.pathname.includes('admin/index.html');
                if (isAdminPage) {
                    this.log('elementCheck', '✅ 当前在管理主页，可以进行完整检查', 'success');
                    this.checkAdminPageElements();
                } else {
                    this.log('elementCheck', '⚠️ 当前不在管理主页，建议在管理主页运行完整检查', 'warning');
                    this.log('elementCheck', '💡 请访问 admin/index.html 并打开浏览器控制台查看详细信息', 'info');
                }

                if (missingCurrentElements === 0) {
                    this.results.system.status = 'success';
                } else {
                    this.results.system.status = 'warning';
                    this.results.system.issues.push('建议在管理主页进行完整的元素检查');
                }
            }

            checkAdminPageElements() {
                this.log('elementCheck', '🔍 检查管理主页的关键元素...', 'info');

                const adminElements = {
                    'accessDenied': '访问受限提示',
                    'adminSection': '管理功能区域',
                    'userInfo': '用户信息区域',
                    'loginBtn': '登录按钮',
                    'realName': '用户姓名显示',
                    'totalUsers': '总用户数统计',
                    'activeUsers': '活跃用户统计',
                    'todayOperations': '今日操作统计',
                    'systemWarnings': '系统警告统计'
                };

                let missingElements = 0;
                Object.entries(adminElements).forEach(([id, name]) => {
                    const element = document.getElementById(id);
                    if (element) {
                        this.log('elementCheck', `✅ ${name} (${id}) 存在`, 'success');
                    } else {
                        this.log('elementCheck', `❌ ${name} (${id}) 不存在`, 'error');
                        missingElements++;
                        this.results.system.issues.push(`管理主页元素 ${id} 不存在`);
                    }
                });

                // 检查管理模块链接
                const moduleLinks = document.querySelectorAll('.admin-module-link');
                this.log('elementCheck', `📋 找到 ${moduleLinks.length} 个管理模块链接`, 'info');

                if (missingElements === 0) {
                    this.log('elementCheck', '✅ 管理主页所有关键元素都存在', 'success');
                    this.results.system.status = 'success';
                } else {
                    this.log('elementCheck', `❌ 管理主页发现 ${missingElements} 个缺失的元素`, 'error');
                    this.results.system.status = 'error';
                }
            }

            updateOverallStatus() {
                const statusMap = {
                    'success': '✅',
                    'warning': '⚠️',
                    'error': '❌',
                    'unknown': '❓'
                };

                document.getElementById('userStatus').textContent = statusMap[this.results.user.status];
                document.getElementById('roleStatus').textContent = statusMap[this.results.role.status];
                document.getElementById('permissionStatus').textContent = statusMap[this.results.permission.status];
                document.getElementById('systemStatus').textContent = statusMap[this.results.system.status];
            }

            generateSuggestions() {
                const suggestions = document.getElementById('suggestions');
                const allIssues = [
                    ...this.results.user.issues,
                    ...this.results.role.issues,
                    ...this.results.permission.issues,
                    ...this.results.system.issues
                ];

                if (allIssues.length === 0) {
                    this.log('suggestions', '🎉 恭喜！没有发现任何问题，权限系统运行正常。', 'success');
                    return;
                }

                this.log('suggestions', '🔧 发现以下问题，建议采取相应措施:', 'warning');

                allIssues.forEach(issue => {
                    this.log('suggestions', `• ${issue}`, 'error');
                });

                // 提供具体的修复建议
                this.log('suggestions', '', 'info');
                this.log('suggestions', '💡 修复建议:', 'info');

                if (this.results.user.issues.includes('用户未登录')) {
                    this.log('suggestions', '• 请先登录系统', 'info');
                }

                if (this.results.role.issues.includes('用户没有分配角色')) {
                    this.log('suggestions', '• 请联系管理员为您分配适当的角色', 'info');
                }

                if (this.results.role.issues.includes('用户没有管理员权限')) {
                    this.log('suggestions', '• 如需访问管理功能，请联系管理员提升权限', 'info');
                }

                if (this.results.permission.issues.includes('权限管理器未加载')) {
                    this.log('suggestions', '• 检查 permission-manager.js 文件是否正确加载', 'info');
                }

                if (this.results.system.issues.some(issue => issue.includes('页面元素'))) {
                    this.log('suggestions', '• 检查页面HTML结构是否完整', 'info');
                    this.log('suggestions', '• 使用 admin/element-check.html 进行详细的元素检查', 'info');
                    this.log('suggestions', '• 或在管理主页点击"检查元素"按钮进行实时检查', 'info');
                }
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            function initApp() {
                if (typeof AV !== 'undefined') {
                    const reportApp = new PermissionReportApp();
                    reportApp.init();
                } else {
                    setTimeout(initApp, 100);
                }
            }
            initApp();
        });
    </script>
</body>
</html>
