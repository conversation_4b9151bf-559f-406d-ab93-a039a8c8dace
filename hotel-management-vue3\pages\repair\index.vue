<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">报修管理</h1>
          <p class="mt-1 text-sm text-gray-600">
            管理酒店设备报修工单，跟踪维修进度
          </p>
        </div>
        
        <div class="mt-4 sm:mt-0 flex space-x-3">
          <button 
            @click="showCreateModal = true"
            class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center"
          >
            <Icon name="mdi:plus" size="16" class="mr-1" />
            新建报修
          </button>
          
          <button 
            @click="showFilterModal = true"
            class="border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 transition-colors flex items-center"
          >
            <Icon name="mdi:filter" size="16" class="mr-1" />
            筛选
          </button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6 mb-8">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:wrench" size="24" class="text-blue-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">总工单</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.total }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:clock-outline" size="24" class="text-orange-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">待处理</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.pending }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:progress-wrench" size="24" class="text-blue-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">处理中</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.inProgress }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:check-circle" size="24" class="text-green-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">已完成</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.completed }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:alert" size="24" class="text-red-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">紧急</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.urgent }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:timer" size="24" class="text-purple-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">平均时长</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.avgCompletionTime }}h</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速筛选标签 -->
    <div class="mb-6">
      <div class="flex flex-wrap gap-2">
        <button 
          @click="setQuickFilter('all')"
          :class="currentFilter === 'all' ? 'bg-blue-100 text-blue-700 border-blue-300' : 'bg-white text-gray-700 border-gray-300'"
          class="px-3 py-1 border rounded-full text-sm hover:bg-gray-50 transition-colors"
        >
          全部 ({{ stats.total }})
        </button>
        
        <button 
          @click="setQuickFilter('pending')"
          :class="currentFilter === 'pending' ? 'bg-orange-100 text-orange-700 border-orange-300' : 'bg-white text-gray-700 border-gray-300'"
          class="px-3 py-1 border rounded-full text-sm hover:bg-gray-50 transition-colors"
        >
          待处理 ({{ stats.pending }})
        </button>
        
        <button 
          @click="setQuickFilter('in_progress')"
          :class="currentFilter === 'in_progress' ? 'bg-blue-100 text-blue-700 border-blue-300' : 'bg-white text-gray-700 border-gray-300'"
          class="px-3 py-1 border rounded-full text-sm hover:bg-gray-50 transition-colors"
        >
          处理中 ({{ stats.inProgress }})
        </button>
        
        <button 
          @click="setQuickFilter('urgent')"
          :class="currentFilter === 'urgent' ? 'bg-red-100 text-red-700 border-red-300' : 'bg-white text-gray-700 border-gray-300'"
          class="px-3 py-1 border rounded-full text-sm hover:bg-gray-50 transition-colors"
        >
          紧急 ({{ stats.urgent }})
        </button>
        
        <button 
          v-if="authStore.user"
          @click="setQuickFilter('my')"
          :class="currentFilter === 'my' ? 'bg-green-100 text-green-700 border-green-300' : 'bg-white text-gray-700 border-gray-300'"
          class="px-3 py-1 border rounded-full text-sm hover:bg-gray-50 transition-colors"
        >
          我的报修 ({{ myRepairsCount }})
        </button>
        
        <button 
          v-if="authStore.user && (authStore.user.roles.includes('engineer') || authStore.isAdmin)"
          @click="setQuickFilter('assigned')"
          :class="currentFilter === 'assigned' ? 'bg-purple-100 text-purple-700 border-purple-300' : 'bg-white text-gray-700 border-gray-300'"
          class="px-3 py-1 border rounded-full text-sm hover:bg-gray-50 transition-colors"
        >
          分配给我 ({{ assignedToMeCount }})
        </button>
      </div>
    </div>

    <!-- 报修单列表 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <!-- 列表头部 -->
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900">工单列表</h3>
          <div class="flex items-center space-x-2">
            <button 
              @click="viewMode = 'list'"
              :class="viewMode === 'list' ? 'bg-blue-100 text-blue-700' : 'text-gray-500 hover:text-gray-700'"
              class="p-2 rounded-md transition-colors"
            >
              <Icon name="mdi:format-list-bulleted" size="16" />
            </button>
            <button 
              @click="viewMode = 'grid'"
              :class="viewMode === 'grid' ? 'bg-blue-100 text-blue-700' : 'text-gray-500 hover:text-gray-700'"
              class="p-2 rounded-md transition-colors"
            >
              <Icon name="mdi:view-grid" size="16" />
            </button>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="repairStore.loading && repairs.length === 0" class="p-6">
        <div class="space-y-4">
          <div v-for="i in 5" :key="i" class="animate-pulse">
            <div class="flex items-center space-x-4">
              <div class="w-12 h-12 bg-gray-200 rounded"></div>
              <div class="flex-1 space-y-2">
                <div class="h-4 bg-gray-200 rounded w-1/4"></div>
                <div class="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
              <div class="w-20 h-4 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else-if="repairs.length === 0" class="text-center py-12">
        <Icon name="mdi:wrench-outline" size="64" class="text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无报修工单</h3>
        <p class="text-gray-600 mb-6">还没有任何报修工单</p>
        <button 
          @click="showCreateModal = true"
          class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          创建报修工单
        </button>
      </div>

      <!-- 列表视图 -->
      <div v-else-if="viewMode === 'list'" class="divide-y divide-gray-200">
        <RepairListItem
          v-for="repair in repairs"
          :key="repair.id"
          :repair="repair"
          @view="handleViewRepair"
          @edit="handleEditRepair"
          @assign="handleAssignRepair"
          @start="handleStartRepair"
          @complete="handleCompleteRepair"
          @cancel="handleCancelRepair"
        />
      </div>

      <!-- 网格视图 -->
      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
        <RepairCard
          v-for="repair in repairs"
          :key="repair.id"
          :repair="repair"
          @view="handleViewRepair"
          @edit="handleEditRepair"
          @assign="handleAssignRepair"
          @start="handleStartRepair"
          @complete="handleCompleteRepair"
          @cancel="handleCancelRepair"
        />
      </div>
    </div>

    <!-- 模态框 -->
    <CreateRepairModal
      v-model:show="showCreateModal"
      :edit-repair="editingRepair"
      @created="handleRepairCreated"
      @updated="handleRepairUpdated"
    />

    <RepairDetailModal
      v-model:show="showDetailModal"
      :repair="selectedRepair"
    />

    <AssignRepairModal
      v-model:show="showAssignModal"
      :repair="selectedRepair"
      @assigned="handleRepairAssigned"
    />

    <CompleteRepairModal
      v-model:show="showCompleteModal"
      :repair="selectedRepair"
      @completed="handleRepairCompleted"
    />

    <RepairFilterModal
      v-model:show="showFilterModal"
      @apply="handleApplyFilters"
    />
  </div>
</template>

<script setup lang="ts">
import type { Repair } from '~/types'

// 导入组件
import CreateRepairModal from '~/components/repair/CreateRepairModal.vue'
import RepairDetailModal from '~/components/repair/RepairDetailModal.vue'
import AssignRepairModal from '~/components/repair/AssignRepairModal.vue'
import CompleteRepairModal from '~/components/repair/CompleteRepairModal.vue'
import RepairFilterModal from '~/components/repair/RepairFilterModal.vue'
import RepairListItem from '~/components/repair/RepairListItem.vue'
import RepairCard from '~/components/repair/RepairCard.vue'

// 页面元数据
definePageMeta({
  title: '报修管理',
  middleware: ['auth', 'permission'],
  permissions: ['repair.view']
})

// 状态管理
const repairStore = useRepairStore()
const authStore = useAuthStore()

// 响应式数据
const showCreateModal = ref(false)
const showDetailModal = ref(false)
const showAssignModal = ref(false)
const showCompleteModal = ref(false)
const showFilterModal = ref(false)
const editingRepair = ref<Repair | null>(null)
const selectedRepair = ref<Repair | null>(null)
const viewMode = ref<'list' | 'grid'>('list')
const currentFilter = ref('all')

// 计算属性
const repairs = computed(() => {
  let filtered = repairStore.filteredRepairs
  
  // 应用快速筛选
  switch (currentFilter.value) {
    case 'pending':
      filtered = filtered.filter(r => r.status === 'pending')
      break
    case 'in_progress':
      filtered = filtered.filter(r => r.status === 'in_progress')
      break
    case 'urgent':
      filtered = filtered.filter(r => r.priority === 'urgent')
      break
    case 'my':
      filtered = repairStore.myRepairs
      break
    case 'assigned':
      filtered = repairStore.assignedToMe
      break
  }
  
  return filtered
})

const stats = computed(() => repairStore.repairStats)
const myRepairsCount = computed(() => repairStore.myRepairs.length)
const assignedToMeCount = computed(() => repairStore.assignedToMe.length)

// 方法
const setQuickFilter = (filter: string) => {
  currentFilter.value = filter
}

const handleRepairCreated = () => {
  showCreateModal.value = false
  editingRepair.value = null
  repairStore.fetchRepairs({ page: 0 })
}

const handleRepairUpdated = () => {
  showCreateModal.value = false
  editingRepair.value = null
}

const handleViewRepair = (repair: Repair) => {
  selectedRepair.value = repair
  showDetailModal.value = true
}

const handleEditRepair = (repair: Repair) => {
  editingRepair.value = repair
  showCreateModal.value = true
}

const handleAssignRepair = (repair: Repair) => {
  selectedRepair.value = repair
  showAssignModal.value = true
}

const handleStartRepair = async (repair: Repair) => {
  const result = await repairStore.startRepair(repair.id)
  if (!result.success) {
    alert(result.error || '操作失败')
  }
}

const handleCompleteRepair = (repair: Repair) => {
  selectedRepair.value = repair
  showCompleteModal.value = true
}

const handleCancelRepair = async (repair: Repair) => {
  if (confirm('确定要取消这个报修工单吗？')) {
    const result = await repairStore.updateRepair(repair.id, { status: 'cancelled' })
    if (!result.success) {
      alert(result.error || '操作失败')
    }
  }
}

const handleRepairAssigned = () => {
  showAssignModal.value = false
  selectedRepair.value = null
}

const handleRepairCompleted = () => {
  showCompleteModal.value = false
  selectedRepair.value = null
}

const handleApplyFilters = (filters: any) => {
  showFilterModal.value = false
  repairStore.setFilters(filters)
}

// 生命周期
onMounted(async () => {
  await repairStore.fetchRepairs({ page: 0 })
})

// 页面标题
useHead({
  title: '报修管理 - 酒店管理系统'
})
</script>
