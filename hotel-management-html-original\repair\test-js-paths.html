<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试JS路径</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>🔧 JavaScript路径测试</h1>
    <p>测试repair模块中的JavaScript文件路径是否正确。</p>
    
    <div id="results"></div>

    <script>
        const jsFiles = [
            '../js/config.js',
            '../js/utils.js',
            '../js/auth.js',
            '../js/base-app.js',
            '../js/modules/repair.js'
        ];

        const resultsDiv = document.getElementById('results');

        function testJSFile(url) {
            return new Promise((resolve) => {
                const script = document.createElement('script');
                script.src = url;
                
                script.onload = function() {
                    resolve({ url, status: 'success', message: '加载成功' });
                };
                
                script.onerror = function() {
                    resolve({ url, status: 'error', message: '加载失败 - 404 Not Found' });
                };
                
                // 不实际添加到DOM，只测试加载
                document.head.appendChild(script);
                
                // 5秒超时
                setTimeout(() => {
                    resolve({ url, status: 'error', message: '加载超时' });
                }, 5000);
            });
        }

        async function testAllFiles() {
            resultsDiv.innerHTML = '<p>正在测试JavaScript文件路径...</p>';
            
            for (const file of jsFiles) {
                const result = await testJSFile(file);
                
                const div = document.createElement('div');
                div.className = `status ${result.status}`;
                div.innerHTML = `
                    <strong>${result.url}</strong><br>
                    状态: ${result.message}
                `;
                resultsDiv.appendChild(div);
            }
            
            // 测试完成后的总结
            const summary = document.createElement('div');
            summary.className = 'status';
            summary.style.background = '#d1ecf1';
            summary.style.color = '#0c5460';
            summary.innerHTML = `
                <strong>测试完成</strong><br>
                如果所有文件都显示"加载成功"，说明路径配置正确。<br>
                如果有文件显示"加载失败"，请检查文件是否存在于正确的位置。
            `;
            resultsDiv.appendChild(summary);
        }

        // 页面加载后开始测试
        window.addEventListener('load', testAllFiles);
    </script>
</body>
</html>
