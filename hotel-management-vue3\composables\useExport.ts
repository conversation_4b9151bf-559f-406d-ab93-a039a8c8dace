// composables/useExport.ts
// 数据导出工具组合式函数

import * as XLSX from 'xlsx'
import jsPDF from 'jspdf'
import 'jspdf-autotable'

// 扩展jsPDF类型
declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF
  }
}

export interface ExportColumn {
  key: string
  title: string
  width?: number
  formatter?: (value: any) => string
}

export interface ExportOptions {
  filename?: string
  title?: string
  columns: ExportColumn[]
  data: any[]
  format: 'excel' | 'pdf' | 'csv'
}

export function useExport() {
  
  // 格式化数据
  const formatData = (data: any[], columns: ExportColumn[]) => {
    return data.map(row => {
      const formattedRow: any = {}
      columns.forEach(col => {
        let value = row[col.key]
        
        // 应用格式化函数
        if (col.formatter && value !== undefined && value !== null) {
          value = col.formatter(value)
        }
        
        // 处理日期
        if (value instanceof Date) {
          value = value.toLocaleDateString('zh-CN')
        }
        
        // 处理数组
        if (Array.isArray(value)) {
          value = value.join(', ')
        }
        
        formattedRow[col.title] = value || ''
      })
      return formattedRow
    })
  }

  // 导出Excel
  const exportToExcel = (options: ExportOptions) => {
    try {
      const formattedData = formatData(options.data, options.columns)
      
      // 创建工作簿
      const wb = XLSX.utils.book_new()
      
      // 创建工作表
      const ws = XLSX.utils.json_to_sheet(formattedData)
      
      // 设置列宽
      const colWidths = options.columns.map(col => ({
        wch: col.width || 15
      }))
      ws['!cols'] = colWidths
      
      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(wb, ws, options.title || 'Sheet1')
      
      // 生成文件名
      const filename = options.filename || `export_${new Date().getTime()}.xlsx`
      
      // 导出文件
      XLSX.writeFile(wb, filename)
      
      return { success: true, filename }
    } catch (error: any) {
      console.error('Excel导出失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 导出PDF
  const exportToPDF = (options: ExportOptions) => {
    try {
      const doc = new jsPDF()
      
      // 设置中文字体（需要额外配置）
      // doc.setFont('helvetica')
      
      // 添加标题
      if (options.title) {
        doc.setFontSize(16)
        doc.text(options.title, 14, 20)
      }
      
      // 准备表格数据
      const headers = options.columns.map(col => col.title)
      const rows = options.data.map(row => 
        options.columns.map(col => {
          let value = row[col.key]
          
          if (col.formatter && value !== undefined && value !== null) {
            value = col.formatter(value)
          }
          
          if (value instanceof Date) {
            value = value.toLocaleDateString('zh-CN')
          }
          
          if (Array.isArray(value)) {
            value = value.join(', ')
          }
          
          return value || ''
        })
      )
      
      // 生成表格
      doc.autoTable({
        head: [headers],
        body: rows,
        startY: options.title ? 30 : 20,
        styles: {
          fontSize: 8,
          cellPadding: 2
        },
        headStyles: {
          fillColor: [66, 139, 202],
          textColor: 255
        },
        columnStyles: options.columns.reduce((styles, col, index) => {
          if (col.width) {
            styles[index] = { cellWidth: col.width }
          }
          return styles
        }, {} as any)
      })
      
      // 生成文件名
      const filename = options.filename || `export_${new Date().getTime()}.pdf`
      
      // 保存文件
      doc.save(filename)
      
      return { success: true, filename }
    } catch (error: any) {
      console.error('PDF导出失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 导出CSV
  const exportToCSV = (options: ExportOptions) => {
    try {
      const formattedData = formatData(options.data, options.columns)
      
      // 创建CSV内容
      const headers = options.columns.map(col => col.title).join(',')
      const rows = formattedData.map(row => 
        Object.values(row).map(value => 
          typeof value === 'string' && value.includes(',') 
            ? `"${value}"` 
            : value
        ).join(',')
      )
      
      const csvContent = [headers, ...rows].join('\n')
      
      // 添加BOM以支持中文
      const BOM = '\uFEFF'
      const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' })
      
      // 创建下载链接
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      
      const filename = options.filename || `export_${new Date().getTime()}.csv`
      link.setAttribute('download', filename)
      
      // 触发下载
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      return { success: true, filename }
    } catch (error: any) {
      console.error('CSV导出失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 导出文本格式（用于复制粘贴）
  const exportToText = (options: ExportOptions) => {
    try {
      const formattedData = formatData(options.data, options.columns)
      
      // 创建表格文本
      const headers = options.columns.map(col => col.title).join('\t')
      const rows = formattedData.map(row => 
        Object.values(row).join('\t')
      )
      
      const textContent = [headers, ...rows].join('\n')
      
      // 复制到剪贴板
      if (navigator.clipboard) {
        navigator.clipboard.writeText(textContent)
        return { success: true, message: '数据已复制到剪贴板' }
      } else {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = textContent
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        return { success: true, message: '数据已复制到剪贴板' }
      }
    } catch (error: any) {
      console.error('文本导出失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 统一导出方法
  const exportData = (options: ExportOptions) => {
    switch (options.format) {
      case 'excel':
        return exportToExcel(options)
      case 'pdf':
        return exportToPDF(options)
      case 'csv':
        return exportToCSV(options)
      default:
        return { success: false, error: '不支持的导出格式' }
    }
  }

  return {
    exportData,
    exportToExcel,
    exportToPDF,
    exportToCSV,
    exportToText
  }
}
