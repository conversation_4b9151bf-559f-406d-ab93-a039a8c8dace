/**
 * 性能优化模块
 * 提供缓存管理、分页、防抖节流等性能优化功能
 */

class PerformanceManager {
    constructor() {
        this.cache = new Map();
        this.ttlMap = new Map();
        this.defaultTTL = 5 * 60 * 1000; // 5分钟默认TTL
        this.maxCacheSize = 100;
        
        // 定期清理过期缓存
        this.startCacheCleanup();
    }

    /**
     * 缓存管理
     */
    
    /**
     * 设置缓存
     * @param {string} key - 缓存键
     * @param {any} value - 缓存值
     * @param {number} ttl - 生存时间（毫秒）
     */
    setCache(key, value, ttl = this.defaultTTL) {
        // 检查缓存大小限制
        if (this.cache.size >= this.maxCacheSize) {
            this.evictOldestCache();
        }

        this.cache.set(key, {
            value,
            timestamp: Date.now()
        });
        
        this.ttlMap.set(key, Date.now() + ttl);
    }

    /**
     * 获取缓存
     * @param {string} key - 缓存键
     * @returns {any} 缓存值或null
     */
    getCache(key) {
        if (!this.cache.has(key)) {
            return null;
        }

        // 检查是否过期
        if (this.isExpired(key)) {
            this.deleteCache(key);
            return null;
        }

        return this.cache.get(key).value;
    }

    /**
     * 删除缓存
     * @param {string} key - 缓存键
     */
    deleteCache(key) {
        this.cache.delete(key);
        this.ttlMap.delete(key);
    }

    /**
     * 清空所有缓存
     */
    clearCache() {
        this.cache.clear();
        this.ttlMap.clear();
    }

    /**
     * 检查缓存是否过期
     * @param {string} key - 缓存键
     * @returns {boolean} 是否过期
     */
    isExpired(key) {
        const expireTime = this.ttlMap.get(key);
        return expireTime && Date.now() > expireTime;
    }

    /**
     * 淘汰最旧的缓存
     */
    evictOldestCache() {
        let oldestKey = null;
        let oldestTime = Infinity;

        for (const [key, data] of this.cache.entries()) {
            if (data.timestamp < oldestTime) {
                oldestTime = data.timestamp;
                oldestKey = key;
            }
        }

        if (oldestKey) {
            this.deleteCache(oldestKey);
        }
    }

    /**
     * 启动缓存清理定时器
     */
    startCacheCleanup() {
        setInterval(() => {
            for (const key of this.cache.keys()) {
                if (this.isExpired(key)) {
                    this.deleteCache(key);
                }
            }
        }, 60000); // 每分钟清理一次
    }

    /**
     * 分页管理
     */
    
    /**
     * 创建分页查询
     * @param {AV.Query} query - LeanCloud查询对象
     * @param {Object} options - 分页选项
     * @returns {Object} 分页管理器
     */
    createPagination(query, options = {}) {
        const {
            pageSize = 20,
            cacheKey = null,
            cacheTTL = this.defaultTTL
        } = options;

        return {
            pageSize,
            currentPage: 1,
            totalCount: 0,
            totalPages: 0,
            data: [],
            loading: false,
            
            async loadPage(page = 1) {
                this.loading = true;
                
                try {
                    // 检查缓存
                    const cacheKeyForPage = cacheKey ? `${cacheKey}_page_${page}` : null;
                    if (cacheKeyForPage) {
                        const cachedData = performanceManager.getCache(cacheKeyForPage);
                        if (cachedData) {
                            this.currentPage = page;
                            this.data = cachedData.data;
                            this.totalCount = cachedData.totalCount;
                            this.totalPages = Math.ceil(this.totalCount / this.pageSize);
                            this.loading = false;
                            return cachedData;
                        }
                    }

                    // 获取总数
                    const totalCount = await query.count();
                    
                    // 设置分页参数
                    query.limit(this.pageSize);
                    query.skip((page - 1) * this.pageSize);
                    
                    // 执行查询
                    const results = await query.find();
                    
                    // 更新分页信息
                    this.currentPage = page;
                    this.totalCount = totalCount;
                    this.totalPages = Math.ceil(totalCount / this.pageSize);
                    this.data = results;
                    
                    // 缓存结果
                    if (cacheKeyForPage) {
                        performanceManager.setCache(cacheKeyForPage, {
                            data: results,
                            totalCount
                        }, cacheTTL);
                    }
                    
                    this.loading = false;
                    return { data: results, totalCount };
                    
                } catch (error) {
                    this.loading = false;
                    throw error;
                }
            },
            
            async nextPage() {
                if (this.currentPage < this.totalPages) {
                    return await this.loadPage(this.currentPage + 1);
                }
            },
            
            async prevPage() {
                if (this.currentPage > 1) {
                    return await this.loadPage(this.currentPage - 1);
                }
            },
            
            async firstPage() {
                return await this.loadPage(1);
            },
            
            async lastPage() {
                return await this.loadPage(this.totalPages);
            }
        };
    }

    /**
     * 防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} delay - 延迟时间（毫秒）
     * @returns {Function} 防抖后的函数
     */
    debounce(func, delay = 300) {
        let timeoutId;
        return function(...args) {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(this, args), delay);
        };
    }

    /**
     * 节流函数
     * @param {Function} func - 要节流的函数
     * @param {number} limit - 时间限制（毫秒）
     * @returns {Function} 节流后的函数
     */
    throttle(func, limit = 300) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * 批量操作管理
     * @param {Array} items - 要处理的项目数组
     * @param {Function} processor - 处理函数
     * @param {Object} options - 选项
     */
    async batchProcess(items, processor, options = {}) {
        const {
            batchSize = 10,
            delay = 100,
            onProgress = null,
            onError = null
        } = options;

        const results = [];
        const errors = [];
        
        for (let i = 0; i < items.length; i += batchSize) {
            const batch = items.slice(i, i + batchSize);
            
            try {
                const batchResults = await Promise.all(
                    batch.map(item => processor(item))
                );
                results.push(...batchResults);
                
                // 进度回调
                if (onProgress) {
                    onProgress({
                        processed: i + batch.length,
                        total: items.length,
                        percentage: Math.round(((i + batch.length) / items.length) * 100)
                    });
                }
                
                // 批次间延迟
                if (delay > 0 && i + batchSize < items.length) {
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
                
            } catch (error) {
                errors.push({
                    batch: i / batchSize,
                    items: batch,
                    error
                });
                
                if (onError) {
                    onError(error, batch);
                }
            }
        }
        
        return { results, errors };
    }

    /**
     * 图片懒加载
     * @param {string} selector - 图片选择器
     * @param {Object} options - 选项
     */
    lazyLoadImages(selector = 'img[data-src]', options = {}) {
        const {
            rootMargin = '50px',
            threshold = 0.1
        } = options;

        if (!('IntersectionObserver' in window)) {
            // 降级处理：直接加载所有图片
            document.querySelectorAll(selector).forEach(img => {
                if (img.dataset.src) {
                    img.src = img.dataset.src;
                    img.removeAttribute('data-src');
                }
            });
            return;
        }

        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.removeAttribute('data-src');
                        imageObserver.unobserve(img);
                    }
                }
            });
        }, {
            rootMargin,
            threshold
        });

        document.querySelectorAll(selector).forEach(img => {
            imageObserver.observe(img);
        });
    }

    /**
     * 虚拟滚动实现
     * @param {HTMLElement} container - 容器元素
     * @param {Array} data - 数据数组
     * @param {Function} renderItem - 渲染函数
     * @param {Object} options - 选项
     */
    createVirtualScroll(container, data, renderItem, options = {}) {
        const {
            itemHeight = 50,
            bufferSize = 5,
            containerHeight = container.clientHeight
        } = options;

        const visibleCount = Math.ceil(containerHeight / itemHeight);
        const totalHeight = data.length * itemHeight;
        
        let scrollTop = 0;
        let startIndex = 0;
        let endIndex = Math.min(visibleCount + bufferSize, data.length);

        // 创建虚拟容器
        const virtualContainer = document.createElement('div');
        virtualContainer.style.height = `${totalHeight}px`;
        virtualContainer.style.position = 'relative';
        
        const visibleContainer = document.createElement('div');
        visibleContainer.style.position = 'absolute';
        visibleContainer.style.top = '0';
        visibleContainer.style.width = '100%';
        
        virtualContainer.appendChild(visibleContainer);
        container.appendChild(virtualContainer);

        function updateVisibleItems() {
            startIndex = Math.floor(scrollTop / itemHeight);
            endIndex = Math.min(startIndex + visibleCount + bufferSize, data.length);
            
            visibleContainer.style.transform = `translateY(${startIndex * itemHeight}px)`;
            visibleContainer.innerHTML = '';
            
            for (let i = startIndex; i < endIndex; i++) {
                const item = renderItem(data[i], i);
                item.style.height = `${itemHeight}px`;
                visibleContainer.appendChild(item);
            }
        }

        container.addEventListener('scroll', this.throttle(() => {
            scrollTop = container.scrollTop;
            updateVisibleItems();
        }, 16)); // 60fps

        // 初始渲染
        updateVisibleItems();

        return {
            updateData(newData) {
                data = newData;
                virtualContainer.style.height = `${data.length * itemHeight}px`;
                updateVisibleItems();
            },
            scrollToIndex(index) {
                container.scrollTop = index * itemHeight;
            }
        };
    }

    /**
     * 获取性能指标
     * @returns {Object} 性能指标
     */
    getPerformanceMetrics() {
        const navigation = performance.getEntriesByType('navigation')[0];
        const paint = performance.getEntriesByType('paint');
        
        return {
            // 页面加载时间
            loadTime: navigation ? navigation.loadEventEnd - navigation.loadEventStart : 0,
            // DOM解析时间
            domParseTime: navigation ? navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart : 0,
            // 首次绘制时间
            firstPaint: paint.find(p => p.name === 'first-paint')?.startTime || 0,
            // 首次内容绘制时间
            firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0,
            // 缓存统计
            cacheStats: {
                size: this.cache.size,
                maxSize: this.maxCacheSize,
                hitRate: this.calculateCacheHitRate()
            }
        };
    }

    /**
     * 计算缓存命中率
     * @returns {number} 命中率百分比
     */
    calculateCacheHitRate() {
        // 这里需要实际的统计数据，简化实现
        return Math.round(Math.random() * 100);
    }
}

// 创建全局性能管理器实例
const performanceManager = new PerformanceManager();

// 导出到全局
if (typeof window !== 'undefined') {
    window.PerformanceManager = PerformanceManager;
    window.performanceManager = performanceManager;
}

// Node.js环境支持
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { PerformanceManager, performanceManager };
}
