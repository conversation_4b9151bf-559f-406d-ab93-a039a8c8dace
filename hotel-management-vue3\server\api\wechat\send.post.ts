// server/api/wechat/send.post.ts
export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  const { access_token, message } = body

  if (!access_token || !message) {
    throw createError({
      statusCode: 400,
      statusMessage: '缺少必要参数'
    })
  }

  try {
    // 发送消息到企业微信
    const response = await $fetch('https://qyapi.weixin.qq.com/cgi-bin/message/send', {
      method: 'POST',
      query: {
        access_token
      },
      body: message
    })

    if (response.errcode === 0) {
      return {
        success: true,
        msgid: response.msgid,
        response_code: response.response_code
      }
    } else {
      throw createError({
        statusCode: 400,
        statusMessage: `企业微信发送消息失败: ${response.errmsg}`
      })
    }
  } catch (error: any) {
    throw createError({
      statusCode: 500,
      statusMessage: error.message || '发送企业微信消息失败'
    })
  }
})
