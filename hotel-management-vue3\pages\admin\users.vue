<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">用户管理</h1>
          <p class="mt-1 text-sm text-gray-600">
            管理系统用户账户、权限和部门信息
          </p>
        </div>
        
        <div class="mt-4 sm:mt-0 flex space-x-3">
          <button 
            @click="showCreateModal = true"
            class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center"
          >
            <Icon name="mdi:plus" size="16" class="mr-1" />
            新增用户
          </button>
          
          <button
            @click="showFilterModal = true"
            class="border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 transition-colors flex items-center"
          >
            <Icon name="mdi:filter" size="16" class="mr-1" />
            筛选
          </button>

          <ExportButton
            :data="filteredUsers"
            :columns="exportColumns"
            filename="用户列表"
            title="用户管理数据"
            @exported="handleExported"
          />
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:account-group" size="24" class="text-blue-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">总用户</p>
            <p class="text-2xl font-semibold text-gray-900">{{ userStats.total }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:account-check" size="24" class="text-green-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">活跃用户</p>
            <p class="text-2xl font-semibold text-gray-900">{{ userStats.active }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:shield-account" size="24" class="text-purple-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">管理员</p>
            <p class="text-2xl font-semibold text-gray-900">{{ userStats.admin }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:account-plus" size="24" class="text-orange-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">本月新增</p>
            <p class="text-2xl font-semibold text-gray-900">{{ userStats.newThisMonth }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <h3 class="text-lg font-medium text-gray-900">用户列表</h3>
          
          <!-- 搜索框 -->
          <div class="mt-4 sm:mt-0 flex items-center space-x-3">
            <div class="relative">
              <Icon name="mdi:magnify" size="16" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input 
                v-model="searchQuery"
                type="text"
                placeholder="搜索用户名、姓名、部门..."
                class="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            
            <!-- 部门筛选 -->
            <select 
              v-model="selectedDepartment"
              class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">全部部门</option>
              <option value="前厅部">前厅部</option>
              <option value="客房部">客房部</option>
              <option value="餐饮部">餐饮部</option>
              <option value="工程部">工程部</option>
              <option value="保安部">保安部</option>
              <option value="财务部">财务部</option>
              <option value="人事部">人事部</option>
            </select>
          </div>
        </div>
      </div>

      <!-- 用户表格 -->
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                用户信息
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                部门/角色
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                最后登录
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                注册时间
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="user in filteredUsers" :key="user.id" class="hover:bg-gray-50">
              <!-- 用户信息 -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium mr-4">
                    {{ (user.realName || user.username).charAt(0) }}
                  </div>
                  <div>
                    <div class="text-sm font-medium text-gray-900">{{ user.realName || user.username }}</div>
                    <div class="text-sm text-gray-500">@{{ user.username }}</div>
                    <div v-if="user.phone" class="text-sm text-gray-500">{{ user.phone }}</div>
                  </div>
                </div>
              </td>

              <!-- 部门/角色 -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ user.department || '未设置' }}</div>
                <div class="flex space-x-1 mt-1">
                  <span 
                    v-for="role in user.roles" 
                    :key="role"
                    class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium"
                    :class="getRoleStyle(role)"
                  >
                    {{ getRoleText(role) }}
                  </span>
                </div>
              </td>

              <!-- 状态 -->
              <td class="px-6 py-4 whitespace-nowrap">
                <span 
                  class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                  :class="user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                >
                  <div 
                    class="w-1.5 h-1.5 rounded-full mr-1"
                    :class="user.isActive ? 'bg-green-400' : 'bg-red-400'"
                  ></div>
                  {{ user.isActive ? '活跃' : '禁用' }}
                </span>
              </td>

              <!-- 最后登录 -->
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ user.lastLoginAt ? formatTime(user.lastLoginAt) : '从未登录' }}
              </td>

              <!-- 注册时间 -->
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ formatDate(user.createdAt) }}
              </td>

              <!-- 操作 -->
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex items-center justify-end space-x-2">
                  <button 
                    @click="handleEditUser(user)"
                    class="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50"
                    title="编辑"
                  >
                    <Icon name="mdi:pencil" size="16" />
                  </button>
                  
                  <button 
                    @click="handleToggleUserStatus(user)"
                    class="p-1 rounded hover:bg-gray-50"
                    :class="user.isActive ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'"
                    :title="user.isActive ? '禁用' : '启用'"
                  >
                    <Icon :name="user.isActive ? 'mdi:account-off' : 'mdi:account-check'" size="16" />
                  </button>
                  
                  <button 
                    @click="handleResetPassword(user)"
                    class="text-orange-600 hover:text-orange-900 p-1 rounded hover:bg-orange-50"
                    title="重置密码"
                  >
                    <Icon name="mdi:key" size="16" />
                  </button>
                  
                  <button 
                    v-if="user.id !== authStore.user?.id"
                    @click="handleDeleteUser(user)"
                    class="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50"
                    title="删除"
                  >
                    <Icon name="mdi:delete" size="16" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredUsers.length === 0" class="text-center py-12">
        <Icon name="mdi:account-search" size="64" class="text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">未找到用户</h3>
        <p class="text-gray-600 mb-6">没有符合条件的用户</p>
      </div>
    </div>

    <!-- 模态框 -->
    <CreateUserModal
      v-model:show="showCreateModal"
      :edit-user="editingUser"
      @created="handleUserCreated"
      @updated="handleUserUpdated"
    />

    <UserFilterModal
      v-model:show="showFilterModal"
      @apply="handleApplyFilters"
    />
  </div>
</template>

<script setup lang="ts">
import type { User } from '~/types'

// 导入组件
import CreateUserModal from '~/components/admin/CreateUserModal.vue'
import UserFilterModal from '~/components/admin/UserFilterModal.vue'
import ExportButton from '~/components/common/ExportButton.vue'

// 页面元数据
definePageMeta({
  title: '用户管理',
  middleware: 'auth',
  layout: 'default'
})

// 检查管理员权限
const authStore = useAuthStore()
if (!authStore.isAdmin) {
  throw createError({
    statusCode: 403,
    statusMessage: '权限不足，无法访问用户管理页面'
  })
}

// 响应式数据
const showCreateModal = ref(false)
const showFilterModal = ref(false)
const editingUser = ref<User | null>(null)
const searchQuery = ref('')
const selectedDepartment = ref('')

const userStats = reactive({
  total: 0,
  active: 0,
  admin: 0,
  newThisMonth: 0
})

// 模拟用户数据
const users = ref<(User & { isActive?: boolean; lastLoginAt?: Date })[]>([
  {
    id: 'user1',
    username: 'admin',
    realName: '系统管理员',
    department: '信息部',
    phone: '13800138000',
    roles: ['admin'],
    isActive: true,
    lastLoginAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
    createdAt: new Date('2024-01-01')
  },
  {
    id: 'user2',
    username: 'frontdesk',
    realName: '前台小李',
    department: '前厅部',
    phone: '13800138001',
    roles: ['user'],
    isActive: true,
    lastLoginAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
    createdAt: new Date('2024-01-15')
  },
  {
    id: 'user3',
    username: 'engineer1',
    realName: '张工程师',
    department: '工程部',
    phone: '13800138002',
    roles: ['engineer'],
    isActive: true,
    lastLoginAt: new Date(Date.now() - 30 * 60 * 1000),
    createdAt: new Date('2024-02-01')
  }
])

// 计算属性
const filteredUsers = computed(() => {
  let filtered = users.value
  
  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(user => 
      user.username.toLowerCase().includes(query) ||
      (user.realName && user.realName.toLowerCase().includes(query)) ||
      (user.department && user.department.toLowerCase().includes(query)) ||
      (user.phone && user.phone.includes(query))
    )
  }
  
  // 部门筛选
  if (selectedDepartment.value) {
    filtered = filtered.filter(user => user.department === selectedDepartment.value)
  }
  
  return filtered
})

// 导出列配置
const exportColumns = [
  { key: 'username', title: '用户名', width: 15 },
  { key: 'realName', title: '真实姓名', width: 15 },
  { key: 'phone', title: '电话', width: 15 },
  { key: 'department', title: '部门', width: 15 },
  {
    key: 'roles',
    title: '角色',
    width: 20,
    formatter: (value: string | string[]) => {
      if (Array.isArray(value)) {
        return value.join(', ')
      }
      return value || ''
    }
  },
  {
    key: 'createdAt',
    title: '创建时间',
    width: 20,
    formatter: (value: Date) => {
      return value ? new Date(value).toLocaleString('zh-CN') : ''
    }
  }
]

// 方法
const loadUserStats = () => {
  userStats.total = users.value.length
  userStats.active = users.value.filter(u => u.isActive).length
  userStats.admin = users.value.filter(u => u.roles.includes('admin')).length
  
  const thisMonth = new Date()
  thisMonth.setDate(1)
  userStats.newThisMonth = users.value.filter(u => new Date(u.createdAt) >= thisMonth).length
}

const getRoleStyle = (role: string) => {
  const styles = {
    'admin': 'bg-red-100 text-red-800',
    'engineer': 'bg-blue-100 text-blue-800',
    'user': 'bg-gray-100 text-gray-800'
  }
  return styles[role as keyof typeof styles] || styles.user
}

const getRoleText = (role: string) => {
  const texts = {
    'admin': '管理员',
    'engineer': '工程师',
    'user': '普通用户'
  }
  return texts[role as keyof typeof texts] || '用户'
}

const formatTime = (date: Date) => {
  const now = new Date()
  const targetDate = new Date(date)
  const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000)
  
  if (diffInSeconds < 60) {
    return '刚刚'
  } else if (diffInSeconds < 3600) {
    return `${Math.floor(diffInSeconds / 60)}分钟前`
  } else if (diffInSeconds < 86400) {
    return `${Math.floor(diffInSeconds / 3600)}小时前`
  } else {
    return targetDate.toLocaleDateString('zh-CN')
  }
}

const formatDate = (date: Date) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

const handleEditUser = (user: User) => {
  editingUser.value = user
  showCreateModal.value = true
}

const handleToggleUserStatus = async (user: User & { isActive?: boolean }) => {
  if (user.id === authStore.user?.id) {
    alert('不能禁用自己的账户')
    return
  }
  
  const action = user.isActive ? '禁用' : '启用'
  if (confirm(`确定要${action}用户 ${user.realName || user.username} 吗？`)) {
    user.isActive = !user.isActive
    loadUserStats()
  }
}

const handleResetPassword = async (user: User) => {
  if (confirm(`确定要重置用户 ${user.realName || user.username} 的密码吗？`)) {
    // 这里应该调用API重置密码
    alert('密码重置成功，新密码已发送到用户手机')
  }
}

const handleDeleteUser = async (user: User) => {
  if (confirm(`确定要删除用户 ${user.realName || user.username} 吗？此操作不可恢复！`)) {
    const index = users.value.findIndex(u => u.id === user.id)
    if (index > -1) {
      users.value.splice(index, 1)
      loadUserStats()
    }
  }
}

const handleUserCreated = () => {
  showCreateModal.value = false
  editingUser.value = null
  // 重新加载用户列表
  loadUserStats()
}

const handleUserUpdated = () => {
  showCreateModal.value = false
  editingUser.value = null
  loadUserStats()
}

const handleApplyFilters = (filters: any) => {
  showFilterModal.value = false
  // 应用筛选条件
}

// 导出处理
const handleExported = (format: string, success: boolean) => {
  if (success) {
    console.log(`${format} 导出成功`)
  } else {
    console.error(`${format} 导出失败`)
  }
}

// 生命周期
onMounted(() => {
  loadUserStats()
})

// 页面标题
useHead({
  title: '用户管理 - 酒店管理系统'
})
</script>
