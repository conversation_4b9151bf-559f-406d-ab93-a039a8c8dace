<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>库存盘点 - 库存管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- LeanCloud SDK - 使用更稳定的 CDN 源 -->
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <!-- 备用 CDN 源 -->
    <script>
        window.addEventListener('load', function() {
            if (typeof AV === 'undefined') {
                console.log('主 CDN 加载失败，尝试备用源...');
                var script = document.createElement('script');
                script.src = 'https://cdn.bootcdn.net/ajax/libs/leancloud-storage/4.15.2/av-min.js';
                script.onerror = function() {
                    console.error('所有 CDN 源都加载失败，请检查网络连接');
                    alert('网络连接异常，请检查网络后刷新页面');
                };
                document.head.appendChild(script);
            }
        });
    </script>
    <style>
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .btn-fix {
            touch-action: manipulation;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="index.html" class="text-blue-600 hover:text-blue-800 mr-4">
                        ← 返回库存管理
                    </a>
                    <h1 class="text-xl font-semibold text-gray-900">库存盘点</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- 用户信息 -->
                    <div id="userInfo" class="flex items-center space-x-2" style="display: none;">
                        <span class="text-sm text-gray-700">用户：</span>
                        <span id="realName" class="text-sm font-medium text-gray-900"></span>
                        <button id="logoutBtn" class="text-sm text-red-600 hover:text-red-800 btn-fix">退出</button>
                    </div>
                    <button id="loginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                        登录
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 盘点操作 -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">库存盘点操作</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button id="startCountBtn" class="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg btn-fix">
                    <div class="text-center">
                        <svg class="w-6 h-6 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        <div>开始盘点</div>
                    </div>
                </button>
                <button id="continueCountBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg btn-fix">
                    <div class="text-center">
                        <svg class="w-6 h-6 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m2-10h.01M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                        </svg>
                        <div>继续盘点</div>
                    </div>
                </button>
                <button id="finishCountBtn" class="bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg btn-fix">
                    <div class="text-center">
                        <svg class="w-6 h-6 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>完成盘点</div>
                    </div>
                </button>
            </div>
        </div>

        <!-- 盘点表单 -->
        <div id="countForm" class="bg-white rounded-lg shadow p-6 mb-6" style="display: none;">
            <h2 class="text-lg font-medium text-gray-900 mb-4">商品盘点</h2>
            <form id="inventoryCountForm" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="productName" class="block text-sm font-medium text-gray-700 mb-1">商品名称</label>
                        <input type="text" id="productName" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <label for="productCode" class="block text-sm font-medium text-gray-700 mb-1">商品编码</label>
                        <input type="text" id="productCode" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <label for="warehouse" class="block text-sm font-medium text-gray-700 mb-1">盘点仓库</label>
                        <select id="warehouse" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">请选择仓库</option>
                            <option value="主仓库">主仓库</option>
                            <option value="备用仓库">备用仓库</option>
                            <option value="临时仓库">临时仓库</option>
                        </select>
                    </div>
                    <div>
                        <label for="location" class="block text-sm font-medium text-gray-700 mb-1">存放位置</label>
                        <input type="text" id="location" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <label for="systemQuantity" class="block text-sm font-medium text-gray-700 mb-1">系统数量</label>
                        <input type="number" id="systemQuantity" readonly class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50">
                    </div>
                    <div>
                        <label for="actualQuantity" class="block text-sm font-medium text-gray-700 mb-1">实际数量</label>
                        <input type="number" id="actualQuantity" required min="0" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <label for="unit" class="block text-sm font-medium text-gray-700 mb-1">单位</label>
                        <select id="unit" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">请选择单位</option>
                            <option value="个">个</option>
                            <option value="台">台</option>
                            <option value="套">套</option>
                            <option value="箱">箱</option>
                            <option value="包">包</option>
                        </select>
                    </div>
                    <div>
                        <label for="difference" class="block text-sm font-medium text-gray-700 mb-1">盘点差异</label>
                        <input type="number" id="difference" readonly class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50">
                    </div>
                </div>
                <div>
                    <label for="remarks" class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                    <textarea id="remarks" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"></textarea>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" id="resetBtn" class="px-4 py-2 text-gray-600 hover:text-gray-800 btn-fix">重置</button>
                    <button type="submit" class="bg-yellow-500 hover:bg-yellow-600 text-white px-6 py-2 rounded-md btn-fix">记录盘点</button>
                </div>
            </form>
        </div>

        <!-- 盘点记录 -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">盘点记录</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">商品信息</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">仓库位置</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">系统数量</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">实际数量</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">差异</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">盘点时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">盘点人</th>
                        </tr>
                    </thead>
                    <tbody id="countTableBody" class="bg-white divide-y divide-gray-200">
                        <!-- 盘点记录将在这里显示 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 引入模块化JS文件 -->
    <script src="../js/config.js"></script>
    <script src="../js/permission-manager.js"></script>
    <script>
        // 库存盘点应用类
        class InventoryCountApp {
            constructor() {
                this.elements = {};
                this.currentCountSession = null;
            }

            init() {
                this.initElements();
                this.bindEvents();
                this.checkAccess();
                this.loadCountRecords();
            }

            initElements() {
                this.elements = {
                    realName: document.getElementById('realName'),
                    userInfo: document.getElementById('userInfo'),
                    loginBtn: document.getElementById('loginBtn'),
                    logoutBtn: document.getElementById('logoutBtn'),
                    countForm: document.getElementById('countForm'),
                    inventoryCountForm: document.getElementById('inventoryCountForm'),
                    resetBtn: document.getElementById('resetBtn'),
                    countTableBody: document.getElementById('countTableBody'),
                    startCountBtn: document.getElementById('startCountBtn'),
                    continueCountBtn: document.getElementById('continueCountBtn'),
                    finishCountBtn: document.getElementById('finishCountBtn')
                };
            }

            bindEvents() {
                // 盘点操作按钮事件
                this.elements.startCountBtn.addEventListener('click', () => {
                    this.startCount();
                });

                this.elements.continueCountBtn.addEventListener('click', () => {
                    this.continueCount();
                });

                this.elements.finishCountBtn.addEventListener('click', () => {
                    this.finishCount();
                });

                // 表单提交事件
                this.elements.inventoryCountForm.addEventListener('submit', async (e) => {
                    e.preventDefault();
                    await this.handleCount();
                });

                // 重置按钮事件
                this.elements.resetBtn.addEventListener('click', () => {
                    this.elements.inventoryCountForm.reset();
                });

                // 实际数量变化时计算差异
                document.getElementById('actualQuantity').addEventListener('input', () => {
                    this.calculateDifference();
                });

                // 登录相关事件
                if (this.elements.loginBtn) {
                    this.elements.loginBtn.addEventListener('click', () => {
                        this.showLoginModal();
                    });
                }

                if (this.elements.logoutBtn) {
                    this.elements.logoutBtn.addEventListener('click', async () => {
                        try {
                            await AV.User.logOut();
                            this.checkAccess();
                        } catch (error) {
                            console.error('退出登录失败:', error);
                        }
                    });
                }
            }

            async checkAccess() {
                const currentUser = AV.User.current();
                
                if (currentUser) {
                    // 检查盘点权限
                    const hasCountAccess = await this.checkCountPermission(currentUser);
                    
                    if (hasCountAccess) {
                        // 更新用户信息显示
                        this.elements.realName.textContent = currentUser.get('realName') || currentUser.get('username');
                        this.elements.userInfo.style.display = 'flex';
                        this.elements.loginBtn.style.display = 'none';
                    } else {
                        alert('您没有库存盘点权限');
                        window.location.href = 'index.html';
                    }
                } else {
                    alert('请先登录');
                    window.location.href = 'index.html';
                }
            }

            async checkCountPermission(user) {
                const roles = user.get('roles') || [];
                
                // 管理员和超级管理员有盘点权限
                if (roles.includes('admin') || roles.includes('super_admin')) {
                    return true;
                }
                
                // 检查是否有特定的盘点权限
                if (window.permissionManager) {
                    try {
                        await window.permissionManager.init();
                        return window.permissionManager.hasPermission('inventory', 'count', 'create');
                    } catch (error) {
                        console.error('权限检查失败:', error);
                    }
                }
                
                return false;
            }

            startCount() {
                this.currentCountSession = new Date().toISOString();
                this.elements.countForm.style.display = 'block';
                alert('盘点已开始，请逐一录入商品信息');
            }

            continueCount() {
                if (!this.currentCountSession) {
                    alert('请先开始盘点');
                    return;
                }
                this.elements.countForm.style.display = 'block';
            }

            finishCount() {
                if (!this.currentCountSession) {
                    alert('没有进行中的盘点');
                    return;
                }
                
                if (confirm('确定要完成本次盘点吗？')) {
                    this.currentCountSession = null;
                    this.elements.countForm.style.display = 'none';
                    alert('盘点已完成');
                }
            }

            calculateDifference() {
                const systemQuantity = parseFloat(document.getElementById('systemQuantity').value) || 0;
                const actualQuantity = parseFloat(document.getElementById('actualQuantity').value) || 0;
                const difference = actualQuantity - systemQuantity;
                document.getElementById('difference').value = difference;
            }

            async handleCount() {
                try {
                    const currentUser = AV.User.current();
                    if (!currentUser) {
                        alert('请先登录');
                        return;
                    }

                    // 获取表单数据
                    const productCode = document.getElementById('productCode').value;
                    const actualQuantity = parseFloat(document.getElementById('actualQuantity').value);
                    const warehouse = document.getElementById('warehouse').value;

                    // 表单验证
                    if (!productCode) {
                        alert('请输入商品编码');
                        return;
                    }

                    if (isNaN(actualQuantity) || actualQuantity < 0) {
                        alert('请输入有效的实际数量');
                        return;
                    }

                    if (!warehouse) {
                        alert('请选择仓库');
                        return;
                    }

                    // 检查商品是否存在
                    const productQuery = new AV.Query('Product');
                    productQuery.equalTo('code', productCode);
                    const product = await productQuery.first();

                    if (!product) {
                        alert('商品不存在，请检查商品编码');
                        return;
                    }

                    // 自动填充商品信息
                    document.getElementById('productName').value = product.get('name');
                    document.getElementById('unit').value = product.get('unit');

                    const systemQuantity = product.get('currentStock') || 0;
                    document.getElementById('systemQuantity').value = systemQuantity;

                    const difference = actualQuantity - systemQuantity;
                    document.getElementById('difference').value = difference;

                    const formData = {
                        productName: product.get('name'),
                        productCode: productCode,
                        warehouse: warehouse,
                        location: document.getElementById('location').value,
                        systemQuantity: systemQuantity,
                        actualQuantity: actualQuantity,
                        difference: difference,
                        unit: product.get('unit'),
                        remarks: document.getElementById('remarks').value,
                        countSession: this.currentCountSession || new Date().toISOString().split('T')[0],
                        counter: currentUser.get('realName') || currentUser.get('username'),
                        counterName: currentUser.get('realName') || currentUser.get('username'),
                        counterId: currentUser.id,
                        countTime: new Date()
                    };

                    // 保存盘点记录
                    const CountRecord = AV.Object.extend('InventoryCountRecord');
                    const record = new CountRecord();
                    
                    Object.keys(formData).forEach(key => {
                        record.set(key, formData[key]);
                    });

                    await record.save();
                    
                    alert('盘点记录已保存');
                    this.elements.inventoryCountForm.reset();
                    this.loadCountRecords();

                } catch (error) {
                    console.error('保存盘点记录失败:', error);
                    alert('保存盘点记录失败: ' + error.message);
                }
            }

            async loadCountRecords() {
                try {
                    const query = new AV.Query('InventoryCountRecord');
                    query.descending('createdAt');
                    query.limit(20);

                    const records = await query.find();

                    if (records.length === 0) {
                        this.elements.countTableBody.innerHTML = `
                            <tr>
                                <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                    <div class="flex flex-col items-center py-8">
                                        <i class="fas fa-clipboard-check fa-3x text-gray-300 mb-4"></i>
                                        <p class="text-lg font-medium">暂无盘点记录</p>
                                        <p class="text-sm">请先进行库存盘点</p>
                                    </div>
                                </td>
                            </tr>
                        `;
                        return;
                    }

                    const html = records.map(record => {
                        const difference = record.get('difference');
                        const differenceClass = difference > 0 ? 'text-green-600' : difference < 0 ? 'text-red-600' : 'text-gray-900';

                        return `
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">${record.get('productName')}</div>
                                    <div class="text-sm text-gray-500">${record.get('productCode')}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">${record.get('warehouse') || '主仓库'}</div>
                                    <div class="text-sm text-gray-500">${record.get('location') || '-'}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    ${record.get('systemQuantity')} ${record.get('unit')}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    ${record.get('actualQuantity')} ${record.get('unit')}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm ${differenceClass}">
                                    ${difference > 0 ? '+' : ''}${difference} ${record.get('unit')}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    ${record.get('countTime') ? record.get('countTime').toLocaleString() : new Date(record.createdAt).toLocaleString()}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    ${record.get('counter') || record.get('counterName') || '未知'}
                                </td>
                            </tr>
                        `;
                    }).join('');

                    this.elements.countTableBody.innerHTML = html;

                } catch (error) {
                    console.error('加载盘点记录失败:', error);

                    // 如果是表不存在的错误，显示友好提示
                    if (error.code === 101 || error.message.includes("doesn't exists")) {
                        this.elements.countTableBody.innerHTML = `
                            <tr>
                                <td colspan="7" class="px-6 py-4 text-center text-blue-500">
                                    <div class="flex flex-col items-center py-8">
                                        <i class="fas fa-database fa-3x text-blue-300 mb-4"></i>
                                        <p class="text-lg font-medium">盘点记录表尚未创建</p>
                                        <p class="text-sm mb-4">请先进行库存盘点或初始化数据</p>
                                        <a href="init-data.html" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm">
                                            初始化数据
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        `;
                    } else {
                        this.elements.countTableBody.innerHTML = `
                            <tr>
                                <td colspan="7" class="px-6 py-4 text-center text-red-500">
                                    加载盘点记录失败: ${error.message}
                                </td>
                            </tr>
                        `;
                    }
                }
            }

            showLoginModal() {
                alert('请返回库存管理首页进行登录');
                window.location.href = 'index.html';
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            function initApp() {
                if (typeof AV !== 'undefined') {
                    const countApp = new InventoryCountApp();
                    countApp.init();
                } else {
                    setTimeout(initApp, 100);
                }
            }
            initApp();
        });
    </script>
</body>
</html>
