<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>任务详情</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- LeanCloud SDK -->
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <style>
        .status-pending { @apply bg-yellow-100 text-yellow-800; }
        .status-in-progress { @apply bg-blue-100 text-blue-800; }
        .status-completed { @apply bg-green-100 text-green-800; }
        .status-overdue { @apply bg-red-100 text-red-800; }
        .status-cancelled { @apply bg-gray-100 text-gray-800; }
        
        .priority-高 { @apply bg-red-100 text-red-800; }
        .priority-中 { @apply bg-yellow-100 text-yellow-800; }
        .priority-低 { @apply bg-green-100 text-green-800; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-4xl mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-3">
                    <a href="task-list.html" class="text-gray-600 hover:text-gray-800">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                    </a>
                    <h1 class="text-xl font-semibold text-gray-800">任务详情</h1>
                </div>
                <div id="userInfo" class="hidden items-center space-x-4">
                    <span id="realName" class="text-gray-800 font-medium"></span>
                    <button id="logoutBtn" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm">
                        退出登录
                    </button>
                </div>
                <button id="loginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm">
                    登录
                </button>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="max-w-4xl mx-auto px-4 py-6">
        <!-- 加载状态 -->
        <div id="loadingSection" class="text-center py-12">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">加载中...</p>
        </div>

        <!-- 错误状态 -->
        <div id="errorSection" class="hidden text-center py-12">
            <div class="mb-4">
                <svg class="mx-auto h-12 w-12 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">任务不存在</h3>
            <p class="text-gray-500 mb-4">请检查任务ID是否正确</p>
            <a href="task-list.html" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg">
                返回任务列表
            </a>
        </div>

        <!-- 任务详情 -->
        <div id="detailSection" class="hidden space-y-6">
            <!-- 任务状态卡片 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 id="taskTitle" class="text-2xl font-bold text-gray-800"></h2>
                    <div class="flex items-center space-x-3">
                        <span id="taskStatus" class="px-3 py-1 rounded-full text-sm font-medium"></span>
                        <span id="taskPriority" class="px-3 py-1 rounded-full text-sm font-medium"></span>
                    </div>
                </div>
                
                <!-- 进度条 -->
                <div class="mb-4">
                    <div class="flex justify-between text-sm text-gray-600 mb-2">
                        <span>任务进度</span>
                        <span id="progressText">0%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-3">
                        <div id="progressBar" class="bg-blue-500 h-3 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                </div>
                
                <!-- 时间信息 -->
                <div id="timeInfo" class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <!-- 时间信息将通过JS动态填充 -->
                </div>
            </div>

            <!-- 基本信息 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">基本信息</h3>
                <div id="basicInfo">
                    <!-- 基本信息将通过JS动态填充 -->
                </div>
            </div>

            <!-- 任务描述 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">任务描述</h3>
                <div id="taskDescription" class="text-gray-700 whitespace-pre-wrap">
                    <!-- 描述将通过JS动态填充 -->
                </div>
            </div>

            <!-- 附件 -->
            <div id="attachmentsSection" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hidden">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">附件</h3>
                <div id="attachmentsList">
                    <!-- 附件列表将通过JS动态填充 -->
                </div>
            </div>

            <!-- 进度记录和评论 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">进度记录</h3>
                <div id="commentsList" class="space-y-4">
                    <!-- 评论列表将通过JS动态填充 -->
                </div>
                
                <!-- 添加评论 -->
                <div id="addCommentSection" class="mt-6 pt-6 border-t border-gray-200">
                    <textarea id="newComment" rows="3" placeholder="添加评论或进度说明..."
                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></textarea>
                    <div class="flex justify-end mt-3">
                        <button id="addCommentBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                            添加评论
                        </button>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div id="actionButtons" class="flex justify-end space-x-4">
                <!-- 操作按钮将通过JS动态填充 -->
            </div>
        </div>
    </main>

    <!-- 登录弹窗 -->
    <div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-800">用户登录</h2>
            </div>
            <div class="p-6">
                <form id="loginForm" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                        <input type="text" id="loginUsername" required
                            class="w-full p-3 border border-gray-300 rounded-lg">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                        <input type="password" id="loginPassword" required
                            class="w-full p-3 border border-gray-300 rounded-lg">
                    </div>
                    <div class="flex gap-3 pt-4">
                        <button type="button" id="cancelLogin" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-3 rounded-lg">
                            取消
                        </button>
                        <button type="submit" class="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg">
                            登录
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="../js/config.js?v=2.1"></script>
    <script src="../js/core/error-handler.js?v=2.1"></script>
    <script src="../js/utils.js?v=2.1"></script>
    <script src="../js/auth.js?v=2.1"></script>
    <script src="../js/base-app.js?v=2.1"></script>
    <script>
        class TaskDetailApp extends BaseWorkLogApp {
            constructor() {
                super({
                    pageType: 'task-detail',
                    requiredElements: []
                });
                
                this.taskId = null;
                this.task = null;
            }

            getPageElements() {
                return {
                    // 头部元素
                    userInfo: 'userInfo',
                    realName: 'realName',
                    loginBtn: 'loginBtn',
                    logoutBtn: 'logoutBtn',
                    // 登录弹窗元素
                    loginModal: 'loginModal',
                    loginForm: 'loginForm',
                    loginUsername: 'loginUsername',
                    loginPassword: 'loginPassword',
                    cancelLogin: 'cancelLogin',
                    // 页面状态元素
                    loadingSection: 'loadingSection',
                    errorSection: 'errorSection',
                    detailSection: 'detailSection',
                    // 任务信息元素
                    taskTitle: 'taskTitle',
                    taskStatus: 'taskStatus',
                    taskPriority: 'taskPriority',
                    progressBar: 'progressBar',
                    progressText: 'progressText',
                    timeInfo: 'timeInfo',
                    basicInfo: 'basicInfo',
                    taskDescription: 'taskDescription',
                    attachmentsSection: 'attachmentsSection',
                    attachmentsList: 'attachmentsList',
                    commentsList: 'commentsList',
                    addCommentSection: 'addCommentSection',
                    newComment: 'newComment',
                    addCommentBtn: 'addCommentBtn',
                    actionButtons: 'actionButtons'
                };
            }

            bindPageEvents() {
                // 添加评论
                if (this.elements.addCommentBtn) {
                    this.elements.addCommentBtn.addEventListener('click', () => this.addComment());
                }
            }

            async init() {
                await super.init();
                
                // 获取URL参数中的任务ID
                this.taskId = this.getTaskIdFromURL();
                console.log('任务ID:', this.taskId);
                
                if (!this.taskId) {
                    this.showError();
                    return;
                }

                // 如果用户已登录，加载任务
                if (this.currentUser) {
                    await this.loadTask();
                }
            }

            onUserLoggedIn() {
                console.log('=== 任务详情页面：用户登录成功 ===');
                this.loadTask();
            }

            onUserLoggedOut() {
                window.location.href = '../index.html';
            }

            getTaskIdFromURL() {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get('id');
            }

            async loadTask() {
                try {
                    console.log('=== 加载任务详情 ===');
                    console.log('任务ID:', this.taskId);
                    
                    const Task = AV.Object.extend('Task');
                    const query = new AV.Query(Task);
                    
                    this.task = await query.get(this.taskId);
                    console.log('任务加载成功:', this.task.get('title'));
                    
                    // 检查权限
                    if (!this.hasPermission()) {
                        this.showError();
                        return;
                    }
                    
                    this.renderTask();
                    this.showDetail();

                } catch (error) {
                    console.error('=== 加载任务失败 ===');
                    console.error('错误详情:', error);
                    this.showError();
                }
            }

            hasPermission() {
                if (!this.task || !this.currentUser) return false;
                
                const userDept = this.currentUser.get('department');
                const creatorDept = this.task.get('creatorDept');
                const assigneeDept = this.task.get('assigneeDept');
                const creator = this.task.get('creator');
                const assignee = this.task.get('assignee');
                
                // 检查是否有权限查看
                return (
                    (creator && creator.id === this.currentUser.id) ||  // 创建人
                    (assignee && assignee.id === this.currentUser.id) || // 执行人
                    (creatorDept === userDept) ||  // 同部门（创建人）
                    (assigneeDept === userDept)    // 同部门（执行人）
                );
            }

            renderTask() {
                if (!this.task) return;

                // 渲染基本信息
                this.renderBasicInfo();
                
                // 渲染进度
                this.renderProgress();
                
                // 渲染时间信息
                this.renderTimeInfo();
                
                // 渲染描述
                this.renderDescription();
                
                // 渲染附件
                this.renderAttachments();
                
                // 渲染评论
                this.renderComments();
                
                // 渲染操作按钮
                this.renderActionButtons();
            }

            renderBasicInfo() {
                const title = this.task.get('title');
                const status = this.task.get('status');
                const priority = this.task.get('priority');
                const dueDate = this.task.get('dueDate');
                
                // 检查是否逾期
                const isOverdue = dueDate && new Date(dueDate) < new Date() && status !== 'completed';
                const displayStatus = isOverdue ? 'overdue' : status;
                
                this.elements.taskTitle.textContent = title;
                this.elements.taskStatus.textContent = this.getStatusText(displayStatus);
                this.elements.taskStatus.className = `px-3 py-1 rounded-full text-sm font-medium status-${displayStatus}`;
                this.elements.taskPriority.textContent = priority;
                this.elements.taskPriority.className = `px-3 py-1 rounded-full text-sm font-medium priority-${priority}`;

                // 基本信息表格
                this.elements.basicInfo.innerHTML = `
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="font-medium text-gray-700">任务ID</div>
                            <div class="text-gray-900">${this.task.get('taskId')}</div>
                        </div>
                        <div>
                            <div class="font-medium text-gray-700">优先级</div>
                            <div class="text-gray-900">${priority}</div>
                        </div>
                        <div>
                            <div class="font-medium text-gray-700">创建人</div>
                            <div class="text-gray-900">${this.task.get('creatorName')} (${this.task.get('creatorDept')})</div>
                        </div>
                        <div>
                            <div class="font-medium text-gray-700">执行人</div>
                            <div class="text-gray-900">${this.task.get('assigneeName')} (${this.task.get('assigneeDept')})</div>
                        </div>
                        <div>
                            <div class="font-medium text-gray-700">截止时间</div>
                            <div class="text-gray-900">${dueDate ? new Date(dueDate).toLocaleString() : '-'}</div>
                        </div>
                        <div>
                            <div class="font-medium text-gray-700">标签</div>
                            <div class="text-gray-900">${(this.task.get('tags') || []).join(', ') || '无'}</div>
                        </div>
                    </div>
                `;
            }

            renderProgress() {
                const progress = this.task.get('progress') || 0;
                this.elements.progressBar.style.width = `${progress}%`;
                this.elements.progressText.textContent = `${progress}%`;
            }

            renderTimeInfo() {
                const createTime = this.task.get('createTime');
                const startTime = this.task.get('startTime');
                const completeTime = this.task.get('completeTime');

                this.elements.timeInfo.innerHTML = `
                    <div>
                        <div class="font-medium text-gray-700">创建时间</div>
                        <div class="text-gray-600">${createTime ? new Date(createTime).toLocaleString() : '-'}</div>
                    </div>
                    <div>
                        <div class="font-medium text-gray-700">开始时间</div>
                        <div class="text-gray-600">${startTime ? new Date(startTime).toLocaleString() : '-'}</div>
                    </div>
                    <div>
                        <div class="font-medium text-gray-700">完成时间</div>
                        <div class="text-gray-600">${completeTime ? new Date(completeTime).toLocaleString() : '-'}</div>
                    </div>
                `;
            }

            renderDescription() {
                this.elements.taskDescription.textContent = this.task.get('description');
            }

            renderAttachments() {
                const attachments = this.task.get('attachments') || [];
                if (attachments.length === 0) {
                    this.elements.attachmentsSection.classList.add('hidden');
                    return;
                }

                this.elements.attachmentsSection.classList.remove('hidden');
                // TODO: 实现附件显示逻辑
            }

            renderComments() {
                const comments = this.task.get('comments') || [];
                
                if (comments.length === 0) {
                    this.elements.commentsList.innerHTML = `
                        <div class="text-center text-gray-500 py-4">
                            暂无进度记录
                        </div>
                    `;
                    return;
                }

                const commentsHtml = comments.map(comment => `
                    <div class="border-l-4 border-blue-200 pl-4">
                        <div class="flex justify-between items-start mb-1">
                            <span class="font-medium text-gray-800">${comment.author}</span>
                            <span class="text-sm text-gray-500">${new Date(comment.createTime).toLocaleString()}</span>
                        </div>
                        <div class="text-gray-700">${comment.content}</div>
                    </div>
                `).join('');

                this.elements.commentsList.innerHTML = commentsHtml;
            }

            renderActionButtons() {
                const status = this.task.get('status');
                const assignee = this.task.get('assignee');
                const creator = this.task.get('creator');
                const currentUserId = this.currentUser.id;
                
                let buttons = [];

                // 返回列表按钮
                buttons.push(`<button onclick="window.location.href='task-list.html'" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">返回列表</button>`);

                // 如果是分配给自己的任务且未完成，显示更新进度按钮
                if (assignee && assignee.id === currentUserId && status !== 'completed') {
                    buttons.push(`<button onclick="window.location.href='task-list.html'" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">更新进度</button>`);
                }

                // 如果是自己创建的任务且未完成，显示编辑按钮
                if (creator && creator.id === currentUserId && status !== 'completed') {
                    buttons.push(`<button onclick="alert('编辑功能开发中')" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg">编辑任务</button>`);
                }

                this.elements.actionButtons.innerHTML = buttons.join('');
            }

            async addComment() {
                const content = this.elements.newComment.value.trim();
                if (!content) {
                    alert('请输入评论内容');
                    return;
                }

                try {
                    const comments = this.task.get('comments') || [];
                    comments.push({
                        type: 'comment',
                        author: this.currentUser.get('realName') || this.currentUser.get('username'),
                        content: content,
                        createTime: new Date()
                    });

                    this.task.set('comments', comments);
                    await this.task.save();

                    this.elements.newComment.value = '';
                    this.renderComments();
                    
                    alert('评论添加成功！');

                } catch (error) {
                    console.error('添加评论失败:', error);
                    alert('添加评论失败: ' + error.message);
                }
            }

            getStatusText(status) {
                const statusMap = {
                    'pending': '待开始',
                    'in-progress': '进行中',
                    'completed': '已完成',
                    'overdue': '已逾期',
                    'cancelled': '已取消'
                };
                return statusMap[status] || status;
            }

            showLoading() {
                this.elements.loadingSection?.classList.remove('hidden');
                this.elements.errorSection?.classList.add('hidden');
                this.elements.detailSection?.classList.add('hidden');
            }

            showError() {
                this.elements.loadingSection?.classList.add('hidden');
                this.elements.detailSection?.classList.add('hidden');
                this.elements.errorSection?.classList.remove('hidden');
            }

            showDetail() {
                this.elements.loadingSection?.classList.add('hidden');
                this.elements.errorSection?.classList.add('hidden');
                this.elements.detailSection?.classList.remove('hidden');
            }
        }

        // 全局变量
        let taskDetailApp;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            function initApp() {
                if (typeof AV !== 'undefined') {
                    taskDetailApp = new TaskDetailApp();
                    taskDetailApp.init();
                } else {
                    setTimeout(initApp, 100);
                }
            }
            initApp();
        });
    </script>
</body>
</html>
