<template>
  <div 
    v-if="show" 
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    @click="handleBackdropClick"
  >
    <div 
      class="bg-white rounded-lg w-full max-w-md"
      @click.stop
    >
      <!-- 头部 -->
      <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-900">筛选工作日志</h3>
        <button 
          @click="handleClose"
          class="text-gray-400 hover:text-gray-600"
        >
          <Icon name="mdi:close" size="24" />
        </button>
      </div>

      <!-- 内容 -->
      <div class="px-6 py-4">
        <form @submit.prevent="handleApply">
          <!-- 日志类型筛选 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              日志类型
            </label>
            <select 
              v-model="filters.type"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">全部类型</option>
              <option value="daily">日常工作</option>
              <option value="maintenance">设备维护</option>
              <option value="cleaning">清洁工作</option>
              <option value="security">安全检查</option>
              <option value="guest_service">客户服务</option>
              <option value="training">培训学习</option>
              <option value="meeting">会议记录</option>
              <option value="other">其他</option>
            </select>
          </div>

          <!-- 页面类型筛选 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              页面类型
            </label>
            <select 
              v-model="filters.pageType"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">全部页面</option>
              <option value="front_desk">前台</option>
              <option value="housekeeping">客房</option>
              <option value="restaurant">餐厅</option>
              <option value="engineering">工程</option>
              <option value="security">保安</option>
              <option value="management">管理</option>
            </select>
          </div>

          <!-- 用户筛选 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              创建人
            </label>
            <input 
              v-model="filters.userName"
              type="text"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="输入用户名或姓名"
            />
          </div>

          <!-- 时间范围筛选 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              创建时间
            </label>
            <div class="grid grid-cols-2 gap-3">
              <div>
                <input 
                  v-model="filters.startDate"
                  type="date"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <div class="text-xs text-gray-500 mt-1">开始日期</div>
              </div>
              <div>
                <input 
                  v-model="filters.endDate"
                  type="date"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <div class="text-xs text-gray-500 mt-1">结束日期</div>
              </div>
            </div>
          </div>

          <!-- 快速筛选 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-3">
              快速筛选
            </label>
            <div class="space-y-2">
              <label class="flex items-center">
                <input 
                  v-model="filters.hasImages"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span class="ml-2 text-sm text-gray-700">包含图片</span>
              </label>
              
              <label class="flex items-center">
                <input 
                  v-model="filters.todayOnly"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span class="ml-2 text-sm text-gray-700">仅显示今天</span>
              </label>
              
              <label class="flex items-center">
                <input 
                  v-model="filters.myLogsOnly"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span class="ml-2 text-sm text-gray-700">仅显示我的日志</span>
              </label>
            </div>
          </div>

          <!-- 关键词搜索 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              关键词搜索
            </label>
            <input 
              v-model="filters.keyword"
              type="text"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="搜索标题、内容等"
            />
          </div>
        </form>
      </div>

      <!-- 底部按钮 -->
      <div class="px-6 py-4 border-t border-gray-200 flex justify-between">
        <button 
          type="button"
          @click="handleReset"
          class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
        >
          重置筛选
        </button>
        
        <div class="flex space-x-3">
          <button 
            type="button"
            @click="handleClose"
            class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
          >
            取消
          </button>
          <button 
            @click="handleApply"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            应用筛选
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  show: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:show': [value: boolean]
  apply: [filters: any]
}>()

// 响应式数据
const filters = reactive({
  type: '',
  pageType: '',
  userName: '',
  startDate: '',
  endDate: '',
  hasImages: false,
  todayOnly: false,
  myLogsOnly: false,
  keyword: ''
})

// 方法
const resetFilters = () => {
  filters.type = ''
  filters.pageType = ''
  filters.userName = ''
  filters.startDate = ''
  filters.endDate = ''
  filters.hasImages = false
  filters.todayOnly = false
  filters.myLogsOnly = false
  filters.keyword = ''
}

const handleClose = () => {
  emit('update:show', false)
}

const handleBackdropClick = () => {
  handleClose()
}

const handleReset = () => {
  resetFilters()
}

const handleApply = () => {
  // 构建筛选对象，过滤掉空值
  const appliedFilters: any = {}
  
  if (filters.type) appliedFilters.type = filters.type
  if (filters.pageType) appliedFilters.pageType = filters.pageType
  if (filters.userName.trim()) appliedFilters.userName = filters.userName.trim()
  if (filters.startDate) appliedFilters.startDate = new Date(filters.startDate)
  if (filters.endDate) appliedFilters.endDate = new Date(filters.endDate)
  if (filters.hasImages) appliedFilters.hasImages = filters.hasImages
  if (filters.todayOnly) appliedFilters.todayOnly = filters.todayOnly
  if (filters.myLogsOnly) appliedFilters.myLogsOnly = filters.myLogsOnly
  if (filters.keyword.trim()) appliedFilters.keyword = filters.keyword.trim()
  
  emit('apply', appliedFilters)
  handleClose()
}

// 键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.show) {
    handleClose()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>
