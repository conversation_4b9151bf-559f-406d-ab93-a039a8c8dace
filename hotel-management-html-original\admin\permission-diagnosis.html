<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限诊断 - 系统管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
</head>
<body class="bg-gray-50 min-h-screen p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold mb-8">权限诊断工具</h1>
        
        <!-- 当前用户信息 -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-medium mb-4">当前用户信息</h2>
            <div id="currentUserInfo" class="space-y-2 text-sm">
                <!-- 用户信息将在这里显示 -->
            </div>
        </div>

        <!-- 权限测试 -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-medium mb-4">权限测试</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <button id="testUserRead" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                    测试读取用户
                </button>
                <button id="testUserWrite" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">
                    测试写入用户
                </button>
                <button id="testMasterKey" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded">
                    测试Master Key
                </button>
                <button id="fixRoleData" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded">
                    修复角色数据
                </button>
            </div>
            <div id="testResults" class="space-y-2 text-sm">
                <!-- 测试结果将在这里显示 -->
            </div>
        </div>

        <!-- 用户角色数据分析 -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-medium mb-4">用户角色数据分析</h2>
            <div id="roleAnalysis" class="space-y-2 text-sm">
                <!-- 角色分析结果将在这里显示 -->
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="space-x-4">
            <button id="runFullDiagnosis" class="bg-indigo-500 hover:bg-indigo-600 text-white px-6 py-2 rounded">
                运行完整诊断
            </button>
            <a href="users.html" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded inline-block">
                返回用户管理
            </a>
        </div>
    </div>

    <!-- 引入配置文件 -->
    <script src="../js/config.js"></script>
    <script src="../js/permission-manager.js"></script>
    <script>
        class PermissionDiagnosisApp {
            constructor() {
                this.currentUserInfo = document.getElementById('currentUserInfo');
                this.testResults = document.getElementById('testResults');
                this.roleAnalysis = document.getElementById('roleAnalysis');
            }

            init() {
                this.bindEvents();
                this.showCurrentUserInfo();
                this.analyzeRoleData();
            }

            bindEvents() {
                document.getElementById('testUserRead').addEventListener('click', () => {
                    this.testUserRead();
                });

                document.getElementById('testUserWrite').addEventListener('click', () => {
                    this.testUserWrite();
                });

                document.getElementById('testMasterKey').addEventListener('click', () => {
                    this.testMasterKey();
                });

                document.getElementById('fixRoleData').addEventListener('click', () => {
                    this.fixRoleData();
                });

                document.getElementById('runFullDiagnosis').addEventListener('click', () => {
                    this.runFullDiagnosis();
                });
            }

            log(containerId, message, type = 'info') {
                const colors = {
                    'success': 'text-green-600',
                    'error': 'text-red-600',
                    'warning': 'text-yellow-600',
                    'info': 'text-blue-600'
                };

                const div = document.createElement('div');
                div.className = colors[type] || 'text-gray-600';
                div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
                document.getElementById(containerId).appendChild(div);
            }

            showCurrentUserInfo() {
                const currentUser = AV.User.current();
                if (!currentUser) {
                    this.log('currentUserInfo', '用户未登录', 'error');
                    return;
                }

                const roles = currentUser.get('roles') || [];
                this.log('currentUserInfo', `用户名: ${currentUser.get('username')}`, 'info');
                this.log('currentUserInfo', `真实姓名: ${currentUser.get('realName') || '未设置'}`, 'info');
                this.log('currentUserInfo', `用户ID: ${currentUser.id}`, 'info');
                this.log('currentUserInfo', `角色数据: ${JSON.stringify(roles)}`, 'info');
                this.log('currentUserInfo', `角色类型: ${typeof roles}`, 'info');
                this.log('currentUserInfo', `是否数组: ${Array.isArray(roles)}`, 'info');
                this.log('currentUserInfo', `部门: ${currentUser.get('department') || '未设置'}`, 'info');
                this.log('currentUserInfo', `注册时间: ${currentUser.createdAt.toLocaleString()}`, 'info');
            }

            async testUserRead() {
                this.log('testResults', '测试读取用户权限...', 'info');
                
                try {
                    const query = new AV.Query('_User');
                    query.limit(5);
                    const users = await query.find();
                    this.log('testResults', `✅ 成功读取 ${users.length} 个用户`, 'success');
                } catch (error) {
                    this.log('testResults', `❌ 读取用户失败: ${error.message}`, 'error');
                }
            }

            async testUserWrite() {
                this.log('testResults', '测试写入用户权限...', 'info');
                
                try {
                    const currentUser = AV.User.current();
                    if (!currentUser) {
                        this.log('testResults', '❌ 用户未登录', 'error');
                        return;
                    }

                    // 尝试更新当前用户的一个无关紧要的字段
                    const testField = 'lastDiagnosisTime';
                    currentUser.set(testField, new Date());
                    await currentUser.save();
                    this.log('testResults', '✅ 成功更新用户数据（普通权限）', 'success');
                } catch (error) {
                    this.log('testResults', `❌ 更新用户失败: ${error.message}`, 'error');
                }
            }

            async testMasterKey() {
                this.log('testResults', '测试Master Key权限...', 'info');
                
                try {
                    const currentUser = AV.User.current();
                    if (!currentUser) {
                        this.log('testResults', '❌ 用户未登录', 'error');
                        return;
                    }

                    // 尝试使用master key更新用户
                    const testField = 'lastMasterKeyTest';
                    currentUser.set(testField, new Date());
                    await currentUser.save(null, { useMasterKey: true });
                    this.log('testResults', '✅ Master Key权限可用', 'success');
                } catch (error) {
                    this.log('testResults', `❌ Master Key测试失败: ${error.message}`, 'error');
                }
            }

            async analyzeRoleData() {
                this.log('roleAnalysis', '分析用户角色数据...', 'info');
                
                try {
                    const query = new AV.Query('_User');
                    query.limit(100);
                    const users = await query.find();
                    
                    let roleStats = {
                        total: users.length,
                        withRoles: 0,
                        withoutRoles: 0,
                        stringFormat: 0,
                        arrayFormat: 0,
                        invalidFormat: 0,
                        needsFix: 0
                    };

                    users.forEach(user => {
                        const roles = user.get('roles');
                        
                        if (!roles || (Array.isArray(roles) && roles.length === 0)) {
                            roleStats.withoutRoles++;
                        } else {
                            roleStats.withRoles++;
                            
                            if (typeof roles === 'string') {
                                roleStats.stringFormat++;
                                // 检查是否需要修复
                                try {
                                    JSON.parse(roles);
                                    roleStats.needsFix++;
                                } catch (e) {
                                    // 单个字符串角色，也需要转换为数组
                                    roleStats.needsFix++;
                                }
                            } else if (Array.isArray(roles)) {
                                roleStats.arrayFormat++;
                            } else {
                                roleStats.invalidFormat++;
                                roleStats.needsFix++;
                            }
                        }
                    });

                    this.log('roleAnalysis', `📊 角色数据统计:`, 'info');
                    this.log('roleAnalysis', `总用户数: ${roleStats.total}`, 'info');
                    this.log('roleAnalysis', `有角色用户: ${roleStats.withRoles}`, 'info');
                    this.log('roleAnalysis', `无角色用户: ${roleStats.withoutRoles}`, 'info');
                    this.log('roleAnalysis', `字符串格式: ${roleStats.stringFormat}`, 'warning');
                    this.log('roleAnalysis', `数组格式: ${roleStats.arrayFormat}`, 'success');
                    this.log('roleAnalysis', `无效格式: ${roleStats.invalidFormat}`, 'error');
                    this.log('roleAnalysis', `需要修复: ${roleStats.needsFix}`, roleStats.needsFix > 0 ? 'warning' : 'success');

                } catch (error) {
                    this.log('roleAnalysis', `❌ 分析失败: ${error.message}`, 'error');
                }
            }

            async fixRoleData() {
                if (!confirm('确定要修复所有用户的角色数据吗？')) {
                    return;
                }

                this.log('testResults', '开始修复角色数据...', 'info');
                
                try {
                    const query = new AV.Query('_User');
                    query.limit(100);
                    const users = await query.find();
                    
                    let fixedCount = 0;
                    let errorCount = 0;

                    for (const user of users) {
                        try {
                            const currentRoles = user.get('roles');
                            let normalizedRoles = [];

                            if (currentRoles) {
                                if (typeof currentRoles === 'string') {
                                    try {
                                        const parsed = JSON.parse(currentRoles);
                                        normalizedRoles = Array.isArray(parsed) ? parsed : [currentRoles];
                                    } catch (e) {
                                        normalizedRoles = [currentRoles];
                                    }
                                } else if (Array.isArray(currentRoles)) {
                                    normalizedRoles = currentRoles;
                                } else {
                                    normalizedRoles = [String(currentRoles)];
                                }
                            }

                            if (JSON.stringify(currentRoles) !== JSON.stringify(normalizedRoles)) {
                                user.set('roles', normalizedRoles);
                                await user.save(null, { useMasterKey: true });
                                fixedCount++;
                                this.log('testResults', `修复用户 ${user.get('username')}: ${JSON.stringify(currentRoles)} -> ${JSON.stringify(normalizedRoles)}`, 'success');
                            }
                        } catch (error) {
                            errorCount++;
                            this.log('testResults', `修复用户 ${user.get('username')} 失败: ${error.message}`, 'error');
                        }
                    }

                    this.log('testResults', `修复完成！成功: ${fixedCount}, 失败: ${errorCount}`, 'info');
                } catch (error) {
                    this.log('testResults', `❌ 修复失败: ${error.message}`, 'error');
                }
            }

            async runFullDiagnosis() {
                this.testResults.innerHTML = '';
                this.roleAnalysis.innerHTML = '';
                
                this.log('testResults', '开始完整诊断...', 'info');
                
                await this.testUserRead();
                await this.testUserWrite();
                await this.testMasterKey();
                await this.analyzeRoleData();
                
                this.log('testResults', '完整诊断完成！', 'success');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            function initApp() {
                if (typeof AV !== 'undefined') {
                    const diagnosisApp = new PermissionDiagnosisApp();
                    diagnosisApp.init();
                } else {
                    setTimeout(initApp, 100);
                }
            }
            initApp();
        });
    </script>
</body>
</html>
