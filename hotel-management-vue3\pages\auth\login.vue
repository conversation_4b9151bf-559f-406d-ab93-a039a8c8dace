<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- 头部 -->
      <div class="text-center">
        <Icon name="mdi:hotel" size="48" class="mx-auto text-blue-600" />
        <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
          登录账户
        </h2>
        <p class="mt-2 text-sm text-gray-600">
          或者
          <NuxtLink to="/auth/register" class="font-medium text-blue-600 hover:text-blue-500">
            注册新账户
          </NuxtLink>
        </p>
      </div>

      <!-- 登录表单 -->
      <div class="bg-white py-8 px-6 shadow-lg rounded-lg">
        <form @submit.prevent="handleSubmit">
          <div class="mb-4">
            <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
              用户名
            </label>
            <div class="relative">
              <Icon name="mdi:account" size="16" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                id="username"
                v-model="formData.username"
                type="text"
                required
                :disabled="loading"
                class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="请输入用户名"
              />
            </div>
          </div>

          <div class="mb-4">
            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
              密码
            </label>
            <div class="relative">
              <Icon name="mdi:lock" size="16" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                id="password"
                v-model="formData.password"
                :type="showPassword ? 'text' : 'password'"
                required
                :disabled="loading"
                class="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="请输入密码"
              />
              <button
                type="button"
                @click="showPassword = !showPassword"
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <Icon :name="showPassword ? 'mdi:eye-off' : 'mdi:eye'" size="16" />
              </button>
            </div>
          </div>

          <div class="flex items-center justify-between mb-6">
            <div class="flex items-center">
              <input
                id="remember-me"
                v-model="rememberMe"
                type="checkbox"
                :disabled="loading"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label for="remember-me" class="ml-2 block text-sm text-gray-900">
                记住我
              </label>
            </div>
            <NuxtLink to="/auth/forgot-password" class="text-sm text-blue-600 hover:text-blue-500">
              忘记密码？
            </NuxtLink>
          </div>

          <button
            type="submit"
            :disabled="loading"
            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {{ loading ? '登录中...' : '登录' }}
          </button>
        </form>

        <!-- 错误提示 -->
        <div v-if="errorMessage" class="mt-4">
          <div class="bg-red-50 border border-red-200 rounded-md p-3">
            <div class="flex">
              <Icon name="mdi:alert-circle" size="16" class="text-red-400 mr-2 mt-0.5" />
              <div class="text-sm text-red-700">
                {{ errorMessage }}
              </div>
            </div>
          </div>
        </div>

        <!-- 快速登录提示 -->
        <div class="mt-6 text-center">
          <p class="text-xs text-gray-500">
            测试账号：admin / 123456
          </p>
        </div>
      </div>

      <!-- 底部链接 -->
      <div class="text-center">
        <p class="text-sm text-gray-600">
          还没有账户？
          <NuxtLink to="/auth/register" class="font-medium text-blue-600 hover:text-blue-500">
            立即注册
          </NuxtLink>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面元数据
definePageMeta({
  layout: false,
  title: '用户登录'
})

// 状态管理
const authStore = useAuthStore()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const errorMessage = ref('')
const rememberMe = ref(false)
const showPassword = ref(false)

const formData = reactive({
  username: '',
  password: ''
})

// 方法
const handleSubmit = async () => {
  // 基础验证
  if (!formData.username.trim()) {
    errorMessage.value = '请输入用户名'
    return
  }
  
  if (!formData.password.trim()) {
    errorMessage.value = '请输入密码'
    return
  }
  
  if (formData.username.length < 3) {
    errorMessage.value = '用户名长度不能少于3个字符'
    return
  }
  
  if (formData.password.length < 6) {
    errorMessage.value = '密码长度不能少于6个字符'
    return
  }
  
  loading.value = true
  errorMessage.value = ''
  
  try {
    // 执行登录
    const result = await authStore.login(formData.username, formData.password)
    
    if (result.success) {
      // 获取重定向地址
      const redirectTo = route.query.redirect as string || '/'
      
      // 跳转到目标页面
      await navigateTo(redirectTo)
    } else {
      errorMessage.value = result.error || '登录失败，请检查用户名和密码'
    }
  } catch (error) {
    console.error('登录错误:', error)
    errorMessage.value = '登录过程中发生错误，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 检查是否已登录
onMounted(() => {
  if (authStore.isLoggedIn) {
    navigateTo('/')
  }
})

// 页面标题
useHead({
  title: '用户登录 - 酒店管理系统'
})
</script>

<style scoped>
/* 自定义样式 */
.form-input {
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
</style>
