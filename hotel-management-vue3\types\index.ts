export interface User {
  id: string
  username: string
  realName?: string
  phone?: string
  department?: string
  roles: string[]
  createdAt: Date
}

export interface WorkLog {
  id: string
  content: string
  images?: string[]
  pageType: string
  user?: User
  createdAt: Date
  updatedAt?: Date
}

export interface Repair {
  id: string
  title: string
  description: string
  images?: string[]
  location: string
  category: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  status: 'pending' | 'assigned' | 'in_progress' | 'completed' | 'cancelled' | 'rejected'
  reporter?: User
  assignee?: User
  estimatedTime?: number // 预计完成时间（小时）
  actualTime?: number // 实际完成时间（小时）
  solution?: string // 解决方案
  feedback?: string // 用户反馈
  rating?: number // 用户评分 1-5
  cost?: number // 维修成本
  materials?: string[] // 使用的材料
  createdAt: Date
  updatedAt?: Date
  assignedAt?: Date
  startedAt?: Date
  completedAt?: Date
}

export interface RepairComment {
  id: string
  repairId: string
  user?: User
  content: string
  images?: string[]
  type: 'comment' | 'status_change' | 'assignment'
  createdAt: Date
}

export interface RepairCategory {
  id: string
  name: string
  description?: string
  icon?: string
  color?: string
  isActive: boolean
  createdAt: Date
}

export interface InventoryItem {
  id: string
  name: string
  category: string
  quantity: number
  unit: string
  minStock: number
  maxStock?: number
  location: string
  supplier?: string
  price?: number
  barcode?: string
  description?: string
  images?: string[]
  status: 'active' | 'inactive' | 'discontinued'
  createdAt: Date
  updatedAt?: Date
}

export interface InventoryTransaction {
  id: string
  itemId: string
  item?: InventoryItem
  type: 'in' | 'out' | 'adjust' | 'transfer'
  quantity: number
  beforeQuantity: number
  afterQuantity: number
  reason: string
  reference?: string
  operator?: User
  location?: string
  createdAt: Date
}

export interface StockAlert {
  id: string
  itemId: string
  item?: InventoryItem
  type: 'low_stock' | 'out_of_stock' | 'overstock'
  message: string
  isRead: boolean
  createdAt: Date
}

export interface StockCountPlan {
  id: string
  name: string
  description?: string
  status: 'draft' | 'active' | 'completed' | 'cancelled'
  startDate: Date
  endDate?: Date
  createdBy?: User
  items: string[] // 盘点物品ID列表
  createdAt: Date
  updatedAt?: Date
}

export interface StockCountRecord {
  id: string
  planId: string
  plan?: StockCountPlan
  itemId: string
  item?: InventoryItem
  systemQuantity: number
  actualQuantity: number
  difference: number
  remark?: string
  countBy?: User
  countDate: Date
  createdAt: Date
}

export interface Department {
  id: string
  name: string
  description?: string
  isSystem: boolean
  userCount: number
  createdAt: Date
}

export interface Role {
  id: string
  code: string
  name: string
  description?: string
  isSystem: boolean
  permissions: string[]
  userCount: number
  createdAt: Date
}

export interface SystemLog {
  id: string
  action: string
  module: string
  description: string
  userId?: string
  userName?: string
  ip?: string
  userAgent?: string
  createdAt: Date
}

export interface SystemSettings {
  id: string
  siteName: string
  siteDescription?: string
  contactEmail?: string
  contactPhone?: string
  enableRegistration: boolean
  enableEmailNotification: boolean
  enableSMSNotification: boolean
  enableWeChatNotification: boolean
  maxFileSize: number
  allowedFileTypes: string[]
  sessionTimeout: number
  passwordMinLength: number
  passwordRequireSpecialChar: boolean
  backupFrequency: 'daily' | 'weekly' | 'monthly'
  logRetentionDays: number
  updatedAt: Date
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
}

export interface PaginationOptions {
  page?: number
  limit?: number
}

export interface FilterOptions {
  startDate?: Date
  endDate?: Date
  userId?: string
  pageType?: string
  status?: string
  category?: string
}

// 页面类型枚举
export enum PageType {
  MAIN = 'main',
  POWERSTATION = 'powerstation',
  WATERFILTER = 'waterfilter',
  AIRCONDITION = 'aircondition',
  CONSTRUCTION = 'construction'
}

// 用户角色枚举
export enum UserRole {
  USER = 'user',
  ADMIN = 'admin',
  ENGINEER = 'engineer',
  MANAGER = 'manager'
}
