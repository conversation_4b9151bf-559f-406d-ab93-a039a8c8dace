<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>任务统计</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- LeanCloud SDK -->
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-3">
                    <a href="index.html" class="text-gray-600 hover:text-gray-800">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                    </a>
                    <h1 class="text-xl font-semibold text-gray-800">任务统计</h1>
                </div>
                <div id="userInfo" class="hidden items-center space-x-4">
                    <span id="realName" class="text-gray-800 font-medium"></span>
                    <button id="logoutBtn" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm">
                        退出登录
                    </button>
                </div>
                <button id="loginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm">
                    登录
                </button>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="max-w-7xl mx-auto px-4 py-6">
        <!-- 未登录提示 -->
        <div id="accessDenied" class="bg-white rounded-lg shadow p-8 text-center">
            <h2 class="text-2xl font-bold text-gray-800 mb-4">需要登录</h2>
            <p class="text-gray-600 mb-6">请先登录后查看任务统计</p>
            <button id="promptLoginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg">
                立即登录
            </button>
        </div>

        <!-- 统计界面 -->
        <div id="statsSection" class="hidden space-y-6">
            <!-- 总体统计 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="text-3xl font-bold text-blue-600" id="totalTasks">0</div>
                        <div class="ml-3">
                            <div class="text-sm font-medium text-gray-500">总任务数</div>
                            <div class="text-xs text-gray-400">本月</div>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="text-3xl font-bold text-green-600" id="completedTasks">0</div>
                        <div class="ml-3">
                            <div class="text-sm font-medium text-gray-500">已完成</div>
                            <div class="text-xs text-gray-400">本月</div>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="text-3xl font-bold text-yellow-600" id="completionRate">0%</div>
                        <div class="ml-3">
                            <div class="text-sm font-medium text-gray-500">完成率</div>
                            <div class="text-xs text-gray-400">本月</div>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="text-3xl font-bold text-red-600" id="overdueTasks">0</div>
                        <div class="ml-3">
                            <div class="text-sm font-medium text-gray-500">逾期任务</div>
                            <div class="text-xs text-gray-400">当前</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 个人统计 -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">个人统计</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600" id="myCreatedTasks">0</div>
                        <div class="text-sm text-gray-500">我创建的任务</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600" id="myAssignedTasks">0</div>
                        <div class="text-sm text-gray-500">分配给我的任务</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600" id="myCompletedTasks">0</div>
                        <div class="text-sm text-gray-500">我完成的任务</div>
                    </div>
                </div>
            </div>

            <!-- 部门统计 -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">部门统计</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-medium text-gray-700 mb-3">任务状态分布</h4>
                        <div class="space-y-2">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">待开始</span>
                                <div class="flex items-center space-x-2">
                                    <div class="w-20 bg-gray-200 rounded-full h-2">
                                        <div id="pendingBar" class="bg-yellow-500 h-2 rounded-full" style="width: 0%"></div>
                                    </div>
                                    <span id="pendingCount" class="text-sm font-medium">0</span>
                                </div>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">进行中</span>
                                <div class="flex items-center space-x-2">
                                    <div class="w-20 bg-gray-200 rounded-full h-2">
                                        <div id="inProgressBar" class="bg-blue-500 h-2 rounded-full" style="width: 0%"></div>
                                    </div>
                                    <span id="inProgressCount" class="text-sm font-medium">0</span>
                                </div>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">已完成</span>
                                <div class="flex items-center space-x-2">
                                    <div class="w-20 bg-gray-200 rounded-full h-2">
                                        <div id="completedBar" class="bg-green-500 h-2 rounded-full" style="width: 0%"></div>
                                    </div>
                                    <span id="completedCount" class="text-sm font-medium">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-700 mb-3">优先级分布</h4>
                        <div class="space-y-2">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">高优先级</span>
                                <div class="flex items-center space-x-2">
                                    <div class="w-20 bg-gray-200 rounded-full h-2">
                                        <div id="highPriorityBar" class="bg-red-500 h-2 rounded-full" style="width: 0%"></div>
                                    </div>
                                    <span id="highPriorityCount" class="text-sm font-medium">0</span>
                                </div>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">中优先级</span>
                                <div class="flex items-center space-x-2">
                                    <div class="w-20 bg-gray-200 rounded-full h-2">
                                        <div id="mediumPriorityBar" class="bg-yellow-500 h-2 rounded-full" style="width: 0%"></div>
                                    </div>
                                    <span id="mediumPriorityCount" class="text-sm font-medium">0</span>
                                </div>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">低优先级</span>
                                <div class="flex items-center space-x-2">
                                    <div class="w-20 bg-gray-200 rounded-full h-2">
                                        <div id="lowPriorityBar" class="bg-green-500 h-2 rounded-full" style="width: 0%"></div>
                                    </div>
                                    <span id="lowPriorityCount" class="text-sm font-medium">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 最近完成的任务 -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">最近完成的任务</h3>
                <div id="recentCompletedTasks">
                    <!-- 最近完成的任务列表将通过JS动态填充 -->
                </div>
            </div>
        </div>
    </main>

    <!-- 登录弹窗 -->
    <div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-800">用户登录</h2>
            </div>
            <div class="p-6">
                <form id="loginForm" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                        <input type="text" id="loginUsername" required
                            class="w-full p-3 border border-gray-300 rounded-lg">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                        <input type="password" id="loginPassword" required
                            class="w-full p-3 border border-gray-300 rounded-lg">
                    </div>
                    <div class="flex gap-3 pt-4">
                        <button type="button" id="cancelLogin" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-3 rounded-lg">
                            取消
                        </button>
                        <button type="submit" class="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg">
                            登录
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="../js/config.js?v=2.1"></script>
    <script src="../js/core/error-handler.js?v=2.1"></script>
    <script src="../js/utils.js?v=2.1"></script>
    <script src="../js/auth.js?v=2.1"></script>
    <script src="../js/base-app.js?v=2.1"></script>
    <script>
        class TaskStatsApp extends BaseWorkLogApp {
            constructor() {
                super({
                    pageType: 'task-stats',
                    requiredElements: ['statsSection']
                });
            }

            getPageElements() {
                return {
                    // 头部元素
                    userInfo: 'userInfo',
                    realName: 'realName',
                    loginBtn: 'loginBtn',
                    logoutBtn: 'logoutBtn',
                    // 登录弹窗元素
                    loginModal: 'loginModal',
                    loginForm: 'loginForm',
                    loginUsername: 'loginUsername',
                    loginPassword: 'loginPassword',
                    cancelLogin: 'cancelLogin',
                    // 页面内容元素
                    accessDenied: 'accessDenied',
                    statsSection: 'statsSection',
                    promptLoginBtn: 'promptLoginBtn',
                    // 统计元素
                    totalTasks: 'totalTasks',
                    completedTasks: 'completedTasks',
                    completionRate: 'completionRate',
                    overdueTasks: 'overdueTasks',
                    myCreatedTasks: 'myCreatedTasks',
                    myAssignedTasks: 'myAssignedTasks',
                    myCompletedTasks: 'myCompletedTasks',
                    // 分布统计元素
                    pendingCount: 'pendingCount',
                    pendingBar: 'pendingBar',
                    inProgressCount: 'inProgressCount',
                    inProgressBar: 'inProgressBar',
                    completedCount: 'completedCount',
                    completedBar: 'completedBar',
                    highPriorityCount: 'highPriorityCount',
                    highPriorityBar: 'highPriorityBar',
                    mediumPriorityCount: 'mediumPriorityCount',
                    mediumPriorityBar: 'mediumPriorityBar',
                    lowPriorityCount: 'lowPriorityCount',
                    lowPriorityBar: 'lowPriorityBar',
                    recentCompletedTasks: 'recentCompletedTasks'
                };
            }

            bindPageEvents() {
                // 提示登录
                if (this.elements.promptLoginBtn) {
                    this.elements.promptLoginBtn.addEventListener('click', () => this.showLoginModal());
                }
            }

            onUserLoggedIn() {
                console.log('=== 任务统计页面：用户登录成功 ===');
                console.log('当前用户:', this.currentUser?.get('username'));
                console.log('用户部门:', this.currentUser?.get('department'));
                
                this.showUserInterface();
                this.loadStatistics();
            }

            onUserLoggedOut() {
                this.showAccessDenied();
            }

            showUserInterface() {
                // 调用父类方法更新头部显示
                super.showUserInterface();
                
                // 更新页面内容显示
                this.elements.accessDenied.style.display = 'none';
                this.elements.statsSection.style.display = 'block';
            }

            showAccessDenied() {
                // 调用父类方法更新头部显示
                super.showLoginPrompt();
                
                // 更新页面内容显示
                this.elements.accessDenied.style.display = 'block';
                this.elements.statsSection.style.display = 'none';
            }

            async loadStatistics() {
                if (!this.currentUser) return;

                try {
                    console.log('=== 开始加载统计数据 ===');
                    
                    const Task = AV.Object.extend('Task');
                    const userDept = this.currentUser.get('department');
                    
                    // 构建查询条件：部门任务 + 分配给自己的任务 + 自己创建的任务
                    const deptQuery = new AV.Query(Task);
                    deptQuery.equalTo('creatorDept', userDept);
                    
                    const assignedQuery = new AV.Query(Task);
                    assignedQuery.equalTo('assignee', this.currentUser);
                    
                    const createdQuery = new AV.Query(Task);
                    createdQuery.equalTo('creator', this.currentUser);
                    
                    const mainQuery = AV.Query.or(deptQuery, assignedQuery, createdQuery);
                    
                    // 获取本月任务
                    const now = new Date();
                    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
                    mainQuery.greaterThanOrEqualTo('createTime', monthStart);
                    
                    const tasks = await mainQuery.find();
                    console.log('查询成功！找到', tasks.length, '条本月任务');
                    
                    this.calculateStatistics(tasks);
                    this.loadRecentCompletedTasks();
                    
                } catch (error) {
                    console.error('=== 加载统计数据失败 ===');
                    console.error('错误详情:', error);
                    
                    // 显示默认值
                    this.showDefaultValues();
                }
            }

            calculateStatistics(tasks) {
                const stats = {
                    total: tasks.length,
                    completed: 0,
                    overdue: 0,
                    myCreated: 0,
                    myAssigned: 0,
                    myCompleted: 0,
                    statusCounts: { pending: 0, 'in-progress': 0, completed: 0 },
                    priorityCounts: { '高': 0, '中': 0, '低': 0 }
                };

                const now = new Date();
                const currentUserId = this.currentUser.id;

                tasks.forEach(task => {
                    const status = task.get('status');
                    const priority = task.get('priority');
                    const dueDate = task.get('dueDate');
                    const creator = task.get('creator');
                    const assignee = task.get('assignee');

                    // 基本统计
                    if (status === 'completed') {
                        stats.completed++;
                    }
                    
                    // 逾期统计
                    if (dueDate && new Date(dueDate) < now && status !== 'completed') {
                        stats.overdue++;
                    }

                    // 个人统计
                    if (creator && creator.id === currentUserId) {
                        stats.myCreated++;
                    }
                    if (assignee && assignee.id === currentUserId) {
                        stats.myAssigned++;
                        if (status === 'completed') {
                            stats.myCompleted++;
                        }
                    }

                    // 状态分布
                    if (stats.statusCounts.hasOwnProperty(status)) {
                        stats.statusCounts[status]++;
                    }

                    // 优先级分布
                    if (stats.priorityCounts.hasOwnProperty(priority)) {
                        stats.priorityCounts[priority]++;
                    }
                });

                this.updateStatisticsDisplay(stats);
            }

            updateStatisticsDisplay(stats) {
                // 总体统计
                this.elements.totalTasks.textContent = stats.total;
                this.elements.completedTasks.textContent = stats.completed;
                this.elements.overdueTasks.textContent = stats.overdue;
                
                const completionRate = stats.total > 0 ? Math.round((stats.completed / stats.total) * 100) : 0;
                this.elements.completionRate.textContent = completionRate + '%';

                // 个人统计
                this.elements.myCreatedTasks.textContent = stats.myCreated;
                this.elements.myAssignedTasks.textContent = stats.myAssigned;
                this.elements.myCompletedTasks.textContent = stats.myCompleted;

                // 状态分布
                this.updateDistributionBars('status', stats.statusCounts, stats.total);
                
                // 优先级分布
                this.updateDistributionBars('priority', stats.priorityCounts, stats.total);
            }

            updateDistributionBars(type, counts, total) {
                if (type === 'status') {
                    const statusMap = {
                        'pending': { count: 'pendingCount', bar: 'pendingBar' },
                        'in-progress': { count: 'inProgressCount', bar: 'inProgressBar' },
                        'completed': { count: 'completedCount', bar: 'completedBar' }
                    };

                    Object.keys(statusMap).forEach(status => {
                        const count = counts[status] || 0;
                        const percentage = total > 0 ? (count / total) * 100 : 0;
                        
                        this.elements[statusMap[status].count].textContent = count;
                        this.elements[statusMap[status].bar].style.width = percentage + '%';
                    });
                } else if (type === 'priority') {
                    const priorityMap = {
                        '高': { count: 'highPriorityCount', bar: 'highPriorityBar' },
                        '中': { count: 'mediumPriorityCount', bar: 'mediumPriorityBar' },
                        '低': { count: 'lowPriorityCount', bar: 'lowPriorityBar' }
                    };

                    Object.keys(priorityMap).forEach(priority => {
                        const count = counts[priority] || 0;
                        const percentage = total > 0 ? (count / total) * 100 : 0;
                        
                        this.elements[priorityMap[priority].count].textContent = count;
                        this.elements[priorityMap[priority].bar].style.width = percentage + '%';
                    });
                }
            }

            async loadRecentCompletedTasks() {
                try {
                    const Task = AV.Object.extend('Task');
                    const userDept = this.currentUser.get('department');
                    
                    const deptQuery = new AV.Query(Task);
                    deptQuery.equalTo('creatorDept', userDept);
                    
                    const assignedQuery = new AV.Query(Task);
                    assignedQuery.equalTo('assignee', this.currentUser);
                    
                    const createdQuery = new AV.Query(Task);
                    createdQuery.equalTo('creator', this.currentUser);
                    
                    const mainQuery = AV.Query.or(deptQuery, assignedQuery, createdQuery);
                    mainQuery.equalTo('status', 'completed');
                    mainQuery.descending('completeTime');
                    mainQuery.limit(5);
                    
                    const tasks = await mainQuery.find();
                    this.renderRecentCompletedTasks(tasks);

                } catch (error) {
                    console.error('加载最近完成任务失败:', error);
                    this.elements.recentCompletedTasks.innerHTML = `
                        <div class="text-center text-gray-500 py-4">
                            加载失败
                        </div>
                    `;
                }
            }

            renderRecentCompletedTasks(tasks) {
                if (tasks.length === 0) {
                    this.elements.recentCompletedTasks.innerHTML = `
                        <div class="text-center text-gray-500 py-4">
                            暂无已完成的任务
                        </div>
                    `;
                    return;
                }

                const tasksHtml = tasks.map(task => {
                    const completeTime = task.get('completeTime');
                    return `
                        <div class="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                            <div>
                                <h4 class="font-medium text-gray-900">${task.get('title')}</h4>
                                <p class="text-sm text-gray-600">执行人: ${task.get('assigneeName')}</p>
                            </div>
                            <div class="text-sm text-gray-500">
                                ${completeTime ? new Date(completeTime).toLocaleDateString() : '-'}
                            </div>
                        </div>
                    `;
                }).join('');

                this.elements.recentCompletedTasks.innerHTML = tasksHtml;
            }

            showDefaultValues() {
                // 显示默认的统计值
                const defaultElements = [
                    'totalTasks', 'completedTasks', 'overdueTasks',
                    'myCreatedTasks', 'myAssignedTasks', 'myCompletedTasks',
                    'pendingCount', 'inProgressCount', 'completedCount',
                    'highPriorityCount', 'mediumPriorityCount', 'lowPriorityCount'
                ];
                
                defaultElements.forEach(id => {
                    if (this.elements[id]) {
                        this.elements[id].textContent = '--';
                    }
                });
                
                this.elements.completionRate.textContent = '--%';
            }
        }

        // 全局变量
        let taskStatsApp;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            function initApp() {
                if (typeof AV !== 'undefined') {
                    taskStatsApp = new TaskStatsApp();
                    taskStatsApp.init();
                } else {
                    setTimeout(initApp, 100);
                }
            }
            initApp();
        });
    </script>
</body>
</html>
