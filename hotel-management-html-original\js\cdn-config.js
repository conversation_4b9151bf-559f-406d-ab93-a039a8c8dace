/**
 * CDN配置文件
 * 提供多个CDN源的备用方案
 */

const CDN_CONFIG = {
    // Bootstrap CSS CDN源
    bootstrap_css: [
        'https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css',
        'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css',
        'https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css',
        'https://unpkg.com/bootstrap@5.3.0/dist/css/bootstrap.min.css',
        'assets/css/bootstrap.min.css' // 本地备用
    ],
    
    // Bootstrap JavaScript CDN源
    bootstrap_js: [
        'https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js',
        'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js',
        'https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js',
        'https://unpkg.com/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js',
        'assets/js/bootstrap.bundle.min.js' // 本地备用
    ],
    
    // Font Awesome CDN源
    fontawesome: [
        'https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css',
        'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
        'https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css',
        'https://unpkg.com/@fortawesome/fontawesome-free@6.4.0/css/all.min.css',
        'assets/css/font-awesome.min.css' // 本地备用
    ],
    
    // LeanCloud SDK CDN源
    leancloud: [
        'https://cdn.jsdelivr.net/npm/leancloud-storage@4.15.2/dist/av-min.js',
        'https://cdnjs.cloudflare.com/ajax/libs/leancloud-storage/4.15.2/av-min.js',
        'https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js',
        'assets/js/leancloud.min.js' // 本地备用
    ]
};

/**
 * 动态加载CSS文件
 * @param {Array} urls CDN URL数组
 * @param {Function} callback 回调函数
 */
function loadCSS(urls, callback) {
    let currentIndex = 0;
    
    function tryLoad() {
        if (currentIndex >= urls.length) {
            console.error('所有CSS CDN源都加载失败');
            if (callback) callback(false);
            return;
        }
        
        const url = urls[currentIndex];
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = url;
        
        link.onload = function() {
            console.log(`✅ CSS加载成功: ${url}`);
            if (callback) callback(true, url);
        };
        
        link.onerror = function() {
            console.warn(`❌ CSS加载失败: ${url}`);
            currentIndex++;
            tryLoad();
        };
        
        document.head.appendChild(link);
    }
    
    tryLoad();
}

/**
 * 动态加载JavaScript文件
 * @param {Array} urls CDN URL数组
 * @param {Function} callback 回调函数
 */
function loadJS(urls, callback) {
    let currentIndex = 0;
    
    function tryLoad() {
        if (currentIndex >= urls.length) {
            console.error('所有JS CDN源都加载失败');
            if (callback) callback(false);
            return;
        }
        
        const url = urls[currentIndex];
        const script = document.createElement('script');
        script.src = url;
        
        script.onload = function() {
            console.log(`✅ JS加载成功: ${url}`);
            if (callback) callback(true, url);
        };
        
        script.onerror = function() {
            console.warn(`❌ JS加载失败: ${url}`);
            currentIndex++;
            tryLoad();
        };
        
        document.head.appendChild(script);
    }
    
    tryLoad();
}

/**
 * 检测CDN可用性
 * @param {string} url CDN URL
 * @returns {Promise} 检测结果
 */
function checkCDN(url) {
    return new Promise((resolve) => {
        const img = new Image();
        const timeout = setTimeout(() => {
            resolve(false);
        }, 5000); // 5秒超时
        
        img.onload = img.onerror = function() {
            clearTimeout(timeout);
            resolve(true);
        };
        
        // 使用一个小的测试图片来检测网络
        img.src = url.replace(/\/[^\/]*$/, '/') + 'test.png?' + Date.now();
    });
}

/**
 * 自动加载所有必需的资源
 */
function autoLoadResources() {
    console.log('🚀 开始自动加载CDN资源...');
    
    // 加载Bootstrap CSS
    loadCSS(CDN_CONFIG.bootstrap_css, (success, url) => {
        if (success) {
            console.log('Bootstrap CSS加载完成');
        }
    });
    
    // 加载Font Awesome
    loadCSS(CDN_CONFIG.fontawesome, (success, url) => {
        if (success) {
            console.log('Font Awesome加载完成');
        }
    });
    
    // 加载Bootstrap JS
    loadJS(CDN_CONFIG.bootstrap_js, (success, url) => {
        if (success) {
            console.log('Bootstrap JS加载完成');
        }
    });
}

/**
 * 获取最佳CDN源
 * @param {string} type 资源类型
 * @returns {string} 最佳CDN URL
 */
function getBestCDN(type) {
    const urls = CDN_CONFIG[type];
    if (!urls || urls.length === 0) {
        return null;
    }
    
    // 优先返回国内CDN
    return urls[0];
}

/**
 * 生成HTML标签
 * @param {string} type 资源类型
 * @returns {string} HTML标签
 */
function generateTag(type) {
    const url = getBestCDN(type);
    if (!url) return '';
    
    switch (type) {
        case 'bootstrap_css':
        case 'fontawesome':
            return `<link href="${url}" rel="stylesheet">`;
        case 'bootstrap_js':
        case 'leancloud':
            return `<script src="${url}"></script>`;
        default:
            return '';
    }
}

// 导出配置和函数
if (typeof window !== 'undefined') {
    window.CDN_CONFIG = CDN_CONFIG;
    window.loadCSS = loadCSS;
    window.loadJS = loadJS;
    window.checkCDN = checkCDN;
    window.autoLoadResources = autoLoadResources;
    window.getBestCDN = getBestCDN;
    window.generateTag = generateTag;
}

// 如果是Node.js环境
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        CDN_CONFIG,
        loadCSS,
        loadJS,
        checkCDN,
        autoLoadResources,
        getBestCDN,
        generateTag
    };
}
