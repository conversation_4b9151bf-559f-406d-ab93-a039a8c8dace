<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单权限测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
</head>
<body class="bg-gray-50 min-h-screen p-8">
    <div class="max-w-2xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">简单权限测试</h1>
        
        <!-- 测试结果显示区域 -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-lg font-medium mb-4">测试结果</h2>
            <div id="testResults" class="space-y-2 text-sm">
                <!-- 测试结果将在这里显示 -->
            </div>
        </div>

        <!-- 管理界面模拟 -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-medium mb-4">管理界面模拟</h2>
            
            <!-- 访问受限提示 -->
            <div id="accessDenied" class="text-center p-8 border-2 border-red-200 rounded-lg bg-red-50" style="display: none;">
                <h3 class="text-lg font-medium text-red-900 mb-2">访问受限</h3>
                <p class="text-red-700">您需要管理员权限才能访问此页面</p>
            </div>

            <!-- 管理功能区域 -->
            <div id="adminSection" class="space-y-4" style="display: none;">
                <div class="grid grid-cols-2 gap-4">
                    <div class="admin-module-link p-4 border rounded-lg bg-blue-50">
                        <h3 class="font-medium">用户管理</h3>
                        <p class="text-sm text-gray-600">管理系统用户</p>
                    </div>
                    <div class="admin-module-link p-4 border rounded-lg bg-green-50">
                        <h3 class="font-medium">角色权限</h3>
                        <p class="text-sm text-gray-600">配置用户权限</p>
                    </div>
                    <div class="admin-module-link p-4 border rounded-lg bg-purple-50">
                        <h3 class="font-medium">系统设置</h3>
                        <p class="text-sm text-gray-600">系统参数配置</p>
                    </div>
                    <div class="admin-module-link p-4 border rounded-lg bg-yellow-50">
                        <h3 class="font-medium">操作日志</h3>
                        <p class="text-sm text-gray-600">查看操作记录</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="mt-6 space-x-4">
            <button id="testBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                重新测试
            </button>
            <a href="index.html" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded inline-block">
                返回管理首页
            </a>
        </div>
    </div>

    <!-- 引入配置文件 -->
    <script src="../js/config.js"></script>
    <script>
        class SimplePermissionTest {
            constructor() {
                this.resultsContainer = document.getElementById('testResults');
                this.accessDenied = document.getElementById('accessDenied');
                this.adminSection = document.getElementById('adminSection');
            }

            init() {
                this.bindEvents();
                this.runTest();
            }

            bindEvents() {
                document.getElementById('testBtn').addEventListener('click', () => {
                    this.runTest();
                });
            }

            log(message, type = 'info') {
                const colors = {
                    'info': 'text-blue-600',
                    'success': 'text-green-600',
                    'error': 'text-red-600',
                    'warning': 'text-yellow-600'
                };
                
                const div = document.createElement('div');
                div.className = colors[type] || 'text-gray-600';
                div.textContent = message;
                this.resultsContainer.appendChild(div);
            }

            clearResults() {
                this.resultsContainer.innerHTML = '';
            }

            runTest() {
                this.clearResults();
                this.log('开始权限测试...', 'info');

                // 1. 检查用户登录状态
                const currentUser = AV.User.current();
                if (!currentUser) {
                    this.log('❌ 用户未登录', 'error');
                    this.showAccessDenied();
                    return;
                }

                this.log(`✅ 用户已登录: ${currentUser.get('username')}`, 'success');

                // 2. 获取用户角色
                const roles = currentUser.get('roles');
                this.log(`📋 用户角色 (原始): ${JSON.stringify(roles)}`, 'info');
                this.log(`📋 角色数据类型: ${typeof roles}`, 'info');
                this.log(`📋 是否为数组: ${Array.isArray(roles)}`, 'info');

                // 3. 解析角色数据
                let roleArray = [];
                if (typeof roles === 'string') {
                    try {
                        roleArray = JSON.parse(roles);
                        this.log(`✅ 成功解析JSON角色: ${JSON.stringify(roleArray)}`, 'success');
                    } catch (e) {
                        roleArray = [roles];
                        this.log(`⚠️ JSON解析失败，作为单个角色处理: ${roles}`, 'warning');
                    }
                } else if (Array.isArray(roles)) {
                    roleArray = roles;
                    this.log(`✅ 角色已是数组格式: ${JSON.stringify(roleArray)}`, 'success');
                } else {
                    this.log(`❌ 未知的角色数据格式`, 'error');
                    this.showAccessDenied();
                    return;
                }

                // 4. 检查管理员权限
                const isAdmin = roleArray.includes('admin') || roleArray.includes('super_admin');
                this.log(`🔍 管理员权限检查: ${isAdmin}`, isAdmin ? 'success' : 'error');

                if (isAdmin) {
                    this.log(`✅ 用户拥有管理员权限，显示管理界面`, 'success');
                    this.showAdminSection();
                } else {
                    this.log(`❌ 用户没有管理员权限，显示访问受限`, 'error');
                    this.showAccessDenied();
                }

                // 5. 检查具体角色
                this.log(`📝 角色详情:`, 'info');
                roleArray.forEach(role => {
                    this.log(`  - ${role}`, 'info');
                });

                // 6. 模拟管理主页的检查逻辑
                this.log(`🔄 模拟管理主页检查逻辑...`, 'info');
                this.simulateMainPageLogic(currentUser);
            }

            simulateMainPageLogic(currentUser) {
                // 完全复制管理主页的逻辑
                const roles = currentUser.get('roles') || [];
                let isAdmin = false;
                
                if (typeof roles === 'string') {
                    try {
                        const roleArray = JSON.parse(roles);
                        isAdmin = roleArray.includes('admin') || roleArray.includes('super_admin');
                        this.log(`🔄 主页逻辑 - JSON解析成功: ${isAdmin}`, isAdmin ? 'success' : 'error');
                    } catch (e) {
                        isAdmin = roles === 'admin' || roles === 'super_admin';
                        this.log(`🔄 主页逻辑 - JSON解析失败，字符串比较: ${isAdmin}`, isAdmin ? 'success' : 'error');
                    }
                } else if (Array.isArray(roles)) {
                    isAdmin = roles.includes('admin') || roles.includes('super_admin');
                    this.log(`🔄 主页逻辑 - 数组检查: ${isAdmin}`, isAdmin ? 'success' : 'error');
                }

                this.log(`🔄 主页逻辑最终结果: ${isAdmin}`, isAdmin ? 'success' : 'error');

                // 模拟元素显示/隐藏
                if (isAdmin) {
                    this.log(`🔄 主页逻辑 - 应该显示管理界面`, 'success');
                } else {
                    this.log(`🔄 主页逻辑 - 应该显示访问受限`, 'error');
                }
            }

            showAccessDenied() {
                this.accessDenied.style.display = 'block';
                this.adminSection.style.display = 'none';
            }

            showAdminSection() {
                this.accessDenied.style.display = 'none';
                this.adminSection.style.display = 'block';
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            function initApp() {
                if (typeof AV !== 'undefined') {
                    const testApp = new SimplePermissionTest();
                    testApp.init();
                } else {
                    setTimeout(initApp, 100);
                }
            }
            initApp();
        });
    </script>
</body>
</html>
