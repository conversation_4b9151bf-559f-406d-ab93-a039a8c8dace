<template>
  <header class="bg-white shadow-sm border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- 左侧：Logo和标题 -->
        <div class="flex items-center">
          <Icon name="mdi:hotel" size="32" class="text-blue-600 mr-3" />
          <div>
            <h1 class="text-xl font-semibold text-gray-900">酒店管理系统</h1>
            <p class="text-xs text-gray-500">Hotel Management System</p>
          </div>
        </div>

        <!-- 中间：导航菜单 -->
        <nav class="hidden md:flex space-x-8">
          <NuxtLink 
            to="/" 
            class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors"
            :class="{ 'text-blue-600 bg-blue-50': $route.path === '/' }"
          >
            <Icon name="mdi:home" size="16" class="inline mr-1" />
            首页
          </NuxtLink>
          
          <NuxtLink 
            to="/work-log" 
            class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors"
            :class="{ 'text-blue-600 bg-blue-50': $route.path.startsWith('/work-log') }"
          >
            <Icon name="mdi:notebook" size="16" class="inline mr-1" />
            工作日志
          </NuxtLink>
          
          <NuxtLink 
            to="/inventory" 
            class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors"
            :class="{ 'text-blue-600 bg-blue-50': $route.path.startsWith('/inventory') }"
          >
            <Icon name="mdi:package-variant" size="16" class="inline mr-1" />
            库存管理
          </NuxtLink>
          
          <NuxtLink 
            to="/repair" 
            class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors"
            :class="{ 'text-blue-600 bg-blue-50': $route.path.startsWith('/repair') }"
          >
            <Icon name="mdi:wrench" size="16" class="inline mr-1" />
            报修系统
          </NuxtLink>
          
          <NuxtLink 
            v-if="authStore.isAdmin"
            to="/admin" 
            class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors"
            :class="{ 'text-blue-600 bg-blue-50': $route.path.startsWith('/admin') }"
          >
            <Icon name="mdi:cog" size="16" class="inline mr-1" />
            管理后台
          </NuxtLink>
        </nav>

        <!-- 右侧：用户菜单 -->
        <div class="flex items-center space-x-4">
          <!-- 通知 -->
          <button class="text-gray-400 hover:text-gray-600 relative">
            <Icon name="mdi:bell" size="20" />
            <span class="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
              3
            </span>
          </button>

          <!-- 用户菜单 -->
          <template v-if="!authStore.isLoggedIn">
            <button 
              @click="navigateTo('/auth/login')"
              class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors text-sm"
            >
              登录
            </button>
          </template>
          
          <template v-else>
            <!-- 用户头像和下拉菜单 -->
            <div class="relative" ref="userMenuRef">
              <button 
                @click="userMenuOpen = !userMenuOpen"
                class="flex items-center space-x-2 text-gray-700 hover:text-gray-900 focus:outline-none"
              >
                <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                  {{ authStore.userDisplayName.charAt(0) }}
                </div>
                <span class="hidden md:block text-sm font-medium">{{ authStore.userDisplayName }}</span>
                <Icon name="mdi:chevron-down" size="16" class="hidden md:block" />
              </button>

              <!-- 下拉菜单 -->
              <div 
                v-if="userMenuOpen"
                class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200"
              >
                <div class="px-4 py-2 text-sm text-gray-500 border-b border-gray-100">
                  <div class="font-medium text-gray-900">{{ authStore.userDisplayName }}</div>
                  <div class="text-xs">{{ authStore.user?.department || '未设置部门' }}</div>
                </div>
                
                <NuxtLink 
                  to="/profile" 
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                  @click="userMenuOpen = false"
                >
                  <Icon name="mdi:account" size="16" class="inline mr-2" />
                  个人资料
                </NuxtLink>
                
                <NuxtLink 
                  to="/settings" 
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                  @click="userMenuOpen = false"
                >
                  <Icon name="mdi:cog" size="16" class="inline mr-2" />
                  设置
                </NuxtLink>
                
                <div class="border-t border-gray-100 my-1"></div>
                
                <button 
                  @click="handleLogout"
                  class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                >
                  <Icon name="mdi:logout" size="16" class="inline mr-2" />
                  登出
                </button>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
const authStore = useAuthStore()
const userMenuOpen = ref(false)
const userMenuRef = ref()

// 点击外部关闭菜单
onClickOutside(userMenuRef, () => {
  userMenuOpen.value = false
})

const handleLogout = async () => {
  userMenuOpen.value = false
  const result = await authStore.logout()
  if (result.success) {
    await navigateTo('/')
  }
}
</script>
