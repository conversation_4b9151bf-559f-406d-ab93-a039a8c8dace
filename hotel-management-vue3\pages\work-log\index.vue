aircondition.htmlaircondition.html<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">工作日志</h1>
          <p class="mt-1 text-sm text-gray-600">
            记录和管理日常工作内容
          </p>
        </div>
        
        <div class="mt-4 sm:mt-0 flex space-x-3">
          <button
            @click="handleCreateClick"
            class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center"
          >
            <Icon name="mdi:plus" size="16" class="mr-1" />
            新建日志
          </button>
          
          <button 
            @click="showFilterModal = true"
            class="border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 transition-colors flex items-center"
          >
            <Icon name="mdi:filter" size="16" class="mr-1" />
            筛选
          </button>
        </div>
      </div>
    </div>

    <!-- 筛选标签 -->
    <div v-if="hasActiveFilters" class="mb-6">
      <div class="flex flex-wrap items-center gap-2">
        <span class="text-sm text-gray-600">当前筛选:</span>
        
        <span 
          v-if="currentPageType"
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
        >
          {{ getPageTypeName(currentPageType) }}
          <button @click="clearPageTypeFilter" class="ml-1 hover:text-blue-600">
            <Icon name="mdi:close" size="12" />
          </button>
        </span>
        
        <button 
          @click="clearAllFilters"
          class="text-xs text-gray-500 hover:text-gray-700 underline"
        >
          清除所有筛选
        </button>
      </div>
    </div>

    <!-- 日志列表 -->
    <div class="space-y-4">
      <!-- 加载状态 -->
      <div v-if="workLogStore.loading && logs.length === 0" class="space-y-4">
        <div v-for="i in 5" :key="i" class="animate-pulse">
          <div class="bg-white rounded-lg border border-gray-200 p-4">
            <div class="flex items-center space-x-3 mb-3">
              <div class="w-8 h-8 bg-gray-200 rounded-full"></div>
              <div class="flex-1">
                <div class="h-4 bg-gray-200 rounded w-1/4 mb-1"></div>
                <div class="h-3 bg-gray-200 rounded w-1/6"></div>
              </div>
            </div>
            <div class="space-y-2">
              <div class="h-4 bg-gray-200 rounded w-3/4"></div>
              <div class="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else-if="logs.length === 0" class="text-center py-12">
        <Icon name="mdi:file-document-outline" size="64" class="text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无工作日志</h3>
        <p class="text-gray-600 mb-6">开始记录你的第一条工作日志吧</p>
        <button 
          @click="showCreateModal = true"
          class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          创建日志
        </button>
      </div>

      <!-- 日志卡片列表 -->
      <template v-else>
        <WorkLogCard
          v-for="log in logs"
          :key="log.id"
          :log="log"
          @delete="handleDeleteLog"
          @edit="handleEditLog"
        />
        
        <!-- 加载更多 -->
        <div v-if="hasMore" class="text-center py-6">
          <button 
            @click="loadMore"
            :disabled="workLogStore.loading"
            class="bg-gray-100 text-gray-700 px-6 py-2 rounded-md hover:bg-gray-200 transition-colors disabled:opacity-50"
          >
            {{ workLogStore.loading ? '加载中...' : '加载更多' }}
          </button>
        </div>
      </template>
    </div>

    <!-- 创建/编辑日志模态框 -->
    <CreateWorkLogModal
      v-model:show="showCreateModal"
      :edit-log="editingLog"
      @created="handleLogCreated"
      @updated="handleLogUpdated"
    />

    <!-- 筛选模态框 -->
    <FilterModal
      v-model:show="showFilterModal"
      @apply="handleApplyFilters"
    />

    <!-- 调试信息 -->
    <div v-if="showCreateModal" class="fixed top-4 left-4 bg-red-500 text-white p-2 rounded z-50">
      模态框应该显示了！showCreateModal = {{ showCreateModal }}
    </div>
  </div>
</template>

<script setup lang="ts">
import type { WorkLog } from '~/types'

// 导入组件
import CreateWorkLogModal from '~/components/work-log/CreateWorkLogModal.vue'
import FilterModal from '~/components/work-log/FilterModal.vue'
import WorkLogCard from '~/components/work-log/WorkLogCard.vue'

// 页面元数据
definePageMeta({
  title: '工作日志',
  middleware: 'auth'
})

// 状态管理
const workLogStore = useWorkLogStore()
const route = useRoute()

// 响应式数据
const showCreateModal = ref(false)
const showFilterModal = ref(false)
const editingLog = ref<WorkLog | null>(null)
const currentPage = ref(0)
const pageSize = 20

// 计算属性
const currentPageType = computed(() => route.query.type as string)

const logs = computed(() => {
  let filtered = workLogStore.logs
  
  // 按页面类型筛选
  if (currentPageType.value) {
    filtered = filtered.filter(log => log.pageType === currentPageType.value)
  }
  
  return filtered
})

const hasActiveFilters = computed(() => {
  return !!currentPageType.value
})

const hasMore = computed(() => {
  // 简化的判断逻辑，实际应该基于服务器返回的数据
  return logs.value.length >= pageSize && !workLogStore.loading
})

// 方法
const getPageTypeName = (pageType: string) => {
  const typeMap: Record<string, string> = {
    'main': '工作日志',
    'powerstation': '变电站',
    'waterfilter': '净水器',
    'aircondition': '空调',
    'construction': '施工登记'
  }
  return typeMap[pageType] || pageType
}

const loadLogs = async (reset = false) => {
  const page = reset ? 0 : currentPage.value
  
  await workLogStore.fetchLogs({
    page,
    limit: pageSize,
    pageType: currentPageType.value
  })
  
  if (reset) {
    currentPage.value = 0
  }
}

const loadMore = async () => {
  currentPage.value++
  await loadLogs()
}

const handleCreateClick = () => {
  console.log('点击了新建日志按钮')
  showCreateModal.value = true
}

const handleLogCreated = () => {
  showCreateModal.value = false
  editingLog.value = null
  // 重新加载第一页
  loadLogs(true)
}

const handleLogUpdated = () => {
  showCreateModal.value = false
  editingLog.value = null
  // 重新加载当前页
  loadLogs(true)
}

const handleDeleteLog = async (logId: string) => {
  const result = await workLogStore.deleteLog(logId)
  if (result.success) {
    // 如果当前页没有数据了，回到上一页
    if (logs.value.length === 1 && currentPage.value > 0) {
      currentPage.value--
    }
  }
}

const handleEditLog = (log: WorkLog) => {
  editingLog.value = log
  showCreateModal.value = true
}

const clearPageTypeFilter = () => {
  navigateTo('/work-log')
}

const clearAllFilters = () => {
  navigateTo('/work-log')
}

const handleApplyFilters = (filters: any) => {
  showFilterModal.value = false
  // 应用筛选逻辑
  workLogStore.setFilters(filters)
  loadLogs(true)
}

// 监听路由变化
watch(() => route.query.type, () => {
  loadLogs(true)
})

// 生命周期
onMounted(async () => {
  await loadLogs(true)
})

// 页面标题
useHead({
  title: computed(() => {
    const baseTitle = '工作日志'
    if (currentPageType.value) {
      return `${getPageTypeName(currentPageType.value)} - ${baseTitle}`
    }
    return baseTitle
  })
})
</script>
