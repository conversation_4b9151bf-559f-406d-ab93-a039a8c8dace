// composables/useNotification.ts
interface NotificationOptions {
  title?: string
  duration?: number
}

interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title?: string
  message: string
  duration?: number
}

// 全局通知状态
const notifications = ref<Notification[]>([])

export const useNotification = () => {
  // 添加通知
  const addNotification = (notification: Omit<Notification, 'id'>) => {
    const id = Date.now().toString()
    const newNotification: Notification = {
      id,
      duration: 5000,
      ...notification
    }
    
    notifications.value.push(newNotification)
    
    // 自动移除
    if (newNotification.duration && newNotification.duration > 0) {
      setTimeout(() => {
        removeNotification(id)
      }, newNotification.duration)
    }
    
    return id
  }

  // 移除通知
  const removeNotification = (id: string) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  // 清除所有通知
  const clearAll = () => {
    notifications.value = []
  }

  // 便捷方法
  const success = (message: string, options?: NotificationOptions) => {
    return addNotification({ 
      type: 'success', 
      message, 
      title: options?.title,
      duration: options?.duration
    })
  }

  const error = (message: string, options?: NotificationOptions) => {
    return addNotification({ 
      type: 'error', 
      message, 
      title: options?.title,
      duration: options?.duration
    })
  }

  const warning = (message: string, options?: NotificationOptions) => {
    return addNotification({ 
      type: 'warning', 
      message, 
      title: options?.title,
      duration: options?.duration
    })
  }

  const info = (message: string, options?: NotificationOptions) => {
    return addNotification({ 
      type: 'info', 
      message, 
      title: options?.title,
      duration: options?.duration
    })
  }

  return {
    notifications: readonly(notifications),
    addNotification,
    removeNotification,
    clearAll,
    success,
    error,
    warning,
    info
  }
}
