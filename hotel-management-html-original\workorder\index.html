<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <!-- 微信浏览器兼容性设置 -->
    <meta name="x5-orientation" content="portrait">
    <meta name="x5-fullscreen" content="true">
    <meta name="x5-page-mode" content="app">
    <title>工单管理</title>

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- LeanCloud SDK -->
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <!-- 备用 CDN 源 -->
    <script>
        window.addEventListener('load', function() {
            if (typeof AV === 'undefined') {
                console.log('主 CDN 加载失败，尝试备用源...');
                var script = document.createElement('script');
                script.src = 'https://cdn.bootcdn.net/ajax/libs/leancloud-storage/4.15.2/av-min.js';
                script.onerror = function() {
                    console.error('所有 CDN 源都加载失败，请检查网络连接');
                    alert('网络连接异常，请检查网络后刷新页面');
                };
                document.head.appendChild(script);
            }
        });
    </script>
    <style>
        /* 微信浏览器兼容性修复 */
        * {
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
        }
        
        body {
            -webkit-overflow-scrolling: touch;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        
        .btn-fix {
            -webkit-appearance: none;
            appearance: none;
            border-radius: 8px;
            border: none;
            outline: none;
        }
        
        .fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航 -->
    <nav class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-10">
        <div class="max-w-4xl mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-3">
                    <a href="../index.html" class="text-gray-600 hover:text-gray-800 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                    </a>
                    <h1 class="text-xl font-semibold text-gray-800">工单管理</h1>
                </div>
                <div id="userInfo" class="hidden items-center space-x-2 sm:space-x-4">
                    <span id="realName" class="text-gray-800 font-medium text-sm sm:text-base"></span>
                    <button id="logoutBtn" class="bg-red-500 hover:bg-red-600 text-white px-2 py-1 sm:px-4 sm:py-2 rounded-lg text-xs sm:text-sm transition-colors btn-fix">
                        <span class="hidden sm:inline">退出登录</span>
                        <span class="sm:hidden">退出</span>
                    </button>
                </div>
                <button id="loginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm transition-colors btn-fix">
                    登录
                </button>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="max-w-4xl mx-auto px-4 py-6">
        <!-- 欢迎页面（未登录时显示） -->
        <div id="welcomePage" class="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg shadow-sm border border-blue-200 p-8 text-center">
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-800 mb-4">工单管理系统</h1>
                <div class="text-xl text-blue-600 font-semibold mb-2">
                    相润金鹏酒店工程部
                </div>
                <p class="text-gray-600 text-lg mb-8">
                    统一管理各类工作任务和服务请求，提高工作效率
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <div class="text-blue-500 text-3xl mb-3">📝</div>
                    <h3 class="font-semibold text-gray-800 mb-2">工单提交</h3>
                    <p class="text-gray-600 text-sm">快速提交各类工作任务和服务请求</p>
                </div>
                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <div class="text-green-500 text-3xl mb-3">📊</div>
                    <h3 class="font-semibold text-gray-800 mb-2">进度跟踪</h3>
                    <p class="text-gray-600 text-sm">实时查看工单处理进度和状态</p>
                </div>
                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <div class="text-purple-500 text-3xl mb-3">⚡</div>
                    <h3 class="font-semibold text-gray-800 mb-2">高效处理</h3>
                    <p class="text-gray-600 text-sm">工程部快速响应和处理工单</p>
                </div>
            </div>

            <div class="bg-white rounded-lg p-6 shadow-sm">
                <h3 class="font-semibold text-gray-800 mb-3">开始使用</h3>
                <p class="text-gray-600 mb-4">请先登录账号，然后即可使用工单管理功能</p>
                <button id="promptLoginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors font-medium btn-fix">
                    立即登录
                </button>
            </div>
        </div>

        <!-- 工单管理主界面 -->
        <div id="workOrderSection" class="hidden">
            <!-- 欢迎信息 -->
            <div class="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg shadow-sm border border-blue-200 p-6 mb-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-xl font-bold text-gray-800 mb-2">工单管理系统</h2>
                        <p class="text-gray-600">
                            统一管理各类工作任务和服务请求
                        </p>
                    </div>
                    <div class="text-blue-500 text-4xl">
                        📋
                    </div>
                </div>
            </div>

            <!-- 快速操作 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">快速操作</h3>
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    <a href="workorder.html" class="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white p-4 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-md">
                        <div class="flex items-center space-x-3">
                            <div class="text-2xl">➕</div>
                            <div>
                                <h4 class="font-semibold">创建工单</h4>
                                <p class="text-blue-100 text-sm">提交新的工作任务</p>
                            </div>
                        </div>
                    </a>
                    
                    <a href="workorder-query.html" class="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white p-4 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-md">
                        <div class="flex items-center space-x-3">
                            <div class="text-2xl">🔍</div>
                            <div>
                                <h4 class="font-semibold">我的工单</h4>
                                <p class="text-green-100 text-sm">查看工单状态</p>
                            </div>
                        </div>
                    </a>
                    
                    <a href="workorder-manage.html" class="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white p-4 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-md">
                        <div class="flex items-center space-x-3">
                            <div class="text-2xl">⚙️</div>
                            <div>
                                <h4 class="font-semibold">工单处理</h4>
                                <p class="text-purple-100 text-sm">处理和跟踪工单</p>
                            </div>
                        </div>
                    </a>
                </div>
            </div>

            <!-- 工单统计 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">工单统计</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600" id="totalOrders">--</div>
                        <div class="text-sm text-gray-500">总工单</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-yellow-600" id="pendingOrders">--</div>
                        <div class="text-sm text-gray-500">待处理</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600" id="processingOrders">--</div>
                        <div class="text-sm text-gray-500">处理中</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600" id="completedOrders">--</div>
                        <div class="text-sm text-gray-500">已完成</div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 登录弹窗 -->
    <div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 fade-in">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-xl font-semibold text-gray-800">用户登录</h2>
                    <button id="closeLoginModal" class="text-gray-400 hover:text-gray-600 text-2xl btn-fix">&times;</button>
                </div>
            </div>
            <div class="p-6">
                <form id="loginForm" class="space-y-4">
                    <div>
                        <label for="loginUsername" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                        <input type="text" id="loginUsername" required
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                    </div>
                    <div>
                        <label for="loginPassword" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                        <input type="password" id="loginPassword" required
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                    </div>
                    <div class="flex gap-3 pt-4">
                        <button type="button" id="cancelLogin" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                            取消
                        </button>
                        <button type="submit" class="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                            登录
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 引入脚本 -->
    <script src="../js/config.js?v=2.1"></script>
    <script src="../js/core/error-handler.js?v=2.1"></script>
    <script src="../js/utils.js?v=2.1"></script>
    <script src="../js/auth.js?v=2.1"></script>
    <script src="../js/base-app.js?v=2.1"></script>
    <script>
        class WorkOrderIndexApp extends BaseWorkLogApp {
            constructor() {
                super({
                    pageType: 'workorder-index',
                    requiredElements: ['workOrderSection']
                });
            }

            getPageElements() {
                return {
                    // 头部元素
                    userInfo: 'userInfo',
                    realName: 'realName',
                    loginBtn: 'loginBtn',
                    logoutBtn: 'logoutBtn',
                    // 页面内容元素
                    welcomePage: 'welcomePage',
                    workOrderSection: 'workOrderSection',
                    promptLoginBtn: 'promptLoginBtn',
                    totalOrders: 'totalOrders',
                    pendingOrders: 'pendingOrders',
                    processingOrders: 'processingOrders',
                    completedOrders: 'completedOrders'
                };
            }

            onUserLoggedIn() {
                console.log('=== 用户登录成功，更新界面 ===');
                console.log('当前用户:', this.currentUser?.get('username'));
                console.log('用户真实姓名:', this.currentUser?.get('realName'));

                // 检查头部元素是否存在
                console.log('userInfo元素:', this.elements.userInfo);
                console.log('realName元素:', this.elements.realName);
                console.log('loginBtn元素:', this.elements.loginBtn);
                console.log('logoutBtn元素:', this.elements.logoutBtn);

                this.showUserInterface();
                this.loadStatistics();
            }

            onUserLoggedOut() {
                this.showWelcomePage();
            }

            showUserInterface() {
                this.elements.welcomePage.style.display = 'none';
                this.elements.workOrderSection.style.display = 'block';
            }

            showWelcomePage() {
                this.elements.welcomePage.style.display = 'block';
                this.elements.workOrderSection.style.display = 'none';
            }

            async loadStatistics() {
                if (!this.currentUser) return;

                try {
                    // 暂时使用RepairOrder表名，后续可以迁移到WorkOrder
                    const RepairOrder = AV.Object.extend('RepairOrder');
                    const query = new AV.Query(RepairOrder);
                    query.equalTo('reporter', this.currentUser);

                    // 总工单数
                    const total = await query.count();
                    this.elements.totalOrders.textContent = total;

                    // 待处理工单
                    const pendingQuery = new AV.Query(RepairOrder);
                    pendingQuery.equalTo('reporter', this.currentUser);
                    pendingQuery.equalTo('status', 'pending');
                    const pending = await pendingQuery.count();
                    this.elements.pendingOrders.textContent = pending;

                    // 处理中工单
                    const processingQuery = new AV.Query(RepairOrder);
                    processingQuery.equalTo('reporter', this.currentUser);
                    processingQuery.containedIn('status', ['accepted', 'processing']);
                    const processing = await processingQuery.count();
                    this.elements.processingOrders.textContent = processing;

                    // 已完成工单
                    const completedQuery = new AV.Query(RepairOrder);
                    completedQuery.equalTo('reporter', this.currentUser);
                    completedQuery.equalTo('status', 'completed');
                    const completed = await completedQuery.count();
                    this.elements.completedOrders.textContent = completed;

                } catch (error) {
                    console.error('加载统计数据失败:', error);
                    // 显示默认值
                    ['totalOrders', 'pendingOrders', 'processingOrders', 'completedOrders'].forEach(id => {
                        if (this.elements[id]) {
                            this.elements[id].textContent = '--';
                        }
                    });
                }
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始状态：显示欢迎页面，隐藏功能界面
            const welcomePage = document.getElementById('welcomePage');
            const workOrderSection = document.getElementById('workOrderSection');

            if (welcomePage) welcomePage.style.display = 'block';
            if (workOrderSection) workOrderSection.style.display = 'none';

            function initApp() {
                if (typeof AV !== 'undefined') {
                    const workOrderIndexApp = new WorkOrderIndexApp();
                    workOrderIndexApp.init();
                } else {
                    setTimeout(initApp, 100);
                }
            }
            initApp();
        });
    </script>
</body>
</html>
