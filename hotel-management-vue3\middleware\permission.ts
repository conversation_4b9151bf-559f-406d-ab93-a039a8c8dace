// middleware/permission.ts
// 权限验证中间件

import { usePermissions } from '~/composables/usePermissions'

export default defineNuxtRouteMiddleware((to) => {
  const { hasPermission, hasAnyPermission, isAdmin } = usePermissions()

  // 从路由元信息中获取所需权限
  const requiredPermissions = to.meta.permissions as string[] | string | undefined
  const requiredRoles = to.meta.roles as string[] | string | undefined

  // 如果没有权限要求，直接通过
  if (!requiredPermissions && !requiredRoles) {
    return
  }

  // 管理员拥有所有权限
  if (isAdmin.value) {
    return
  }

  // 检查角色权限
  if (requiredRoles) {
    const { useAuthStore } = useAuthStore()
    const authStore = useAuthStore()
    
    const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles]
    const userRoles = Array.isArray(authStore.user?.roles) 
      ? authStore.user.roles 
      : [authStore.user?.roles].filter(Boolean)
    
    const hasRole = roles.some(role => userRoles.includes(role))
    
    if (!hasRole) {
      throw createError({
        statusCode: 403,
        statusMessage: '权限不足，无法访问此页面'
      })
    }
  }

  // 检查功能权限
  if (requiredPermissions) {
    const permissions = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions]
    
    if (!hasAnyPermission(permissions)) {
      throw createError({
        statusCode: 403,
        statusMessage: '权限不足，无法访问此页面'
      })
    }
  }
})
