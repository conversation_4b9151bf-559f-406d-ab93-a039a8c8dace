// composables/usePermissions.ts
// 权限管理组合式函数

import { computed } from 'vue'
import { useAuthStore } from '~/stores/auth'

// 权限定义
export const PERMISSIONS = {
  // 用户管理权限
  USER_VIEW: 'user.view',
  USER_CREATE: 'user.create',
  USER_EDIT: 'user.edit',
  USER_DELETE: 'user.delete',
  USER_MANAGE: 'user.manage',

  // 工作日志权限
  WORKLOG_VIEW: 'worklog.view',
  WORKLOG_CREATE: 'worklog.create',
  WORKLOG_EDIT: 'worklog.edit',
  WORKLOG_DELETE: 'worklog.delete',
  WORKLOG_VIEW_ALL: 'worklog.view_all',

  // 报修管理权限
  REPAIR_VIEW: 'repair.view',
  REPAIR_CREATE: 'repair.create',
  REPAIR_EDIT: 'repair.edit',
  REPAIR_DELETE: 'repair.delete',
  REPAIR_ASSIGN: 'repair.assign',
  REPAIR_MANAGE: 'repair.manage',

  // 库存管理权限
  INVENTORY_VIEW: 'inventory.view',
  INVENTORY_CREATE: 'inventory.create',
  INVENTORY_EDIT: 'inventory.edit',
  INVENTORY_DELETE: 'inventory.delete',
  INVENTORY_ADJUST: 'inventory.adjust',
  INVENTORY_COUNT: 'inventory.count',

  // 报表权限
  REPORT_VIEW: 'report.view',
  REPORT_EXPORT: 'report.export',

  // 系统管理权限
  SYSTEM_SETTINGS: 'system.settings',
  SYSTEM_LOGS: 'system.logs',

  // 部门管理权限
  DEPARTMENT_VIEW: 'department.view',
  DEPARTMENT_MANAGE: 'department.manage',

  // 角色管理权限
  ROLE_VIEW: 'role.view',
  ROLE_MANAGE: 'role.manage'
} as const

// 角色权限映射
export const ROLE_PERMISSIONS = {
  admin: ['*'], // 管理员拥有所有权限
  
  manager: [
    PERMISSIONS.USER_VIEW,
    PERMISSIONS.USER_MANAGE,
    PERMISSIONS.WORKLOG_VIEW_ALL,
    PERMISSIONS.REPAIR_VIEW,
    PERMISSIONS.REPAIR_ASSIGN,
    PERMISSIONS.REPAIR_MANAGE,
    PERMISSIONS.INVENTORY_VIEW,
    PERMISSIONS.INVENTORY_ADJUST,
    PERMISSIONS.REPORT_VIEW,
    PERMISSIONS.REPORT_EXPORT,
    PERMISSIONS.DEPARTMENT_VIEW
  ],
  
  supervisor: [
    PERMISSIONS.USER_VIEW,
    PERMISSIONS.WORKLOG_VIEW_ALL,
    PERMISSIONS.REPAIR_VIEW,
    PERMISSIONS.REPAIR_ASSIGN,
    PERMISSIONS.INVENTORY_VIEW,
    PERMISSIONS.REPORT_VIEW
  ],
  
  employee: [
    PERMISSIONS.WORKLOG_VIEW,
    PERMISSIONS.WORKLOG_CREATE,
    PERMISSIONS.WORKLOG_EDIT,
    PERMISSIONS.REPAIR_VIEW,
    PERMISSIONS.REPAIR_CREATE,
    PERMISSIONS.INVENTORY_VIEW
  ],
  
  engineer: [
    PERMISSIONS.WORKLOG_VIEW,
    PERMISSIONS.WORKLOG_CREATE,
    PERMISSIONS.REPAIR_VIEW,
    PERMISSIONS.REPAIR_EDIT,
    PERMISSIONS.REPAIR_MANAGE,
    PERMISSIONS.INVENTORY_VIEW,
    PERMISSIONS.INVENTORY_ADJUST
  ],
  
  housekeeper: [
    PERMISSIONS.WORKLOG_VIEW,
    PERMISSIONS.WORKLOG_CREATE,
    PERMISSIONS.WORKLOG_EDIT,
    PERMISSIONS.REPAIR_VIEW,
    PERMISSIONS.REPAIR_CREATE,
    PERMISSIONS.INVENTORY_VIEW
  ]
}

export function usePermissions() {
  const authStore = useAuthStore()

  // 获取当前用户权限
  const userPermissions = computed(() => {
    if (!authStore.user) return []
    
    const userRoles = Array.isArray(authStore.user.roles) 
      ? authStore.user.roles 
      : [authStore.user.roles].filter(Boolean)
    
    const permissions = new Set<string>()
    
    for (const role of userRoles) {
      const rolePerms = ROLE_PERMISSIONS[role as keyof typeof ROLE_PERMISSIONS] || []
      
      // 如果角色有 '*' 权限，表示拥有所有权限
      if (rolePerms.includes('*')) {
        return Object.values(PERMISSIONS)
      }
      
      rolePerms.forEach(perm => permissions.add(perm))
    }
    
    return Array.from(permissions)
  })

  // 检查是否有指定权限
  const hasPermission = (permission: string): boolean => {
    if (!authStore.user) return false
    
    // 管理员拥有所有权限
    if (authStore.user.roles?.includes('admin')) return true
    
    return userPermissions.value.includes(permission)
  }

  // 检查是否有任一权限
  const hasAnyPermission = (permissions: string[]): boolean => {
    return permissions.some(permission => hasPermission(permission))
  }

  // 检查是否有所有权限
  const hasAllPermissions = (permissions: string[]): boolean => {
    return permissions.every(permission => hasPermission(permission))
  }

  // 检查是否是管理员
  const isAdmin = computed(() => {
    return authStore.user?.roles?.includes('admin') || false
  })

  // 检查是否是经理
  const isManager = computed(() => {
    return authStore.user?.roles?.includes('manager') || false
  })

  // 检查是否是工程师
  const isEngineer = computed(() => {
    return authStore.user?.roles?.includes('engineer') || false
  })

  // 检查是否可以查看所有数据（管理员和经理）
  const canViewAll = computed(() => {
    return isAdmin.value || isManager.value
  })

  // 检查是否只能查看自己的数据
  const canOnlyViewOwn = computed(() => {
    return !canViewAll.value
  })

  // 权限检查快捷方法
  const can = {
    // 用户管理
    viewUsers: () => hasPermission(PERMISSIONS.USER_VIEW),
    manageUsers: () => hasPermission(PERMISSIONS.USER_MANAGE),
    createUser: () => hasPermission(PERMISSIONS.USER_CREATE),
    editUser: () => hasPermission(PERMISSIONS.USER_EDIT),
    deleteUser: () => hasPermission(PERMISSIONS.USER_DELETE),

    // 工作日志
    viewWorkLogs: () => hasPermission(PERMISSIONS.WORKLOG_VIEW),
    viewAllWorkLogs: () => hasPermission(PERMISSIONS.WORKLOG_VIEW_ALL),
    createWorkLog: () => hasPermission(PERMISSIONS.WORKLOG_CREATE),
    editWorkLog: () => hasPermission(PERMISSIONS.WORKLOG_EDIT),
    deleteWorkLog: () => hasPermission(PERMISSIONS.WORKLOG_DELETE),

    // 报修管理
    viewRepairs: () => hasPermission(PERMISSIONS.REPAIR_VIEW),
    createRepair: () => hasPermission(PERMISSIONS.REPAIR_CREATE),
    editRepair: () => hasPermission(PERMISSIONS.REPAIR_EDIT),
    assignRepair: () => hasPermission(PERMISSIONS.REPAIR_ASSIGN),
    manageRepairs: () => hasPermission(PERMISSIONS.REPAIR_MANAGE),

    // 库存管理
    viewInventory: () => hasPermission(PERMISSIONS.INVENTORY_VIEW),
    createInventory: () => hasPermission(PERMISSIONS.INVENTORY_CREATE),
    editInventory: () => hasPermission(PERMISSIONS.INVENTORY_EDIT),
    adjustInventory: () => hasPermission(PERMISSIONS.INVENTORY_ADJUST),
    countInventory: () => hasPermission(PERMISSIONS.INVENTORY_COUNT),

    // 报表
    viewReports: () => hasPermission(PERMISSIONS.REPORT_VIEW),
    exportReports: () => hasPermission(PERMISSIONS.REPORT_EXPORT),

    // 系统管理
    manageSystem: () => hasPermission(PERMISSIONS.SYSTEM_SETTINGS),
    viewSystemLogs: () => hasPermission(PERMISSIONS.SYSTEM_LOGS),

    // 部门和角色
    viewDepartments: () => hasPermission(PERMISSIONS.DEPARTMENT_VIEW),
    manageDepartments: () => hasPermission(PERMISSIONS.DEPARTMENT_MANAGE),
    viewRoles: () => hasPermission(PERMISSIONS.ROLE_VIEW),
    manageRoles: () => hasPermission(PERMISSIONS.ROLE_MANAGE)
  }

  return {
    userPermissions,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    isAdmin,
    isManager,
    isEngineer,
    canViewAll,
    canOnlyViewOwn,
    can
  }
}
