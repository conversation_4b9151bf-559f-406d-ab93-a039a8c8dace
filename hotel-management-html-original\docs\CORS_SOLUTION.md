# 企业微信CORS问题解决方案

## 问题说明

您遇到的 `Failed to fetch` 错误是由于浏览器的CORS（跨域资源共享）安全策略导致的。浏览器阻止了从 `localhost` 向 `qyapi.weixin.qq.com` 的直接请求。

## 🚀 解决方案

### 方案一：使用代理服务（推荐）

我已经为您创建了一个Node.js代理服务来解决这个问题。

#### 1. 安装Node.js

如果您还没有安装Node.js：
- 访问：https://nodejs.org/
- 下载并安装LTS版本

#### 2. 启动代理服务

**Windows用户**：
```bash
# 双击运行
start-proxy.bat

# 或者在命令行中运行
npm install
npm start
```

**Mac/Linux用户**：
```bash
npm install
npm start
```

#### 3. 验证服务

访问：http://localhost:3001/health
应该看到：`{"status":"ok","message":"企业微信代理服务运行正常"}`

#### 4. 测试功能

现在重新访问用户管理工具：
```
http://localhost:8080/wechat-user-manager.html
```

点击"从企业微信获取用户列表"，应该可以正常工作了。

### 方案二：手动配置用户映射

如果您不想使用代理服务，可以手动配置用户映射：

#### 1. 获取企业微信用户ID

登录企业微信管理后台：
1. 访问：https://work.weixin.qq.com/
2. 进入"通讯录"
3. 点击用户查看详情
4. 记录"账号"字段（这就是UserID）

#### 2. 手动编辑配置

编辑 `js/wechat-config.js` 文件：

```javascript
userMapping: {
    admins: {
        'junwei': 'WangJunWei'  // 您已经配置的
    },
    engineers: {
        // 添加工程师映射
        'engineer1': 'engineer_userid_1',
        'engineer2': 'engineer_userid_2'
    },
    users: {
        // 添加普通用户映射
        'user1': 'user_userid_1',
        'user2': 'user_userid_2'
    }
}
```

### 方案三：使用浏览器扩展

安装CORS浏览器扩展（仅用于开发测试）：

1. **Chrome用户**：
   - 安装"CORS Unblock"扩展
   - 启用扩展后重新测试

2. **Firefox用户**：
   - 安装"CORS Everywhere"扩展
   - 启用扩展后重新测试

⚠️ **注意**：这种方法仅适用于开发测试，不建议在生产环境使用。

## 🔧 代理服务详情

### 服务功能

代理服务提供以下API端点：

1. **获取Token**
   ```
   GET /api/wechat/gettoken?corpid=xxx&corpsecret=xxx
   ```

2. **获取部门列表**
   ```
   GET /api/wechat/department/list?access_token=xxx
   ```

3. **获取用户列表**
   ```
   GET /api/wechat/user/list?access_token=xxx&department_id=xxx
   ```

4. **发送消息**
   ```
   POST /api/wechat/message/send?access_token=xxx
   ```

### 服务特性

- ✅ **自动CORS处理**：解决跨域问题
- ✅ **错误处理**：完善的错误处理和日志
- ✅ **参数验证**：验证必要参数
- ✅ **健康检查**：提供服务状态检查
- ✅ **自动降级**：代理不可用时自动尝试直接调用

### 服务配置

代理服务默认运行在端口3001，您可以通过环境变量修改：

```bash
PORT=3002 npm start
```

如果修改了端口，需要同时更新 `js/wechat-work.js` 中的代理地址：

```javascript
this.proxyBase = 'http://localhost:3002/api/wechat';
```

## 🧪 测试步骤

### 1. 启动代理服务

```bash
npm start
```

看到以下输出表示启动成功：
```
企业微信代理服务启动成功，端口: 3001
健康检查: http://localhost:3001/health
```

### 2. 测试代理服务

访问健康检查端点：
```
http://localhost:3001/health
```

### 3. 测试用户管理工具

访问用户管理工具：
```
http://localhost:8080/wechat-user-manager.html
```

点击"从企业微信获取用户列表"，应该能够成功获取用户数据。

### 4. 测试消息发送

访问测试页面：
```
http://localhost:8080/wechat-test.html
```

输入您的企业微信UserID，测试消息发送功能。

## 🔍 故障排除

### 问题1：代理服务启动失败

**可能原因**：
- Node.js未安装
- 端口3001被占用
- 依赖包安装失败

**解决方案**：
```bash
# 检查Node.js版本
node --version

# 重新安装依赖
rm -rf node_modules
npm install

# 使用其他端口
PORT=3002 npm start
```

### 问题2：仍然出现CORS错误

**可能原因**：
- 代理服务未启动
- 代理地址配置错误

**解决方案**：
1. 确认代理服务正在运行
2. 检查浏览器控制台的网络请求
3. 确认代理地址配置正确

### 问题3：企业微信API调用失败

**可能原因**：
- 企业微信配置错误
- 网络连接问题
- API权限不足

**解决方案**：
1. 检查企业微信配置（corpId、agentId、secret）
2. 确认应用权限设置
3. 检查网络连接

## 📝 生产环境部署

在生产环境中，建议：

1. **使用HTTPS**：配置SSL证书
2. **环境变量**：使用环境变量管理敏感信息
3. **进程管理**：使用PM2等工具管理进程
4. **负载均衡**：使用Nginx等反向代理
5. **监控日志**：添加日志监控和告警

### PM2部署示例

```bash
# 安装PM2
npm install -g pm2

# 启动服务
pm2 start wechat-proxy.js --name "wechat-proxy"

# 查看状态
pm2 status

# 查看日志
pm2 logs wechat-proxy
```

## 📞 技术支持

如果您在使用过程中遇到问题：

1. **查看控制台日志**：按F12查看浏览器控制台
2. **检查网络请求**：查看Network标签页的请求状态
3. **查看代理服务日志**：检查代理服务的输出日志
4. **参考企业微信文档**：https://developer.work.weixin.qq.com/

---

**推荐使用方案一（代理服务）**，这是最稳定和安全的解决方案。
