<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">系统日志</h1>
          <p class="mt-1 text-sm text-gray-600">
            查看系统操作记录和用户行为日志
          </p>
        </div>
        
        <div class="mt-4 sm:mt-0 flex space-x-3">
          <!-- 时间范围选择 -->
          <select 
            v-model="selectedPeriod"
            @change="loadLogs"
            class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="1">今天</option>
            <option value="7">最近7天</option>
            <option value="30">最近30天</option>
            <option value="90">最近90天</option>
          </select>
          
          <button 
            @click="exportLogs"
            class="border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 transition-colors flex items-center"
          >
            <Icon name="mdi:download" size="16" class="mr-1" />
            导出日志
          </button>
          
          <button 
            @click="clearLogs"
            class="border border-red-300 text-red-700 px-4 py-2 rounded-md hover:bg-red-50 transition-colors flex items-center"
          >
            <Icon name="mdi:delete" size="16" class="mr-1" />
            清理日志
          </button>
        </div>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">操作类型</label>
          <select 
            v-model="filters.action"
            @change="loadLogs"
            class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">全部操作</option>
            <option value="user_login">用户登录</option>
            <option value="user_logout">用户登出</option>
            <option value="user_create">创建用户</option>
            <option value="user_update">更新用户</option>
            <option value="user_delete">删除用户</option>
            <option value="worklog_create">创建日志</option>
            <option value="repair_create">创建报修</option>
            <option value="repair_update">更新报修</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">模块</label>
          <select 
            v-model="filters.module"
            @change="loadLogs"
            class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">全部模块</option>
            <option value="auth">认证模块</option>
            <option value="admin">管理模块</option>
            <option value="worklog">工作日志</option>
            <option value="repair">报修管理</option>
            <option value="inventory">库存管理</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">用户</label>
          <input 
            v-model="filters.userName"
            @input="debounceLoadLogs"
            type="text"
            class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="搜索用户名"
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">IP地址</label>
          <input 
            v-model="filters.ip"
            @input="debounceLoadLogs"
            type="text"
            class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="搜索IP地址"
          />
        </div>
      </div>
    </div>

    <!-- 日志列表 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">操作日志</h3>
      </div>
      
      <div v-if="loading" class="p-8 text-center">
        <Icon name="mdi:loading" size="32" class="text-gray-400 animate-spin mx-auto mb-4" />
        <p class="text-gray-600">加载中...</p>
      </div>
      
      <div v-else-if="systemLogs.length === 0" class="p-8 text-center">
        <Icon name="mdi:file-document-outline" size="64" class="text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无日志记录</h3>
        <p class="text-gray-600">当前筛选条件下没有找到日志记录</p>
      </div>
      
      <div v-else class="divide-y divide-gray-200">
        <div 
          v-for="log in systemLogs" 
          :key="log.id"
          class="p-6 hover:bg-gray-50 transition-colors"
        >
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-3 mb-2">
                <span 
                  :class="getActionClass(log.action)"
                  class="px-2 py-1 text-xs font-medium rounded-full"
                >
                  {{ getActionName(log.action) }}
                </span>
                <span class="text-sm text-gray-500">{{ log.module }}</span>
              </div>
              
              <p class="text-gray-900 mb-2">{{ log.description }}</p>
              
              <div class="flex items-center space-x-6 text-sm text-gray-500">
                <div class="flex items-center">
                  <Icon name="mdi:account" size="16" class="mr-1" />
                  {{ log.userName || '未知用户' }}
                </div>
                <div v-if="log.ip" class="flex items-center">
                  <Icon name="mdi:ip-network" size="16" class="mr-1" />
                  {{ log.ip }}
                </div>
                <div class="flex items-center">
                  <Icon name="mdi:clock" size="16" class="mr-1" />
                  {{ formatDateTime(log.createdAt) }}
                </div>
              </div>
            </div>
            
            <div class="flex items-center space-x-2">
              <button 
                @click="viewLogDetail(log)"
                class="text-blue-600 hover:text-blue-700 p-2 rounded-md hover:bg-blue-50 transition-colors"
                title="查看详情"
              >
                <Icon name="mdi:eye" size="16" />
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 分页 -->
      <div v-if="systemLogs.length > 0" class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
        <div class="text-sm text-gray-700">
          显示第 {{ (currentPage * 10) + 1 }} - {{ Math.min((currentPage + 1) * 10, systemLogs.length) }} 条，共 {{ systemLogs.length }} 条记录
        </div>
        <div class="flex space-x-2">
          <button 
            @click="prevPage"
            :disabled="currentPage === 0"
            class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            上一页
          </button>
          <button 
            @click="nextPage"
            :disabled="(currentPage + 1) * 10 >= systemLogs.length"
            class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            下一页
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { SystemLog } from '~/types'

// 页面元数据
definePageMeta({
  title: '系统日志',
  middleware: 'auth',
  requiresAdmin: true
})

// Store
const adminStore = useAdminStore()

// 响应式数据
const selectedPeriod = ref('7')
const currentPage = ref(0)

const filters = reactive({
  action: '',
  module: '',
  userName: '',
  ip: ''
})

// 计算属性
const systemLogs = computed(() => adminStore.systemLogs)
const loading = computed(() => adminStore.loading)

// 操作类型映射
const actionNames: Record<string, string> = {
  'user_login': '用户登录',
  'user_logout': '用户登出',
  'user_create': '创建用户',
  'user_update': '更新用户',
  'user_delete': '删除用户',
  'worklog_create': '创建日志',
  'repair_create': '创建报修',
  'repair_update': '更新报修'
}

// 方法
const formatDateTime = (date: Date) => {
  return new Date(date).toLocaleString('zh-CN')
}

const getActionName = (action: string) => {
  return actionNames[action] || action
}

const getActionClass = (action: string) => {
  if (action.includes('create')) return 'bg-green-100 text-green-800'
  if (action.includes('update')) return 'bg-blue-100 text-blue-800'
  if (action.includes('delete')) return 'bg-red-100 text-red-800'
  if (action.includes('login')) return 'bg-purple-100 text-purple-800'
  return 'bg-gray-100 text-gray-800'
}

const loadLogs = async () => {
  await adminStore.fetchSystemLogs({
    page: currentPage.value,
    filters: filters
  })
}

// 防抖加载
const debounceLoadLogs = debounce(loadLogs, 500)

const exportLogs = () => {
  // 导出日志功能
  alert('导出功能待实现')
}

const clearLogs = () => {
  if (confirm('确定要清理历史日志吗？此操作不可恢复！')) {
    alert('清理功能待实现')
  }
}

const viewLogDetail = (log: SystemLog) => {
  // 查看日志详情
  alert(`日志详情：\n${JSON.stringify(log, null, 2)}`)
}

const prevPage = () => {
  if (currentPage.value > 0) {
    currentPage.value--
    loadLogs()
  }
}

const nextPage = () => {
  currentPage.value++
  loadLogs()
}

// 防抖函数
function debounce(func: Function, wait: number) {
  let timeout: NodeJS.Timeout
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 生命周期
onMounted(async () => {
  await loadLogs()
})

// 页面标题
useHead({
  title: '系统日志 - 酒店管理系统'
})
</script>
