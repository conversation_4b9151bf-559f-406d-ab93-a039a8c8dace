<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">库存管理</h1>
          <p class="mt-1 text-sm text-gray-600">
            管理酒店物品库存，跟踪出入库记录
          </p>
        </div>
        
        <div class="mt-4 sm:mt-0 flex space-x-3">
          <button 
            @click="showCreateModal = true"
            class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center"
          >
            <Icon name="mdi:plus" size="16" class="mr-1" />
            新增物品
          </button>
          
          <button
            @click="showStockModal = true"
            class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors flex items-center"
          >
            <Icon name="mdi:package-variant" size="16" class="mr-1" />
            库存调整
          </button>

          <button
            @click="showCountModal = true"
            class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors flex items-center"
          >
            <Icon name="mdi:clipboard-list" size="16" class="mr-1" />
            库存盘点
          </button>

          <button
            @click="showFilterModal = true"
            class="border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 transition-colors flex items-center"
          >
            <Icon name="mdi:filter" size="16" class="mr-1" />
            筛选
          </button>

          <ExportButton
            :data="items"
            :columns="exportColumns"
            filename="库存列表"
            title="库存管理数据"
            @exported="handleExported"
          />
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:package-variant" size="24" class="text-blue-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">总物品数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.total }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:alert-circle" size="24" class="text-orange-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">低库存</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.lowStock }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:close-circle" size="24" class="text-red-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">缺货</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.outOfStock }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:tag" size="24" class="text-green-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">分类数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.categories }}</p>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <Icon name="mdi:currency-cny" size="24" class="text-purple-600 mr-3" />
          <div>
            <p class="text-sm text-gray-600">总价值</p>
            <p class="text-2xl font-semibold text-gray-900">¥{{ stats.totalValue.toFixed(0) }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 库存警告 -->
    <div v-if="alerts.length > 0" class="mb-6">
      <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
        <div class="flex items-center mb-3">
          <Icon name="mdi:alert" size="20" class="text-orange-600 mr-2" />
          <h3 class="text-sm font-medium text-orange-800">库存警告</h3>
        </div>
        <div class="space-y-2">
          <div 
            v-for="alert in alerts.slice(0, 3)" 
            :key="alert.id"
            class="text-sm text-orange-700"
          >
            {{ alert.message }}
          </div>
          <div v-if="alerts.length > 3" class="text-sm text-orange-600">
            还有 {{ alerts.length - 3 }} 条警告...
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选标签 -->
    <div v-if="hasActiveFilters" class="mb-6">
      <div class="flex flex-wrap items-center gap-2">
        <span class="text-sm text-gray-600">当前筛选:</span>
        
        <span 
          v-if="currentCategory"
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
        >
          {{ currentCategory }}
          <button @click="clearCategoryFilter" class="ml-1 hover:text-blue-600">
            <Icon name="mdi:close" size="12" />
          </button>
        </span>
        
        <span 
          v-if="currentLocation"
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
        >
          {{ currentLocation }}
          <button @click="clearLocationFilter" class="ml-1 hover:text-green-600">
            <Icon name="mdi:close" size="12" />
          </button>
        </span>
        
        <button 
          @click="clearAllFilters"
          class="text-xs text-gray-500 hover:text-gray-700 underline"
        >
          清除所有筛选
        </button>
      </div>
    </div>

    <!-- 库存列表 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <!-- 表格头部 -->
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900">库存清单</h3>
          <div class="flex items-center space-x-2">
            <button 
              @click="viewMode = 'table'"
              :class="viewMode === 'table' ? 'bg-blue-100 text-blue-700' : 'text-gray-500 hover:text-gray-700'"
              class="p-2 rounded-md transition-colors"
            >
              <Icon name="mdi:table" size="16" />
            </button>
            <button 
              @click="viewMode = 'grid'"
              :class="viewMode === 'grid' ? 'bg-blue-100 text-blue-700' : 'text-gray-500 hover:text-gray-700'"
              class="p-2 rounded-md transition-colors"
            >
              <Icon name="mdi:view-grid" size="16" />
            </button>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="inventoryStore.loading && items.length === 0" class="p-6">
        <div class="space-y-4">
          <div v-for="i in 5" :key="i" class="animate-pulse">
            <div class="flex items-center space-x-4">
              <div class="w-12 h-12 bg-gray-200 rounded"></div>
              <div class="flex-1 space-y-2">
                <div class="h-4 bg-gray-200 rounded w-1/4"></div>
                <div class="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
              <div class="w-20 h-4 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else-if="items.length === 0" class="text-center py-12">
        <Icon name="mdi:package-variant-closed" size="64" class="text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无库存物品</h3>
        <p class="text-gray-600 mb-6">开始添加你的第一个库存物品吧</p>
        <button 
          @click="showCreateModal = true"
          class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          添加物品
        </button>
      </div>

      <!-- 表格视图 -->
      <div v-else-if="viewMode === 'table'" class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                物品信息
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                分类
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                库存数量
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                位置
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <InventoryTableRow
              v-for="item in items"
              :key="item.id"
              :item="item"
              @edit="handleEditItem"
              @delete="handleDeleteItem"
              @adjust="handleAdjustStock"
              @stock-in="handleStockIn"
              @stock-out="handleStockOut"
            />
          </tbody>
        </table>
      </div>

      <!-- 网格视图 -->
      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 p-6">
        <InventoryCard
          v-for="item in items"
          :key="item.id"
          :item="item"
          @edit="handleEditItem"
          @delete="handleDeleteItem"
          @adjust="handleAdjustStock"
          @stock-in="handleStockIn"
          @stock-out="handleStockOut"
        />
      </div>
    </div>

    <!-- 模态框 -->
    <CreateInventoryModal
      v-model:show="showCreateModal"
      :edit-item="editingItem"
      @created="handleItemCreated"
      @updated="handleItemUpdated"
    />

    <StockAdjustModal
      v-model:show="showStockModal"
      :item="selectedItem"
      @adjusted="handleStockAdjusted"
    />

    <InventoryFilterModal
      v-model:show="showFilterModal"
      @apply="handleApplyFilters"
    />

    <StockCountModal
      v-model:show="showCountModal"
      @created="handleCountPlanCreated"
      @completed="handleCountCompleted"
    />
  </div>
</template>

<script setup lang="ts">
import type { InventoryItem } from '~/types'

// 导入组件
import CreateInventoryModal from '~/components/inventory/CreateInventoryModal.vue'
import StockAdjustModal from '~/components/inventory/StockAdjustModal.vue'
import InventoryFilterModal from '~/components/inventory/InventoryFilterModal.vue'
import StockCountModal from '~/components/inventory/StockCountModal.vue'
import InventoryCard from '~/components/inventory/InventoryCard.vue'
import InventoryTableRow from '~/components/inventory/InventoryTableRow.vue'
import ExportButton from '~/components/common/ExportButton.vue'

// 页面元数据
definePageMeta({
  title: '库存管理',
  middleware: 'auth'
})

// 状态管理
const inventoryStore = useInventoryStore()

// 响应式数据
const showCreateModal = ref(false)
const showStockModal = ref(false)
const showFilterModal = ref(false)
const showCountModal = ref(false)
const editingItem = ref<InventoryItem | null>(null)
const selectedItem = ref<InventoryItem | null>(null)
const viewMode = ref<'table' | 'grid'>('table')

// 计算属性
const items = computed(() => inventoryStore.filteredItems)
const stats = computed(() => inventoryStore.inventoryStats)
const alerts = computed(() => inventoryStore.alerts.filter(alert => !alert.isRead))

const currentCategory = computed(() => inventoryStore.filters.category)
const currentLocation = computed(() => inventoryStore.filters.location)

const hasActiveFilters = computed(() => {
  return !!(currentCategory.value || currentLocation.value || inventoryStore.filters.lowStock)
})

// 导出列配置
const exportColumns = [
  { key: 'name', title: '物品名称', width: 20 },
  { key: 'category', title: '分类', width: 15 },
  { key: 'quantity', title: '当前库存', width: 12 },
  { key: 'unit', title: '单位', width: 10 },
  { key: 'minStock', title: '最低库存', width: 12 },
  { key: 'maxStock', title: '最高库存', width: 12 },
  {
    key: 'price',
    title: '单价',
    width: 12,
    formatter: (value: number) => value ? `¥${value.toFixed(2)}` : ''
  },
  { key: 'supplier', title: '供应商', width: 15 },
  { key: 'location', title: '存放位置', width: 15 },
  { key: 'description', title: '描述', width: 25 },
  {
    key: 'createdAt',
    title: '创建时间',
    width: 20,
    formatter: (value: Date) => {
      return value ? new Date(value).toLocaleString('zh-CN') : ''
    }
  }
]

// 方法
const handleItemCreated = () => {
  showCreateModal.value = false
  editingItem.value = null
  inventoryStore.fetchItems({ page: 0 })
}

const handleItemUpdated = () => {
  showCreateModal.value = false
  editingItem.value = null
}

const handleEditItem = (item: InventoryItem) => {
  editingItem.value = item
  showCreateModal.value = true
}

const handleDeleteItem = async (item: InventoryItem) => {
  if (confirm(`确定要删除 ${item.name} 吗？`)) {
    await inventoryStore.deleteItem(item.id)
  }
}

const handleAdjustStock = (item: InventoryItem) => {
  selectedItem.value = item
  showStockModal.value = true
}

const handleStockIn = (item: InventoryItem) => {
  selectedItem.value = item
  showStockModal.value = true
}

const handleStockOut = (item: InventoryItem) => {
  selectedItem.value = item
  showStockModal.value = true
}

const handleStockAdjusted = () => {
  showStockModal.value = false
  selectedItem.value = null
}

const clearCategoryFilter = () => {
  inventoryStore.setFilters({ category: undefined })
}

const clearLocationFilter = () => {
  inventoryStore.setFilters({ location: undefined })
}

const clearAllFilters = () => {
  inventoryStore.clearFilters()
}

const handleApplyFilters = (filters: any) => {
  showFilterModal.value = false
  inventoryStore.setFilters(filters)
}

const handleCountPlanCreated = () => {
  showCountModal.value = false
  // 可以添加成功提示
  alert('盘点计划创建成功！')
}

const handleCountCompleted = () => {
  showCountModal.value = false
  // 刷新库存数据
  inventoryStore.fetchItems({ page: 0 })
  alert('库存盘点完成！')
}

// 导出处理
const handleExported = (format: string, success: boolean) => {
  if (success) {
    console.log(`${format} 导出成功`)
  } else {
    console.error(`${format} 导出失败`)
  }
}

// 生命周期
onMounted(async () => {
  await inventoryStore.fetchItems({ page: 0 })
})

// 页面标题
useHead({
  title: '库存管理 - 酒店管理系统'
})
</script>
