# 工作汇报系统设计方案

## 1. 功能概述

从当天的日志数据中自动生成工作汇报，截止时间为当天晚上8点。

## 2. 数据结构分析

### 现有数据表
1. **WorkLog** - 工作日志
   - pageType: 'main' (普通工作日志)
   - pageType: 'repair' (维修记录)
   - pageType: 'powerstation', 'waterfilter', 'aircondition' (其他专业记录)

2. **报修记录** (pageType: 'repair')
   - serialNumber: 流水号
   - repairSource: 报修来源
   - department: 报修部门
   - reporter: 报修人
   - room: 涉及房间
   - description: 报修内容
   - status: 处置结果 ('已完成' | '待处理')
   - assignee: 登录用户

### 需要新增的数据结构
```javascript
// 工作汇报记录
const WorkReport = {
    objectId: String,
    date: Date,           // 汇报日期
    user: Pointer,        // 用户指针
    reportData: {
        // 基本信息
        userName: String,
        realName: String,
        date: String,
        
        // 今日报修
        repairSummary: {
            total: Number,
            completed: Number,
            pending: Number,
            items: [
                {
                    serialNumber: String,
                    description: String,
                    status: String,
                    room: String
                }
            ]
        },
        
        // 今日工作
        workSummary: {
            total: Number,
            items: [
                {
                    content: String,
                    createdAt: Date,
                    pageType: String
                }
            ]
        },
        
        // 工作完成度
        completionRate: {
            percentage: Number,
            completedTasks: Number,
            totalTasks: Number
        }
    },
    createdAt: Date,
    updatedAt: Date
}
```

## 3. 汇报模板

```
日期：2025年7月5日 工作汇报
用户：张三

今日报修：
- 总计：5项
- 已完成：3项
- 待处理：2项
- 详细列表：
  1. [001] 客房空调不制冷 - 已完成
  2. [002] 大堂灯具故障 - 已完成
  3. [003] 电梯异响 - 待处理

今日工作：
- 总计：8项
- 详细列表：
  1. 完成客房设备巡检
  2. 处理前台报修问题
  3. 更换大堂LED灯具

今日工作完成度：75% (6/8项已完成)
```

## 4. 页面设计

### 4.1 工作汇报生成页面 (work-report.html)
- 日期选择器（默认当天）
- 用户选择器（管理员可选择用户，普通用户只能看自己）
- 生成汇报按钮
- 汇报预览区域
- 导出功能

### 4.2 汇报展示组件
- 汇报标题
- 用户信息
- 报修统计图表
- 工作列表
- 完成度进度条

## 5. 核心功能实现

### 5.1 数据查询逻辑
```javascript
async function generateDailyReport(userId, date) {
    // 1. 查询当天8点前的所有日志
    const endTime = new Date(date);
    endTime.setHours(20, 0, 0, 0); // 晚上8点
    
    const startTime = new Date(date);
    startTime.setHours(0, 0, 0, 0); // 当天0点
    
    // 2. 查询工作日志
    const workLogs = await queryWorkLogs(userId, startTime, endTime);
    
    // 3. 查询报修记录
    const repairLogs = await queryRepairLogs(userId, startTime, endTime);
    
    // 4. 生成汇报数据
    return generateReportData(workLogs, repairLogs);
}
```

### 5.2 汇报生成算法
1. **报修统计**：
   - 统计总数、已完成数、待处理数
   - 计算完成率
   - 提取关键信息

2. **工作统计**：
   - 按pageType分类统计
   - 提取工作内容摘要
   - 计算工作量

3. **完成度计算**：
   - 基于报修完成情况
   - 结合工作日志数量
   - 生成综合评分

## 6. 实现步骤

1. ✅ 分析现有数据结构
2. 🔄 创建工作汇报页面
3. ⏳ 实现数据查询逻辑
4. ⏳ 开发汇报生成算法
5. ⏳ 设计汇报展示界面
6. ⏳ 添加导出功能

## 7. 技术要点

### 7.1 时间处理
- 使用本地时区
- 截止时间：当天20:00
- 支持历史日期查询

### 7.2 权限控制
- 普通用户：只能查看自己的汇报
- 管理员：可以查看所有用户的汇报
- 数据隔离和安全

### 7.3 性能优化
- 数据缓存机制
- 分页查询大量数据
- 异步处理和加载状态

## 8. 扩展功能

1. **汇报对比**：不同日期的汇报对比
2. **统计图表**：工作量趋势图
3. **自动推送**：定时生成并推送汇报
4. **模板自定义**：允许自定义汇报格式
