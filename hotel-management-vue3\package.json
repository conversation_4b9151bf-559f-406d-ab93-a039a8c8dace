{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/icon": "^1.15.0", "@nuxtjs/tailwindcss": "^6.14.0", "@pinia/nuxt": "^0.11.1", "@vueuse/core": "^13.5.0", "@vueuse/nuxt": "^13.5.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "leancloud-storage": "^4.15.2", "naive-ui": "^2.42.0", "nuxt": "^3.17.6", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-router": "^4.5.1", "xlsx": "^0.18.5"}}