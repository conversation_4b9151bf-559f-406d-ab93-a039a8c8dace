<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <div class="flex items-center justify-between mb-6">
      <div>
        <h3 class="text-lg font-semibold text-gray-900">企业微信设置</h3>
        <p class="text-sm text-gray-600">配置企业微信通知和集成功能</p>
      </div>
      <div class="flex items-center">
        <Icon 
          :name="isWeChatWork ? 'mdi:check-circle' : 'mdi:alert-circle'" 
          :size="20" 
          :class="isWeChatWork ? 'text-green-500' : 'text-orange-500'"
        />
        <span class="ml-2 text-sm" :class="isWeChatWork ? 'text-green-600' : 'text-orange-600'">
          {{ isWeChatWork ? '企业微信环境' : '非企业微信环境' }}
        </span>
      </div>
    </div>

    <!-- 通知设置 -->
    <div class="space-y-6">
      <div>
        <h4 class="text-md font-medium text-gray-900 mb-4">通知设置</h4>
        
        <div class="space-y-4">
          <!-- 工作日志通知 -->
          <div class="flex items-center justify-between">
            <div>
              <label class="text-sm font-medium text-gray-700">工作日志通知</label>
              <p class="text-xs text-gray-500">创建工作日志时发送通知</p>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input 
                v-model="settings.workLogNotification" 
                type="checkbox" 
                class="sr-only peer"
                @change="saveSettings"
              />
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <!-- 变电站日志通知 -->
          <div class="flex items-center justify-between">
            <div>
              <label class="text-sm font-medium text-gray-700">变电站日志通知</label>
              <p class="text-xs text-gray-500">变电站相关日志自动通知</p>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input 
                v-model="settings.powerstationNotification" 
                type="checkbox" 
                class="sr-only peer"
                @change="saveSettings"
              />
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <!-- 施工登记通知 -->
          <div class="flex items-center justify-between">
            <div>
              <label class="text-sm font-medium text-gray-700">施工登记通知</label>
              <p class="text-xs text-gray-500">施工登记时发送安全提醒</p>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input 
                v-model="settings.constructionNotification" 
                type="checkbox" 
                class="sr-only peer"
                @change="saveSettings"
              />
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <!-- 报修通知 -->
          <div class="flex items-center justify-between">
            <div>
              <label class="text-sm font-medium text-gray-700">报修通知</label>
              <p class="text-xs text-gray-500">新报修工单自动通知相关人员</p>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input 
                v-model="settings.repairNotification" 
                type="checkbox" 
                class="sr-only peer"
                @change="saveSettings"
              />
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>
      </div>

      <!-- 测试功能 -->
      <div class="border-t border-gray-200 pt-6">
        <h4 class="text-md font-medium text-gray-900 mb-4">测试功能</h4>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              测试消息内容
            </label>
            <textarea 
              v-model="testMessage"
              rows="3"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="输入测试消息内容..."
            ></textarea>
          </div>
          
          <div class="flex space-x-3">
            <button 
              @click="sendTestMessage"
              :disabled="!testMessage.trim() || sending"
              class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              <Icon v-if="sending" name="mdi:loading" size="16" class="mr-1 animate-spin" />
              {{ sending ? '发送中...' : '发送测试消息' }}
            </button>
            
            <button 
              @click="checkConnection"
              :disabled="checking"
              class="border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 transition-colors disabled:opacity-50 flex items-center"
            >
              <Icon v-if="checking" name="mdi:loading" size="16" class="mr-1 animate-spin" />
              {{ checking ? '检查中...' : '检查连接' }}
            </button>
          </div>
        </div>
      </div>

      <!-- 状态信息 -->
      <div v-if="statusMessage" class="border-t border-gray-200 pt-6">
        <div 
          class="p-4 rounded-md"
          :class="statusType === 'success' ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'"
        >
          <div class="flex">
            <Icon 
              :name="statusType === 'success' ? 'mdi:check-circle' : 'mdi:alert-circle'" 
              size="16" 
              :class="statusType === 'success' ? 'text-green-400' : 'text-red-400'"
              class="mr-2 mt-0.5"
            />
            <div :class="statusType === 'success' ? 'text-green-700' : 'text-red-700'" class="text-sm">
              {{ statusMessage }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const { isWeChatWork, sendTextMessage, getAccessToken } = useWechat()

// 响应式数据
const settings = reactive({
  workLogNotification: true,
  powerstationNotification: true,
  constructionNotification: true,
  repairNotification: true
})

const testMessage = ref('这是一条来自酒店管理系统的测试消息 📱')
const sending = ref(false)
const checking = ref(false)
const statusMessage = ref('')
const statusType = ref<'success' | 'error'>('success')

// 方法
const saveSettings = () => {
  // 保存设置到本地存储
  if (process.client) {
    localStorage.setItem('wechat_settings', JSON.stringify(settings))
  }
  
  showStatus('设置已保存', 'success')
}

const loadSettings = () => {
  // 从本地存储加载设置
  if (process.client) {
    const saved = localStorage.getItem('wechat_settings')
    if (saved) {
      Object.assign(settings, JSON.parse(saved))
    }
  }
}

const sendTestMessage = async () => {
  if (!testMessage.value.trim()) return
  
  sending.value = true
  statusMessage.value = ''
  
  try {
    const result = await sendTextMessage(testMessage.value)
    
    if (result.success) {
      showStatus('测试消息发送成功！', 'success')
    } else {
      showStatus(`发送失败: ${result.error}`, 'error')
    }
  } catch (error: any) {
    showStatus(`发送失败: ${error.message}`, 'error')
  } finally {
    sending.value = false
  }
}

const checkConnection = async () => {
  checking.value = true
  statusMessage.value = ''
  
  try {
    const result = await getAccessToken()
    
    if (result.success) {
      showStatus('企业微信连接正常！', 'success')
    } else {
      showStatus(`连接失败: ${result.error}`, 'error')
    }
  } catch (error: any) {
    showStatus(`连接失败: ${error.message}`, 'error')
  } finally {
    checking.value = false
  }
}

const showStatus = (message: string, type: 'success' | 'error') => {
  statusMessage.value = message
  statusType.value = type
  
  // 3秒后自动清除状态消息
  setTimeout(() => {
    statusMessage.value = ''
  }, 3000)
}

// 生命周期
onMounted(() => {
  loadSettings()
})
</script>
