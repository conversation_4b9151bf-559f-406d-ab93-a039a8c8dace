// middleware/auth.ts
export default defineNuxtRouteMiddleware(async (to) => {
  const authStore = useAuthStore()

  // 检查是否需要认证
  const publicRoutes = ['/auth/login', '/auth/register']
  const isPublicRoute = publicRoutes.includes(to.path)

  // 如果是公开路由，直接通过
  if (isPublicRoute) {
    return
  }

  // 确保登录状态已经检查过
  if (!authStore.isLoggedIn) {
    // 尝试检查登录状态
    const isAuthenticated = await authStore.checkAuth()

    if (!isAuthenticated) {
      // 保存用户想要访问的页面
      const redirectTo = to.fullPath
      return navigateTo(`/auth/login?redirect=${encodeURIComponent(redirectTo)}`)
    }
  }

  // 检查页面元数据中的权限要求
  const requiresAdmin = to.meta.requiresAdmin
  const isAdminRoute = to.path.startsWith('/admin')

  // 检查管理员权限
  if ((requiresAdmin || isAdminRoute) && !authStore.isAdmin) {
    throw createError({
      statusCode: 403,
      statusMessage: '权限不足，无法访问此页面'
    })
  }
})
