<template>
  <Teleport to="body">
    <div class="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
      <TransitionGroup
        name="notification"
        tag="div"
        class="space-y-2"
      >
        <div
          v-for="notification in notifications"
          :key="notification.id"
          :class="[
            'bg-white rounded-lg shadow-lg border-l-4 p-4 relative',
            getNotificationClass(notification.type)
          ]"
        >
          <!-- 关闭按钮 -->
          <button
            @click="removeNotification(notification.id)"
            class="absolute top-2 right-2 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <Icon name="mdi:close" size="16" />
          </button>

          <!-- 图标 -->
          <div class="flex items-start">
            <div class="flex-shrink-0 mr-3">
              <Icon 
                :name="getNotificationIcon(notification.type)" 
                size="20" 
                :class="getIconClass(notification.type)"
              />
            </div>

            <!-- 内容 -->
            <div class="flex-1 min-w-0 pr-6">
              <h4 class="text-sm font-medium text-gray-900 mb-1">
                {{ notification.title }}
              </h4>
              
              <p 
                v-if="notification.message" 
                class="text-sm text-gray-600 mb-2"
              >
                {{ notification.message }}
              </p>

              <!-- 操作按钮 -->
              <div 
                v-if="notification.actions && notification.actions.length > 0"
                class="flex space-x-2 mt-3"
              >
                <button
                  v-for="action in notification.actions"
                  :key="action.label"
                  @click="handleAction(action, notification.id)"
                  :class="[
                    'px-3 py-1 text-xs font-medium rounded transition-colors',
                    action.style === 'primary' 
                      ? 'bg-blue-600 text-white hover:bg-blue-700'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  ]"
                >
                  {{ action.label }}
                </button>
              </div>

              <!-- 时间戳 -->
              <div class="text-xs text-gray-400 mt-2">
                {{ formatTime(notification.createdAt) }}
              </div>
            </div>
          </div>
        </div>
      </TransitionGroup>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { useGlobalNotifications, type NotificationAction } from '~/composables/useNotifications'

const { notifications, removeNotification } = useGlobalNotifications()

// 获取通知样式类
const getNotificationClass = (type: string) => {
  const classes = {
    success: 'border-green-400',
    error: 'border-red-400',
    warning: 'border-yellow-400',
    info: 'border-blue-400'
  }
  return classes[type as keyof typeof classes] || classes.info
}

// 获取通知图标
const getNotificationIcon = (type: string) => {
  const icons = {
    success: 'mdi:check-circle',
    error: 'mdi:alert-circle',
    warning: 'mdi:alert',
    info: 'mdi:information'
  }
  return icons[type as keyof typeof icons] || icons.info
}

// 获取图标样式类
const getIconClass = (type: string) => {
  const classes = {
    success: 'text-green-500',
    error: 'text-red-500',
    warning: 'text-yellow-500',
    info: 'text-blue-500'
  }
  return classes[type as keyof typeof classes] || classes.info
}

// 处理操作按钮点击
const handleAction = (action: NotificationAction, notificationId: string) => {
  action.action()
  removeNotification(notificationId)
}

// 格式化时间
const formatTime = (date: Date) => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 24小时内
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}
</script>

<style scoped>
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.notification-move {
  transition: transform 0.3s ease;
}
</style>
