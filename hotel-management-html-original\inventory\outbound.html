<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>出库管理 - 库存管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- LeanCloud SDK - 使用更稳定的 CDN 源 -->
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <!-- 备用 CDN 源 -->
    <script>
        window.addEventListener('load', function() {
            if (typeof AV === 'undefined') {
                console.log('主 CDN 加载失败，尝试备用源...');
                var script = document.createElement('script');
                script.src = 'https://cdn.bootcdn.net/ajax/libs/leancloud-storage/4.15.2/av-min.js';
                script.onerror = function() {
                    console.error('所有 CDN 源都加载失败，请检查网络连接');
                    alert('网络连接异常，请检查网络后刷新页面');
                };
                document.head.appendChild(script);
            }
        });
    </script>
    <style>
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .btn-fix {
            touch-action: manipulation;
        }
        .suggestion-item {
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #f3f4f6;
        }
        .suggestion-item:hover {
            background-color: #f9fafb;
        }
        .suggestion-item:last-child {
            border-bottom: none;
        }
        .suggestion-item.selected {
            background-color: #dbeafe;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="index.html" class="text-blue-600 hover:text-blue-800 mr-4">
                        ← 返回库存管理
                    </a>
                    <h1 class="text-xl font-semibold text-gray-900">出库管理</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- 用户信息 -->
                    <div id="userInfo" class="flex items-center space-x-2" style="display: none;">
                        <span class="text-sm text-gray-700">用户：</span>
                        <span id="realName" class="text-sm font-medium text-gray-900"></span>
                        <button id="logoutBtn" class="text-sm text-red-600 hover:text-red-800 btn-fix">退出</button>
                    </div>
                    <button id="loginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                        登录
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 出库表单 -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">商品出库</h2>
            <form id="outboundForm" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="relative">
                        <label for="productName" class="block text-sm font-medium text-gray-700 mb-1">商品名称</label>
                        <input type="text" id="productName" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" autocomplete="off">
                        <div id="productNameSuggestions" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-48 overflow-y-auto hidden">
                            <!-- 商品名称联想建议将在这里显示 -->
                        </div>
                    </div>
                    <div class="relative">
                        <label for="productCode" class="block text-sm font-medium text-gray-700 mb-1">商品编码</label>
                        <input type="text" id="productCode" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" autocomplete="off">
                        <div id="productCodeSuggestions" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-48 overflow-y-auto hidden">
                            <!-- 商品编码联想建议将在这里显示 -->
                        </div>
                    </div>
                    <div>
                        <label for="quantity" class="block text-sm font-medium text-gray-700 mb-1">出库数量</label>
                        <input type="number" id="quantity" required min="1" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <label for="unit" class="block text-sm font-medium text-gray-700 mb-1">单位</label>
                        <select id="unit" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">请选择单位</option>
                            <option value="个">个</option>
                            <option value="台">台</option>
                            <option value="套">套</option>
                            <option value="箱">箱</option>
                            <option value="包">包</option>
                        </select>
                    </div>
                    <div>
                        <label for="warehouse" class="block text-sm font-medium text-gray-700 mb-1">出库仓库</label>
                        <select id="warehouse" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">请选择仓库</option>
                            <option value="主仓库">主仓库</option>
                            <option value="备用仓库">备用仓库</option>
                            <option value="临时仓库">临时仓库</option>
                        </select>
                    </div>
                    <div>
                        <label for="recipient" class="block text-sm font-medium text-gray-700 mb-1">领用人</label>
                        <input type="text" id="recipient" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <label for="purpose" class="block text-sm font-medium text-gray-700 mb-1">用途</label>
                        <select id="purpose" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">请选择用途</option>
                            <option value="维修使用">维修使用</option>
                            <option value="项目使用">项目使用</option>
                            <option value="日常消耗">日常消耗</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>
                    <div>
                        <label for="department" class="block text-sm font-medium text-gray-700 mb-1">领用部门</label>
                        <input type="text" id="department" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                </div>
                <div>
                    <label for="remarks" class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                    <textarea id="remarks" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"></textarea>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" id="resetBtn" class="px-4 py-2 text-gray-600 hover:text-gray-800 btn-fix">重置</button>
                    <button type="submit" class="bg-red-500 hover:bg-red-600 text-white px-6 py-2 rounded-md btn-fix">确认出库</button>
                </div>
            </form>
        </div>

        <!-- 出库记录 -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">出库记录</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">商品信息</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数量</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">仓库</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">领用信息</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">出库时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作人</th>
                        </tr>
                    </thead>
                    <tbody id="outboundTableBody" class="bg-white divide-y divide-gray-200">
                        <!-- 出库记录将在这里显示 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 引入模块化JS文件 -->
    <script src="../js/config.js"></script>
    <script src="../js/permission-manager.js"></script>
    <script>
        // 出库管理应用类
        class OutboundApp {
            constructor() {
                this.elements = {};
            }

            init() {
                this.initElements();
                this.bindEvents();
                this.checkAccess();
                this.loadOutboundRecords();
                this.loadSuggestionData();
            }

            initElements() {
                this.elements = {
                    realName: document.getElementById('realName'),
                    userInfo: document.getElementById('userInfo'),
                    loginBtn: document.getElementById('loginBtn'),
                    logoutBtn: document.getElementById('logoutBtn'),
                    outboundForm: document.getElementById('outboundForm'),
                    resetBtn: document.getElementById('resetBtn'),
                    outboundTableBody: document.getElementById('outboundTableBody'),
                    productName: document.getElementById('productName'),
                    productCode: document.getElementById('productCode'),
                    productNameSuggestions: document.getElementById('productNameSuggestions'),
                    productCodeSuggestions: document.getElementById('productCodeSuggestions')
                };

                // 初始化数据缓存
                this.productNames = [];
                this.productCodes = [];
                this.selectedSuggestionIndex = -1;
            }

            bindEvents() {
                // 表单提交事件
                this.elements.outboundForm.addEventListener('submit', async (e) => {
                    e.preventDefault();
                    await this.handleOutbound();
                });

                // 重置按钮事件
                this.elements.resetBtn.addEventListener('click', () => {
                    this.elements.outboundForm.reset();
                    this.hideSuggestions();
                });

                // 商品名称联想功能
                this.elements.productName.addEventListener('input', (e) => {
                    this.handleProductNameInput(e.target.value);
                });

                this.elements.productName.addEventListener('keydown', (e) => {
                    this.handleSuggestionKeydown(e, 'productName');
                });

                this.elements.productName.addEventListener('blur', () => {
                    setTimeout(() => this.hideSuggestions('productName'), 150);
                });

                // 商品编码联想功能
                this.elements.productCode.addEventListener('input', (e) => {
                    this.handleProductCodeInput(e.target.value);
                });

                this.elements.productCode.addEventListener('keydown', (e) => {
                    this.handleSuggestionKeydown(e, 'productCode');
                });

                this.elements.productCode.addEventListener('blur', () => {
                    setTimeout(() => this.hideSuggestions('productCode'), 150);
                });

                // 登录相关事件
                if (this.elements.loginBtn) {
                    this.elements.loginBtn.addEventListener('click', () => {
                        this.showLoginModal();
                    });
                }

                if (this.elements.logoutBtn) {
                    this.elements.logoutBtn.addEventListener('click', async () => {
                        try {
                            await AV.User.logOut();
                            this.checkAccess();
                        } catch (error) {
                            console.error('退出登录失败:', error);
                        }
                    });
                }
            }

            async checkAccess() {
                const currentUser = AV.User.current();
                
                if (currentUser) {
                    // 检查出库权限
                    const hasOutboundAccess = await this.checkOutboundPermission(currentUser);
                    
                    if (hasOutboundAccess) {
                        // 更新用户信息显示
                        this.elements.realName.textContent = currentUser.get('realName') || currentUser.get('username');
                        this.elements.userInfo.style.display = 'flex';
                        this.elements.loginBtn.style.display = 'none';
                    } else {
                        alert('您没有出库管理权限');
                        window.location.href = 'index.html';
                    }
                } else {
                    alert('请先登录');
                    window.location.href = 'index.html';
                }
            }

            async checkOutboundPermission(user) {
                const roles = user.get('roles') || [];
                
                // 管理员和超级管理员有出库权限
                if (roles.includes('admin') || roles.includes('super_admin')) {
                    return true;
                }
                
                // 检查是否有特定的出库权限
                if (window.permissionManager) {
                    try {
                        await window.permissionManager.init();
                        return window.permissionManager.hasPermission('inventory', 'outbound', 'create');
                    } catch (error) {
                        console.error('权限检查失败:', error);
                    }
                }
                
                return false;
            }

            async handleOutbound() {
                try {
                    const currentUser = AV.User.current();
                    if (!currentUser) {
                        alert('请先登录');
                        return;
                    }

                    // 获取表单数据
                    const productCode = document.getElementById('productCode').value;
                    const quantity = parseInt(document.getElementById('quantity').value);
                    const recipient = document.getElementById('recipient').value;
                    const purpose = document.getElementById('purpose').value;

                    // 表单验证
                    if (!productCode) {
                        alert('请输入商品编码');
                        return;
                    }

                    if (isNaN(quantity) || quantity <= 0) {
                        alert('请输入有效的出库数量');
                        return;
                    }

                    if (!recipient) {
                        alert('请输入领用人');
                        return;
                    }

                    if (!purpose) {
                        alert('请选择出库用途');
                        return;
                    }

                    // 检查商品是否存在和库存是否足够
                    const productQuery = new AV.Query('Product');
                    productQuery.equalTo('code', productCode);
                    const product = await productQuery.first();

                    if (!product) {
                        alert('商品不存在，请检查商品编码');
                        return;
                    }

                    const currentStock = product.get('currentStock') || 0;
                    if (quantity > currentStock) {
                        alert(`库存不足，当前库存: ${currentStock}${product.get('unit')}，请减少出库数量或先进行入库`);
                        return;
                    }

                    // 自动填充商品信息
                    document.getElementById('productName').value = product.get('name');
                    document.getElementById('unit').value = product.get('unit');

                    const formData = {
                        productName: product.get('name'),
                        productCode: productCode,
                        quantity: quantity,
                        unit: product.get('unit'),
                        warehouse: document.getElementById('warehouse').value || product.get('warehouse') || 'main',
                        recipient: recipient,
                        purpose: purpose,
                        department: document.getElementById('department').value,
                        remarks: document.getElementById('remarks').value,
                        operator: currentUser.get('realName') || currentUser.get('username'),
                        operatorId: currentUser.id,
                        operatorName: currentUser.get('realName') || currentUser.get('username'),
                        outboundTime: new Date()
                    };

                    // 保存出库记录
                    const OutboundRecord = AV.Object.extend('OutboundRecord');
                    const record = new OutboundRecord();
                    
                    Object.keys(formData).forEach(key => {
                        record.set(key, formData[key]);
                    });

                    await record.save();

                    // 更新商品库存
                    await this.updateProductStock(formData.productCode, formData.quantity, 'outbound');

                    alert('出库成功');
                    this.elements.outboundForm.reset();
                    this.loadOutboundRecords();

                } catch (error) {
                    console.error('出库失败:', error);
                    alert('出库失败: ' + error.message);
                }
            }

            async loadOutboundRecords() {
                try {
                    const query = new AV.Query('OutboundRecord');
                    query.descending('createdAt');
                    query.limit(20);

                    const records = await query.find();

                    if (records.length === 0) {
                        this.elements.outboundTableBody.innerHTML = `
                            <tr>
                                <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                    <div class="flex flex-col items-center py-8">
                                        <i class="fas fa-inbox fa-3x text-gray-300 mb-4"></i>
                                        <p class="text-lg font-medium">暂无出库记录</p>
                                        <p class="text-sm">请先进行出库操作</p>
                                    </div>
                                </td>
                            </tr>
                        `;
                        return;
                    }

                    const html = records.map(record => `
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">${record.get('productName')}</div>
                                <div class="text-sm text-gray-500">${record.get('productCode')}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                ${record.get('quantity')} ${record.get('unit')}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                ${record.get('warehouse') || '主仓库'}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">${record.get('recipient')}</div>
                                <div class="text-sm text-gray-500">${record.get('purpose')} - ${record.get('department') || ''}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                ${record.get('outboundTime') ? record.get('outboundTime').toLocaleString() : new Date(record.createdAt).toLocaleString()}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                ${record.get('operator') || record.get('operatorName') || '未知'}
                            </td>
                        </tr>
                    `).join('');

                    this.elements.outboundTableBody.innerHTML = html;

                } catch (error) {
                    console.error('加载出库记录失败:', error);

                    // 如果是表不存在的错误，显示友好提示
                    if (error.code === 101 || error.message.includes("doesn't exists")) {
                        this.elements.outboundTableBody.innerHTML = `
                            <tr>
                                <td colspan="6" class="px-6 py-4 text-center text-blue-500">
                                    <div class="flex flex-col items-center py-8">
                                        <i class="fas fa-database fa-3x text-blue-300 mb-4"></i>
                                        <p class="text-lg font-medium">出库记录表尚未创建</p>
                                        <p class="text-sm mb-4">请先进行出库操作或初始化数据</p>
                                        <a href="init-data.html" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm">
                                            初始化数据
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        `;
                    } else {
                        this.elements.outboundTableBody.innerHTML = `
                            <tr>
                                <td colspan="6" class="px-6 py-4 text-center text-red-500">
                                    加载出库记录失败: ${error.message}
                                </td>
                            </tr>
                        `;
                    }
                }
            }

            async updateProductStock(productCode, quantity, operation) {
                try {
                    // 查找商品
                    const productQuery = new AV.Query('Product');
                    productQuery.equalTo('code', productCode);
                    const product = await productQuery.first();

                    if (product) {
                        const currentStock = product.get('currentStock') || 0;
                        let newStock = currentStock;

                        if (operation === 'inbound') {
                            newStock = currentStock + quantity;
                        } else if (operation === 'outbound') {
                            newStock = Math.max(0, currentStock - quantity);
                        }

                        product.set('currentStock', newStock);
                        await product.save();

                        console.log(`商品 ${productCode} 库存已更新: ${currentStock} → ${newStock}`);
                    } else {
                        console.warn(`未找到商品编码为 ${productCode} 的商品`);
                    }
                } catch (error) {
                    console.error('更新商品库存失败:', error);
                    // 不阻断主流程，只记录错误
                }
            }

            // 加载联想数据
            async loadSuggestionData() {
                try {
                    // 加载商品名称和编码
                    const productQuery = new AV.Query('Product');
                    productQuery.select('name', 'code');
                    productQuery.limit(1000);
                    const products = await productQuery.find();
                    this.productNames = [...new Set(products.map(p => p.get('name')).filter(name => name))];
                    this.productCodes = [...new Set(products.map(p => p.get('code')).filter(code => code))];

                    console.log('联想数据加载完成:', {
                        productNames: this.productNames.length,
                        productCodes: this.productCodes.length
                    });
                } catch (error) {
                    console.warn('加载联想数据失败:', error);
                    this.productNames = [];
                    this.productCodes = [];
                }
            }

            // 处理商品名称输入
            handleProductNameInput(value) {
                if (!value.trim()) {
                    this.hideSuggestions('productName');
                    return;
                }

                const suggestions = this.productNames.filter(name =>
                    name.toLowerCase().includes(value.toLowerCase())
                ).slice(0, 10);

                this.showSuggestions('productName', suggestions);
            }

            // 处理商品编码输入
            handleProductCodeInput(value) {
                if (!value.trim()) {
                    this.hideSuggestions('productCode');
                    return;
                }

                const suggestions = this.productCodes.filter(code =>
                    code.toLowerCase().includes(value.toLowerCase())
                ).slice(0, 10);

                this.showSuggestions('productCode', suggestions);
            }

            // 显示建议
            showSuggestions(type, suggestions) {
                const container = type === 'productName' ?
                    this.elements.productNameSuggestions :
                    this.elements.productCodeSuggestions;

                if (suggestions.length === 0) {
                    container.classList.add('hidden');
                    return;
                }

                container.innerHTML = suggestions.map((suggestion, index) =>
                    `<div class="suggestion-item" data-index="${index}" data-value="${suggestion}">
                        ${suggestion}
                    </div>`
                ).join('');

                // 添加点击事件
                container.querySelectorAll('.suggestion-item').forEach(item => {
                    item.addEventListener('click', () => {
                        const input = type === 'productName' ?
                            this.elements.productName :
                            this.elements.productCode;
                        input.value = item.dataset.value;
                        this.hideSuggestions(type);
                    });
                });

                container.classList.remove('hidden');
                this.selectedSuggestionIndex = -1;
            }

            // 隐藏建议
            hideSuggestions(type) {
                if (type) {
                    const container = type === 'productName' ?
                        this.elements.productNameSuggestions :
                        this.elements.productCodeSuggestions;
                    container.classList.add('hidden');
                } else {
                    this.elements.productNameSuggestions.classList.add('hidden');
                    this.elements.productCodeSuggestions.classList.add('hidden');
                }
                this.selectedSuggestionIndex = -1;
            }

            // 处理键盘导航
            handleSuggestionKeydown(e, type) {
                const container = type === 'productName' ?
                    this.elements.productNameSuggestions :
                    this.elements.productCodeSuggestions;

                if (container.classList.contains('hidden')) return;

                const items = container.querySelectorAll('.suggestion-item');
                if (items.length === 0) return;

                switch (e.key) {
                    case 'ArrowDown':
                        e.preventDefault();
                        this.selectedSuggestionIndex = Math.min(this.selectedSuggestionIndex + 1, items.length - 1);
                        this.updateSuggestionSelection(items);
                        break;
                    case 'ArrowUp':
                        e.preventDefault();
                        this.selectedSuggestionIndex = Math.max(this.selectedSuggestionIndex - 1, -1);
                        this.updateSuggestionSelection(items);
                        break;
                    case 'Enter':
                        e.preventDefault();
                        if (this.selectedSuggestionIndex >= 0) {
                            const selectedItem = items[this.selectedSuggestionIndex];
                            const input = type === 'productName' ?
                                this.elements.productName :
                                this.elements.productCode;
                            input.value = selectedItem.dataset.value;
                            this.hideSuggestions(type);
                        }
                        break;
                    case 'Escape':
                        this.hideSuggestions(type);
                        break;
                }
            }

            // 更新建议选择状态
            updateSuggestionSelection(items) {
                items.forEach((item, index) => {
                    if (index === this.selectedSuggestionIndex) {
                        item.classList.add('selected');
                    } else {
                        item.classList.remove('selected');
                    }
                });
            }

            showLoginModal() {
                alert('请返回库存管理首页进行登录');
                window.location.href = 'index.html';
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            function initApp() {
                if (typeof AV !== 'undefined') {
                    const outboundApp = new OutboundApp();
                    outboundApp.init();
                } else {
                    setTimeout(initApp, 100);
                }
            }
            initApp();
        });
    </script>
</body>
</html>
