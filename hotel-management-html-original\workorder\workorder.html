<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <!-- 微信浏览器兼容性设置 -->
    <meta name="x5-orientation" content="portrait">
    <meta name="x5-fullscreen" content="true">
    <meta name="x5-page-mode" content="app">
    <title>工单提交</title>

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- LeanCloud SDK -->
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <!-- 备用 CDN 源 -->
    <script>
        window.addEventListener('load', function() {
            if (typeof AV === 'undefined') {
                console.log('主 CDN 加载失败，尝试备用源...');
                var script = document.createElement('script');
                script.src = 'https://cdn.bootcdn.net/ajax/libs/leancloud-storage/4.15.2/av-min.js';
                script.onerror = function() {
                    console.error('所有 CDN 源都加载失败，请检查网络连接');
                    alert('网络连接异常，请检查网络后刷新页面');
                };
                document.head.appendChild(script);
            }
        });
    </script>
    <style>
        /* 微信浏览器兼容性修复 */
        * {
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
        }
        
        body {
            -webkit-overflow-scrolling: touch;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        
        input, textarea, button {
            -webkit-appearance: none;
            appearance: none;
            border-radius: 8px;
        }
        
        .btn-fix {
            -webkit-appearance: none;
            appearance: none;
            border-radius: 8px;
            border: none;
            outline: none;
        }
        
        .fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航 -->
    <nav class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-10">
        <div class="max-w-4xl mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-3">
                    <a href="index.html" class="text-gray-600 hover:text-gray-800 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                    </a>
                    <h1 class="text-xl font-semibold text-gray-800">工单提交</h1>
                </div>
                <div id="userInfo" class="hidden items-center space-x-2 sm:space-x-4">
                    <span id="realName" class="text-gray-800 font-medium text-sm sm:text-base"></span>
                    <button id="logoutBtn" class="bg-red-500 hover:bg-red-600 text-white px-2 py-1 sm:px-4 sm:py-2 rounded-lg text-xs sm:text-sm transition-colors btn-fix">
                        <span class="hidden sm:inline">退出登录</span>
                        <span class="sm:hidden">退出</span>
                    </button>
                </div>
                <button id="loginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm transition-colors btn-fix">
                    登录
                </button>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="max-w-4xl mx-auto px-4 py-6">
        <!-- 未登录提示 -->
        <div id="loginPrompt" class="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg shadow-sm border border-blue-200 p-8 mb-6 text-center">
            <div class="mb-6">
                <h2 class="text-2xl font-bold text-gray-800 mb-4">请先登录</h2>
                <p class="text-gray-600 text-lg mb-6">
                    需要登录后才能提交工单
                </p>
                <button id="promptLoginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors font-medium btn-fix">
                    立即登录
                </button>
            </div>
        </div>
        
        <!-- 欢迎页面 -->
        <div id="welcomeSection" class="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg shadow-sm border border-blue-200 p-8 text-center">
            <div class="mb-6">
                <h1 class="text-3xl font-bold text-gray-800 mb-4">工单提交系统</h1>
                <div class="text-xl text-blue-600 font-semibold mb-2">
                    相润金鹏酒店工程部
                </div>
                <p class="text-gray-600 text-lg mb-6">
                    快速提交各类工作任务和服务请求，工程部将及时响应处理
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <div class="text-blue-500 text-3xl mb-3">📝</div>
                    <h3 class="font-semibold text-gray-800 mb-2">提交工单</h3>
                    <p class="text-gray-600 text-sm">详细描述工作任务和需求</p>
                </div>
                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <div class="text-green-500 text-3xl mb-3">📸</div>
                    <h3 class="font-semibold text-gray-800 mb-2">图片记录</h3>
                    <p class="text-gray-600 text-sm">支持上传现场照片</p>
                </div>
                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <div class="text-purple-500 text-3xl mb-3">⚡</div>
                    <h3 class="font-semibold text-gray-800 mb-2">快速响应</h3>
                    <p class="text-gray-600 text-sm">工程部及时处理工单</p>
                </div>
            </div>
            
            <div class="bg-white rounded-lg p-6 shadow-sm">
                <h3 class="font-semibold text-gray-800 mb-3">开始使用</h3>
                <p class="text-gray-600 mb-4">请先登录账号，然后填写工单信息</p>
                <button id="welcomeLoginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors font-medium btn-fix">
                    立即登录
                </button>
            </div>
        </div>

        <!-- 工单表单 -->
        <div id="workOrderSection" class="hidden">
            <!-- 基本信息 -->
            <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 mb-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <span class="bg-blue-100 text-blue-600 rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">1</span>
                    基本信息
                </h2>
                <form id="workOrderForm" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                                工单类别 <span class="text-red-500">*</span>
                            </label>
                            <select id="category" required
                                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="">请选择类别</option>
                                <option value="设备维修">设备维修</option>
                                <option value="设备保养">设备保养</option>
                                <option value="安装调试">安装调试</option>
                                <option value="技术支持">技术支持</option>
                                <option value="巡检检查">巡检检查</option>
                                <option value="其他服务">其他服务</option>
                            </select>
                        </div>

                        <div>
                            <label for="urgency" class="block text-sm font-medium text-gray-700 mb-2">
                                紧急程度 <span class="text-red-500">*</span>
                            </label>
                            <select id="urgency" required
                                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="">请选择紧急程度</option>
                                <option value="低">低 - 不影响正常使用</option>
                                <option value="中">中 - 影响部分功能</option>
                                <option value="高">高 - 严重影响使用</option>
                                <option value="紧急">紧急 - 存在安全隐患</option>
                            </select>
                        </div>

                        <div>
                            <label for="location" class="block text-sm font-medium text-gray-700 mb-2">
                                位置 <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="location" required
                                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="如：3楼客房301、大堂、地下室等">
                        </div>

                        <div class="md:col-span-2">
                            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                                工单描述 <span class="text-red-500">*</span>
                            </label>
                            <textarea id="description" rows="4" required
                                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="请详细描述工作任务、问题现象、发生时间等信息"></textarea>
                        </div>
                    </div>
                </form>
            </div>

            <!-- 图片上传 -->
            <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 mb-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <span class="bg-blue-100 text-blue-600 rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">2</span>
                    相关图片 <span class="text-gray-500 text-sm font-normal ml-2">(可选)</span>
                </h2>
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <input type="file" id="imageInput" accept="image/*" multiple class="hidden">
                    <div id="uploadArea" class="cursor-pointer">
                        <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                        <p class="mt-2 text-sm text-gray-600">
                            <span class="font-medium text-blue-600 hover:text-blue-500">点击上传图片</span>
                            或拖拽图片到此处
                        </p>
                        <p class="text-xs text-gray-500">支持 PNG, JPG, GIF 格式，最大 5MB</p>
                    </div>
                </div>
                <div id="imagePreview" class="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 hidden">
                    <!-- 图片预览将在这里显示 -->
                </div>
            </div>

            <!-- 提交按钮 -->
            <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
                <div class="flex justify-between items-center">
                    <div class="text-sm text-gray-500">
                        <p>提交后将生成工单号，您可以通过工单号查询处理进度</p>
                    </div>
                    <div class="flex space-x-4">
                        <button type="button" id="cancelBtn" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg transition-colors font-medium btn-fix">
                            取消
                        </button>
                        <button type="submit" id="submitBtn" form="workOrderForm" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors font-medium btn-fix">
                            提交工单
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 登录弹窗 -->
    <div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 fade-in">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-xl font-semibold text-gray-800">用户登录</h2>
                    <button id="closeLoginModal" class="text-gray-400 hover:text-gray-600 text-2xl btn-fix">&times;</button>
                </div>
            </div>
            <div class="p-6">
                <form id="loginForm" class="space-y-4">
                    <div>
                        <label for="loginUsername" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                        <input type="text" id="loginUsername" required
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                    </div>
                    <div>
                        <label for="loginPassword" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                        <input type="password" id="loginPassword" required
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                    </div>
                    <div class="flex gap-3 pt-4">
                        <button type="button" id="cancelLogin" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                            取消
                        </button>
                        <button type="submit" class="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                            登录
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="../js/config.js?v=1.0"></script>
    <script src="../js/core/error-handler.js?v=1.0"></script>
    <script src="../js/security/validator.js?v=1.0"></script>
    <script src="../js/utils.js?v=1.0"></script>
    <script src="../js/auth.js?v=1.0"></script>
    <script src="../js/base-app.js?v=1.0"></script>
    <script src="../js/modules/workorder.js?v=1.0"></script>
    <script>
        // 初始化工单应用
        let workOrderApp;

        document.addEventListener('DOMContentLoaded', function() {
            function checkAndInitApp() {
                if (typeof AV !== 'undefined' && typeof WorkOrderApp !== 'undefined') {
                    try {
                        workOrderApp = new WorkOrderApp();
                        workOrderApp.init();
                        console.log('工单应用初始化成功');
                    } catch (error) {
                        console.error('工单应用初始化失败:', error);
                        alert('应用初始化失败: ' + error.message);
                    }
                } else {
                    // 如果SDK还没加载，等待一段时间后重试
                    setTimeout(checkAndInitApp, 100);
                }
            }
            
            checkAndInitApp();
        });
    </script>
</body>
</html>
