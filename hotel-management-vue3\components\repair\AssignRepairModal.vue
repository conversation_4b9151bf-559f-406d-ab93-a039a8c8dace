<template>
  <div 
    v-if="show && repair" 
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    @click="handleBackdropClick"
  >
    <div 
      class="bg-white rounded-lg w-full max-w-md"
      @click.stop
    >
      <!-- 头部 -->
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900">分配工单</h3>
          <button 
            @click="handleClose"
            class="text-gray-400 hover:text-gray-600"
          >
            <Icon name="mdi:close" size="24" />
          </button>
        </div>
        
        <!-- 工单信息 -->
        <div class="mt-4 p-3 bg-gray-50 rounded-lg">
          <div class="flex items-center justify-between">
            <div>
              <h4 class="text-sm font-medium text-gray-900">{{ repair.title }}</h4>
              <p class="text-xs text-gray-500">{{ repair.location }} · {{ repair.category }}</p>
            </div>
            <span 
              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
              :class="getPriorityStyle(repair.priority)"
            >
              {{ getPriorityText(repair.priority) }}
            </span>
          </div>
        </div>
      </div>

      <!-- 内容 -->
      <div class="px-6 py-4">
        <form @submit.prevent="handleSubmit">
          <!-- 选择处理人 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-3">
              选择处理人 *
            </label>
            
            <!-- 工程师列表 -->
            <div class="space-y-3 max-h-60 overflow-y-auto">
              <label 
                v-for="engineer in engineers" 
                :key="engineer.id"
                class="relative flex cursor-pointer rounded-lg border p-4 focus:outline-none"
                :class="selectedEngineer === engineer.id ? 'border-blue-600 ring-2 ring-blue-600 bg-blue-50' : 'border-gray-300 hover:bg-gray-50'"
              >
                <input
                  v-model="selectedEngineer"
                  type="radio"
                  :value="engineer.id"
                  class="sr-only"
                  :disabled="loading"
                />
                <div class="flex w-full items-center justify-between">
                  <div class="flex items-center">
                    <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium mr-3">
                      {{ (engineer.realName || engineer.username).charAt(0) }}
                    </div>
                    <div>
                      <div class="text-sm font-medium text-gray-900">
                        {{ engineer.realName || engineer.username }}
                      </div>
                      <div class="text-xs text-gray-500">
                        {{ engineer.department || '工程部' }}
                      </div>
                      <div class="text-xs text-gray-500">
                        当前工单: {{ engineer.currentWorkload || 0 }} 个
                      </div>
                    </div>
                  </div>
                  
                  <!-- 状态指示器 -->
                  <div class="flex items-center">
                    <div 
                      class="w-2 h-2 rounded-full"
                      :class="engineer.isOnline ? 'bg-green-400' : 'bg-gray-400'"
                    ></div>
                    <span class="ml-1 text-xs text-gray-500">
                      {{ engineer.isOnline ? '在线' : '离线' }}
                    </span>
                  </div>
                </div>
              </label>
            </div>
            
            <!-- 没有可用工程师 -->
            <div v-if="engineers.length === 0" class="text-center py-8">
              <Icon name="mdi:account-off" size="48" class="text-gray-400 mx-auto mb-2" />
              <p class="text-gray-500 text-sm">暂无可用的工程师</p>
            </div>
          </div>

          <!-- 预计完成时间 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              预计完成时间 (小时)
            </label>
            <input 
              v-model.number="estimatedTime"
              type="number"
              min="0.5"
              max="72"
              step="0.5"
              :disabled="loading"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请输入预计完成时间"
            />
            <div class="mt-1 text-xs text-gray-500">
              根据问题复杂度和优先级估算
            </div>
          </div>

          <!-- 分配说明 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              分配说明
            </label>
            <textarea 
              v-model="assignmentNote"
              rows="3"
              :disabled="loading"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
              placeholder="可以添加特殊要求或注意事项..."
            ></textarea>
          </div>

          <!-- 紧急处理 -->
          <div class="mb-6">
            <label class="flex items-center">
              <input 
                v-model="isUrgent"
                type="checkbox"
                :disabled="loading"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span class="ml-2 text-sm text-gray-700">标记为紧急处理</span>
            </label>
            <div class="mt-1 text-xs text-gray-500">
              紧急工单将优先处理并发送即时通知
            </div>
          </div>
        </form>
      </div>

      <!-- 底部按钮 -->
      <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
        <button 
          type="button"
          @click="handleClose"
          :disabled="loading"
          class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50"
        >
          取消
        </button>
        <button 
          @click="handleSubmit"
          :disabled="loading || !selectedEngineer"
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center"
        >
          <Icon v-if="loading" name="mdi:loading" size="16" class="mr-1 animate-spin" />
          {{ loading ? '分配中...' : '确认分配' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Repair, User } from '~/types'

interface Props {
  show: boolean
  repair?: Repair | null
}

const props = withDefaults(defineProps<Props>(), {
  repair: null
})

const emit = defineEmits<{
  'update:show': [value: boolean]
  assigned: []
}>()

const repairStore = useRepairStore()

// 响应式数据
const loading = ref(false)
const selectedEngineer = ref('')
const estimatedTime = ref(2)
const assignmentNote = ref('')
const isUrgent = ref(false)

// 模拟工程师数据
const engineers = ref<(User & { currentWorkload?: number; isOnline?: boolean })[]>([
  {
    id: 'engineer1',
    username: 'engineer1',
    realName: '张工程师',
    department: '工程部',
    roles: ['engineer'],
    createdAt: new Date(),
    currentWorkload: 2,
    isOnline: true
  },
  {
    id: 'engineer2',
    username: 'engineer2',
    realName: '李工程师',
    department: '工程部',
    roles: ['engineer'],
    createdAt: new Date(),
    currentWorkload: 1,
    isOnline: true
  },
  {
    id: 'engineer3',
    username: 'engineer3',
    realName: '王工程师',
    department: '工程部',
    roles: ['engineer'],
    createdAt: new Date(),
    currentWorkload: 3,
    isOnline: false
  }
])

// 方法
const resetForm = () => {
  selectedEngineer.value = ''
  estimatedTime.value = 2
  assignmentNote.value = ''
  isUrgent.value = false
}

const handleClose = () => {
  if (!loading.value) {
    emit('update:show', false)
    resetForm()
  }
}

const handleBackdropClick = () => {
  handleClose()
}

const getPriorityStyle = (priority: string) => {
  const styles = {
    'low': 'bg-gray-100 text-gray-800',
    'medium': 'bg-yellow-100 text-yellow-800',
    'high': 'bg-orange-100 text-orange-800',
    'urgent': 'bg-red-100 text-red-800'
  }
  return styles[priority as keyof typeof styles] || styles.medium
}

const getPriorityText = (priority: string) => {
  const texts = {
    'low': '低优先级',
    'medium': '中优先级',
    'high': '高优先级',
    'urgent': '紧急'
  }
  return texts[priority as keyof typeof texts] || '中优先级'
}

const handleSubmit = async () => {
  if (!selectedEngineer.value || loading.value || !props.repair) return
  
  loading.value = true
  
  try {
    // 分配工单
    const result = await repairStore.assignRepair(props.repair.id, selectedEngineer.value)
    
    if (result.success) {
      // 更新预计时间
      if (estimatedTime.value > 0) {
        await repairStore.updateRepair(props.repair.id, {
          estimatedTime: estimatedTime.value
        })
      }
      
      // 如果标记为紧急，更新优先级
      if (isUrgent.value && props.repair.priority !== 'urgent') {
        await repairStore.updateRepair(props.repair.id, {
          priority: 'urgent'
        })
      }
      
      emit('assigned')
      handleClose()
    } else {
      alert(result.error || '分配失败')
    }
  } catch (error) {
    console.error('分配工单失败:', error)
    alert('分配失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 监听props变化
watch(() => props.show, (newValue) => {
  if (newValue) {
    resetForm()
    
    // 根据优先级设置默认预计时间
    if (props.repair) {
      switch (props.repair.priority) {
        case 'urgent':
          estimatedTime.value = 1
          isUrgent.value = true
          break
        case 'high':
          estimatedTime.value = 2
          break
        case 'medium':
          estimatedTime.value = 4
          break
        case 'low':
          estimatedTime.value = 8
          break
      }
    }
  }
})

// 键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.show) {
    handleClose()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>
