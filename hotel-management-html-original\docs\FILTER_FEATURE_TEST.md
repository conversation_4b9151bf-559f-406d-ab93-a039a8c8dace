# 日志筛选功能测试指南

## 功能概述

新增的日志筛选功能实现了以下需求：
1. **权限控制**：普通用户只能查看自己的日志，管理员可以查看所有用户的日志
2. **筛选功能**：管理员可以按日期范围和用户筛选日志
3. **界面控制**：筛选控件只对管理员用户显示

## 实现的功能

### 1. 权限控制
- 普通用户登录后只能看到自己发布的日志
- 管理员用户（roles包含'admin'）可以看到所有用户的日志
- 筛选功能只对管理员开放

### 2. 筛选功能
- **日期筛选**：可以设置开始日期和结束日期
- **用户筛选**：可以选择特定用户或查看所有用户
- **应用筛选**：点击"应用筛选"按钮执行筛选
- **清除筛选**：点击"清除筛选"按钮重置所有筛选条件

### 3. 界面变化
- 在日志列表上方添加了筛选控件区域
- 筛选区域包含：开始日期、结束日期、用户选择下拉框
- 提供"应用筛选"和"清除筛选"按钮

## 测试步骤

### 测试1：普通用户权限验证
1. 使用普通用户账号登录
2. 验证：
   - 筛选控件区域应该隐藏（不显示）
   - 只能看到自己发布的日志
   - 无法看到其他用户的日志

### 测试2：管理员权限验证
1. 使用管理员账号登录（用户的roles字段应该是数组格式：`["admin"]`）
2. 验证：
   - 筛选控件区域应该显示
   - 可以看到所有用户的日志
   - 用户下拉框中包含所有用户选项
   - 浏览器控制台显示权限检查日志

### 测试3：日期筛选功能
1. 以管理员身份登录
2. 设置开始日期和结束日期
3. 点击"应用筛选"
4. 验证：
   - 只显示指定日期范围内的日志
   - 页面显示"筛选已应用"消息

### 测试4：用户筛选功能
1. 以管理员身份登录
2. 在用户下拉框中选择特定用户
3. 点击"应用筛选"
4. 验证：
   - 只显示选中用户的日志
   - 页面显示"筛选已应用"消息

### 测试5：清除筛选功能
1. 在应用筛选后，点击"清除筛选"按钮
2. 验证：
   - 所有筛选条件被清空
   - 显示所有日志
   - 页面显示"筛选已清除"消息

### 测试6：组合筛选
1. 同时设置日期范围和用户筛选
2. 点击"应用筛选"
3. 验证：
   - 显示符合所有条件的日志
   - 筛选条件正确组合

## 技术实现要点

### 1. 权限判断
```javascript
isCurrentUserAdmin() {
    if (!this.currentUser) return false;

    const userRoles = this.currentUser.get('roles') || [];
    const username = this.currentUser.get('username');

    // 确保roles是数组格式，例如: ["admin"]
    if (!Array.isArray(userRoles)) {
        console.warn(`用户 ${username} 的roles字段不是数组格式:`, userRoles);
        return false;
    }

    const isAdmin = userRoles.includes('admin');
    console.log(`用户 ${username} 权限检查: roles=${JSON.stringify(userRoles)}, isAdmin=${isAdmin}`);

    return isAdmin;
}
```

### 2. 查询权限控制
```javascript
// 普通用户只能看到自己的日志
if (!this.isCurrentUserAdmin() && this.currentUser) {
    query.equalTo('user', this.currentUser);
}
```

### 3. 筛选条件应用
```javascript
applyFilterToQuery(query) {
    // 日期筛选
    if (this.filterOptions.startDate) {
        const startDate = new Date(this.filterOptions.startDate);
        startDate.setHours(0, 0, 0, 0);
        query.greaterThanOrEqualTo('createdAt', startDate);
    }
    // 用户筛选
    if (this.filterOptions.userId) {
        const user = AV.Object.createWithoutData('_User', this.filterOptions.userId);
        query.equalTo('user', user);
    }
}
```

## 注意事项

1. **管理员账号设置**：确保测试用的管理员账号在数据库中的roles字段是数组格式：`["admin"]`
2. **权限字段格式**：roles字段必须是数组，不能是字符串或其他格式
3. **调试信息**：浏览器控制台会显示权限检查的详细日志，便于调试
4. **数据权限**：普通用户的数据隔离是在前端实现的，建议在后端也实施相应的权限控制
5. **性能考虑**：大量数据时建议添加索引优化查询性能
6. **用户体验**：筛选操作会重置分页，从第一页开始显示结果

## 管理员账号设置

要将用户设置为管理员，需要在LeanCloud数据库中修改用户记录：

1. 登录LeanCloud控制台
2. 进入数据存储 -> _User表
3. 找到要设置为管理员的用户记录
4. 添加或修改 `roles` 字段，设置为数组格式：`["admin"]`
5. 保存更改

**示例数据格式：**
```json
{
  "username": "admin_user",
  "realName": "管理员",
  "roles": ["admin"]
}
```

## 故障排除

如果功能不正常，请检查：
1. 用户的roles字段是否正确设置为 `["admin"]` 数组格式
2. 浏览器控制台的权限检查日志信息
3. 浏览器控制台是否有JavaScript错误
4. 网络请求是否正常
5. LeanCloud数据库连接是否正常

**常见问题：**
- 如果roles字段是字符串 `"admin"` 而不是数组 `["admin"]`，权限检查会失败
- 控制台会显示详细的权限检查日志，帮助诊断问题
