// composables/useWechat.ts
import type { ApiResponse } from '~/types'

interface WeChatMessage {
  touser?: string
  toparty?: string
  totag?: string
  msgtype: 'text' | 'markdown' | 'image' | 'news'
  agentid: number
  text?: {
    content: string
  }
  markdown?: {
    content: string
  }
  safe?: number
}

interface WeChatUser {
  userid: string
  name: string
  department: number[]
  position?: string
  mobile?: string
  email?: string
  avatar?: string
}

interface AccessTokenResponse {
  access_token: string
  expires_in: number
}

export const useWechat = () => {
  const config = useRuntimeConfig()
  
  // 获取访问令牌
  const getAccessToken = async (): Promise<ApiResponse<string>> => {
    try {
      // 这里应该调用后端API来获取access_token
      // 因为corpSecret不能暴露在前端
      const response = await $fetch('/api/wechat/token', {
        method: 'POST'
      })
      
      return { success: true, data: response.access_token }
    } catch (error: any) {
      return { success: false, error: error.message || '获取访问令牌失败' }
    }
  }

  // 发送消息
  const sendMessage = async (message: WeChatMessage): Promise<ApiResponse> => {
    try {
      const tokenResult = await getAccessToken()
      if (!tokenResult.success || !tokenResult.data) {
        return { success: false, error: '获取访问令牌失败' }
      }

      const response = await $fetch('/api/wechat/send', {
        method: 'POST',
        body: {
          access_token: tokenResult.data,
          message
        }
      })

      return { success: true, data: response }
    } catch (error: any) {
      return { success: false, error: error.message || '发送消息失败' }
    }
  }

  // 发送文本消息
  const sendTextMessage = async (
    content: string, 
    touser?: string, 
    toparty?: string
  ): Promise<ApiResponse> => {
    const message: WeChatMessage = {
      msgtype: 'text',
      agentid: parseInt(config.public.wechatAgentId),
      text: { content },
      safe: 0
    }

    if (touser) message.touser = touser
    if (toparty) message.toparty = toparty
    if (!touser && !toparty) message.touser = '@all'

    return await sendMessage(message)
  }

  // 发送Markdown消息
  const sendMarkdownMessage = async (
    content: string,
    touser?: string,
    toparty?: string
  ): Promise<ApiResponse> => {
    const message: WeChatMessage = {
      msgtype: 'markdown',
      agentid: parseInt(config.public.wechatAgentId),
      markdown: { content },
      safe: 0
    }

    if (touser) message.touser = touser
    if (toparty) message.toparty = toparty
    if (!touser && !toparty) message.touser = '@all'

    return await sendMessage(message)
  }

  // 发送工作日志通知
  const sendWorkLogNotification = async (
    logData: {
      userName: string
      userDepartment: string
      pageType: string
      content: string
      createdAt: Date
    },
    touser?: string
  ): Promise<ApiResponse> => {
    const pageTypeNames: Record<string, string> = {
      'main': '工作日志',
      'powerstation': '变电站',
      'waterfilter': '净水器',
      'aircondition': '空调',
      'construction': '施工登记'
    }

    const typeName = pageTypeNames[logData.pageType] || logData.pageType
    const time = new Date(logData.createdAt).toLocaleString('zh-CN')

    const content = `## 📝 新的${typeName}
    
**提交人：** ${logData.userName}
**部门：** ${logData.userDepartment}
**时间：** ${time}

**内容：**
${logData.content.substring(0, 200)}${logData.content.length > 200 ? '...' : ''}

---
*来自酒店管理系统*`

    return await sendMarkdownMessage(content, touser)
  }

  // 发送报修通知
  const sendRepairNotification = async (
    repairData: {
      title: string
      reporter: string
      location: string
      priority: string
      description: string
      createdAt: Date
    },
    touser?: string
  ): Promise<ApiResponse> => {
    const priorityNames: Record<string, string> = {
      'low': '🟢 低',
      'medium': '🟡 中',
      'high': '🟠 高',
      'urgent': '🔴 紧急'
    }

    const priorityText = priorityNames[repairData.priority] || repairData.priority
    const time = new Date(repairData.createdAt).toLocaleString('zh-CN')

    const content = `## 🔧 新的报修工单

**标题：** ${repairData.title}
**报修人：** ${repairData.reporter}
**位置：** ${repairData.location}
**优先级：** ${priorityText}
**时间：** ${time}

**问题描述：**
${repairData.description.substring(0, 200)}${repairData.description.length > 200 ? '...' : ''}

---
*请及时处理，来自酒店管理系统*`

    return await sendMarkdownMessage(content, touser)
  }

  // 发送报修完成通知
  const sendRepairCompletedNotification = async (
    repairData: {
      title: string
      assignee: string
      reporter: string
      location: string
      completedAt: Date
      solution?: string
    },
    touser?: string
  ): Promise<ApiResponse> => {
    const time = new Date(repairData.completedAt).toLocaleString('zh-CN')

    const content = `## ✅ 报修工单已完成

**标题：** ${repairData.title}
**处理人：** ${repairData.assignee}
**位置：** ${repairData.location}
**完成时间：** ${time}

${repairData.solution ? `**解决方案：**\n${repairData.solution}` : ''}

---
*工单已完成，感谢您的耐心等待*`

    return await sendMarkdownMessage(content, touser)
  }

  // 获取用户信息
  const getUserInfo = async (userid: string): Promise<ApiResponse<WeChatUser>> => {
    try {
      const response = await $fetch('/api/wechat/user', {
        method: 'GET',
        query: { userid }
      })

      return { success: true, data: response }
    } catch (error: any) {
      return { success: false, error: error.message || '获取用户信息失败' }
    }
  }

  // 检查企业微信环境
  const isWeChatWork = (): boolean => {
    if (process.client) {
      const userAgent = navigator.userAgent.toLowerCase()
      return userAgent.includes('wxwork')
    }
    return false
  }

  return {
    getAccessToken,
    sendMessage,
    sendTextMessage,
    sendMarkdownMessage,
    sendWorkLogNotification,
    sendRepairNotification,
    sendRepairCompletedNotification,
    getUserInfo,
    isWeChatWork
  }
}
