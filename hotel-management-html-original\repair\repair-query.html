<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <!-- 微信浏览器兼容性设置 -->
    <meta name="x5-orientation" content="portrait">
    <meta name="x5-fullscreen" content="true">
    <meta name="x5-page-mode" content="app">
    <title>工单查询</title>
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- LeanCloud SDK -->
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <style>
        /* 微信浏览器兼容性修复 */
        * {
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
        }
        
        body {
            -webkit-overflow-scrolling: touch;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        
        .btn-fix {
            -webkit-appearance: none;
            appearance: none;
            border-radius: 8px;
            border: none;
            outline: none;
        }
        
        .fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .status-pending { @apply bg-yellow-100 text-yellow-800; }
        .status-accepted { @apply bg-blue-100 text-blue-800; }
        .status-processing { @apply bg-green-100 text-green-800; }
        .status-completed { @apply bg-purple-100 text-purple-800; }
        .status-cancelled { @apply bg-red-100 text-red-800; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航 -->
    <nav class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-10">
        <div class="max-w-6xl mx-auto px-4">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-4">
                    <a href="index.html" class="flex items-center text-gray-600 hover:text-gray-800 transition-colors">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        返回首页
                    </a>
                    <h1 class="text-xl font-semibold text-gray-800">工单查询</h1>
                </div>
                <div class="flex items-center space-x-3">
                    <button id="myOrdersBtn" class="bg-green-500 hover:bg-green-600 text-white px-3 py-1.5 rounded-lg text-sm transition-colors btn-fix">
                        我的工单
                    </button>
                    <button id="refreshBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1.5 rounded-lg text-sm transition-colors btn-fix">
                        刷新
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="max-w-4xl mx-auto px-4 py-6">
        <!-- 加载状态 -->
        <div id="loadingSection" class="text-center py-12">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">正在加载工单信息...</p>
        </div>

        <!-- 错误状态 -->
        <div id="errorSection" class="hidden text-center py-12">
            <div class="text-red-500 text-6xl mb-4">❌</div>
            <h2 class="text-xl font-semibold text-gray-800 mb-2">工单未找到</h2>
            <p class="text-gray-600 mb-6">请检查工单号是否正确，或联系相关人员</p>
            <div class="space-x-4">
                <a href="repair.html" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors btn-fix">
                    重新报修
                </a>
                <a href="index.html" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors btn-fix">
                    返回首页
                </a>
            </div>
        </div>

        <!-- 我的工单列表 -->
        <div id="myOrdersSection" class="hidden">
            <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-gray-800">我的报修工单</h2>
                    <button id="backToQueryBtn" class="text-blue-600 hover:text-blue-800 text-sm">
                        返回工单查询
                    </button>
                </div>
                <div id="myOrdersList">
                    <!-- 工单列表将通过JS动态填充 -->
                </div>
            </div>
        </div>

        <!-- 工单详情 -->
        <div id="orderSection" class="hidden space-y-6">
            <!-- 工单状态卡片 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-gray-800">工单状态</h2>
                    <span id="orderStatus" class="px-3 py-1 rounded-full text-sm font-medium">-</span>
                </div>
                
                <!-- 状态进度条 -->
                <div class="mb-6">
                    <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
                        <span>已提交</span>
                        <span>已接单</span>
                        <span>处理中</span>
                        <span>已完成</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div id="progressBar" class="bg-blue-500 h-2 rounded-full transition-all duration-500" style="width: 25%"></div>
                    </div>
                </div>

                <!-- 时间信息 -->
                <div id="timeInfo" class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <!-- 时间信息将在这里动态填充 -->
                </div>
            </div>

            <!-- 工单详情卡片 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">工单详情</h3>
                <div id="orderDetails" class="space-y-4">
                    <!-- 工单详情将在这里动态填充 -->
                </div>
            </div>

            <!-- 处理信息卡片 -->
            <div id="processInfo" class="hidden bg-white rounded-lg shadow-sm border border-gray-100 p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">处理信息</h3>
                <div id="processDetails" class="space-y-4">
                    <!-- 处理信息将在这里动态填充 -->
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex justify-center space-x-4">
                <a href="repair.html" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors btn-fix">
                    提交新报修
                </a>
                <a href="index.html" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors btn-fix">
                    返回首页
                </a>
            </div>
        </div>
    </main>

    <!-- 引入脚本 -->
    <script src="../js/config.js?v=1.0"></script>
    <script src="../js/utils.js?v=1.0"></script>
    <script>
        class RepairQueryApp {
            constructor() {
                this.orderNumber = null;
                this.order = null;
                this.elements = {
                    loadingSection: 'loadingSection',
                    errorSection: 'errorSection',
                    orderSection: 'orderSection',
                    myOrdersSection: 'myOrdersSection',
                    myOrdersList: 'myOrdersList',
                    orderStatus: 'orderStatus',
                    progressBar: 'progressBar',
                    timeInfo: 'timeInfo',
                    orderDetails: 'orderDetails',
                    processInfo: 'processInfo',
                    processDetails: 'processDetails',
                    refreshBtn: 'refreshBtn',
                    myOrdersBtn: 'myOrdersBtn',
                    backToQueryBtn: 'backToQueryBtn'
                };
                
                // 获取DOM元素
                Object.keys(this.elements).forEach(key => {
                    this.elements[key] = document.getElementById(this.elements[key]);
                });
            }

            async init() {
                try {
                    // 初始化LeanCloud
                    if (typeof AV !== 'undefined' && window.LeanCloudConfig) {
                        AV.init(window.LeanCloudConfig);
                    }

                    // 检查用户登录状态
                    this.currentUser = AV.User.current();
                    if (!this.currentUser) {
                        this.showLoginRequired();
                        return;
                    }

                    // 获取URL参数中的工单号
                    this.orderNumber = this.getOrderNumberFromURL();

                    if (!this.orderNumber) {
                        // 如果没有指定工单号，显示我的工单列表
                        await this.showMyOrders();
                        this.bindEvents();
                        return;
                    }

                    // 加载工单信息
                    await this.loadOrder();

                    // 绑定事件
                    this.bindEvents();

                } catch (error) {
                    console.error('初始化失败:', error);
                    this.showError();
                }
            }

            getOrderNumberFromURL() {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get('order');
            }

            async loadOrder() {
                try {
                    const RepairOrder = AV.Object.extend('RepairOrder');
                    const query = new AV.Query(RepairOrder);
                    query.equalTo('orderNumber', this.orderNumber);

                    // 重要：只查询当前用户的工单
                    query.equalTo('reporter', this.currentUser);

                    const orders = await query.find();

                    if (orders.length === 0) {
                        this.showAccessDenied();
                        return;
                    }

                    this.order = orders[0];
                    this.renderOrder();
                    this.showOrder();

                } catch (error) {
                    console.error('加载工单失败:', error);
                    this.showError();
                }
            }

            renderOrder() {
                if (!this.order) return;

                // 渲染状态
                this.renderStatus();
                
                // 渲染时间信息
                this.renderTimeInfo();
                
                // 渲染工单详情
                this.renderOrderDetails();
                
                // 渲染处理信息
                this.renderProcessInfo();
            }

            renderStatus() {
                const status = this.order.get('status');
                const statusText = this.getStatusText(status);
                
                if (this.elements.orderStatus) {
                    this.elements.orderStatus.textContent = statusText;
                    this.elements.orderStatus.className = `px-3 py-1 rounded-full text-sm font-medium status-${status}`;
                }

                // 更新进度条
                const progress = this.getProgressPercentage(status);
                if (this.elements.progressBar) {
                    this.elements.progressBar.style.width = progress + '%';
                }
            }

            renderTimeInfo() {
                const timeData = [
                    { label: '提交时间', value: this.order.get('reportTime') },
                    { label: '接单时间', value: this.order.get('acceptTime') },
                    { label: '开始时间', value: this.order.get('startTime') },
                    { label: '完成时间', value: this.order.get('completeTime') }
                ];

                if (this.elements.timeInfo) {
                    this.elements.timeInfo.innerHTML = timeData.map(item => `
                        <div class="text-center">
                            <div class="text-gray-600">${item.label}</div>
                            <div class="font-medium text-gray-800">
                                ${item.value ? item.value.toLocaleString('zh-CN') : '-'}
                            </div>
                        </div>
                    `).join('');
                }
            }

            renderOrderDetails() {
                const details = [
                    { label: '工单号', value: this.order.get('orderNumber') },
                    { label: '故障类型', value: this.order.get('category') },
                    { label: '紧急程度', value: this.order.get('urgency') },
                    { label: '故障位置', value: this.order.get('location') },
                    { label: '故障描述', value: this.order.get('description') },
                    { label: '报修人', value: this.order.get('reporterName') },
                    { label: '联系电话', value: this.order.get('reporterPhone') },
                    { label: '所属部门', value: this.order.get('reporterDept') }
                ];

                if (this.elements.orderDetails) {
                    this.elements.orderDetails.innerHTML = details.map(item => `
                        <div class="flex justify-between py-2 border-b border-gray-100 last:border-b-0">
                            <span class="text-gray-600">${item.label}：</span>
                            <span class="font-medium text-gray-800">${item.value || '-'}</span>
                        </div>
                    `).join('');
                }
            }

            renderProcessInfo() {
                const status = this.order.get('status');
                
                if (status === 'pending') {
                    this.elements.processInfo?.classList.add('hidden');
                    return;
                }

                const processDetails = [
                    { label: '处理人', value: this.order.get('assigneeName') },
                    { label: '处理结果', value: this.order.get('result') }
                ];

                if (this.elements.processDetails) {
                    this.elements.processDetails.innerHTML = processDetails.map(item => `
                        <div class="flex justify-between py-2 border-b border-gray-100 last:border-b-0">
                            <span class="text-gray-600">${item.label}：</span>
                            <span class="font-medium text-gray-800">${item.value || '-'}</span>
                        </div>
                    `).join('');
                }

                this.elements.processInfo?.classList.remove('hidden');
            }

            getStatusText(status) {
                const statusMap = {
                    'pending': '待接单',
                    'accepted': '已接单',
                    'processing': '处理中',
                    'completed': '已完成',
                    'cancelled': '已取消'
                };
                return statusMap[status] || status;
            }

            getProgressPercentage(status) {
                const progressMap = {
                    'pending': 25,
                    'accepted': 50,
                    'processing': 75,
                    'completed': 100,
                    'cancelled': 0
                };
                return progressMap[status] || 0;
            }

            bindEvents() {
                if (this.elements.refreshBtn) {
                    this.elements.refreshBtn.addEventListener('click', () => {
                        this.loadOrder();
                    });
                }

                if (this.elements.myOrdersBtn) {
                    this.elements.myOrdersBtn.addEventListener('click', () => {
                        this.showMyOrders();
                    });
                }

                if (this.elements.backToQueryBtn) {
                    this.elements.backToQueryBtn.addEventListener('click', () => {
                        this.showQueryMode();
                    });
                }
            }

            showLoading() {
                this.elements.loadingSection?.classList.remove('hidden');
                this.elements.errorSection?.classList.add('hidden');
                this.elements.orderSection?.classList.add('hidden');
            }

            showError() {
                this.elements.loadingSection?.classList.add('hidden');
                this.elements.errorSection?.classList.remove('hidden');
                this.elements.orderSection?.classList.add('hidden');
            }

            showOrder() {
                this.elements.loadingSection?.classList.add('hidden');
                this.elements.errorSection?.classList.add('hidden');
                this.elements.orderSection?.classList.remove('hidden');
            }

            showLoginRequired() {
                this.elements.loadingSection?.classList.add('hidden');
                this.elements.orderSection?.classList.add('hidden');

                // 显示登录提示
                if (this.elements.errorSection) {
                    this.elements.errorSection.innerHTML = `
                        <div class="text-center py-12">
                            <div class="mb-4">
                                <svg class="mx-auto h-12 w-12 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                </svg>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">需要登录</h3>
                            <p class="text-gray-500 mb-4">请先登录后再查看报修工单</p>
                            <a href="../index.html" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg">
                                返回登录
                            </a>
                        </div>
                    `;
                    this.elements.errorSection.classList.remove('hidden');
                }
            }

            showAccessDenied() {
                this.elements.loadingSection?.classList.add('hidden');
                this.elements.orderSection?.classList.add('hidden');

                // 显示访问拒绝提示
                if (this.elements.errorSection) {
                    this.elements.errorSection.innerHTML = `
                        <div class="text-center py-12">
                            <div class="mb-4">
                                <svg class="mx-auto h-12 w-12 text-orange-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 0h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                </svg>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">工单不存在或无权访问</h3>
                            <p class="text-gray-500 mb-4">您只能查看自己提交的报修工单</p>
                            <div class="space-x-4">
                                <a href="index.html" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg">
                                    返回报修管理
                                </a>
                                <a href="repair.html" class="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg">
                                    提交新报修
                                </a>
                            </div>
                        </div>
                    `;
                    this.elements.errorSection.classList.remove('hidden');
                }
            }

            async showMyOrders() {
                try {
                    this.showLoading();

                    const RepairOrder = AV.Object.extend('RepairOrder');
                    const query = new AV.Query(RepairOrder);
                    query.equalTo('reporter', this.currentUser);
                    query.descending('createdAt');
                    query.limit(20); // 限制显示最近20条

                    const orders = await query.find();

                    this.renderMyOrdersList(orders);
                    this.showMyOrdersSection();

                } catch (error) {
                    console.error('加载我的工单失败:', error);
                    this.showError();
                }
            }

            renderMyOrdersList(orders) {
                if (!this.elements.myOrdersList) return;

                if (orders.length === 0) {
                    this.elements.myOrdersList.innerHTML = `
                        <div class="text-center py-8">
                            <div class="text-gray-400 text-4xl mb-4">📋</div>
                            <p class="text-gray-500">暂无报修工单</p>
                            <a href="repair.html" class="mt-4 inline-block bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                                提交新报修
                            </a>
                        </div>
                    `;
                    return;
                }

                const ordersHtml = orders.map(order => {
                    const status = order.get('status');
                    const statusText = this.getStatusText(status);
                    const urgency = order.get('urgency');
                    const location = order.get('location');
                    const reportTime = order.get('reportTime');
                    const orderNumber = order.get('orderNumber');

                    return `
                        <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                             onclick="window.location.href='repair-query.html?order=${orderNumber}'">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-medium text-gray-900">${order.get('title') || '报修工单'}</h4>
                                <span class="px-2 py-1 rounded-full text-xs font-medium status-${status}">
                                    ${statusText}
                                </span>
                            </div>
                            <div class="text-sm text-gray-600 space-y-1">
                                <div>工单号：${orderNumber}</div>
                                <div>位置：${location}</div>
                                <div>紧急程度：${urgency}</div>
                                <div>提交时间：${reportTime ? new Date(reportTime).toLocaleString() : '-'}</div>
                            </div>
                        </div>
                    `;
                }).join('');

                this.elements.myOrdersList.innerHTML = ordersHtml;
            }

            showMyOrdersSection() {
                this.elements.loadingSection?.classList.add('hidden');
                this.elements.errorSection?.classList.add('hidden');
                this.elements.orderSection?.classList.add('hidden');
                this.elements.myOrdersSection?.classList.remove('hidden');
            }

            showQueryMode() {
                this.elements.myOrdersSection?.classList.add('hidden');
                if (this.order) {
                    this.showOrder();
                } else {
                    this.showLoading();
                    this.loadOrder();
                }
            }
        }

        // 初始化应用
        let repairQueryApp;
        
        document.addEventListener('DOMContentLoaded', function() {
            function checkAndInitApp() {
                if (typeof AV !== 'undefined') {
                    repairQueryApp = new RepairQueryApp();
                    repairQueryApp.init();
                } else {
                    console.log('等待LeanCloud SDK加载...');
                    setTimeout(checkAndInitApp, 100);
                }
            }
            
            checkAndInitApp();
        });
    </script>
</body>
</html>
