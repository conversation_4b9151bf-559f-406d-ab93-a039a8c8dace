<template>
  <div 
    v-if="show"
    class="flex items-center justify-center"
    :class="containerClass"
  >
    <div class="flex flex-col items-center">
      <!-- 加载动画 -->
      <div 
        class="animate-spin rounded-full border-4 border-gray-200"
        :class="spinnerClass"
        :style="{ width: size + 'px', height: size + 'px' }"
      >
        <div 
          class="rounded-full border-4 border-transparent"
          :class="colorClass"
        ></div>
      </div>
      
      <!-- 加载文本 -->
      <p 
        v-if="text"
        class="mt-4 text-sm font-medium"
        :class="textColorClass"
      >
        {{ text }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  show?: boolean
  size?: number
  text?: string
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple' | 'gray'
  fullscreen?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  show: true,
  size: 32,
  text: '',
  color: 'blue',
  fullscreen: false
})

// 计算属性
const containerClass = computed(() => {
  if (props.fullscreen) {
    return 'fixed inset-0 bg-white bg-opacity-75 z-50'
  }
  return 'py-8'
})

const spinnerClass = computed(() => {
  const colorMap = {
    blue: 'border-t-blue-600',
    green: 'border-t-green-600',
    red: 'border-t-red-600',
    yellow: 'border-t-yellow-600',
    purple: 'border-t-purple-600',
    gray: 'border-t-gray-600'
  }
  return colorMap[props.color]
})

const colorClass = computed(() => {
  const colorMap = {
    blue: 'border-t-blue-600',
    green: 'border-t-green-600',
    red: 'border-t-red-600',
    yellow: 'border-t-yellow-600',
    purple: 'border-t-purple-600',
    gray: 'border-t-gray-600'
  }
  return colorMap[props.color]
})

const textColorClass = computed(() => {
  const colorMap = {
    blue: 'text-blue-600',
    green: 'text-green-600',
    red: 'text-red-600',
    yellow: 'text-yellow-600',
    purple: 'text-purple-600',
    gray: 'text-gray-600'
  }
  return colorMap[props.color]
})
</script>
