<template>
  <tr class="hover:bg-gray-50">
    <!-- 物品信息 -->
    <td class="px-6 py-4 whitespace-nowrap">
      <div class="flex items-center">
        <div class="flex-shrink-0 h-12 w-12">
          <img 
            v-if="item.images && item.images.length > 0"
            :src="item.images[0]" 
            :alt="item.name"
            class="h-12 w-12 rounded-lg object-cover"
            @error="handleImageError"
          />
          <div v-else class="h-12 w-12 rounded-lg bg-gray-100 flex items-center justify-center">
            <Icon name="mdi:package-variant" size="24" class="text-gray-400" />
          </div>
        </div>
        <div class="ml-4">
          <div class="text-sm font-medium text-gray-900">{{ item.name }}</div>
          <div class="text-sm text-gray-500">{{ item.description || '暂无描述' }}</div>
          <div v-if="item.barcode" class="text-xs text-gray-400">条码: {{ item.barcode }}</div>
        </div>
      </div>
    </td>

    <!-- 分类 -->
    <td class="px-6 py-4 whitespace-nowrap">
      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
        {{ item.category }}
      </span>
    </td>

    <!-- 库存数量 -->
    <td class="px-6 py-4 whitespace-nowrap">
      <div class="flex items-center">
        <div class="flex-1">
          <div class="flex items-center justify-between mb-1">
            <span 
              class="text-sm font-medium"
              :class="getQuantityColor(item.quantity, item.minStock)"
            >
              {{ item.quantity }} {{ item.unit }}
            </span>
            <span v-if="isLowStock" class="text-xs text-red-600">
              <Icon name="mdi:alert" size="12" class="inline" />
            </span>
          </div>
          
          <!-- 库存进度条 -->
          <div class="w-full bg-gray-200 rounded-full h-1.5">
            <div 
              class="h-1.5 rounded-full transition-all"
              :class="getProgressBarColor(item.quantity, item.minStock)"
              :style="{ width: getProgressWidth(item.quantity, item.maxStock) }"
            ></div>
          </div>
          
          <div class="flex justify-between text-xs text-gray-500 mt-1">
            <span>最低: {{ item.minStock }}</span>
            <span v-if="item.maxStock">最高: {{ item.maxStock }}</span>
          </div>
        </div>
      </div>
    </td>

    <!-- 位置 -->
    <td class="px-6 py-4 whitespace-nowrap">
      <div class="text-sm text-gray-900">{{ item.location }}</div>
      <div v-if="item.supplier" class="text-sm text-gray-500">{{ item.supplier }}</div>
    </td>

    <!-- 状态 -->
    <td class="px-6 py-4 whitespace-nowrap">
      <span 
        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
        :class="getStatusStyle(item.status)"
      >
        {{ getStatusText(item.status) }}
      </span>
      
      <!-- 价值信息 -->
      <div v-if="item.price" class="text-xs text-gray-500 mt-1">
        单价: ¥{{ item.price.toFixed(2) }}
      </div>
      <div v-if="item.price" class="text-xs text-gray-500">
        总值: ¥{{ totalValue.toFixed(2) }}
      </div>
    </td>

    <!-- 操作 -->
    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
      <div class="flex items-center justify-end space-x-2">
        <!-- 快速操作按钮 -->
        <button 
          @click="handleStockIn"
          class="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50"
          title="入库"
        >
          <Icon name="mdi:plus" size="16" />
        </button>
        
        <button 
          @click="handleStockOut"
          class="text-orange-600 hover:text-orange-900 p-1 rounded hover:bg-orange-50"
          title="出库"
        >
          <Icon name="mdi:minus" size="16" />
        </button>
        
        <button 
          @click="handleAdjust"
          class="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50"
          title="调整"
        >
          <Icon name="mdi:tune" size="16" />
        </button>

        <!-- 更多操作下拉菜单 -->
        <div class="relative" ref="dropdownRef">
          <button 
            @click="dropdownOpen = !dropdownOpen"
            class="text-gray-400 hover:text-gray-600 p-1 rounded hover:bg-gray-50"
          >
            <Icon name="mdi:dots-vertical" size="16" />
          </button>
          
          <div 
            v-if="dropdownOpen"
            class="absolute right-0 mt-1 w-32 bg-white rounded-md shadow-lg py-1 z-10 border border-gray-200"
          >
            <button 
              @click="handleEdit"
              class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            >
              <Icon name="mdi:pencil" size="14" class="inline mr-2" />
              编辑
            </button>
            
            <button 
              @click="handleViewHistory"
              class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            >
              <Icon name="mdi:history" size="14" class="inline mr-2" />
              历史记录
            </button>
            
            <div class="border-t border-gray-100 my-1"></div>
            
            <button 
              @click="handleDelete"
              class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50"
            >
              <Icon name="mdi:delete" size="14" class="inline mr-2" />
              删除
            </button>
          </div>
        </div>
      </div>
    </td>
  </tr>
</template>

<script setup lang="ts">
import type { InventoryItem } from '~/types'

interface Props {
  item: InventoryItem
}

const props = defineProps<Props>()
const emit = defineEmits<{
  edit: [item: InventoryItem]
  delete: [item: InventoryItem]
  adjust: [item: InventoryItem]
  'stock-in': [item: InventoryItem]
  'stock-out': [item: InventoryItem]
  'view-history': [item: InventoryItem]
}>()

const dropdownOpen = ref(false)
const dropdownRef = ref()

// 点击外部关闭下拉菜单
onClickOutside(dropdownRef, () => {
  dropdownOpen.value = false
})

// 计算属性
const isLowStock = computed(() => {
  return props.item.quantity <= props.item.minStock
})

const totalValue = computed(() => {
  return props.item.quantity * (props.item.price || 0)
})

// 方法
const getStatusStyle = (status: string) => {
  const styles = {
    'active': 'bg-green-100 text-green-800',
    'inactive': 'bg-gray-100 text-gray-800',
    'discontinued': 'bg-red-100 text-red-800'
  }
  return styles[status as keyof typeof styles] || styles.active
}

const getStatusText = (status: string) => {
  const texts = {
    'active': '正常',
    'inactive': '停用',
    'discontinued': '停产'
  }
  return texts[status as keyof typeof texts] || '正常'
}

const getQuantityColor = (quantity: number, minStock: number) => {
  if (quantity === 0) return 'text-red-600'
  if (quantity <= minStock) return 'text-orange-600'
  return 'text-green-600'
}

const getProgressBarColor = (quantity: number, minStock: number) => {
  if (quantity === 0) return 'bg-red-500'
  if (quantity <= minStock) return 'bg-orange-500'
  return 'bg-green-500'
}

const getProgressWidth = (quantity: number, maxStock?: number) => {
  if (!maxStock) return '100%'
  const percentage = Math.min((quantity / maxStock) * 100, 100)
  return `${percentage}%`
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}

const handleEdit = () => {
  dropdownOpen.value = false
  emit('edit', props.item)
}

const handleDelete = () => {
  dropdownOpen.value = false
  emit('delete', props.item)
}

const handleAdjust = () => {
  emit('adjust', props.item)
}

const handleStockIn = () => {
  emit('stock-in', props.item)
}

const handleStockOut = () => {
  emit('stock-out', props.item)
}

const handleViewHistory = () => {
  dropdownOpen.value = false
  emit('view-history', props.item)
}
</script>
