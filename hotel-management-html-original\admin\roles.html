<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>角色权限管理 - 系统管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <style>
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .btn-fix {
            touch-action: manipulation;
        }
        .permission-tree {
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="index.html" class="text-blue-600 hover:text-blue-800 mr-4">
                        ← 返回管理首页
                    </a>
                    <h1 class="text-xl font-semibold text-gray-900">角色权限管理</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="addRoleBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                        添加角色
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 角色列表 -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">角色列表</h2>
                </div>
                <div class="p-6">
                    <div id="rolesList" class="space-y-4">
                        <!-- 角色列表将在这里动态加载 -->
                    </div>
                </div>
            </div>

            <!-- 权限配置 -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">权限配置</h2>
                    <p class="text-sm text-gray-500 mt-1" id="selectedRoleInfo">请选择一个角色来配置权限</p>
                </div>
                <div class="p-6">
                    <div id="permissionsTree" class="permission-tree">
                        <!-- 权限树将在这里动态加载 -->
                    </div>
                    <div class="mt-6 flex justify-end space-x-4" id="permissionActions" style="display: none;">
                        <button id="cancelPermissionEdit" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                            取消
                        </button>
                        <button id="savePermissions" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                            保存权限
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 角色编辑弹窗 -->
    <div id="roleModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 fade-in">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 id="roleModalTitle" class="text-xl font-semibold text-gray-800">添加角色</h2>
                    <button id="closeRoleModal" class="text-gray-400 hover:text-gray-600 text-2xl btn-fix">&times;</button>
                </div>
            </div>
            <div class="p-6">
                <form id="roleForm" class="space-y-4">
                    <input type="hidden" id="editRoleId">
                    <div>
                        <label for="roleName" class="block text-sm font-medium text-gray-700 mb-1">角色名称 <span class="text-red-500">*</span></label>
                        <input type="text" id="roleName" required
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                    </div>
                    <div>
                        <label for="roleCode" class="block text-sm font-medium text-gray-700 mb-1">角色代码 <span class="text-red-500">*</span></label>
                        <input type="text" id="roleCode" required pattern="[a-zA-Z_][a-zA-Z0-9_]*"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                            placeholder="例如: admin, manager, user">
                        <p class="text-xs text-gray-500 mt-1">只能包含字母、数字和下划线，且以字母或下划线开头</p>
                    </div>
                    <div>
                        <label for="roleDescription" class="block text-sm font-medium text-gray-700 mb-1">角色描述</label>
                        <textarea id="roleDescription" rows="3"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"></textarea>
                    </div>
                    <div>
                        <label for="roleStatus" class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                        <select id="roleStatus"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                            <option value="active">启用</option>
                            <option value="inactive">禁用</option>
                        </select>
                    </div>
                    <div class="flex gap-3 pt-4">
                        <button type="button" id="cancelRoleEdit" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                            取消
                        </button>
                        <button type="submit" class="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                            保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 加载指示器 -->
    <div id="loadingIndicator" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-40" style="display: none;">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <span class="text-gray-700">加载中...</span>
        </div>
    </div>

    <!-- 引入模块化JS文件 -->
    <script src="../js/config.js"></script>
    <script src="../js/permission-manager.js"></script>
    <script>
        // 角色权限管理应用类
        class RoleManagementApp {
            constructor() {
                this.selectedRoleId = null;
                this.permissions = this.getDefaultPermissions();
            }

            init() {
                this.bindEvents();
                this.loadRoles();
            }

            bindEvents() {
                // 添加角色按钮
                document.getElementById('addRoleBtn').addEventListener('click', () => {
                    this.showRoleModal();
                });

                // 角色弹窗事件
                document.getElementById('closeRoleModal').addEventListener('click', () => {
                    document.getElementById('roleModal').style.display = 'none';
                });

                document.getElementById('cancelRoleEdit').addEventListener('click', () => {
                    document.getElementById('roleModal').style.display = 'none';
                });

                // 角色表单提交
                document.getElementById('roleForm').addEventListener('submit', async (e) => {
                    e.preventDefault();
                    await this.saveRole();
                });

                // 权限操作按钮
                document.getElementById('savePermissions').addEventListener('click', () => {
                    this.saveRolePermissions();
                });

                document.getElementById('cancelPermissionEdit').addEventListener('click', () => {
                    this.cancelPermissionEdit();
                });
            }

            getDefaultPermissions() {
                return {
                    'system': {
                        name: '系统管理',
                        children: {
                            'user_management': { name: '用户管理', permissions: ['view', 'create', 'edit', 'delete'] },
                            'role_management': { name: '角色管理', permissions: ['view', 'create', 'edit', 'delete'] },
                            'system_settings': { name: '系统设置', permissions: ['view', 'edit'] },
                            'logs': { name: '日志查看', permissions: ['view', 'export'] }
                        }
                    },
                    'inventory': {
                        name: '库存管理',
                        children: {
                            'products': { name: '商品管理', permissions: ['view', 'create', 'edit', 'delete'] },
                            'warehouses': { name: '仓库管理', permissions: ['view', 'create', 'edit', 'delete'] },
                            'inbound': { name: '入库管理', permissions: ['view', 'create', 'edit', 'delete'] },
                            'outbound': { name: '出库管理', permissions: ['view', 'create', 'edit', 'delete'] },
                            'transfer': { name: '调拨管理', permissions: ['view', 'create', 'edit', 'delete'] },
                            'count': { name: '盘点管理', permissions: ['view', 'create', 'edit', 'delete'] }
                        }
                    },
                    'work': {
                        name: '工作管理',
                        children: {
                            'logs': { name: '工作日志', permissions: ['view', 'create', 'edit', 'delete'] },
                            'reports': { name: '工作报表', permissions: ['view', 'export'] }
                        }
                    },
                    'repair': {
                        name: '维修管理',
                        children: {
                            'orders': { name: '维修单管理', permissions: ['view', 'create', 'edit', 'delete'] },
                            'reports': { name: '维修报表', permissions: ['view', 'export'] }
                        }
                    }
                };
            }

            async loadRoles() {
                try {
                    this.showLoading();

                    const query = new AV.Query('UserRole');
                    query.ascending('name');
                    const roles = await query.find();

                    this.renderRolesList(roles);
                    this.hideLoading();
                } catch (error) {
                    console.error('加载角色列表失败:', error);
                    this.hideLoading();

                    // 如果表不存在，创建默认角色
                    if (error.message.includes('Class or object doesn\'t exists')) {
                        await this.createDefaultRoles();
                    } else {
                        alert('加载角色列表失败: ' + error.message);
                    }
                }
            }

            async createDefaultRoles() {
                try {
                    const defaultRoles = [
                        {
                            name: '超级管理员',
                            code: 'super_admin',
                            description: '拥有系统所有权限的超级管理员',
                            status: 'active',
                            permissions: this.getAllPermissions()
                        },
                        {
                            name: '管理员',
                            code: 'admin',
                            description: '系统管理员，拥有大部分管理权限',
                            status: 'active',
                            permissions: this.getAdminPermissions()
                        },
                        {
                            name: '普通用户',
                            code: 'user',
                            description: '普通用户，拥有基本操作权限',
                            status: 'active',
                            permissions: this.getUserPermissions()
                        }
                    ];

                    for (const roleData of defaultRoles) {
                        const role = new AV.Object('UserRole');
                        Object.keys(roleData).forEach(key => {
                            role.set(key, roleData[key]);
                        });
                        await role.save();
                    }

                    alert('已创建默认角色');
                    this.loadRoles();
                } catch (error) {
                    console.error('创建默认角色失败:', error);
                    alert('创建默认角色失败: ' + error.message);
                }
            }

            getAllPermissions() {
                const allPermissions = {};
                Object.keys(this.permissions).forEach(moduleKey => {
                    allPermissions[moduleKey] = {};
                    Object.keys(this.permissions[moduleKey].children).forEach(subKey => {
                        allPermissions[moduleKey][subKey] = this.permissions[moduleKey].children[subKey].permissions;
                    });
                });
                return allPermissions;
            }

            getAdminPermissions() {
                const adminPermissions = {};
                Object.keys(this.permissions).forEach(moduleKey => {
                    adminPermissions[moduleKey] = {};
                    Object.keys(this.permissions[moduleKey].children).forEach(subKey => {
                        // 管理员除了用户删除权限外，拥有其他所有权限
                        if (moduleKey === 'system' && subKey === 'user_management') {
                            adminPermissions[moduleKey][subKey] = ['view', 'create', 'edit'];
                        } else {
                            adminPermissions[moduleKey][subKey] = this.permissions[moduleKey].children[subKey].permissions;
                        }
                    });
                });
                return adminPermissions;
            }

            getUserPermissions() {
                return {
                    'work': {
                        'logs': ['view', 'create', 'edit']
                    },
                    'repair': {
                        'orders': ['view', 'create']
                    },
                    'inventory': {
                        'products': ['view'],
                        'inbound': ['view'],
                        'outbound': ['view']
                    }
                };
            }

            renderRolesList(roles) {
                const container = document.getElementById('rolesList');
                
                if (roles.length === 0) {
                    container.innerHTML = '<p class="text-gray-500 text-center py-8">暂无角色数据</p>';
                    return;
                }

                const html = roles.map(role => {
                    const isActive = role.get('status') === 'active';
                    return `
                        <div class="role-item border rounded-lg p-4 cursor-pointer hover:bg-gray-50 ${this.selectedRoleId === role.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}"
                             onclick="roleApp.selectRole('${role.id}')">
                            <div class="flex justify-between items-start">
                                <div class="flex-1">
                                    <h3 class="font-medium text-gray-900">${role.get('name')}</h3>
                                    <p class="text-sm text-gray-500 mt-1">${role.get('code')}</p>
                                    <p class="text-sm text-gray-600 mt-2">${role.get('description') || ''}</p>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full ${isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                                        ${isActive ? '启用' : '禁用'}
                                    </span>
                                    <div class="flex space-x-1">
                                        <button onclick="event.stopPropagation(); roleApp.editRole('${role.id}')" 
                                                class="text-blue-600 hover:text-blue-900 text-sm">编辑</button>
                                        <button onclick="event.stopPropagation(); roleApp.deleteRole('${role.id}')" 
                                                class="text-red-600 hover:text-red-900 text-sm">删除</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');

                container.innerHTML = html;
            }

            selectRole(roleId) {
                this.selectedRoleId = roleId;
                this.loadRolePermissions(roleId);
                this.updateRoleSelection();
            }

            updateRoleSelection() {
                // 更新角色选中状态
                document.querySelectorAll('.role-item').forEach(item => {
                    item.classList.remove('border-blue-500', 'bg-blue-50');
                    item.classList.add('border-gray-200');
                });

                if (this.selectedRoleId) {
                    const selectedItem = document.querySelector(`[onclick*="${this.selectedRoleId}"]`);
                    if (selectedItem) {
                        selectedItem.classList.remove('border-gray-200');
                        selectedItem.classList.add('border-blue-500', 'bg-blue-50');
                    }
                }
            }

            async loadRolePermissions(roleId) {
                try {
                    const query = new AV.Query('UserRole');
                    const role = await query.get(roleId);

                    document.getElementById('selectedRoleInfo').textContent = `正在配置角色：${role.get('name')}`;

                    const rolePermissions = role.get('permissions') || {};
                    this.renderPermissionsTree(rolePermissions);

                    document.getElementById('permissionActions').style.display = 'flex';
                } catch (error) {
                    console.error('加载角色权限失败:', error);
                    alert('加载角色权限失败: ' + error.message);
                }
            }

            renderPermissionsTree(rolePermissions) {
                const container = document.getElementById('permissionsTree');
                
                const html = Object.keys(this.permissions).map(moduleKey => {
                    const module = this.permissions[moduleKey];
                    const modulePermissions = rolePermissions[moduleKey] || {};
                    
                    return `
                        <div class="mb-6">
                            <h3 class="font-medium text-gray-900 mb-3">${module.name}</h3>
                            <div class="space-y-3 ml-4">
                                ${Object.keys(module.children).map(subKey => {
                                    const subModule = module.children[subKey];
                                    const subPermissions = modulePermissions[subKey] || [];
                                    
                                    return `
                                        <div class="border rounded-lg p-3">
                                            <h4 class="font-medium text-gray-800 mb-2">${subModule.name}</h4>
                                            <div class="grid grid-cols-2 gap-2">
                                                ${subModule.permissions.map(permission => {
                                                    const isChecked = subPermissions.includes(permission);
                                                    const permissionName = this.getPermissionName(permission);
                                                    return `
                                                        <label class="flex items-center">
                                                            <input type="checkbox" 
                                                                   class="rounded permission-checkbox" 
                                                                   data-module="${moduleKey}" 
                                                                   data-sub="${subKey}" 
                                                                   data-permission="${permission}"
                                                                   ${isChecked ? 'checked' : ''}>
                                                            <span class="ml-2 text-sm text-gray-700">${permissionName}</span>
                                                        </label>
                                                    `;
                                                }).join('')}
                                            </div>
                                        </div>
                                    `;
                                }).join('')}
                            </div>
                        </div>
                    `;
                }).join('');

                container.innerHTML = html;
            }

            getPermissionName(permission) {
                const names = {
                    'view': '查看',
                    'create': '创建',
                    'edit': '编辑',
                    'delete': '删除',
                    'export': '导出'
                };
                return names[permission] || permission;
            }

            async saveRolePermissions() {
                if (!this.selectedRoleId) return;

                try {
                    this.showLoading();

                    // 收集权限数据
                    const permissions = {};
                    document.querySelectorAll('.permission-checkbox:checked').forEach(checkbox => {
                        const module = checkbox.dataset.module;
                        const sub = checkbox.dataset.sub;
                        const permission = checkbox.dataset.permission;

                        if (!permissions[module]) permissions[module] = {};
                        if (!permissions[module][sub]) permissions[module][sub] = [];
                        permissions[module][sub].push(permission);
                    });

                    // 保存到数据库
                    const role = AV.Object.createWithoutData('UserRole', this.selectedRoleId);
                    role.set('permissions', permissions);
                    await role.save();

                    this.hideLoading();
                    alert('权限保存成功');
                } catch (error) {
                    console.error('保存权限失败:', error);
                    this.hideLoading();
                    alert('保存权限失败: ' + error.message);
                }
            }

            cancelPermissionEdit() {
                this.selectedRoleId = null;
                document.getElementById('selectedRoleInfo').textContent = '请选择一个角色来配置权限';
                document.getElementById('permissionsTree').innerHTML = '';
                document.getElementById('permissionActions').style.display = 'none';
                this.updateRoleSelection();
            }

            showRoleModal(roleId = null) {
                const modal = document.getElementById('roleModal');
                const title = document.getElementById('roleModalTitle');
                const form = document.getElementById('roleForm');
                
                form.reset();
                document.getElementById('editRoleId').value = roleId || '';
                
                if (roleId) {
                    title.textContent = '编辑角色';
                    this.loadRoleForEdit(roleId);
                } else {
                    title.textContent = '添加角色';
                }
                
                modal.style.display = 'flex';
            }

            async loadRoleForEdit(roleId) {
                try {
                    const query = new AV.Query('UserRole');
                    const role = await query.get(roleId);

                    document.getElementById('roleName').value = role.get('name');
                    document.getElementById('roleCode').value = role.get('code');
                    document.getElementById('roleDescription').value = role.get('description') || '';
                    document.getElementById('roleStatus').value = role.get('status') || 'active';
                } catch (error) {
                    console.error('加载角色数据失败:', error);
                    alert('加载角色数据失败: ' + error.message);
                }
            }

            async saveRole() {
                try {
                    const roleId = document.getElementById('editRoleId').value;
                    const name = document.getElementById('roleName').value;
                    const code = document.getElementById('roleCode').value;
                    const description = document.getElementById('roleDescription').value;
                    const status = document.getElementById('roleStatus').value;

                    let role;
                    if (roleId) {
                        role = AV.Object.createWithoutData('UserRole', roleId);
                    } else {
                        role = new AV.Object('UserRole');
                    }

                    role.set('name', name);
                    role.set('code', code);
                    role.set('description', description);
                    role.set('status', status);

                    await role.save();

                    alert(roleId ? '角色更新成功' : '角色创建成功');
                    document.getElementById('roleModal').style.display = 'none';
                    this.loadRoles();
                } catch (error) {
                    console.error('保存角色失败:', error);
                    alert('保存角色失败: ' + error.message);
                }
            }

            async editRole(roleId) {
                this.showRoleModal(roleId);
            }

            async deleteRole(roleId) {
                if (!confirm('确定要删除该角色吗？此操作不可恢复！')) {
                    return;
                }

                try {
                    const role = AV.Object.createWithoutData('UserRole', roleId);
                    await role.destroy();
                    alert('角色删除成功');
                    
                    if (this.selectedRoleId === roleId) {
                        this.cancelPermissionEdit();
                    }
                    
                    this.loadRoles();
                } catch (error) {
                    console.error('删除角色失败:', error);
                    alert('删除角色失败: ' + error.message);
                }
            }

            showLoading() {
                document.getElementById('loadingIndicator').style.display = 'flex';
            }

            hideLoading() {
                document.getElementById('loadingIndicator').style.display = 'none';
            }
        }

        // 全局变量
        let roleApp;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            function initApp() {
                if (typeof AV !== 'undefined') {
                    roleApp = new RoleManagementApp();
                    roleApp.init();
                } else {
                    setTimeout(initApp, 100);
                }
            }
            initApp();
        });
    </script>
</body>
</html>
