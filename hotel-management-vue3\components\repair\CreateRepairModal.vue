<template>
  <div 
    v-if="show" 
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    @click="handleBackdropClick"
  >
    <div 
      class="bg-white rounded-lg w-full max-w-3xl max-h-[90vh] overflow-hidden"
      @click.stop
    >
      <!-- 头部 -->
      <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-900">
          {{ isEditing ? '编辑报修工单' : '新建报修工单' }}
        </h3>
        <button 
          @click="handleClose"
          class="text-gray-400 hover:text-gray-600"
        >
          <Icon name="mdi:close" size="24" />
        </button>
      </div>

      <!-- 内容 -->
      <div class="px-6 py-4 max-h-[calc(90vh-140px)] overflow-y-auto">
        <form @submit.prevent="handleSubmit">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 左侧：基本信息 -->
            <div class="space-y-6">
              <h4 class="text-md font-medium text-gray-900">基本信息</h4>
              
              <!-- 报修标题 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  报修标题 *
                </label>
                <input 
                  v-model="formData.title"
                  type="text"
                  required
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="请简要描述问题"
                />
              </div>

              <!-- 报修分类 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  报修分类 *
                </label>
                <select 
                  v-model="formData.category"
                  required
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">请选择分类</option>
                  <option value="空调设备">空调设备</option>
                  <option value="电梯设备">电梯设备</option>
                  <option value="照明设备">照明设备</option>
                  <option value="水电设施">水电设施</option>
                  <option value="门窗设施">门窗设施</option>
                  <option value="网络设备">网络设备</option>
                  <option value="安防设备">安防设备</option>
                  <option value="清洁设备">清洁设备</option>
                  <option value="其他设备">其他设备</option>
                </select>
              </div>

              <!-- 故障位置 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  故障位置 *
                </label>
                <input 
                  v-model="formData.location"
                  type="text"
                  required
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="如：201房间、大堂、3号电梯等"
                />
              </div>

              <!-- 优先级 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  优先级 *
                </label>
                <div class="grid grid-cols-2 gap-3">
                  <label 
                    v-for="priority in priorities" 
                    :key="priority.value"
                    class="relative flex cursor-pointer rounded-lg border p-4 focus:outline-none"
                    :class="formData.priority === priority.value ? 'border-blue-600 ring-2 ring-blue-600' : 'border-gray-300'"
                  >
                    <input
                      v-model="formData.priority"
                      type="radio"
                      :value="priority.value"
                      class="sr-only"
                      :disabled="loading"
                    />
                    <div class="flex w-full items-center justify-between">
                      <div class="flex items-center">
                        <div class="text-sm">
                          <div class="font-medium text-gray-900 flex items-center">
                            <Icon :name="priority.icon" size="16" :class="priority.color" class="mr-2" />
                            {{ priority.label }}
                          </div>
                          <div class="text-gray-500">{{ priority.description }}</div>
                        </div>
                      </div>
                    </div>
                  </label>
                </div>
              </div>
            </div>

            <!-- 右侧：详细描述 -->
            <div class="space-y-6">
              <h4 class="text-md font-medium text-gray-900">详细描述</h4>
              
              <!-- 问题描述 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  问题描述 *
                </label>
                <textarea 
                  v-model="formData.description"
                  rows="6"
                  required
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                  placeholder="请详细描述故障现象、发生时间、影响范围等..."
                ></textarea>
                <div class="mt-1 text-xs text-gray-500">
                  {{ formData.description.length }} / 500 字符
                </div>
              </div>

              <!-- 联系方式 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  联系方式
                </label>
                <input 
                  v-model="formData.contactInfo"
                  type="text"
                  :disabled="loading"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  :placeholder="authStore.user?.phone || '请输入联系电话'"
                />
                <div class="mt-1 text-xs text-gray-500">
                  方便工程师联系您确认问题
                </div>
              </div>

              <!-- 预计影响 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  预计影响
                </label>
                <div class="space-y-2">
                  <label class="flex items-center">
                    <input 
                      v-model="formData.affectsGuests"
                      type="checkbox"
                      :disabled="loading"
                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span class="ml-2 text-sm text-gray-700">影响客人使用</span>
                  </label>
                  
                  <label class="flex items-center">
                    <input 
                      v-model="formData.affectsOperations"
                      type="checkbox"
                      :disabled="loading"
                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span class="ml-2 text-sm text-gray-700">影响酒店运营</span>
                  </label>
                  
                  <label class="flex items-center">
                    <input 
                      v-model="formData.safetyIssue"
                      type="checkbox"
                      :disabled="loading"
                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span class="ml-2 text-sm text-gray-700">存在安全隐患</span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          <!-- 图片上传 -->
          <div class="mt-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              现场照片
            </label>
            
            <!-- 已上传的图片 -->
            <div v-if="formData.images.length > 0" class="mb-4">
              <div class="grid grid-cols-4 gap-2">
                <div 
                  v-for="(image, index) in formData.images" 
                  :key="index"
                  class="relative group"
                >
                  <img 
                    :src="image" 
                    :alt="`图片 ${index + 1}`"
                    class="w-full h-20 object-cover rounded border border-gray-200"
                  />
                  <button 
                    type="button"
                    @click="removeImage(index)"
                    class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600 opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    ×
                  </button>
                </div>
              </div>
            </div>
            
            <!-- 上传按钮 -->
            <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors">
              <input 
                ref="fileInput"
                type="file" 
                multiple 
                accept="image/*"
                class="hidden"
                @change="handleFileSelect"
              />
              
              <button 
                type="button"
                @click="$refs.fileInput?.click()"
                :disabled="loading || uploading"
                class="text-blue-600 hover:text-blue-700 font-medium disabled:opacity-50"
              >
                <Icon name="mdi:camera" size="24" class="mx-auto mb-2" />
                {{ uploading ? '上传中...' : '添加现场照片' }}
              </button>
              
              <p class="text-xs text-gray-500 mt-1">
                支持 JPG、PNG 格式，最多 6 张图片，有助于工程师快速定位问题
              </p>
            </div>
          </div>
        </form>
      </div>

      <!-- 底部按钮 -->
      <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
        <button 
          type="button"
          @click="handleClose"
          :disabled="loading"
          class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50"
        >
          取消
        </button>
        <button 
          @click="handleSubmit"
          :disabled="loading || !isFormValid"
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center"
        >
          <Icon v-if="loading" name="mdi:loading" size="16" class="mr-1 animate-spin" />
          {{ loading ? (isEditing ? '更新中...' : '提交中...') : (isEditing ? '更新' : '提交报修') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Repair } from '~/types'

interface Props {
  show: boolean
  editRepair?: Repair | null
}

const props = withDefaults(defineProps<Props>(), {
  editRepair: null
})

const emit = defineEmits<{
  'update:show': [value: boolean]
  created: []
  updated: []
}>()

const repairStore = useRepairStore()
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const uploading = ref(false)
const fileInput = ref<HTMLInputElement>()

const formData = reactive({
  title: '',
  category: '',
  location: '',
  priority: 'medium' as 'low' | 'medium' | 'high' | 'urgent',
  description: '',
  contactInfo: '',
  affectsGuests: false,
  affectsOperations: false,
  safetyIssue: false,
  images: [] as string[]
})

// 优先级选项
const priorities = [
  {
    value: 'low',
    label: '低',
    description: '不紧急，可安排时间处理',
    icon: 'mdi:arrow-down',
    color: 'text-gray-500'
  },
  {
    value: 'medium',
    label: '中',
    description: '正常优先级',
    icon: 'mdi:minus',
    color: 'text-yellow-500'
  },
  {
    value: 'high',
    label: '高',
    description: '需要优先处理',
    icon: 'mdi:arrow-up',
    color: 'text-orange-500'
  },
  {
    value: 'urgent',
    label: '紧急',
    description: '立即处理',
    icon: 'mdi:alert',
    color: 'text-red-500'
  }
]

// 计算属性
const isEditing = computed(() => !!props.editRepair)

const isFormValid = computed(() => {
  return formData.title.trim().length > 0 && 
         formData.category.length > 0 &&
         formData.location.trim().length > 0 &&
         formData.description.trim().length > 0 &&
         formData.description.length <= 500
})

// 方法
const resetForm = () => {
  formData.title = ''
  formData.category = ''
  formData.location = ''
  formData.priority = 'medium'
  formData.description = ''
  formData.contactInfo = authStore.user?.phone || ''
  formData.affectsGuests = false
  formData.affectsOperations = false
  formData.safetyIssue = false
  formData.images = []
}

const loadEditData = () => {
  if (props.editRepair) {
    formData.title = props.editRepair.title
    formData.category = props.editRepair.category
    formData.location = props.editRepair.location
    formData.priority = props.editRepair.priority
    formData.description = props.editRepair.description
    formData.contactInfo = ''
    formData.affectsGuests = false
    formData.affectsOperations = false
    formData.safetyIssue = false
    formData.images = [...(props.editRepair.images || [])]
  }
}

const handleClose = () => {
  if (!loading.value) {
    emit('update:show', false)
    resetForm()
  }
}

const handleBackdropClick = () => {
  handleClose()
}

const handleFileSelect = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files
  
  if (!files || files.length === 0) return
  
  // 检查图片数量限制
  if (formData.images.length + files.length > 6) {
    alert('最多只能上传6张图片')
    return
  }
  
  uploading.value = true
  
  try {
    for (const fileItem of Array.from(files)) {
      // 检查文件类型
      if (!fileItem.type.startsWith('image/')) {
        alert(`文件 ${fileItem.name} 不是有效的图片格式`)
        continue
      }

      // 检查文件大小（5MB限制）
      if (fileItem.size > 5 * 1024 * 1024) {
        alert(`文件 ${fileItem.name} 大小超过5MB限制`)
        continue
      }

      // 压缩图片（简化版，直接使用原文件）
      const compressedBlob = fileItem

      // 创建新的文件名
      const originalName = fileItem.name
      const nameWithoutExt = originalName.substring(0, originalName.lastIndexOf('.')) || originalName
      const compressedFileName = `repair_${nameWithoutExt}_${Date.now()}.jpg`

      // 构造FormData
      const uploadFormData = new FormData()
      uploadFormData.append('token', '8e7057ee0ba0be565301980fb3e52763')
      uploadFormData.append('image', new File([compressedBlob], compressedFileName, { type: 'image/jpeg' }))

      // 上传到自建API
      const response = await fetch('https://www.junwei.bid:89/web/11/index.php', {
        method: 'POST',
        body: uploadFormData
      })

      if (response.ok) {
        const result = await response.json()
        // 兼容不同返回格式
        if (result.url) {
          formData.images.push(result.url)
        } else if (result.data && result.data.url) {
          formData.images.push(result.data.url)
        } else if (typeof result === 'string' && result.startsWith('http')) {
          formData.images.push(result)
        } else {
          alert(`上传文件 ${fileItem.name} 失败: 返回格式不正确`)
        }
      } else {
        const errorText = await response.text()
        alert(`上传文件 ${fileItem.name} 失败: ${errorText}`)
      }
    }
  } catch (error) {
    console.error('文件上传失败:', error)
    alert('文件上传失败，请稍后重试')
  } finally {
    uploading.value = false
    // 清空文件输入
    if (fileInput.value) {
      fileInput.value.value = ''
    }
  }
}

const removeImage = (index: number) => {
  formData.images.splice(index, 1)
}

const handleSubmit = async () => {
  if (!isFormValid.value || loading.value) return
  
  loading.value = true
  
  try {
    // 根据选择的影响自动调整优先级
    let priority = formData.priority
    if (formData.safetyIssue || (formData.affectsGuests && formData.affectsOperations)) {
      priority = 'urgent'
    } else if (formData.affectsGuests || formData.affectsOperations) {
      priority = priority === 'low' ? 'medium' : priority
    }
    
    const repairData = {
      title: formData.title.trim(),
      category: formData.category,
      location: formData.location.trim(),
      priority,
      description: formData.description.trim(),
      images: formData.images
    }
    
    let result
    
    if (isEditing.value) {
      result = await repairStore.updateRepair(props.editRepair!.id, repairData)
      if (result.success) {
        emit('updated')
      }
    } else {
      result = await repairStore.createRepair(repairData)
      if (result.success) {
        emit('created')
      }
    }
    
    if (!result.success) {
      alert(result.error || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    alert('提交失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 监听props变化
watch(() => props.show, (newValue) => {
  if (newValue) {
    if (props.editRepair) {
      loadEditData()
    } else {
      resetForm()
    }
  }
})

// 键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.show) {
    handleClose()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>
