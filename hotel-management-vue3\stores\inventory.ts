// stores/inventory.ts
import { defineStore } from 'pinia'
import AV from 'leancloud-storage'
import type { InventoryItem, InventoryTransaction, StockAlert, StockCountPlan, StockCountRecord, PaginationOptions, FilterOptions } from '~/types'

interface InventoryState {
  items: InventoryItem[]
  transactions: InventoryTransaction[]
  alerts: StockAlert[]
  countPlans: StockCountPlan[]
  countRecords: StockCountRecord[]
  loading: boolean
  currentPage: number
  totalPages: number
  total: number
  filters: FilterOptions & {
    category?: string
    location?: string
    status?: string
    lowStock?: boolean
  }
}

export const useInventoryStore = defineStore('inventory', {
  state: (): InventoryState => ({
    items: [],
    transactions: [],
    alerts: [],
    countPlans: [],
    countRecords: [],
    loading: false,
    currentPage: 0,
    totalPages: 0,
    total: 0,
    filters: {}
  }),

  getters: {
    // 按分类分组的库存
    itemsByCategory: (state) => {
      const grouped: Record<string, InventoryItem[]> = {}
      
      state.items.forEach(item => {
        if (!grouped[item.category]) {
          grouped[item.category] = []
        }
        grouped[item.category].push(item)
      })
      
      return grouped
    },

    // 低库存商品
    lowStockItems: (state) => {
      return state.items.filter(item => item.quantity <= item.minStock)
    },

    // 缺货商品
    outOfStockItems: (state) => {
      return state.items.filter(item => item.quantity === 0)
    },

    // 库存统计
    inventoryStats: (state) => {
      const total = state.items.length
      const lowStock = state.items.filter(item => item.quantity <= item.minStock).length
      const outOfStock = state.items.filter(item => item.quantity === 0).length
      const totalValue = state.items.reduce((sum, item) => sum + (item.quantity * (item.price || 0)), 0)
      
      return {
        total,
        lowStock,
        outOfStock,
        totalValue,
        categories: [...new Set(state.items.map(item => item.category))].length
      }
    },

    // 筛选后的库存
    filteredItems: (state) => {
      let filtered = [...state.items]
      
      // 按分类筛选
      if (state.filters.category) {
        filtered = filtered.filter(item => item.category === state.filters.category)
      }
      
      // 按位置筛选
      if (state.filters.location) {
        filtered = filtered.filter(item => item.location === state.filters.location)
      }
      
      // 按状态筛选
      if (state.filters.status) {
        filtered = filtered.filter(item => item.status === state.filters.status)
      }
      
      // 只显示低库存
      if (state.filters.lowStock) {
        filtered = filtered.filter(item => item.quantity <= item.minStock)
      }
      
      return filtered
    }
  },

  actions: {
    // 获取库存列表
    async fetchItems(options: PaginationOptions & FilterOptions = {}) {
      this.loading = true

      try {
        const query = new AV.Query('InventoryItem')

        // 分页
        const page = options.page || 0
        const limit = options.limit || 20
        query.skip(page * limit)
        query.limit(limit)

        // 筛选条件
        if (options.category) {
          query.equalTo('category', options.category)
        }

        if (options.keyword) {
          query.contains('name', options.keyword)
        }

        if (options.lowStock) {
          query.lessThanOrEqualTo('quantity', 10)
        }

        // 排序
        query.descending('updatedAt')

        const results = await query.find()
        const items = results.map(item => ({
          id: item.id,
          name: item.get('name'),
          category: item.get('category'),
          quantity: item.get('quantity'),
          unit: item.get('unit'),
          minStock: item.get('minStock'),
          maxStock: item.get('maxStock'),
          price: item.get('price'),
          supplier: item.get('supplier'),
          location: item.get('location'),
          description: item.get('description'),
          status: item.get('status') || 'active',
          images: item.get('images') || [],
          createdAt: item.createdAt,
          updatedAt: item.updatedAt
        }))

        if (options.page === 0) {
          this.items = items
        } else {
          this.items.push(...items)
        }

        this.currentPage = options.page || 0
        this.total = results.length

        return { success: true, data: items }
      } catch (error: any) {
        console.error('获取库存列表失败:', error)

        // 如果LeanCloud调用失败，使用模拟数据作为后备
        const mockItems: InventoryItem[] = [
          {
            id: '1',
            name: '洗发水',
            category: '客房用品',
            quantity: 50,
            unit: '瓶',
            minStock: 20,
            maxStock: 100,
            location: '仓库A',
            supplier: '日化供应商',
            price: 15.5,
            status: 'active',
            description: '高品质洗发水，适合酒店客房使用',
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            id: '2',
            name: '毛巾',
            category: '客房用品',
            quantity: 5,
            unit: '条',
            minStock: 30,
            maxStock: 200,
            location: '仓库B',
            supplier: '纺织品供应商',
            price: 25.0,
            status: 'active',
            description: '纯棉毛巾，柔软舒适',
            createdAt: new Date(),
            updatedAt: new Date()
          }
        ]
        
        if (options.page === 0) {
          this.items = mockItems
        } else {
          this.items.push(...mockItems)
        }
        
        this.currentPage = options.page || 0
        
        return { success: true, data: mockItems }
      } catch (error) {
        console.error('获取库存失败:', error)
        return { success: false, error: '获取库存失败' }
      } finally {
        this.loading = false
      }
    },

    // 创建库存项目
    async createItem(itemData: Omit<InventoryItem, 'id' | 'createdAt' | 'updatedAt'>) {
      try {
        const InventoryItem = AV.Object.extend('InventoryItem')
        const item = new InventoryItem()

        // 设置所有字段
        item.set('name', itemData.name)
        item.set('category', itemData.category)
        item.set('quantity', itemData.quantity)
        item.set('unit', itemData.unit)
        item.set('minStock', itemData.minStock)
        item.set('maxStock', itemData.maxStock || 0)
        item.set('price', itemData.price || 0)
        item.set('supplier', itemData.supplier || '')
        item.set('location', itemData.location || '')
        item.set('description', itemData.description || '')
        item.set('status', itemData.status || 'active')
        item.set('images', itemData.images || [])

        const savedItem = await item.save()

        const newItem: InventoryItem = {
          id: savedItem.id,
          name: savedItem.get('name'),
          category: savedItem.get('category'),
          quantity: savedItem.get('quantity'),
          unit: savedItem.get('unit'),
          minStock: savedItem.get('minStock'),
          maxStock: savedItem.get('maxStock'),
          price: savedItem.get('price'),
          supplier: savedItem.get('supplier'),
          location: savedItem.get('location'),
          description: savedItem.get('description'),
          status: savedItem.get('status'),
          images: savedItem.get('images'),
          createdAt: savedItem.createdAt,
          updatedAt: savedItem.updatedAt
        }

        this.items.unshift(newItem)
        return { success: true, data: newItem }
      } catch (error) {
        console.error('创建库存项目失败:', error)
        return { success: false, error: '创建库存项目失败' }
      }
    },

    // 更新库存项目
    async updateItem(id: string, itemData: Partial<InventoryItem>) {
      try {
        const item = AV.Object.createWithoutData('InventoryItem', id)

        // 更新字段
        Object.keys(itemData).forEach(key => {
          if (key !== 'id' && key !== 'createdAt' && key !== 'updatedAt') {
            const value = (itemData as any)[key]
            if (value !== undefined) {
              item.set(key, value)
            }
          }
        })

        const savedItem = await item.save()

        const updatedItem: InventoryItem = {
          id: savedItem.id,
          name: savedItem.get('name'),
          category: savedItem.get('category'),
          quantity: savedItem.get('quantity'),
          unit: savedItem.get('unit'),
          minStock: savedItem.get('minStock'),
          maxStock: savedItem.get('maxStock'),
          price: savedItem.get('price'),
          supplier: savedItem.get('supplier'),
          location: savedItem.get('location'),
          description: savedItem.get('description'),
          status: savedItem.get('status'),
          images: savedItem.get('images'),
          createdAt: savedItem.createdAt,
          updatedAt: savedItem.updatedAt
        }

        // 更新本地数据
        const index = this.items.findIndex(item => item.id === id)
        if (index > -1) {
          this.items[index] = updatedItem
        }

        return { success: true, data: updatedItem }
      } catch (error) {
        console.error('更新库存项目失败:', error)
        return { success: false, error: '更新库存项目失败' }
      }
    },

    // 删除库存项目
    async deleteItem(id: string) {
      try {
        const item = AV.Object.createWithoutData('InventoryItem', id)
        await item.destroy()

        // 从本地数据中移除
        const index = this.items.findIndex(item => item.id === id)
        if (index > -1) {
          this.items.splice(index, 1)
        }

        return { success: true }
      } catch (error) {
        console.error('删除库存项目失败:', error)
        return { success: false, error: '删除库存项目失败' }
      }
    },

    // 库存调整
    async adjustStock(id: string, quantity: number, reason: string) {
      try {
        // 获取当前库存
        const itemQuery = new AV.Query('InventoryItem')
        const item = await itemQuery.get(id)
        const currentQuantity = item.get('quantity')

        // 更新库存数量
        item.set('quantity', quantity)
        await item.save()

        // 记录库存变动
        const Transaction = AV.Object.extend('InventoryTransaction')
        const transaction = new Transaction()

        transaction.set('itemId', id)
        transaction.set('type', 'adjust')
        transaction.set('quantity', quantity - currentQuantity)
        transaction.set('beforeQuantity', currentQuantity)
        transaction.set('afterQuantity', quantity)
        transaction.set('reason', reason)
        transaction.set('operator', 'current_user') // 这里应该是当前用户

        await transaction.save()

        // 更新本地数据
        const localItem = this.items.find(item => item.id === id)
        if (localItem) {
          localItem.quantity = quantity
          localItem.updatedAt = new Date()

          // 检查库存警告
          this.checkStockAlerts(localItem)
        }

        const transactionData: InventoryTransaction = {
          id: transaction.id,
          itemId: id,
          item: localItem!,
          type: 'adjust',
          quantity: quantity - currentQuantity,
          beforeQuantity: currentQuantity,
          afterQuantity: quantity,
          reason,
          createdAt: transaction.createdAt
        }

        this.transactions.unshift(transactionData)

        return { success: true, data: transactionData }
      } catch (error: any) {
        console.error('库存调整失败:', error)
        return { success: false, error: error.message || '库存调整失败' }
      }
    },

    // 入库
    async stockIn(id: string, quantity: number, reason: string, reference?: string) {
      try {
        const item = this.items.find(item => item.id === id)
        if (!item) {
          return { success: false, error: '库存项目不存在' }
        }
        
        const beforeQuantity = item.quantity
        const afterQuantity = beforeQuantity + quantity
        
        // 更新库存数量
        item.quantity = afterQuantity
        item.updatedAt = new Date()
        
        // 记录库存变动
        const transaction: InventoryTransaction = {
          id: Date.now().toString(),
          itemId: id,
          item,
          type: 'in',
          quantity,
          beforeQuantity,
          afterQuantity,
          reason,
          reference,
          createdAt: new Date()
        }
        
        this.transactions.unshift(transaction)
        
        return { success: true, data: transaction }
      } catch (error) {
        console.error('入库失败:', error)
        return { success: false, error: '入库失败' }
      }
    },

    // 出库
    async stockOut(id: string, quantity: number, reason: string, reference?: string) {
      try {
        const item = this.items.find(item => item.id === id)
        if (!item) {
          return { success: false, error: '库存项目不存在' }
        }
        
        if (item.quantity < quantity) {
          return { success: false, error: '库存不足' }
        }
        
        const beforeQuantity = item.quantity
        const afterQuantity = beforeQuantity - quantity
        
        // 更新库存数量
        item.quantity = afterQuantity
        item.updatedAt = new Date()
        
        // 记录库存变动
        const transaction: InventoryTransaction = {
          id: Date.now().toString(),
          itemId: id,
          item,
          type: 'out',
          quantity: -quantity,
          beforeQuantity,
          afterQuantity,
          reason,
          reference,
          createdAt: new Date()
        }
        
        this.transactions.unshift(transaction)
        
        // 检查库存警告
        this.checkStockAlerts(item)
        
        return { success: true, data: transaction }
      } catch (error) {
        console.error('出库失败:', error)
        return { success: false, error: '出库失败' }
      }
    },

    // 检查库存警告
    checkStockAlerts(item: InventoryItem) {
      // 移除该商品的旧警告
      this.alerts = this.alerts.filter(alert => alert.itemId !== item.id)
      
      // 检查新的警告条件
      if (item.quantity === 0) {
        this.alerts.unshift({
          id: Date.now().toString(),
          itemId: item.id,
          item,
          type: 'out_of_stock',
          message: `${item.name} 已缺货`,
          isRead: false,
          createdAt: new Date()
        })
      } else if (item.quantity <= item.minStock) {
        this.alerts.unshift({
          id: Date.now().toString(),
          itemId: item.id,
          item,
          type: 'low_stock',
          message: `${item.name} 库存不足，当前库存：${item.quantity}${item.unit}`,
          isRead: false,
          createdAt: new Date()
        })
      }
    },

    // 设置筛选条件
    setFilters(filters: any) {
      this.filters = { ...this.filters, ...filters }
    },

    // 清除筛选条件
    clearFilters() {
      this.filters = {}
    },

    // 创建盘点计划
    async createCountPlan(planData: Omit<StockCountPlan, 'id' | 'createdAt' | 'updatedAt'>) {
      try {
        const CountPlan = AV.Object.extend('StockCountPlan')
        const plan = new CountPlan()

        plan.set('name', planData.name)
        plan.set('description', planData.description || '')
        plan.set('startDate', planData.startDate)
        plan.set('endDate', planData.endDate)
        plan.set('items', planData.items)
        plan.set('status', planData.status || 'pending')
        plan.set('createdBy', planData.createdBy)

        const savedPlan = await plan.save()

        const newPlan: StockCountPlan = {
          id: savedPlan.id,
          name: savedPlan.get('name'),
          description: savedPlan.get('description'),
          startDate: savedPlan.get('startDate'),
          endDate: savedPlan.get('endDate'),
          items: savedPlan.get('items'),
          status: savedPlan.get('status'),
          createdBy: savedPlan.get('createdBy'),
          createdAt: savedPlan.createdAt,
          updatedAt: savedPlan.updatedAt
        }

        this.countPlans.unshift(newPlan)
        return { success: true, data: newPlan }
      } catch (error: any) {
        console.error('创建盘点计划失败:', error)
        return { success: false, error: error.message || '创建盘点计划失败' }
      }
    },

    // 获取盘点计划列表
    async fetchCountPlans(options: PaginationOptions = {}) {
      this.loading = true

      try {
        const query = new AV.Query('StockCountPlan')

        // 分页
        const page = options.page || 0
        const limit = options.limit || 20
        query.skip(page * limit)
        query.limit(limit)

        // 排序
        query.descending('createdAt')

        const results = await query.find()
        const plans = results.map(plan => ({
          id: plan.id,
          name: plan.get('name'),
          description: plan.get('description'),
          startDate: plan.get('startDate'),
          endDate: plan.get('endDate'),
          items: plan.get('items'),
          status: plan.get('status'),
          createdBy: plan.get('createdBy'),
          createdAt: plan.createdAt,
          updatedAt: plan.updatedAt
        }))

        if (options.page === 0) {
          this.countPlans = plans
        } else {
          this.countPlans.push(...plans)
        }

        return { success: true, data: plans }
      } catch (error: any) {
        console.error('获取盘点计划失败:', error)
        return { success: false, error: error.message || '获取盘点计划失败' }
      } finally {
        this.loading = false
      }
    },

    // 开始盘点
    async startCount(planId: string) {
      try {
        const plan = this.countPlans.find(p => p.id === planId)
        if (!plan) {
          return { success: false, error: '盘点计划不存在' }
        }

        plan.status = 'active'
        plan.updatedAt = new Date()

        return { success: true, data: plan }
      } catch (error) {
        console.error('开始盘点失败:', error)
        return { success: false, error: '开始盘点失败' }
      }
    },

    // 提交盘点记录
    async submitCountRecord(recordData: Omit<StockCountRecord, 'id' | 'createdAt'>) {
      try {
        const CountRecord = AV.Object.extend('StockCountRecord')
        const record = new CountRecord()

        record.set('planId', recordData.planId)
        record.set('itemId', recordData.itemId)
        record.set('systemQuantity', recordData.systemQuantity)
        record.set('actualQuantity', recordData.actualQuantity)
        record.set('difference', recordData.difference)
        record.set('remark', recordData.remark || '')
        record.set('countBy', recordData.countBy)
        record.set('countDate', recordData.countDate)

        const savedRecord = await record.save()

        const newRecord: StockCountRecord = {
          id: savedRecord.id,
          planId: savedRecord.get('planId'),
          itemId: savedRecord.get('itemId'),
          systemQuantity: savedRecord.get('systemQuantity'),
          actualQuantity: savedRecord.get('actualQuantity'),
          difference: savedRecord.get('difference'),
          remark: savedRecord.get('remark'),
          countBy: savedRecord.get('countBy'),
          countDate: savedRecord.get('countDate'),
          createdAt: savedRecord.createdAt
        }

        this.countRecords.unshift(newRecord)

        // 如果有差异，自动调整库存
        if (newRecord.difference !== 0) {
          await this.adjustStock(
            newRecord.itemId,
            newRecord.actualQuantity,
            `盘点调整 - 计划: ${newRecord.planId}`
          )
        }

        return { success: true, data: newRecord }
      } catch (error) {
        console.error('提交盘点记录失败:', error)
        return { success: false, error: '提交盘点记录失败' }
      }
    },

    // 完成盘点
    async completeCount(planId: string) {
      try {
        const plan = this.countPlans.find(p => p.id === planId)
        if (!plan) {
          return { success: false, error: '盘点计划不存在' }
        }

        plan.status = 'completed'
        plan.endDate = new Date()
        plan.updatedAt = new Date()

        return { success: true, data: plan }
      } catch (error) {
        console.error('完成盘点失败:', error)
        return { success: false, error: '完成盘点失败' }
      }
    },

    // 获取盘点记录
    async getCountRecords(planId?: string) {
      try {
        let records = this.countRecords

        if (planId) {
          records = records.filter(record => record.planId === planId)
        }

        return { success: true, data: records }
      } catch (error) {
        console.error('获取盘点记录失败:', error)
        return { success: false, error: '获取盘点记录失败' }
      }
    },

    // 重置状态
    reset() {
      this.items = []
      this.transactions = []
      this.alerts = []
      this.countPlans = []
      this.countRecords = []
      this.loading = false
      this.currentPage = 0
      this.totalPages = 0
      this.filters = {}
    }
  }
})
