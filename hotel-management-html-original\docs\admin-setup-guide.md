# 管理员账号设置指南

## 快速设置步骤

### 1. 登录LeanCloud控制台
1. 访问 [LeanCloud控制台](https://console.leancloud.cn/)
2. 选择您的应用

### 2. 设置管理员权限
1. 进入 **数据存储** -> **_User** 表
2. 找到要设置为管理员的用户记录
3. 点击该用户记录进行编辑
4. 添加或修改 `roles` 字段：
   - 字段名：`roles`
   - 字段类型：`Array`
   - 字段值：`["admin"]`
5. 保存更改

### 3. 验证设置
1. 用该账号登录工作日志系统
2. 检查浏览器控制台，应该看到类似日志：
   ```
   用户 admin_user 权限检查: roles=["admin"], isAdmin=true
   ```
3. 确认筛选控件区域显示

## 数据格式示例

### 正确的管理员用户数据格式：
```json
{
  "objectId": "用户ID",
  "username": "admin_user",
  "realName": "管理员姓名",
  "roles": ["admin"],
  "createdAt": "创建时间",
  "updatedAt": "更新时间"
}
```

### 错误的格式（不会生效）：
```json
{
  "roles": "admin"        // ❌ 字符串格式
}
```

```json
{
  "roles": ["administrator"]  // ❌ 错误的角色名
}
```

## 权限验证

### 管理员用户应该能够：
- ✅ 看到筛选控件区域
- ✅ 查看所有用户的日志
- ✅ 使用日期筛选功能
- ✅ 使用用户筛选功能
- ✅ 在控制台看到权限检查日志

### 普通用户应该：
- ❌ 看不到筛选控件区域
- ✅ 只能查看自己的日志
- ❌ 无法使用筛选功能
- ✅ 在控制台看到权限检查日志显示 `isAdmin=false`

## 常见问题

### Q: 设置了roles字段但仍然没有管理员权限？
A: 检查以下几点：
1. roles字段是否为数组格式 `["admin"]`
2. 是否重新登录了账号
3. 浏览器控制台的权限检查日志

### Q: 如何批量设置多个管理员？
A: 可以通过LeanCloud的云引擎或REST API批量更新：
```javascript
// 云引擎示例代码
const query = new AV.Query(AV.User);
query.containedIn('username', ['user1', 'user2', 'user3']);
const users = await query.find();
users.forEach(user => {
    user.set('roles', ['admin']);
});
await AV.Object.saveAll(users);
```

### Q: 如何移除管理员权限？
A: 将roles字段设置为空数组 `[]` 或删除该字段

## 安全建议

1. **最小权限原则**：只给必要的用户分配管理员权限
2. **定期审查**：定期检查管理员账号列表
3. **日志监控**：监控管理员的操作行为
4. **备份策略**：在修改权限前备份用户数据

## 技术说明

权限检查的核心代码：
```javascript
isCurrentUserAdmin() {
    if (!this.currentUser) return false;
    
    const userRoles = this.currentUser.get('roles') || [];
    
    // 确保roles是数组格式
    if (!Array.isArray(userRoles)) {
        console.warn(`用户的roles字段不是数组格式:`, userRoles);
        return false;
    }
    
    return userRoles.includes('admin');
}
```

这个实现确保了：
- 只有roles字段包含"admin"的用户才被认为是管理员
- roles必须是数组格式
- 提供详细的调试信息
