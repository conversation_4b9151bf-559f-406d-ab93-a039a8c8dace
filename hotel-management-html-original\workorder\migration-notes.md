# 工单系统说明文档

## 📁 文件结构清理完成

### 当前文件列表
- ✅ **index.html** - 工单管理主页面
- ✅ **workorder.html** - 工单提交页面
- ✅ **workorder-query.html** - 工单查询页面
- ✅ **workorder-manage.html** - 工单处理页面
- 📝 **migration-notes.md** - 系统说明文档

### 已删除的重复文件
- ❌ ~~workorder-manage-v2.html~~ (已合并到正式版本)
- ❌ ~~workorder-query-v2.html~~ (已合并到正式版本)
- ❌ ~~test-header.html~~ (测试文件已删除)

## 🔧 头部登录功能完善

### 修复内容
1. **BaseWorkLogApp页面** (index.html, workorder-manage.html)
   - ✅ 添加头部元素引用到getPageElements()方法
   - ✅ 登录后正确显示用户信息和退出按钮
   - ✅ 未登录时显示登录按钮

2. **WorkOrder模块页面** (workorder.html)
   - ✅ 在workorder.js中添加头部更新逻辑
   - ✅ 实现updateUserDisplay()方法
   - ✅ 完善showUserInterface()和showWelcomePage()方法

### 头部显示逻辑
```javascript
// 登录后显示
userInfo.classList.remove('hidden');  // 显示用户信息
loginBtn.classList.add('hidden');     // 隐藏登录按钮

// 未登录时显示
userInfo.classList.add('hidden');     // 隐藏用户信息
loginBtn.classList.remove('hidden');  // 显示登录按钮
```

## 💾 数据库兼容性

### 当前状态
- 系统界面已从"报修系统"更新为"工单系统"
- 数据库表名暂时保持为 `RepairOrder`，以保持与现有数据的兼容性
- 所有功能正常工作，使用现有的RepairOrder表

## 未来迁移计划

### 1. 数据表迁移（可选）
如果需要将表名从 `RepairOrder` 改为 `WorkOrder`，可以执行以下步骤：

#### 方案A：LeanCloud控制台操作
1. 登录LeanCloud控制台
2. 进入数据存储 > 结构化数据
3. 找到RepairOrder表
4. 导出所有数据
5. 创建新的WorkOrder表
6. 导入数据到WorkOrder表
7. 更新代码中的表名引用

#### 方案B：代码迁移脚本
```javascript
// 迁移脚本示例
async function migrateRepairOrderToWorkOrder() {
    const RepairOrder = AV.Object.extend('RepairOrder');
    const WorkOrder = AV.Object.extend('WorkOrder');
    
    const query = new AV.Query(RepairOrder);
    query.limit(1000); // 分批处理
    
    const repairOrders = await query.find();
    
    for (const repairOrder of repairOrders) {
        const workOrder = new WorkOrder();
        
        // 复制所有字段
        const attributes = repairOrder.attributes;
        Object.keys(attributes).forEach(key => {
            workOrder.set(key, attributes[key]);
        });
        
        // 保存新记录
        await workOrder.save();
    }
}
```

### 2. 代码更新
迁移完成后，需要更新以下文件中的表名：
- `workorder/index.html` - loadStatistics方法
- `js/modules/workorder.js` - saveWorkOrder方法
- `workorder/workorder-query.html` - loadOrder和showMyOrders方法
- `workorder/workorder-manage.html` - loadOrders方法

将所有 `RepairOrder` 替换为 `WorkOrder`

### 3. 测试验证
1. 确保所有工单功能正常
2. 验证数据完整性
3. 测试权限控制
4. 检查统计数据准确性

## 注意事项
- 当前系统完全兼容现有数据
- 迁移是可选的，不影响系统功能
- 建议在低峰期进行迁移操作
- 迁移前务必备份数据

## 系统功能对照

### 原报修系统 → 新工单系统
- 报修登记 → 工单提交
- 报修查询 → 工单查询  
- 报修管理 → 工单处理
- 报修工单 → 工单
- 故障描述 → 工单描述
- 报修类别 → 工单类别

### 数据字段保持不变
- orderNumber: 工单号
- reporter: 提交人
- status: 状态
- category: 类别
- urgency: 紧急程度
- location: 位置
- description: 描述
- images: 图片
- reportTime: 提交时间
- processor: 处理人
- acceptTime: 接单时间
- completeTime: 完成时间
