<template>
  <div>
    <!-- 图片网格 -->
    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
      <div 
        v-for="(image, index) in images" 
        :key="index"
        class="relative group cursor-pointer"
        @click="openPreview(index)"
      >
        <img 
          :src="image" 
          :alt="`图片 ${index + 1}`"
          class="w-full h-24 object-cover rounded border border-gray-200 hover:shadow-md transition-shadow"
        />
        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all rounded flex items-center justify-center">
          <Icon name="mdi:magnify" size="24" class="text-white opacity-0 group-hover:opacity-100 transition-opacity" />
        </div>
      </div>
    </div>

    <!-- 图片预览模态框 -->
    <div 
      v-if="showPreview" 
      class="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50"
      @click="closePreview"
    >
      <div class="relative max-w-4xl max-h-full p-4" @click.stop>
        <!-- 关闭按钮 -->
        <button 
          @click="closePreview"
          class="absolute top-4 right-4 text-white hover:text-gray-300 z-10"
        >
          <Icon name="mdi:close" size="32" />
        </button>

        <!-- 上一张按钮 -->
        <button 
          v-if="images.length > 1"
          @click="previousImage"
          class="absolute left-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 z-10"
        >
          <Icon name="mdi:chevron-left" size="48" />
        </button>

        <!-- 下一张按钮 -->
        <button 
          v-if="images.length > 1"
          @click="nextImage"
          class="absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 z-10"
        >
          <Icon name="mdi:chevron-right" size="48" />
        </button>

        <!-- 当前图片 -->
        <img 
          :src="images[currentIndex]" 
          :alt="`图片 ${currentIndex + 1}`"
          class="max-w-full max-h-full object-contain"
        />

        <!-- 图片信息 -->
        <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white text-center">
          <p class="text-sm">{{ currentIndex + 1 }} / {{ images.length }}</p>
        </div>

        <!-- 缩略图导航 -->
        <div v-if="images.length > 1" class="absolute bottom-16 left-1/2 transform -translate-x-1/2 flex space-x-2">
          <button 
            v-for="(image, index) in images" 
            :key="index"
            @click="currentIndex = index"
            class="w-12 h-12 rounded border-2 overflow-hidden"
            :class="index === currentIndex ? 'border-white' : 'border-gray-400'"
          >
            <img 
              :src="image" 
              :alt="`缩略图 ${index + 1}`"
              class="w-full h-full object-cover"
            />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  images: string[]
}

const props = defineProps<Props>()

// 响应式数据
const showPreview = ref(false)
const currentIndex = ref(0)

// 方法
const openPreview = (index: number) => {
  currentIndex.value = index
  showPreview.value = true
  document.body.style.overflow = 'hidden'
}

const closePreview = () => {
  showPreview.value = false
  document.body.style.overflow = 'auto'
}

const previousImage = () => {
  currentIndex.value = currentIndex.value > 0 ? currentIndex.value - 1 : props.images.length - 1
}

const nextImage = () => {
  currentIndex.value = currentIndex.value < props.images.length - 1 ? currentIndex.value + 1 : 0
}

// 键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (!showPreview.value) return
  
  switch (event.key) {
    case 'Escape':
      closePreview()
      break
    case 'ArrowLeft':
      previousImage()
      break
    case 'ArrowRight':
      nextImage()
      break
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  document.body.style.overflow = 'auto'
})
</script>
