<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <!-- 微信浏览器兼容性设置 -->
    <meta name="x5-orientation" content="portrait">
    <meta name="x5-fullscreen" content="true">
    <meta name="x5-page-mode" content="app">
    <title>工单管理</title>
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- LeanCloud SDK -->
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <style>
        /* 微信浏览器兼容性修复 */
        * {
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
        }
        
        body {
            -webkit-overflow-scrolling: touch;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        
        .btn-fix {
            -webkit-appearance: none;
            appearance: none;
            border-radius: 8px;
            border: none;
            outline: none;
        }
        
        .fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .loading {
            border-top-color: #3498db;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .status-pending { @apply bg-yellow-100 text-yellow-800; }
        .status-accepted { @apply bg-blue-100 text-blue-800; }
        .status-processing { @apply bg-purple-100 text-purple-800; }
        .status-completed { @apply bg-green-100 text-green-800; }
        .status-cancelled { @apply bg-gray-100 text-gray-800; }
        
        .urgency-低 { @apply bg-gray-100 text-gray-800; }
        .urgency-中 { @apply bg-yellow-100 text-yellow-800; }
        .urgency-高 { @apply bg-orange-100 text-orange-800; }
        .urgency-紧急 { @apply bg-red-100 text-red-800; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
        <div class="max-w-6xl mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-4">
                    <a href="index.html" class="text-gray-600 hover:text-gray-800">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                    </a>
                    <h1 class="text-xl font-semibold text-gray-800">工单管理</h1>
                    <span id="refreshTime" class="text-sm text-gray-500"></span>
                </div>
                <div id="userInfo" class="hidden flex items-center space-x-2">
                    <span id="realName" class="text-gray-800 font-medium text-sm"></span>
                    <a id="adminLink" href="admin.html" class="hidden bg-purple-500 hover:bg-purple-600 text-white px-3 py-1 rounded-lg text-sm transition-colors btn-fix">
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            管理
                        </span>
                    </a>
                    <button id="logoutBtn" class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded-lg text-sm transition-colors btn-fix">
                        退出
                    </button>
                </div>
                <button id="loginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm transition-colors btn-fix">
                    登录
                </button>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="max-w-6xl mx-auto px-4 py-6">
        <!-- 欢迎页面 -->
        <div id="welcomeSection" class="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg shadow-sm border border-blue-200 p-8 text-center">
            <h1 class="text-3xl font-bold text-gray-800 mb-4">工单管理系统</h1>
            <div class="text-xl text-blue-600 font-semibold mb-2">
                相润金鹏酒店工程部
            </div>
            <p class="text-gray-600 text-lg mb-6">管理和处理设备报修工单</p>
            
            <div class="grid md:grid-cols-3 gap-6 mb-8">
                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <div class="text-blue-500 text-3xl mb-3">📋</div>
                    <h3 class="font-semibold text-gray-800 mb-2">工单管理</h3>
                    <p class="text-gray-600 text-sm">查看和处理待接单工单</p>
                </div>
                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <div class="text-green-500 text-3xl mb-3">⚡</div>
                    <h3 class="font-semibold text-gray-800 mb-2">快速响应</h3>
                    <p class="text-gray-600 text-sm">及时接单并开始处理</p>
                </div>
                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <div class="text-purple-500 text-3xl mb-3">📊</div>
                    <h3 class="font-semibold text-gray-800 mb-2">状态跟踪</h3>
                    <p class="text-gray-600 text-sm">实时更新工单处理状态</p>
                </div>
            </div>
            
            <button id="welcomeLoginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors font-medium btn-fix">
                立即登录
            </button>
        </div>

        <!-- 工单统计面板 -->
        <div id="statsPanel" class="hidden mb-6">
            <!-- 移动端：2行布局 -->
            <div class="block md:hidden">
                <div class="grid grid-cols-2 gap-3 mb-3">
                    <!-- 待接单 -->
                    <div class="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-4 border border-yellow-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-xs text-yellow-700 mb-1">待接单</p>
                                <p id="pendingCount" class="text-lg font-bold text-yellow-600">0</p>
                            </div>
                            <div class="bg-yellow-200 rounded-full p-2">
                                <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <!-- 处理中 -->
                    <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4 border border-blue-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-xs text-blue-700 mb-1">处理中</p>
                                <p id="processingCount" class="text-lg font-bold text-blue-600">0</p>
                            </div>
                            <div class="bg-blue-200 rounded-full p-2">
                                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-3">
                    <!-- 今日完成 -->
                    <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-4 border border-green-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-xs text-green-700 mb-1">今日完成</p>
                                <p id="todayCompletedCount" class="text-lg font-bold text-green-600">0</p>
                            </div>
                            <div class="bg-green-200 rounded-full p-2">
                                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <!-- 总工单 -->
                    <div class="bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-4 border border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-xs text-gray-700 mb-1">总工单</p>
                                <p id="totalCount" class="text-lg font-bold text-gray-600">0</p>
                            </div>
                            <div class="bg-gray-200 rounded-full p-2">
                                <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 桌面端：1行布局 -->
            <div class="hidden md:grid md:grid-cols-4 gap-6">
                <!-- 待接单 -->
                <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">待接单</p>
                            <p class="pending-count text-2xl font-bold text-yellow-600">0</p>
                        </div>
                        <div class="bg-yellow-100 rounded-full p-3">
                            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- 处理中 -->
                <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">处理中</p>
                            <p class="processing-count text-2xl font-bold text-blue-600">0</p>
                        </div>
                        <div class="bg-blue-100 rounded-full p-3">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- 今日完成 -->
                <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">今日完成</p>
                            <p class="completed-count text-2xl font-bold text-green-600">0</p>
                        </div>
                        <div class="bg-green-100 rounded-full p-3">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- 总工单 -->
                <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">总工单</p>
                            <p class="total-count text-2xl font-bold text-gray-600">0</p>
                        </div>
                        <div class="bg-gray-100 rounded-full p-3">
                            <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选和操作面板 -->
        <div id="controlPanel" class="hidden bg-white rounded-2xl shadow-sm border border-gray-100 p-6 mb-6">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div class="flex flex-wrap gap-4">
                    <!-- 状态筛选 -->
                    <select id="statusFilter" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                        <option value="">全部状态</option>
                        <option value="pending">待接单</option>
                        <option value="accepted">已接单</option>
                        <option value="processing">处理中</option>
                        <option value="completed">已完成</option>
                    </select>
                    
                    <!-- 紧急程度筛选 -->
                    <select id="urgencyFilter" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                        <option value="">全部紧急程度</option>
                        <option value="紧急">紧急</option>
                        <option value="高">高</option>
                        <option value="中">中</option>
                        <option value="低">低</option>
                    </select>
                    
                    <!-- 类别筛选 -->
                    <select id="categoryFilter" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                        <option value="">全部类别</option>
                        <option value="电气">电气设备</option>
                        <option value="水暖">水暖设备</option>
                        <option value="空调">空调设备</option>
                        <option value="电梯">电梯设备</option>
                        <option value="消防">消防设备</option>
                        <option value="其他">其他设备</option>
                    </select>
                </div>
                
                <div class="flex gap-3">
                    <button id="refreshBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors text-sm font-medium btn-fix">
                        <span class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            刷新
                        </span>
                    </button>
                    <button id="autoRefreshBtn" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors text-sm font-medium btn-fix">
                        自动刷新: 关
                    </button>
                </div>
            </div>
        </div>

        <!-- 加载指示器 -->
        <div id="loadingIndicator" class="hidden flex justify-center py-8">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 border-4 border-gray-300 border-t-blue-500 rounded-full loading"></div>
                <span class="text-gray-600">正在加载工单...</span>
            </div>
        </div>

        <!-- 工单列表 -->
        <div id="ordersList" class="hidden space-y-4">
            <!-- 工单项目将在这里动态加载 -->
        </div>

        <!-- 空状态 -->
        <div id="emptyState" class="hidden text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">暂无工单</h3>
            <p class="mt-1 text-sm text-gray-500">当前没有符合条件的工单</p>
        </div>
    </main>

    <!-- 工单详情弹窗 -->
    <div id="orderModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto fade-in">
            <div class="p-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 id="modalTitle" class="text-xl font-semibold text-gray-800">工单详情</h2>
                    <button id="closeModal" class="text-gray-400 hover:text-gray-600 text-2xl btn-fix">&times;</button>
                </div>
            </div>
            <div id="modalContent" class="p-6">
                <!-- 工单详情内容将在这里动态加载 -->
            </div>
        </div>
    </div>

    <!-- 登录弹窗 -->
    <div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md fade-in">
            <div class="p-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-xl font-semibold text-gray-800">登录</h2>
                    <button id="closeLoginModal" class="text-gray-400 hover:text-gray-600 text-2xl btn-fix">&times;</button>
                </div>
            </div>
            <div class="p-4">
                <form id="loginForm" class="space-y-4">
                    <div>
                        <label for="loginUsername" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                        <input type="text" id="loginUsername" autocomplete="username"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm" required>
                    </div>
                    <div>
                        <label for="loginPassword" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                        <input type="password" id="loginPassword" autocomplete="current-password"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm" required>
                    </div>
                    <button type="submit" class="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">
                        登录
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- 引入模块化JS文件 -->
    <script src="../js/config.js?v=1.0"></script>
    <script src="../js/utils.js?v=1.0"></script>
    <script src="../js/auth.js?v=1.0"></script>
    <script src="../js/base-app.js?v=1.0"></script>
    <script src="../js/modules/repair-manage.js?v=1.0"></script>
    <script>
        // 初始化工单管理应用
        let repairManageApp;
        
        document.addEventListener('DOMContentLoaded', function() {
            function checkAndInitApp() {
                if (typeof AV !== 'undefined') {
                    try {
                        repairManageApp = new RepairManageApp();
                        repairManageApp.init();
                        console.log('工单管理应用初始化成功');
                    } catch (error) {
                        console.error('工单管理应用初始化失败:', error);
                        WorkLogUtils.showMessage('应用初始化失败: ' + error.message, 'error');
                    }
                } else {
                    setTimeout(checkAndInitApp, 100);
                }
            }
            
            checkAndInitApp();
        });
    </script>
</body>
</html>
