<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <!-- 微信浏览器兼容性设置 -->
    <meta name="x5-orientation" content="portrait">
    <meta name="x5-fullscreen" content="true">
    <meta name="x5-page-mode" content="app">
    <title>维修登记</title>

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- LeanCloud SDK -->
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <!-- 备用 CDN 源 -->
    <script>
        window.addEventListener('load', function() {
            if (typeof AV === 'undefined') {
                console.log('主 CDN 加载失败，尝试备用源...');
                var script = document.createElement('script');
                script.src = 'https://cdn.bootcdn.net/ajax/libs/leancloud-storage/4.15.2/av-min.js';
                script.onerror = function() {
                    console.error('所有 CDN 源都加载失败，请检查网络连接');
                    alert('网络连接异常，请检查网络后刷新页面');
                };
                document.head.appendChild(script);
            }
        });
    </script>
    <style>
        /* 微信浏览器兼容性修复 */
        * {
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
        }
        
        body {
            -webkit-overflow-scrolling: touch;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        
        input, textarea, button {
            -webkit-appearance: none;
            appearance: none;
            border-radius: 0;
        }
        
        button {
            outline: none;
            border: none;
        }
        
        .overflow-y-auto {
            -webkit-overflow-scrolling: touch;
        }
        
        .modal-overlay {
            backdrop-filter: blur(4px);
        }
        .fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .loading {
            border-top-color: #3498db;
            animation: spin 1s ease-in-out infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .btn-fix {
            -webkit-appearance: none;
            appearance: none;
            border-radius: 8px;
            border: none;
            outline: none;
        }
        
        /* 开关按钮样式 */
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: #2196F3;
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
        <div class="max-w-4xl mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-3">
                    <a href="index.html" class="text-gray-600 hover:text-gray-800 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                    </a>
                    <h1 class="text-xl font-semibold text-gray-800">设备报修</h1>
                </div>
                <div id="userInfo" class="hidden items-center space-x-2 sm:space-x-4">
                    <span id="realName" class="text-gray-800 font-medium text-sm sm:text-base"></span>
                    <a id="adminLink" href="admin.html" class="hidden bg-purple-500 hover:bg-purple-600 text-white px-2 py-1 sm:px-4 sm:py-2 rounded-lg text-xs sm:text-sm transition-colors btn-fix mr-2">
                        <span class="flex items-center">
                            <svg class="w-3 h-3 sm:w-4 sm:h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <span class="hidden sm:inline">管理后台</span>
                            <span class="sm:hidden">管理</span>
                        </span>
                    </a>
                    <button id="logoutBtn" class="bg-red-500 hover:bg-red-600 text-white px-2 py-1 sm:px-4 sm:py-2 rounded-lg text-xs sm:text-sm transition-colors btn-fix">
                        <span class="hidden sm:inline">退出登录</span>
                        <span class="sm:hidden">退出</span>
                    </button>
                </div>
                <button id="loginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm transition-colors btn-fix">
                    登录
                </button>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="max-w-4xl mx-auto px-4 py-6">
        <!-- 未登录提示 -->
        <div id="loginPrompt" class="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg shadow-sm border border-blue-200 p-8 mb-6 text-center">
            <div class="mb-6">
                <h2 class="text-2xl font-bold text-gray-800 mb-4">请先登录</h2>
                <p class="text-gray-600 text-lg mb-6">
                    需要登录后才能进行维修登记
                </p>
                <button id="promptLoginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors font-medium btn-fix">
                    立即登录
                </button>
            </div>
        </div>
        
        <!-- 欢迎页面 -->
        <div id="welcomeSection" class="bg-gradient-to-br from-red-50 to-orange-100 rounded-lg shadow-sm border border-red-200 p-8 text-center">
            <div class="mb-6">
                <h1 class="text-3xl font-bold text-gray-800 mb-4">设备报修系统</h1>
                <div class="text-xl text-red-600 font-semibold mb-2">
                    相润金鹏酒店工程部
                </div>
                <p class="text-gray-600 text-lg mb-6">
                    快速提交设备故障报修，工程部将及时响应处理
                </p>
            </div>

            <div class="grid md:grid-cols-3 gap-6 mb-8">
                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <div class="text-red-500 text-3xl mb-3">🔧</div>
                    <h3 class="font-semibold text-gray-800 mb-2">快速报修</h3>
                    <p class="text-gray-600 text-sm">简单填写故障信息即可提交</p>
                </div>
                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <div class="text-orange-500 text-3xl mb-3">⚡</div>
                    <h3 class="font-semibold text-gray-800 mb-2">及时响应</h3>
                    <p class="text-gray-600 text-sm">工程部将快速接单处理</p>
                </div>
                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <div class="text-green-500 text-3xl mb-3">📱</div>
                    <h3 class="font-semibold text-gray-800 mb-2">实时通知</h3>
                    <p class="text-gray-600 text-sm">处理进度实时推送通知</p>
                </div>
            </div>

            <div class="bg-white rounded-lg p-6 shadow-sm">
                <h3 class="font-semibold text-gray-800 mb-3">开始使用</h3>
                <p class="text-gray-600 mb-4">请先登录账号，然后填写报修信息</p>
                <button id="welcomeLoginBtn" class="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg transition-colors font-medium btn-fix">
                    立即登录
                </button>
            </div>
        </div>

        <!-- 报修表单 -->
        <div id="repairSection" class="hidden">
            <!-- 基本信息 -->
            <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 mb-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <span class="bg-red-100 text-red-600 rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">1</span>
                    基本信息
                </h2>
                <form id="repairForm" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                                报修类别 <span class="text-red-500">*</span>
                            </label>
                            <select id="category" required
                                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent">
                                <option value="">请选择类别</option>
                                <option value="电气">电气设备</option>
                                <option value="水暖">水暖设备</option>
                                <option value="空调">空调设备</option>
                                <option value="电梯">电梯设备</option>
                                <option value="消防">消防设备</option>
                                <option value="其他">其他设备</option>
                            </select>
                        </div>

                        <div>
                            <label for="urgency" class="block text-sm font-medium text-gray-700 mb-2">
                                紧急程度 <span class="text-red-500">*</span>
                            </label>
                            <select id="urgency" required
                                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent">
                                <option value="">请选择紧急程度</option>
                                <option value="低">低 - 不影响正常使用</option>
                                <option value="中">中 - 影响部分功能</option>
                                <option value="高">高 - 严重影响使用</option>
                                <option value="紧急">紧急 - 存在安全隐患</option>
                            </select>
                        </div>

                        <div>
                            <label for="location" class="block text-sm font-medium text-gray-700 mb-2">
                                故障位置 <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="location" required
                                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                                placeholder="如：3楼客房301、大堂、地下室等">
                        </div>

                        <div class="md:col-span-2">
                            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                                故障描述 <span class="text-red-500">*</span>
                            </label>
                            <textarea id="description" rows="4" required
                                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                                placeholder="请详细描述故障现象、发生时间等信息"></textarea>
                        </div>
                    </div>
                </form>
            </div>

            <!-- 报修人信息 (自动填充) -->
            <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 mb-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <span class="bg-red-100 text-red-600 rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">2</span>
                    报修人信息
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">报修人姓名</label>
                        <input type="text" id="reporterName" readonly
                            class="w-full p-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-600"
                            placeholder="自动获取">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">联系电话</label>
                        <input type="text" id="reporterPhone" readonly
                            class="w-full p-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-600"
                            placeholder="自动获取">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">所属部门</label>
                        <input type="text" id="reporterDept" readonly
                            class="w-full p-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-600"
                            placeholder="自动获取">
                    </div>
                </div>
                <div class="mt-4 p-3 bg-blue-50 rounded-lg">
                    <p class="text-sm text-blue-600">
                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        联系信息从您的账户资料中自动获取，如需修改请联系管理员
                    </p>
                </div>
            </div>

            <!-- 图片上传 -->
            <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 mb-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <span class="bg-red-100 text-red-600 rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">3</span>
                    故障图片 <span class="text-gray-500 text-sm font-normal ml-2">(可选)</span>
                </h2>
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <input type="file" id="imageInput" accept="image/*" multiple class="hidden">
                    <div id="uploadArea" class="cursor-pointer">
                        <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                        <p class="mt-2 text-sm text-gray-600">
                            <span class="font-medium text-red-600 hover:text-red-500">点击上传图片</span>
                            或拖拽图片到此处
                        </p>
                        <p class="text-xs text-gray-500">支持 PNG, JPG, GIF 格式，最多5张</p>
                    </div>
                    <div id="imagePreview" class="mt-4 grid grid-cols-2 md:grid-cols-5 gap-4 hidden"></div>
                </div>
            </div>

            <!-- 提交按钮 -->
            <div class="flex justify-center space-x-4">
                <button type="button" id="cancelBtn" class="bg-gray-500 hover:bg-gray-600 text-white px-8 py-3 rounded-lg transition-colors font-medium btn-fix">
                    取消
                </button>
                <button type="submit" id="submitBtn" class="bg-red-500 hover:bg-red-600 text-white px-8 py-3 rounded-lg transition-colors font-medium btn-fix">
                    <span class="flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                        </svg>
                        提交报修
                    </span>
                </button>
            </div>
        </div>
    </main>

    <!-- 登录弹窗 -->
    <div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 modal-overlay flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto fade-in">
            <div class="sticky top-0 bg-white p-4 border-b border-gray-200 rounded-t-lg">
                <div class="flex justify-between items-center">
                    <h2 id="modalTitle" class="text-xl font-semibold text-gray-800">登录</h2>
                    <button id="closeModal" class="text-gray-400 hover:text-gray-600 text-2xl btn-fix">&times;</button>
                </div>
            </div>
            
            <div class="p-4">
                <!-- 登录表单 -->
                <form id="loginForm" class="space-y-4">
                    <div>
                        <label for="loginUsername" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                        <input type="text" id="loginUsername" autocomplete="username" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm" required>
                    </div>
                    <div>
                        <label for="loginPassword" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                        <input type="password" id="loginPassword" autocomplete="current-password" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm" required>
                    </div>
                    <div class="flex flex-col sm:flex-row gap-3 pt-2">
                        <button type="submit" class="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">登录</button>
                    </div>
                </form>
                
                <!-- 注册表单 -->
                <form id="registerForm" class="hidden space-y-4 mt-4">
                    <div>
                        <label for="registerUsername" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                        <input type="text" id="registerUsername" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm" required>
                    </div>
                    <div>
                        <label for="registerRealName" class="block text-sm font-medium text-gray-700 mb-1">真实姓名</label>
                        <input type="text" id="registerRealName" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm" required>
                    </div>
                    <div>
                        <label for="registerPassword" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                        <input type="password" id="registerPassword" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm" required>
                    </div>
                    <div>
                        <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-1">确认密码</label>
                        <input type="password" id="confirmPassword" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm" required>
                    </div>
                    <div class="flex flex-col sm:flex-row gap-3 pt-2">
                        <button type="submit" class="flex-1 bg-green-500 hover:bg-green-600 text-white py-3 rounded-lg transition-colors text-sm font-medium btn-fix">注册</button>
                    </div>
                </form>
                
                <!-- 表单切换 -->
                <div class="mt-4 text-center">
                    <p id="loginSwitchText" class="text-sm text-gray-600">
                        还没有账号？
                        <a href="#" id="showRegisterBtn" class="text-blue-500 hover:text-blue-600 font-medium">立即注册</a>
                    </p>
                    <p id="registerSwitchText" class="hidden text-sm text-gray-600">
                        已有账号？
                        <a href="#" id="showLoginBtn" class="text-blue-500 hover:text-blue-600 font-medium">立即登录</a>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户信息显示 -->
    <div id="userInfo" class="hidden flex items-center space-x-3 p-4 bg-white rounded-xl shadow-sm border border-gray-100 mb-6">
        <div class="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
            <span class="text-blue-600 font-medium">{{ initials }}</span>
        </div>
        <div class="flex-1 min-w-0">
            <p class="text-sm font-medium text-gray-900 truncate" id="realName">加载中...</p>
            <p class="text-sm text-gray-500 flex items-center">
                <span id="username">用户名</span>
                <span class="mx-2">•</span>
                <span>工号: 123456</span>
            </p>
        </div>
        <button id="logoutBtn" class="ml-2 px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 transition-colors">
            退出
        </button>
    </div>

    <script src="../js/config.js?v=1.0"></script>
    <script src="../js/utils.js?v=1.0"></script>
    <script src="../js/auth.js?v=1.0"></script>
    <script src="../js/base-app.js?v=1.0"></script>
    <script src="../js/modules/repair.js?v=1.0"></script>
    <script>
        // 初始化报修应用
        let repairApp;

        document.addEventListener('DOMContentLoaded', function() {
            function checkAndInitApp() {
                if (typeof AV !== 'undefined' && typeof RepairApp !== 'undefined') {
                    try {
                        repairApp = new RepairApp();
                        repairApp.init();
                        console.log('报修应用初始化成功');
                    } catch (error) {
                        console.error('报修应用初始化失败:', error);
                        if (typeof WorkLogUtils !== 'undefined') {
                            WorkLogUtils.showMessage('应用初始化失败: ' + error.message, 'error');
                        }
                    }
                } else {
                    setTimeout(checkAndInitApp, 100);
                }
            }

            checkAndInitApp();
        });
    </script>
</body>
</html>