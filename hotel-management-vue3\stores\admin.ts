// stores/admin.ts
import { defineStore } from 'pinia'
import AV from 'leancloud-storage'
import type { User, Department, Role, SystemLog, SystemSettings } from '~/types'

interface AdminState {
  users: User[]
  departments: Department[]
  roles: Role[]
  systemLogs: SystemLog[]
  settings: SystemSettings | null
  loading: boolean
  currentPage: number
  totalPages: number
  filters: {
    userRole?: string
    department?: string
    status?: string
    dateRange?: [Date, Date]
  }
}

export const useAdminStore = defineStore('admin', {
  state: (): AdminState => ({
    users: [],
    departments: [],
    roles: [],
    systemLogs: [],
    settings: null,
    loading: false,
    currentPage: 0,
    totalPages: 0,
    filters: {}
  }),

  getters: {
    // 用户统计
    userStats: (state) => ({
      total: state.users.length,
      active: state.users.filter(u => u.status === 'active').length,
      admin: state.users.filter(u => u.roles?.includes('admin')).length,
      engineer: state.users.filter(u => u.roles?.includes('engineer')).length
    }),

    // 部门统计
    departmentStats: (state) => ({
      total: state.departments.length,
      system: state.departments.filter(d => d.isSystem).length,
      custom: state.departments.filter(d => !d.isSystem).length
    }),

    // 角色统计
    roleStats: (state) => ({
      total: state.roles.length,
      system: state.roles.filter(r => r.isSystem).length,
      custom: state.roles.filter(r => !r.isSystem).length
    })
  },

  actions: {
    // 获取用户列表
    async fetchUsers(options: { page?: number; filters?: any } = {}) {
      try {
        this.loading = true

        const { userManagement } = useLeanCloud()
        const result = await userManagement.getUsers({
          page: options.page,
          limit: 20,
          department: options.filters?.department,
          role: options.filters?.role,
          keyword: options.filters?.keyword
        })

        if (result.success) {
          if (options.page === 0) {
            this.users = result.users
          } else {
            this.users.push(...result.users)
          }
          this.total = result.total
          return { success: true, data: result.users }
        } else {
          // 如果LeanCloud调用失败，使用模拟数据
          console.warn('LeanCloud调用失败，使用模拟数据:', result.error)
          const mockUsers: User[] = [
          {
            id: '1',
            username: 'admin',
            name: '系统管理员',
            phone: '13800138000',
            department: '管理部',
            roles: ['admin'],
            status: 'active',
            createdAt: new Date('2024-01-01'),
            lastLoginAt: new Date()
          },
          {
            id: '2',
            username: 'engineer1',
            name: '张工程师',
            phone: '13800138001',
            department: '工程部',
            roles: ['engineer'],
            status: 'active',
            createdAt: new Date('2024-01-02'),
            lastLoginAt: new Date()
          }
        ]
        
        this.users = mockUsers
        this.currentPage = options.page || 0
        this.totalPages = Math.ceil(mockUsers.length / 10)
        
        return { success: true, data: mockUsers }
      } catch (error) {
        console.error('获取用户列表失败:', error)
        return { success: false, error: '获取用户列表失败' }
      } finally {
        this.loading = false
      }
    },

    // 创建用户
    async createUser(userData: Omit<User, 'id' | 'createdAt'> & { password: string }) {
      try {
        const { userManagement } = useLeanCloud()
        const result = await userManagement.createUser({
          username: userData.username,
          password: userData.password,
          realName: userData.realName,
          email: userData.email,
          phone: userData.phone,
          department: userData.department,
          roles: userData.roles
        })

        if (result.success && result.user) {
          this.users.unshift(result.user)
          return { success: true, data: result.user }
        } else {
          return { success: false, error: result.error }
        }
      } catch (error: any) {
        console.error('创建用户失败:', error)
        return { success: false, error: error.message || '创建用户失败' }
      }
    },

    // 更新用户
    async updateUser(id: string, userData: Partial<User>) {
      try {
        const index = this.users.findIndex(u => u.id === id)
        if (index === -1) {
          return { success: false, error: '用户不存在' }
        }
        
        this.users[index] = { ...this.users[index], ...userData }
        
        return { success: true, data: this.users[index] }
      } catch (error) {
        console.error('更新用户失败:', error)
        return { success: false, error: '更新用户失败' }
      }
    },

    // 删除用户
    async deleteUser(id: string) {
      try {
        const index = this.users.findIndex(u => u.id === id)
        if (index === -1) {
          return { success: false, error: '用户不存在' }
        }
        
        this.users.splice(index, 1)
        
        return { success: true }
      } catch (error) {
        console.error('删除用户失败:', error)
        return { success: false, error: '删除用户失败' }
      }
    },

    // 获取部门列表
    async fetchDepartments() {
      try {
        this.loading = true

        const { department } = useLeanCloud()
        const result = await department.getList()

        if (result.success) {
          // 转换为前端需要的格式
          this.departments = result.departments.map(dept => ({
            id: dept.id,
            name: dept.name,
            description: dept.description,
            isSystem: true,
            userCount: 0, // 这里可以后续优化为实际统计
            createdAt: dept.createdAt
          }))

          return { success: true, data: this.departments }
        } else {
          // 如果LeanCloud调用失败，使用模拟数据
          console.warn('LeanCloud调用失败，使用模拟数据:', result.error)
          const mockDepartments: Department[] = [
            { id: '1', name: '前厅部', description: '负责客人接待和前台服务', isSystem: true, userCount: 5, createdAt: new Date() },
            { id: '2', name: '客房部', description: '负责客房清洁和维护', isSystem: true, userCount: 8, createdAt: new Date() },
            { id: '3', name: '餐饮部', description: '负责餐厅和宴会服务', isSystem: true, userCount: 12, createdAt: new Date() },
            { id: '4', name: '工程部', description: '负责设备维护和维修', isSystem: true, userCount: 6, createdAt: new Date() },
            { id: '5', name: '保安部', description: '负责酒店安全保卫', isSystem: true, userCount: 4, createdAt: new Date() },
            { id: '6', name: '财务部', description: '负责财务管理和会计', isSystem: true, userCount: 3, createdAt: new Date() },
            { id: '7', name: '人事部', description: '负责人力资源管理', isSystem: true, userCount: 2, createdAt: new Date() },
            { id: '8', name: '销售部', description: '负责市场营销和销售', isSystem: true, userCount: 4, createdAt: new Date() }
          ]

          this.departments = mockDepartments
          return { success: true, data: mockDepartments }
        }
      } catch (error: any) {
        console.error('获取部门列表失败:', error)
        return { success: false, error: error.message || '获取部门列表失败' }
      } finally {
        this.loading = false
      }
    },

    // 创建部门
    async createDepartment(departmentData: Omit<Department, 'id' | 'createdAt' | 'userCount'>) {
      try {
        const newDepartment: Department = {
          ...departmentData,
          id: Date.now().toString(),
          userCount: 0,
          createdAt: new Date()
        }

        this.departments.unshift(newDepartment)

        return { success: true, data: newDepartment }
      } catch (error) {
        console.error('创建部门失败:', error)
        return { success: false, error: '创建部门失败' }
      }
    },

    // 更新部门
    async updateDepartment(id: string, departmentData: Partial<Department>) {
      try {
        const index = this.departments.findIndex(d => d.id === id)
        if (index === -1) {
          return { success: false, error: '部门不存在' }
        }

        this.departments[index] = { ...this.departments[index], ...departmentData }

        return { success: true, data: this.departments[index] }
      } catch (error) {
        console.error('更新部门失败:', error)
        return { success: false, error: '更新部门失败' }
      }
    },

    // 获取角色列表
    async fetchRoles() {
      try {
        this.loading = true
        
        // 模拟默认角色数据
        const mockRoles: Role[] = [
          {
            id: '1',
            code: 'admin',
            name: '管理员',
            description: '系统管理员，拥有所有权限',
            isSystem: true,
            permissions: ['user_management', 'system_settings', 'reports'],
            userCount: 2,
            createdAt: new Date()
          },
          {
            id: '2',
            code: 'engineer',
            name: '工程师',
            description: '工程师，可以处理维修工单',
            isSystem: true,
            permissions: ['repair_management', 'inventory_view'],
            userCount: 6,
            createdAt: new Date()
          },
          {
            id: '3',
            code: 'user',
            name: '普通用户',
            description: '普通用户，可以提交报修',
            isSystem: true,
            permissions: ['repair_submit', 'worklog_create'],
            userCount: 25,
            createdAt: new Date()
          }
        ]
        
        this.roles = mockRoles
        
        return { success: true, data: mockRoles }
      } catch (error) {
        console.error('获取角色列表失败:', error)
        return { success: false, error: '获取角色列表失败' }
      } finally {
        this.loading = false
      }
    },

    // 创建角色
    async createRole(roleData: Omit<Role, 'id' | 'createdAt'>) {
      try {
        const newRole: Role = {
          ...roleData,
          id: Date.now().toString(),
          createdAt: new Date()
        }

        this.roles.unshift(newRole)

        return { success: true, data: newRole }
      } catch (error) {
        console.error('创建角色失败:', error)
        return { success: false, error: '创建角色失败' }
      }
    },

    // 更新角色
    async updateRole(id: string, roleData: Partial<Role>) {
      try {
        const index = this.roles.findIndex(r => r.id === id)
        if (index === -1) {
          return { success: false, error: '角色不存在' }
        }

        this.roles[index] = { ...this.roles[index], ...roleData }

        return { success: true, data: this.roles[index] }
      } catch (error) {
        console.error('更新角色失败:', error)
        return { success: false, error: '更新角色失败' }
      }
    },

    // 获取系统日志
    async fetchSystemLogs(options: { page?: number; filters?: any } = {}) {
      try {
        this.loading = true
        
        // 模拟系统日志数据
        const mockLogs: SystemLog[] = [
          {
            id: '1',
            action: 'user_login',
            module: 'auth',
            description: '用户登录系统',
            userId: '1',
            userName: '系统管理员',
            ip: '*************',
            userAgent: 'Mozilla/5.0...',
            createdAt: new Date()
          },
          {
            id: '2',
            action: 'user_create',
            module: 'admin',
            description: '创建新用户',
            userId: '1',
            userName: '系统管理员',
            ip: '*************',
            userAgent: 'Mozilla/5.0...',
            createdAt: new Date(Date.now() - 3600000)
          }
        ]
        
        this.systemLogs = mockLogs
        
        return { success: true, data: mockLogs }
      } catch (error) {
        console.error('获取系统日志失败:', error)
        return { success: false, error: '获取系统日志失败' }
      } finally {
        this.loading = false
      }
    },

    // 获取系统设置
    async fetchSettings() {
      try {
        this.loading = true
        
        // 模拟系统设置数据
        const mockSettings: SystemSettings = {
          id: '1',
          siteName: '酒店管理系统',
          siteDescription: '现代化酒店管理解决方案',
          contactEmail: '<EMAIL>',
          contactPhone: '************',
          enableRegistration: true,
          enableEmailNotification: true,
          enableSMSNotification: false,
          enableWeChatNotification: true,
          maxFileSize: 5,
          allowedFileTypes: ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],
          sessionTimeout: 30,
          passwordMinLength: 6,
          passwordRequireSpecialChar: false,
          backupFrequency: 'daily',
          logRetentionDays: 30,
          updatedAt: new Date()
        }
        
        this.settings = mockSettings
        
        return { success: true, data: mockSettings }
      } catch (error) {
        console.error('获取系统设置失败:', error)
        return { success: false, error: '获取系统设置失败' }
      } finally {
        this.loading = false
      }
    },

    // 更新系统设置
    async updateSettings(settingsData: Partial<SystemSettings>) {
      try {
        if (!this.settings) {
          return { success: false, error: '系统设置不存在' }
        }
        
        this.settings = { ...this.settings, ...settingsData, updatedAt: new Date() }
        
        return { success: true, data: this.settings }
      } catch (error) {
        console.error('更新系统设置失败:', error)
        return { success: false, error: '更新系统设置失败' }
      }
    },

    // 设置筛选条件
    setFilters(filters: any) {
      this.filters = { ...this.filters, ...filters }
    },

    // 重置状态
    reset() {
      this.users = []
      this.departments = []
      this.roles = []
      this.systemLogs = []
      this.settings = null
      this.loading = false
      this.currentPage = 0
      this.totalPages = 0
      this.filters = {}
    }
  }
})
