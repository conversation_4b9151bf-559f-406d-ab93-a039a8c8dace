<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>入库管理 - 库存管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- LeanCloud SDK - 使用更稳定的 CDN 源 -->
    <script src="https://unpkg.com/leancloud-storage@4.15.2/dist/av-min.js"></script>
    <!-- 备用 CDN 源 -->
    <script>
        window.addEventListener('load', function() {
            if (typeof AV === 'undefined') {
                console.log('主 CDN 加载失败，尝试备用源...');
                var script = document.createElement('script');
                script.src = 'https://cdn.bootcdn.net/ajax/libs/leancloud-storage/4.15.2/av-min.js';
                script.onerror = function() {
                    console.error('所有 CDN 源都加载失败，请检查网络连接');
                    alert('网络连接异常，请检查网络后刷新页面');
                };
                document.head.appendChild(script);
            }
        });
    </script>
    <style>
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .btn-fix {
            touch-action: manipulation;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="index.html" class="text-blue-600 hover:text-blue-800 mr-4">
                        ← 返回库存管理
                    </a>
                    <h1 class="text-xl font-semibold text-gray-900">入库管理</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- 用户信息 -->
                    <div id="userInfo" class="flex items-center space-x-2" style="display: none;">
                        <span class="text-sm text-gray-700">用户：</span>
                        <span id="realName" class="text-sm font-medium text-gray-900"></span>
                        <button id="logoutBtn" class="text-sm text-red-600 hover:text-red-800 btn-fix">退出</button>
                    </div>
                    <button id="loginBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm btn-fix">
                        登录
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 入库表单 -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">商品入库</h2>
            <form id="inboundForm" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="productName" class="block text-sm font-medium text-gray-700 mb-1">商品名称</label>
                        <input type="text" id="productName" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <label for="productCode" class="block text-sm font-medium text-gray-700 mb-1">商品编码</label>
                        <input type="text" id="productCode" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <label for="quantity" class="block text-sm font-medium text-gray-700 mb-1">入库数量</label>
                        <input type="number" id="quantity" required min="1" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <label for="unit" class="block text-sm font-medium text-gray-700 mb-1">单位</label>
                        <select id="unit" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">请选择单位</option>
                            <option value="个">个</option>
                            <option value="台">台</option>
                            <option value="套">套</option>
                            <option value="箱">箱</option>
                            <option value="包">包</option>
                        </select>
                    </div>
                    <div>
                        <label for="warehouse" class="block text-sm font-medium text-gray-700 mb-1">入库仓库</label>
                        <select id="warehouse" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">请选择仓库</option>
                            <option value="主仓库">主仓库</option>
                            <option value="备用仓库">备用仓库</option>
                            <option value="临时仓库">临时仓库</option>
                        </select>
                    </div>
                    <div>
                        <label for="supplier" class="block text-sm font-medium text-gray-700 mb-1">供应商</label>
                        <input type="text" id="supplier" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                </div>
                <div>
                    <label for="remarks" class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                    <textarea id="remarks" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"></textarea>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" id="resetBtn" class="px-4 py-2 text-gray-600 hover:text-gray-800 btn-fix">重置</button>
                    <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-md btn-fix">确认入库</button>
                </div>
            </form>
        </div>

        <!-- 入库记录 -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">入库记录</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">商品信息</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数量</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">仓库</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">供应商</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">入库时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作人</th>
                        </tr>
                    </thead>
                    <tbody id="inboundTableBody" class="bg-white divide-y divide-gray-200">
                        <!-- 入库记录将在这里显示 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 引入模块化JS文件 -->
    <script src="../js/config.js"></script>
    <script src="../js/permission-manager.js"></script>
    <script>
        // 入库管理应用类
        class InboundApp {
            constructor() {
                this.elements = {};
            }

            init() {
                this.initElements();
                this.bindEvents();
                this.checkAccess();
                this.loadInboundRecords();
            }

            initElements() {
                this.elements = {
                    realName: document.getElementById('realName'),
                    userInfo: document.getElementById('userInfo'),
                    loginBtn: document.getElementById('loginBtn'),
                    logoutBtn: document.getElementById('logoutBtn'),
                    inboundForm: document.getElementById('inboundForm'),
                    resetBtn: document.getElementById('resetBtn'),
                    inboundTableBody: document.getElementById('inboundTableBody')
                };
            }

            bindEvents() {
                // 表单提交事件
                this.elements.inboundForm.addEventListener('submit', async (e) => {
                    e.preventDefault();
                    await this.handleInbound();
                });

                // 重置按钮事件
                this.elements.resetBtn.addEventListener('click', () => {
                    this.elements.inboundForm.reset();
                });

                // 登录相关事件
                if (this.elements.loginBtn) {
                    this.elements.loginBtn.addEventListener('click', () => {
                        this.showLoginModal();
                    });
                }

                if (this.elements.logoutBtn) {
                    this.elements.logoutBtn.addEventListener('click', async () => {
                        try {
                            await AV.User.logOut();
                            this.checkAccess();
                        } catch (error) {
                            console.error('退出登录失败:', error);
                        }
                    });
                }
            }

            async checkAccess() {
                const currentUser = AV.User.current();
                
                if (currentUser) {
                    // 检查入库权限
                    const hasInboundAccess = await this.checkInboundPermission(currentUser);
                    
                    if (hasInboundAccess) {
                        // 更新用户信息显示
                        this.elements.realName.textContent = currentUser.get('realName') || currentUser.get('username');
                        this.elements.userInfo.style.display = 'flex';
                        this.elements.loginBtn.style.display = 'none';
                    } else {
                        alert('您没有入库管理权限');
                        window.location.href = 'index.html';
                    }
                } else {
                    alert('请先登录');
                    window.location.href = 'index.html';
                }
            }

            async checkInboundPermission(user) {
                const roles = user.get('roles') || [];
                
                // 管理员和超级管理员有入库权限
                if (roles.includes('admin') || roles.includes('super_admin')) {
                    return true;
                }
                
                // 检查是否有特定的入库权限
                if (window.permissionManager) {
                    try {
                        await window.permissionManager.init();
                        return window.permissionManager.hasPermission('inventory', 'inbound', 'create');
                    } catch (error) {
                        console.error('权限检查失败:', error);
                    }
                }
                
                return false;
            }

            async handleInbound() {
                try {
                    const currentUser = AV.User.current();
                    if (!currentUser) {
                        alert('请先登录');
                        return;
                    }

                    const formData = {
                        productName: document.getElementById('productName').value,
                        productCode: document.getElementById('productCode').value,
                        quantity: parseInt(document.getElementById('quantity').value),
                        unit: document.getElementById('unit').value,
                        warehouse: document.getElementById('warehouse').value,
                        supplier: document.getElementById('supplier').value,
                        remarks: document.getElementById('remarks').value,
                        operator: currentUser.get('realName') || currentUser.get('username'),
                        operatorId: currentUser.id,
                        inboundTime: new Date()
                    };

                    // 保存入库记录
                    const InboundRecord = AV.Object.extend('InboundRecord');
                    const record = new InboundRecord();
                    
                    Object.keys(formData).forEach(key => {
                        record.set(key, formData[key]);
                    });

                    await record.save();

                    // 更新商品库存
                    await this.updateProductStock(formData.productCode, formData.quantity, 'inbound');

                    alert('入库成功');
                    this.elements.inboundForm.reset();
                    this.loadInboundRecords();

                } catch (error) {
                    console.error('入库失败:', error);
                    alert('入库失败: ' + error.message);
                }
            }

            async loadInboundRecords() {
                try {
                    const query = new AV.Query('InboundRecord');
                    query.descending('createdAt');
                    query.limit(20);
                    
                    const records = await query.find();
                    
                    const html = records.map(record => `
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">${record.get('productName')}</div>
                                <div class="text-sm text-gray-500">${record.get('productCode')}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                ${record.get('quantity')} ${record.get('unit')}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                ${record.get('warehouse')}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                ${record.get('supplier') || '-'}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                ${record.get('inboundTime').toLocaleString()}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                ${record.get('operator')}
                            </td>
                        </tr>
                    `).join('');
                    
                    this.elements.inboundTableBody.innerHTML = html;

                } catch (error) {
                    console.error('加载入库记录失败:', error);
                    this.elements.inboundTableBody.innerHTML = `
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-red-500">
                                加载入库记录失败: ${error.message}
                            </td>
                        </tr>
                    `;
                }
            }

            async updateProductStock(productCode, quantity, operation) {
                try {
                    // 查找商品
                    const productQuery = new AV.Query('Product');
                    productQuery.equalTo('code', productCode);
                    const product = await productQuery.first();

                    if (product) {
                        const currentStock = product.get('currentStock') || 0;
                        let newStock = currentStock;

                        if (operation === 'inbound') {
                            newStock = currentStock + quantity;
                        } else if (operation === 'outbound') {
                            newStock = Math.max(0, currentStock - quantity);
                        }

                        product.set('currentStock', newStock);
                        await product.save();

                        console.log(`商品 ${productCode} 库存已更新: ${currentStock} → ${newStock}`);
                    } else {
                        console.warn(`未找到商品编码为 ${productCode} 的商品`);
                    }
                } catch (error) {
                    console.error('更新商品库存失败:', error);
                    // 不阻断主流程，只记录错误
                }
            }

            showLoginModal() {
                alert('请返回库存管理首页进行登录');
                window.location.href = 'index.html';
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            function initApp() {
                if (typeof AV !== 'undefined') {
                    const inboundApp = new InboundApp();
                    inboundApp.init();
                } else {
                    setTimeout(initApp, 100);
                }
            }
            initApp();
        });
    </script>
</body>
</html>
